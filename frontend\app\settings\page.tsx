'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../contexts/AuthContext';
import { useSupabaseAuth } from '../../contexts/SupabaseAuthContext';
import { getSupabaseClient } from '@/lib/supabase-singleton';
import Link from 'next/link';
import SubscriptionPlanDisplay from '../../components/SubscriptionPlanDisplay';

export default function SettingsPage() {
  const { isAuthenticated, isLoading, user, hasActiveSubscription, logout, token, refreshSubscriptionStatus } = useAuth();
  const { user: supabaseUser, isLoading: supabaseLoading } = useSupabaseAuth();
  const router = useRouter();

  const [activeTab, setActiveTab] = useState('account');
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [marketingEmails, setMarketingEmails] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });
  const [isSaving, setIsSaving] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);
  const [userDetails, setUserDetails] = useState<{
    created_at?: string;
    subscription_status?: string;
    subscription_updated_at?: string;
  }>({});

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!isLoading && !supabaseLoading) {
      if (!isAuthenticated || !supabaseUser) {
        router.push('/login');
      } else if (supabaseUser) {
        // First fetch user details from Supabase
        fetchUserDetails(supabaseUser.id, false);

        // Then refresh from the API to ensure we have the latest status
        // Use a slight delay to ensure the initial fetch completes first
        const timer = setTimeout(() => {
          refreshSubscriptionStatusFromAPI();
        }, 500);

        return () => clearTimeout(timer);
      }
    }
  }, [isAuthenticated, isLoading, supabaseUser, supabaseLoading, router]);

  // Function to fetch user details from Supabase
  const fetchUserDetails = async (userId: string, skipStatusCheck = false) => {
    try {
      const supabase = getSupabaseClient();
      const { data, error } = await supabase
        .from('users')
        .select('created_at, subscription_status, subscription_updated_at')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching user details:', error);
        return;
      }

      if (data) {
        console.log('User details fetched from Supabase:', data);
        setUserDetails(data);

        // If the subscription status from Supabase doesn't match the one in AuthContext,
        // we should check with the backend API to get the most up-to-date status
        // Only do this if skipStatusCheck is false to prevent infinite recursion
        if (!skipStatusCheck && data.subscription_status !== (hasActiveSubscription ? 'active' : 'inactive')) {
          console.log('Subscription status mismatch, refreshing from API...');
          await refreshSubscriptionStatusFromAPI();
        }
      }
    } catch (error) {
      console.error('Error in fetchUserDetails:', error);
    }
  };

  // Function to refresh subscription status from the backend API
  const refreshSubscriptionStatusFromAPI = async () => {
    try {
      setIsSaving(true);
      setMessage({ type: '', text: '' });

      // Get the API URL from environment variables or fallback to localhost
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';

      // Get the token for authentication
      const authToken = token || '';

      if (!authToken) {
        throw new Error('Authentication token not available. Please log in again.');
      }

      // Call the API to get the current subscription status directly from Stripe
      console.log('Checking subscription status directly from API...');
      const response = await fetch(`${apiUrl}/api/subscription/status`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to check subscription status');
      }

      const data = await response.json();
      console.log('Direct subscription status check result:', data);

      // Update the local state based on the API response
      setUserDetails(prevDetails => ({
        ...prevDetails,
        subscription_status: data.subscriptionStatus,
        subscription_updated_at: new Date().toISOString()
      }));

      // Also update the AuthContext
      if (refreshSubscriptionStatus) {
        await refreshSubscriptionStatus();
      }

      // Update Supabase with the latest status
      if (supabaseUser && data.subscriptionStatus) {
        try {
          const supabase = getSupabaseClient();
          const { error } = await supabase
            .from('users')
            .update({
              subscription_status: data.subscriptionStatus,
              subscription_updated_at: new Date().toISOString()
            })
            .eq('id', supabaseUser.id);

          if (error) {
            console.error('Error updating Supabase with new subscription status:', error);
          } else {
            console.log('Updated Supabase with new subscription status:', data.subscriptionStatus);
          }
        } catch (supabaseError) {
          console.error('Error updating Supabase:', supabaseError);
        }
      }

      setMessage({
        type: 'success',
        text: 'Subscription status refreshed successfully.'
      });
    } catch (error) {
      console.error('Error refreshing subscription status:', error);
      setMessage({
        type: 'error',
        text: 'Failed to refresh subscription status. Please try again.'
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveNotifications = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    setMessage({ type: '', text: '' });

    try {
      // In a real app, this would update the user's notification preferences
      // For this demo, we'll just simulate a successful update
      setTimeout(() => {
        setMessage({ type: 'success', text: 'Notification preferences updated successfully!' });
        setIsSaving(false);
      }, 1000);
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      setMessage({ type: 'error', text: 'Failed to update notification preferences. Please try again.' });
      setIsSaving(false);
    }
  };



  const handleDeleteAccount = async () => {
    setIsSaving(true);
    setMessage({ type: '', text: '' });

    try {
      // In a real app, this would delete the user's account
      // For this demo, we'll just simulate a successful deletion and logout
      setTimeout(() => {
        logout();
        router.push('/');
      }, 1500);
    } catch (error) {
      console.error('Error deleting account:', error);
      setMessage({ type: 'error', text: 'Failed to delete account. Please try again.' });
      setIsSaving(false);
    }
  };

  const handleCancelSubscription = async () => {
    setIsSaving(true);
    setMessage({ type: '', text: '' });
    setShowCancelConfirm(false);

    try {
      // Get the API URL from environment variables or fallback to localhost
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';

      // Get the token for authentication
      const authToken = token || '';

      if (!authToken) {
        throw new Error('Authentication token not available. Please log in again.');
      }

      // Call the API to cancel the subscription in Stripe
      const response = await fetch(`${apiUrl}/api/subscription/cancel`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to cancel subscription');
      }

      const data = await response.json();
      console.log('Subscription canceled:', data);

      // Update the local state
      setUserDetails({
        ...userDetails,
        subscription_status: 'canceled'
      });

      // Show success message
      setMessage({
        type: 'success',
        text: 'Your subscription has been canceled successfully. You will have access until the end of your current billing period.'
      });

      // Refresh subscription status in AuthContext
      if (refreshSubscriptionStatus) {
        await refreshSubscriptionStatus();
      }
    } catch (error) {
      console.error('Error canceling subscription:', error);
      setMessage({ type: 'error', text: 'Failed to cancel subscription. Please try again or contact support.' });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading || supabaseLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-[#f7f7f7]">
        <div className="text-center">
          <div className="spinner mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-[#f7f7f7]">
      {/* Header */}
      <header className="py-4 px-6 border-b border-gray-200 bg-white">
        <div className="max-w-screen-xl mx-auto flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <Link href="/dashboard">
              <div className="flex items-center space-x-2 cursor-pointer">
                <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full"></div>
                <span className="text-xl font-bold">CreateLex</span>
              </div>
            </Link>
          </div>
          <div className="flex items-center space-x-4">
            {supabaseUser && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">{supabaseUser.email}</span>
                {supabaseUser.user_metadata?.avatar_url ? (
                  <img
                    src={supabaseUser.user_metadata.avatar_url}
                    alt={supabaseUser.user_metadata.full_name || 'User'}
                    className="h-8 w-8 rounded-full object-cover"
                  />
                ) : (
                  <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-700">
                    {supabaseUser.email?.charAt(0).toUpperCase() || 'U'}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="flex-1 py-8 px-6">
        <div className="max-w-screen-lg mx-auto">
          <div className="flex items-center justify-between mb-8">
            <h1 className="text-2xl font-bold text-gray-800">Settings</h1>
            <Link
              href="/profile"
              className="text-blue-600 hover:text-blue-800 flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
              </svg>
              Profile
            </Link>
          </div>

          {message.text && (
            <div className={`mb-6 p-4 rounded-md ${message.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
              {message.text}
            </div>
          )}

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="flex border-b border-gray-200">
              <button
                className={`px-6 py-3 font-medium text-sm focus:outline-none ${
                  activeTab === 'account' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'
                }`}
                onClick={() => setActiveTab('account')}
              >
                Account
              </button>
              <button
                className={`px-6 py-3 font-medium text-sm focus:outline-none ${
                  activeTab === 'notifications' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'
                }`}
                onClick={() => setActiveTab('notifications')}
              >
                Notifications
              </button>
              <button
                className={`px-6 py-3 font-medium text-sm focus:outline-none ${
                  activeTab === 'subscription' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'
                }`}
                onClick={() => setActiveTab('subscription')}
              >
                Subscription
              </button>
            </div>

            <div className="p-6">
              {/* Account Tab */}
              {activeTab === 'account' && (
                <div>
                  <h2 className="text-lg font-semibold mb-4">Account Settings</h2>
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-md font-medium mb-2">Account Information</h3>
                      <div className="bg-gray-50 p-4 rounded-md">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm text-gray-500">Email</p>
                            <p>{supabaseUser?.email}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Name</p>
                            <p>{supabaseUser?.user_metadata?.full_name || 'Not set'}</p>
                          </div>
                          <div>
                            <div className="flex items-center space-x-2">
                              <p className="text-sm text-gray-500">Account Type</p>
                              <button
                                onClick={refreshSubscriptionStatusFromAPI}
                                className="text-blue-600 hover:text-blue-800 p-1 rounded-full hover:bg-blue-50"
                                title="Refresh subscription status"
                                disabled={isSaving}
                              >
                                {isSaving ? (
                                  <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                  </svg>
                                ) : (
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                                  </svg>
                                )}
                              </button>
                            </div>
                            <p>{userDetails.subscription_status === 'active' ? 'Premium' : 'Free'}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Member Since</p>
                            <p>{userDetails.created_at
                                ? new Date(userDetails.created_at).toLocaleDateString('en-US', {
                                    year: 'numeric',
                                    month: 'long',
                                    day: 'numeric'
                                  })
                                : 'Loading...'}</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-md font-medium mb-2">Connected Accounts</h3>
                      <div className="bg-gray-50 p-4 rounded-md">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="h-10 w-10 bg-white rounded-full flex items-center justify-center mr-3">
                              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="h-6 w-6">
                                <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4" />
                                <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853" />
                                <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05" />
                                <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335" />
                              </svg>
                            </div>
                            <div>
                              <p className="font-medium">Google</p>
                              <p className="text-sm text-gray-600">Connected</p>
                            </div>
                          </div>
                          <button className="text-sm text-red-600 hover:text-red-800">
                            Disconnect
                          </button>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-md font-medium mb-2">Data & Privacy</h3>
                      <div className="bg-gray-50 p-4 rounded-md">
                        <div className="space-y-4">
                          <div>
                            <button className="text-blue-600 hover:text-blue-800 text-sm">
                              Download your data
                            </button>
                            <p className="text-xs text-gray-500 mt-1">
                              Get a copy of all the data we have stored for your account.
                            </p>
                          </div>
                          <div>
                            <button
                              className="text-red-600 hover:text-red-800 text-sm"
                              onClick={() => setShowDeleteConfirm(true)}
                            >
                              Delete account
                            </button>
                            <p className="text-xs text-gray-500 mt-1">
                              Permanently delete your account and all associated data.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Notifications Tab */}
              {activeTab === 'notifications' && (
                <div>
                  <h2 className="text-lg font-semibold mb-4">Notification Settings</h2>
                  <form onSubmit={handleSaveNotifications}>
                    <div className="space-y-4">
                      <div className="flex items-start">
                        <div className="flex items-center h-5">
                          <input
                            id="email-notifications"
                            type="checkbox"
                            checked={emailNotifications}
                            onChange={(e) => setEmailNotifications(e.target.checked)}
                            className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          />
                        </div>
                        <div className="ml-3 text-sm">
                          <label htmlFor="email-notifications" className="font-medium text-gray-700">Email Notifications</label>
                          <p className="text-gray-500">Receive email notifications about your account activity, chat sessions, and generated content.</p>
                        </div>
                      </div>
                      <div className="flex items-start">
                        <div className="flex items-center h-5">
                          <input
                            id="marketing-emails"
                            type="checkbox"
                            checked={marketingEmails}
                            onChange={(e) => setMarketingEmails(e.target.checked)}
                            className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          />
                        </div>
                        <div className="ml-3 text-sm">
                          <label htmlFor="marketing-emails" className="font-medium text-gray-700">Marketing Emails</label>
                          <p className="text-gray-500">Receive emails about new features, updates, and special offers.</p>
                        </div>
                      </div>
                      <div className="pt-4">
                        <button
                          type="submit"
                          className={`px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 ${isSaving ? 'opacity-70 cursor-not-allowed' : ''}`}
                          disabled={isSaving}
                        >
                          {isSaving ? 'Saving...' : 'Save Preferences'}
                        </button>
                      </div>
                    </div>
                  </form>
                </div>
              )}

              {/* Subscription Tab */}
              {activeTab === 'subscription' && (
                <div>
                  <h2 className="text-lg font-semibold mb-4">Subscription Management</h2>
                  <div className="bg-gray-50 p-4 rounded-md mb-6">
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="font-medium">Current Plan</h3>
                        <SubscriptionPlanDisplay />
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className={`px-3 py-1 rounded-full text-sm ${userDetails.subscription_status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                          {userDetails.subscription_status === 'active' ? 'Active' : 'Inactive'}
                        </div>
                        <button
                          onClick={refreshSubscriptionStatusFromAPI}
                          className="text-blue-600 hover:text-blue-800 p-1 rounded-full hover:bg-blue-50"
                          title="Refresh subscription status"
                          disabled={isSaving}
                        >
                          {isSaving ? (
                            <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                          ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                            </svg>
                          )}
                        </button>
                      </div>
                    </div>
                  </div>

                  {userDetails.subscription_status === 'active' ? (
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-md font-medium mb-2">Billing Information</h3>
                        <div className="bg-gray-50 p-4 rounded-md">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm text-gray-500">Payment Method</p>
                              <p className="flex items-center">
                                <svg className="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <rect x="2" y="5" width="20" height="14" rx="2" stroke="currentColor" strokeWidth="2" />
                                  <path d="M2 10H22" stroke="currentColor" strokeWidth="2" />
                                </svg>
                                Visa ending in 4242
                              </p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-500">Next Billing Date</p>
                              <p>{userDetails.subscription_updated_at
                                  ? (() => {
                                      // Calculate next billing date (1 month after subscription_updated_at)
                                      const updatedDate = new Date(userDetails.subscription_updated_at);
                                      const nextBillingDate = new Date(updatedDate);
                                      nextBillingDate.setMonth(updatedDate.getMonth() + 1);

                                      return nextBillingDate.toLocaleDateString('en-US', {
                                        year: 'numeric',
                                        month: 'long',
                                        day: 'numeric'
                                      });
                                    })()
                                  : 'Loading...'}</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex space-x-4">
                        <button
                          onClick={() => router.push('/subscription')}
                          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                        >
                          Change Plan
                        </button>
                        <button
                          onClick={() => setShowCancelConfirm(true)}
                          className="px-4 py-2 border border-red-300 text-red-600 rounded-md hover:bg-red-50"
                          disabled={isSaving}
                        >
                          {isSaving ? 'Processing...' : 'Cancel Subscription'}
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <p className="text-gray-600 mb-4">Subscribe to unlock premium features and AI chat capabilities.</p>
                      <Link
                        href="/subscription"
                        className="inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                      >
                        View Plans
                      </Link>
                    </div>
                  )}
                </div>
              )}


            </div>
          </div>
        </div>
      </main>

      {/* Delete Account Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-xl font-bold mb-4">Delete Account</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently removed.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                disabled={isSaving}
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteAccount}
                className={`px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 ${isSaving ? 'opacity-70 cursor-not-allowed' : ''}`}
                disabled={isSaving}
              >
                {isSaving ? 'Deleting...' : 'Delete Account'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Cancel Subscription Confirmation Modal */}
      {showCancelConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-xl font-bold mb-4">Cancel Subscription</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to cancel your subscription? You will lose access to premium features immediately.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowCancelConfirm(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                disabled={isSaving}
              >
                Keep Subscription
              </button>
              <button
                onClick={handleCancelSubscription}
                className={`px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 ${isSaving ? 'opacity-70 cursor-not-allowed' : ''}`}
                disabled={isSaving}
              >
                {isSaving ? 'Canceling...' : 'Cancel Subscription'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Footer */}
      <footer className="py-6 px-8 border-t border-gray-200 bg-white mt-auto">
        <div className="max-w-screen-xl mx-auto flex flex-col md:flex-row justify-between items-center text-sm text-gray-500">
          <div className="mb-4 md:mb-0">
            © {new Date().getFullYear()} CreateLex. All rights reserved.
          </div>
          <div className="flex space-x-6">
            <Link href="/terms" className="hover:text-gray-700">Terms</Link>
            <Link href="/privacy" className="hover:text-gray-700">Privacy</Link>
            <Link href="/contact" className="hover:text-gray-700">Contact</Link>
          </div>
        </div>
      </footer>
    </div>
  );
}
