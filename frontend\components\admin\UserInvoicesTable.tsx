'use client';

import { useState, useEffect } from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Download, ExternalLink } from 'lucide-react';
import { fetchWithAuth, getApiUrl } from '@/lib/auth-utils';
import { format } from 'date-fns';

interface Invoice {
  id: string;
  invoice_id: string;
  amount: number;
  currency: string;
  status: string;
  created_at: string;
  paid_at: string | null;
  invoice_url: string | null;
  receipt_url: string | null;
  description: string;
}

interface UserInvoicesTableProps {
  userId: string;
}

export default function UserInvoicesTable({ userId }: UserInvoicesTableProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  
  useEffect(() => {
    fetchInvoices();
  }, [userId]);
  
  const fetchInvoices = async () => {
    try {
      setLoading(true);
      
      const apiUrl = getApiUrl();
      const response = await fetchWithAuth(`${apiUrl}/api/admin/users/${userId}/invoices`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch invoices: ${response.status}`);
      }
      
      const data = await response.json();
      setInvoices(data.invoices || []);
      
      // If no invoices returned, use mock data for development
      if (!data.invoices || data.invoices.length === 0) {
        const mockData = generateMockInvoices();
        setInvoices(mockData);
      }
    } catch (error) {
      console.error('Error fetching invoices:', error);
      setError('Failed to load invoices');
      
      // Use mock data for development
      const mockData = generateMockInvoices();
      setInvoices(mockData);
    } finally {
      setLoading(false);
    }
  };
  
  const generateMockInvoices = (): Invoice[] => {
    const statuses = ['paid', 'open', 'void', 'draft'];
    const mockInvoices: Invoice[] = [];
    
    for (let i = 0; i < 5; i++) {
      const createdAt = new Date();
      createdAt.setMonth(createdAt.getMonth() - i);
      
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      const paidAt = status === 'paid' ? new Date(createdAt.getTime() + 86400000) : null;
      
      mockInvoices.push({
        id: `inv_${i}`,
        invoice_id: `INV-${1000 + i}`,
        amount: Math.floor(Math.random() * 50) + 20,
        currency: 'usd',
        status,
        created_at: createdAt.toISOString(),
        paid_at: paidAt?.toISOString() || null,
        invoice_url: 'https://example.com/invoice',
        receipt_url: status === 'paid' ? 'https://example.com/receipt' : null,
        description: i % 2 === 0 ? 'Monthly subscription' : 'Token purchase'
      });
    }
    
    return mockInvoices;
  };
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge className="bg-green-500">Paid</Badge>;
      case 'open':
        return <Badge variant="outline" className="text-blue-500 border-blue-500">Open</Badge>;
      case 'void':
        return <Badge variant="outline" className="text-red-500 border-red-500">Void</Badge>;
      case 'draft':
        return <Badge variant="outline" className="text-gray-500">Draft</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };
  
  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase()
    }).format(amount / 100);
  };
  
  if (loading) {
    return (
      <div className="space-y-2">
        {[...Array(3)].map((_, i) => (
          <Skeleton key={i} className="h-12 w-full" />
        ))}
      </div>
    );
  }
  
  if (error) {
    return <div className="text-red-500 p-4">{error}</div>;
  }
  
  if (invoices.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        No invoices found for this user.
      </div>
    );
  }
  
  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Invoice</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {invoices.map((invoice) => (
            <TableRow key={invoice.id}>
              <TableCell className="font-medium">{invoice.invoice_id}</TableCell>
              <TableCell>{format(new Date(invoice.created_at), 'MMM dd, yyyy')}</TableCell>
              <TableCell>{invoice.description}</TableCell>
              <TableCell>{formatCurrency(invoice.amount, invoice.currency)}</TableCell>
              <TableCell>{getStatusBadge(invoice.status)}</TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end space-x-2">
                  {invoice.invoice_url && (
                    <Button variant="ghost" size="icon" asChild>
                      <a href={invoice.invoice_url} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="h-4 w-4" />
                      </a>
                    </Button>
                  )}
                  {invoice.receipt_url && (
                    <Button variant="ghost" size="icon" asChild>
                      <a href={invoice.receipt_url} target="_blank" rel="noopener noreferrer">
                        <Download className="h-4 w-4" />
                      </a>
                    </Button>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
