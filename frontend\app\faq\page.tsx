'use client';

import React, { useState } from 'react';
import Navbar from '../../components/landing/Navbar';
import Footer from '../../components/landing/Footer';
import { useAuth } from '../../contexts/AuthContext';

interface FAQItem {
  question: string;
  answer: string;
  category: string;
}

export default function FAQPage() {
  const { isAuthenticated } = useAuth();
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeCategory, setActiveCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  // Handle scroll events for navbar styling
  React.useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const faqItems: FAQItem[] = [
    // General Questions
    {
      question: "What is CreateLex?",
      answer: "<PERSON><PERSON><PERSON><PERSON> is an AI-powered assistant for Unreal Engine developers. It helps streamline workflows, generate code, and create assets using natural language commands, making game development faster and more intuitive.",
      category: "general"
    },
    {
      question: "How does CreateLex work?",
      answer: "CreateLex uses advanced AI models to understand natural language commands and translate them into Unreal Engine operations. It integrates directly with your Unreal Engine projects through our plugin, allowing you to create materials, generate blueprints, and manipulate objects with simple text prompts.",
      category: "general"
    },
    {
      question: "Is CreateLex compatible with my version of Unreal Engine?",
      answer: "CreateLex is compatible with Unreal Engine 5.0 and above. We regularly update our plugin to ensure compatibility with the latest versions of Unreal Engine.",
      category: "general"
    },
    
    // Pricing & Plans
    {
      question: "What plans do you offer?",
      answer: "We offer three main plans: Basic ($20/month), Pro ($30/month), and Enterprise (custom pricing). Each plan offers different features and capabilities to suit your needs. You can view the details on our pricing page.",
      category: "pricing"
    },
    {
      question: "Do you offer a free trial?",
      answer: "We don't currently offer a free trial, but we do provide a 14-day money-back guarantee on all our plans. This allows you to try our service risk-free.",
      category: "pricing"
    },
    {
      question: "Can I upgrade or downgrade my plan?",
      answer: "Yes, you can upgrade or downgrade your plan at any time. When upgrading, you'll be charged the prorated difference for the remainder of your billing cycle. When downgrading, the new rate will apply at the start of your next billing cycle.",
      category: "pricing"
    },
    {
      question: "Do you offer refunds?",
      answer: "Yes, we offer a 14-day money-back guarantee on all our plans. If you're not satisfied with our service, you can request a refund within 14 days of your purchase.",
      category: "pricing"
    },
    
    // Technical Questions
    {
      question: "How do I install the CreateLex plugin?",
      answer: "You can download the plugin from our website or the Epic Games Marketplace. Once downloaded, you can install it like any other Unreal Engine plugin by adding it to your project's Plugins folder or through the Unreal Engine Plugin interface.",
      category: "technical"
    },
    {
      question: "Does CreateLex work offline?",
      answer: "The CreateLex plugin requires an internet connection to communicate with our AI services. However, once assets are generated, they remain in your project and can be used offline.",
      category: "technical"
    },
    {
      question: "What programming languages does CreateLex support?",
      answer: "CreateLex can generate both Blueprint visual scripting and C++ code for Unreal Engine. This allows you to work with whichever programming approach you prefer.",
      category: "technical"
    },
    {
      question: "Can I use CreateLex with my team?",
      answer: "Yes, our Pro and Enterprise plans support team collaboration. Each team member will need their own account, but you can share projects and assets generated with CreateLex.",
      category: "technical"
    },
    
    // Usage & Features
    {
      question: "What can I create with CreateLex?",
      answer: "With CreateLex, you can generate Blueprints, materials, shaders, and basic 3D models. You can also manipulate existing objects, set up lighting, create particle effects, and more using natural language commands.",
      category: "features"
    },
    {
      question: "Are there any limitations to what CreateLex can generate?",
      answer: "While CreateLex is powerful, it does have some limitations. It works best with standard Unreal Engine features and may have difficulty with highly custom systems or third-party plugins. Very complex blueprints or materials might require some manual refinement after generation.",
      category: "features"
    },
    {
      question: "Do I own the content created with CreateLex?",
      answer: "Yes, you retain full ownership of all content created using CreateLex. You can use it in commercial projects without any additional licensing fees.",
      category: "features"
    },
    {
      question: "Can CreateLex help with optimizing my game?",
      answer: "Yes, CreateLex can provide suggestions for optimizing your game's performance. It can analyze your project and recommend changes to improve frame rates, reduce memory usage, and enhance overall performance.",
      category: "features"
    },
    
    // Support & Troubleshooting
    {
      question: "How do I get support if I have issues?",
      answer: "We offer email support for all plans. Pro and Enterprise plans also include priority support. You can contact <NAME_EMAIL> or through the contact form on our website.",
      category: "support"
    },
    {
      question: "What if CreateLex doesn't understand my command?",
      answer: "If CreateLex doesn't understand your command, try rephrasing it or breaking it down into simpler steps. Our AI is constantly learning, but it may sometimes need more specific instructions.",
      category: "support"
    },
    {
      question: "Is there documentation available?",
      answer: "Yes, we provide comprehensive documentation for all features of CreateLex. You can access it through our website or directly from the plugin interface.",
      category: "support"
    }
  ];

  const categories = [
    { id: 'all', name: 'All Questions' },
    { id: 'general', name: 'General' },
    { id: 'pricing', name: 'Pricing & Plans' },
    { id: 'technical', name: 'Technical' },
    { id: 'features', name: 'Features' },
    { id: 'support', name: 'Support' },
  ];

  // Filter FAQ items based on active category and search query
  const filteredFAQs = faqItems.filter(item => {
    const matchesCategory = activeCategory === 'all' || item.category === activeCategory;
    const matchesSearch = searchQuery === '' || 
      item.question.toLowerCase().includes(searchQuery.toLowerCase()) || 
      item.answer.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesCategory && matchesSearch;
  });

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900 text-white">
      <Navbar isScrolled={isScrolled} isAuthenticated={isAuthenticated} />

      <main className="pt-24 pb-16">
        <div className="container mx-auto px-4 md:px-8 max-w-6xl">
          <div className="text-center mb-12">
            <div className="inline-block mb-4 px-4 py-1 border border-blue-400 rounded-full bg-blue-900/30">
              <span className="text-blue-300 font-medium">Help Center</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Frequently Asked <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-300">Questions</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Find answers to common questions about CreateLex, our features, pricing, and more.
            </p>
          </div>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto mb-10">
            <div className="relative">
              <input
                type="text"
                placeholder="Search for questions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-gray-800/70 border border-gray-700 rounded-lg pl-12 pr-4 py-4 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-gray-400 absolute left-4 top-1/2 transform -translate-y-1/2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
          </div>

          {/* Category Tabs */}
          <div className="flex flex-wrap justify-center gap-2 mb-10">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`px-4 py-2 rounded-full transition-all ${
                  activeCategory === category.id
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* FAQ Items */}
          <div className="space-y-6">
            {filteredFAQs.length > 0 ? (
              filteredFAQs.map((faq, index) => (
                <details
                  key={index}
                  className="group bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-xl overflow-hidden"
                >
                  <summary className="flex justify-between items-center p-6 cursor-pointer list-none">
                    <h3 className="text-xl font-semibold text-white pr-8">{faq.question}</h3>
                    <div className="flex-shrink-0 ml-2 text-gray-400 group-open:rotate-180 transition-transform duration-300">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </summary>
                  <div className="p-6 pt-0 text-gray-300">
                    <p>{faq.answer}</p>
                  </div>
                </details>
              ))
            ) : (
              <div className="text-center py-12 bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-xl">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 className="text-xl font-semibold text-white mb-2">No results found</h3>
                <p className="text-gray-400">
                  We couldn't find any questions matching your search. Try different keywords or browse by category.
                </p>
              </div>
            )}
          </div>

          {/* Contact CTA */}
          <div className="mt-16 text-center p-8 bg-gradient-to-r from-blue-900/30 to-purple-900/30 backdrop-blur-sm border border-blue-800/50 rounded-xl">
            <h3 className="text-2xl font-bold mb-4">Still have questions?</h3>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              If you couldn't find the answer you were looking for, our support team is here to help.
              Contact us and we'll get back to you as soon as possible.
            </p>
            <a
              href="/contact"
              className="inline-block px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-400 hover:from-blue-500 hover:to-blue-300 text-white font-bold rounded-lg transition-all shadow-lg hover:shadow-blue-500/50"
            >
              Contact Support
            </a>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
