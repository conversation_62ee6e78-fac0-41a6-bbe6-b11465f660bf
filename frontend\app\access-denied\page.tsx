'use client';

import { useEffect, useState } from 'react';
import { getMainAppOrigin } from '../../lib/fetch-auth-token';

export default function AccessDeniedPage() {
  const [mainAppUrl, setMainAppUrl] = useState('');

  useEffect(() => {
    // Get the main app URL
    const origin = getMainAppOrigin();
    setMainAppUrl(origin);

    // Remove any existing elements from the body
    // This ensures no sidebars or other components are shown
    if (typeof document !== 'undefined') {
      // Apply styles to hide any potential sidebars
      const style = document.createElement('style');
      style.innerHTML = `
        /* Hide all elements except our container */
        body > *:not(#access-denied-container) {
          display: none !important;
        }

        /* Reset body styles */
        body {
          overflow: auto !important;
          background-color: white !important;
          margin: 0 !important;
          padding: 0 !important;
        }

        /* Hide specific elements that might be causing issues */
        .sidebar,
        nav,
        header,
        aside,
        [data-sidebar],
        [role="navigation"] {
          display: none !important;
        }
      `;
      document.head.appendChild(style);

      // More aggressive approach: remove all elements except our container
      setTimeout(() => {
        const container = document.getElementById('access-denied-container');
        if (container) {
          // Move container to be a direct child of body
          document.body.appendChild(container);

          // Remove all other children of body
          Array.from(document.body.children).forEach(child => {
            if (child.id !== 'access-denied-container') {
              child.remove();
            }
          });
        }
      }, 0);
    }
  }, []);

  return (
    <div id="access-denied-container" style={{
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      backgroundColor: 'white',
      zIndex: 9999,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{
        maxWidth: '500px',
        width: '100%',
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08)',
        overflow: 'hidden',
        textAlign: 'center',
        padding: '30px'
      }}>
        <div style={{ color: '#f59e0b', marginBottom: '20px' }}>
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path>
            <path d="M12 9v4"></path>
            <path d="M12 17h.01"></path>
          </svg>
        </div>

        <h1 style={{
          fontSize: '24px',
          fontWeight: 'bold',
          marginBottom: '8px',
          color: '#111827'
        }}>
          Access Denied
        </h1>

        <p style={{
          fontSize: '16px',
          color: '#6b7280',
          marginBottom: '24px'
        }}>
          Direct access to the chat is not allowed
        </p>

        <p style={{
          fontSize: '14px',
          color: '#6b7280',
          marginBottom: '24px',
          lineHeight: '1.5'
        }}>
          For security reasons, you can only access the chat through the dashboard.
          Please return to the dashboard and click the "Open CreateLex AI" button.
        </p>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          <a
            href={`${mainAppUrl}/dashboard`}
            style={{
              display: 'block',
              width: '100%',
              padding: '10px',
              backgroundColor: '#3b82f6',
              color: 'white',
              borderRadius: '6px',
              textDecoration: 'none',
              fontWeight: 'bold',
              textAlign: 'center'
            }}
          >
            Return to Dashboard
          </a>

          <a
            href={mainAppUrl}
            style={{
              display: 'block',
              width: '100%',
              padding: '10px',
              backgroundColor: 'white',
              color: '#3b82f6',
              border: '1px solid #e5e7eb',
              borderRadius: '6px',
              textDecoration: 'none',
              fontWeight: 'bold',
              textAlign: 'center'
            }}
          >
            Return to Home
          </a>
        </div>
      </div>
    </div>
  );
}
