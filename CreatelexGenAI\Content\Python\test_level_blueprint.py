#!/usr/bin/env python3
"""
Test script to verify level blueprint detection and function creation
"""

import unreal
import json
import sys
import os

# Add the handlers directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'handlers'))

from utils import logging as log
from handlers import blueprint_context_handler

def test_level_blueprint_detection():
    """Test if we can detect and work with level blueprints"""
    
    print("🔍 Testing Level Blueprint Detection...")
    log.log_info("Starting level blueprint detection test")
    
    # Test 1: Basic world detection
    try:
        current_world = unreal.EditorLevelLibrary.get_editor_world()
        if current_world:
            world_name = current_world.get_name()
            print(f"✅ Current world detected: {world_name}")
            log.log_info(f"Current world: {world_name}")
        else:
            print("❌ No current world found")
            return False
    except Exception as e:
        print(f"❌ Error getting current world: {e}")
        return False
    
    # Test 2: Level blueprint path resolution
    try:
        # Test the blueprint context handler's level blueprint detection
        test_command = {
            "type": "get_blueprint_context_detailed",
            "level_path": "/Game/ThirdPerson/Lvl_ThirdPerson"
        }
        
        result = blueprint_context_handler.handle_blueprint_context_request(
            "get_blueprint_context_detailed", 
            test_command
        )
        
        if result.get("success"):
            print("✅ Level blueprint detection successful")
            print(f"   Blueprint path: {result.get('blueprint_path', 'Not found')}")
            print(f"   Is level blueprint: {result.get('is_level_blueprint', False)}")
            print(f"   Function count: {len(result.get('functions', []))}")
        else:
            print(f"❌ Level blueprint detection failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error in blueprint context detection: {e}")
        return False
    
    # Test 3: Try to create a simple function in the level blueprint
    try:
        from handlers.blueprint_commands import handle_add_function
        
        test_function_command = {
            "blueprint_path": result.get("blueprint_path", ""),
            "function_name": "TestAIFunction",
            "inputs": [],
            "outputs": [],
            "description": "Test function created by AI"
        }
        
        function_result = handle_add_function(test_function_command)
        
        if function_result.get("success"):
            print("✅ Function creation successful")
            print(f"   Function GUID: {function_result.get('function_guid', 'N/A')}")
            
            # Test 4: Try to add nodes to the function
            from handlers.blueprint_commands import handle_add_nodes_bulk
            
            test_nodes = [
                {
                    "node_id": "test_node_1",
                    "node_type": "PrintString",
                    "node_properties": {
                        "InString": "Hello from AI Level Blueprint!"
                    }
                }
            ]
            
            nodes_command = {
                "blueprint_path": result.get("blueprint_path", ""),
                "function_name": "TestAIFunction",
                "nodes": test_nodes
            }
            
            nodes_result = handle_add_nodes_bulk(nodes_command)
            
            if nodes_result.get("success"):
                print("✅ Node creation successful")
                print("🎉 Level blueprint functionality is working correctly!")
                return True
            else:
                print(f"❌ Node creation failed: {nodes_result.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ Function creation failed: {function_result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error in function/node creation: {e}")
        return False

if __name__ == "__main__":
    success = test_level_blueprint_detection()
    if success:
        print("\n🎉 All tests passed! Level blueprint functionality is working.")
    else:
        print("\n❌ Some tests failed. Check the logs for details.")