require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials from environment
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

console.log('Supabase URL:', supabaseUrl ? 'Available' : 'Missing');
console.log('Supabase Key:', supabaseKey ? 'Available (first 10 chars): ' + supabaseKey.substring(0, 10) + '...' : 'Missing');

// Create a new Supabase client for testing
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createTokenUsageTable() {
  console.log('Creating token_usage table...');
  
  try {
    // Check if the table already exists
    const { error: checkError } = await supabase
      .from('token_usage')
      .select('id', { count: 'exact', head: true });
    
    if (!checkError) {
      console.log('The token_usage table already exists.');
      return;
    }
    
    if (checkError.code !== '42P01') {
      console.error('Unexpected error checking table existence:', checkError);
      return;
    }
    
    console.log('Table does not exist. Creating token_usage table...');
    
    // Create the token_usage table using REST API
    const { error: createError } = await supabase.rpc('create_token_usage_table');
    
    if (createError) {
      console.error('Error creating table:', createError);
      
      // If RPC fails, try direct SQL approach
      console.log('Trying alternative approach...');
      
      // Create a PostgreSQL function to create the table
      const createFunctionQuery = `
        CREATE OR REPLACE FUNCTION create_token_usage_table()
        RETURNS void AS $$
        BEGIN
          -- Create token usage tracking table
          CREATE TABLE IF NOT EXISTS token_usage (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID NOT NULL,
            model_id TEXT NOT NULL,
            prompt_tokens INTEGER NOT NULL,
            completion_tokens INTEGER NOT NULL,
            total_tokens INTEGER NOT NULL,
            timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            request_type TEXT,
            subscription_plan TEXT
          );
          
          -- Create indexes for efficient querying
          CREATE INDEX IF NOT EXISTS idx_token_usage_user_id ON token_usage(user_id);
          CREATE INDEX IF NOT EXISTS idx_token_usage_timestamp ON token_usage(timestamp);
          CREATE INDEX IF NOT EXISTS idx_token_usage_user_timestamp ON token_usage(user_id, timestamp);
        END;
        $$ LANGUAGE plpgsql;
      `;
      
      // Execute the function creation
      const { error: functionError } = await supabase.rpc('exec_sql', { sql: createFunctionQuery });
      
      if (functionError) {
        console.error('Error creating function:', functionError);
        console.log('Please create the table manually using the SQL in backend/migrations/token_usage_table.sql');
        return;
      }
      
      // Call the function to create the table
      const { error: callError } = await supabase.rpc('create_token_usage_table');
      
      if (callError) {
        console.error('Error calling function:', callError);
        console.log('Please create the table manually using the SQL in backend/migrations/token_usage_table.sql');
        return;
      }
    }
    
    console.log('Token usage table created successfully!');
    
    // Create views
    console.log('Creating views...');
    
    // Daily view
    const dailyViewQuery = `
      CREATE OR REPLACE VIEW daily_token_usage AS
      SELECT 
        user_id,
        DATE_TRUNC('day', timestamp) AS usage_date,
        SUM(prompt_tokens) AS prompt_tokens,
        SUM(completion_tokens) AS completion_tokens,
        SUM(total_tokens) AS total_tokens,
        subscription_plan
      FROM token_usage
      GROUP BY user_id, DATE_TRUNC('day', timestamp), subscription_plan
      ORDER BY DATE_TRUNC('day', timestamp) DESC, SUM(total_tokens) DESC;
    `;
    
    const { error: dailyViewError } = await supabase.rpc('exec_sql', { sql: dailyViewQuery });
    
    if (dailyViewError) {
      console.error('Error creating daily view:', dailyViewError);
    } else {
      console.log('Daily view created successfully!');
    }
    
    // Monthly view
    const monthlyViewQuery = `
      CREATE OR REPLACE VIEW monthly_token_usage AS
      SELECT 
        user_id,
        DATE_TRUNC('month', timestamp) AS usage_month,
        SUM(prompt_tokens) AS prompt_tokens,
        SUM(completion_tokens) AS completion_tokens,
        SUM(total_tokens) AS total_tokens,
        subscription_plan
      FROM token_usage
      GROUP BY user_id, DATE_TRUNC('month', timestamp), subscription_plan
      ORDER BY DATE_TRUNC('month', timestamp) DESC, SUM(total_tokens) DESC;
    `;
    
    const { error: monthlyViewError } = await supabase.rpc('exec_sql', { sql: monthlyViewQuery });
    
    if (monthlyViewError) {
      console.error('Error creating monthly view:', monthlyViewError);
    } else {
      console.log('Monthly view created successfully!');
    }
    
    console.log('Setup complete!');
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

createTokenUsageTable();
