import { useState, useEffect } from 'react';
import { But<PERSON>, Container, Typography, Paper, Box, Alert } from '@mui/material';

export default function CheckTables() {
  const [tableStatus, setTableStatus] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const checkTables = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      const response = await fetch('/api/check-tables');
      const data = await response.json();
      
      setTableStatus(data);
      
      if (data.chats_table_exists && data.messages_table_exists && data.content_column_type === 'jsonb') {
        setSuccess('Tables are correctly configured');
      } else {
        setError('Tables are not correctly configured');
      }
    } catch (error) {
      console.error('Error checking tables:', error);
      setError('Error checking tables');
    } finally {
      setLoading(false);
    }
  };

  const fixTables = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      const response = await fetch('/api/fix-tables', {
        method: 'POST'
      });
      const data = await response.json();
      
      if (data.success) {
        setSuccess('Tables fixed successfully');
        // Check tables again
        await checkTables();
      } else {
        setError(data.error || 'Error fixing tables');
      }
    } catch (error) {
      console.error('Error fixing tables:', error);
      setError('Error fixing tables');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkTables();
  }, []);

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Check Supabase Tables
      </Typography>
      
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Table Status
        </Typography>
        
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}
        
        {tableStatus ? (
          <Box component="pre" sx={{ bgcolor: '#f5f5f5', p: 2, borderRadius: 1, overflow: 'auto' }}>
            {JSON.stringify(tableStatus, null, 2)}
          </Box>
        ) : (
          <Typography>Loading table status...</Typography>
        )}
        
        <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
          <Button 
            variant="contained" 
            onClick={checkTables} 
            disabled={loading}
          >
            Check Tables
          </Button>
          
          <Button 
            variant="contained" 
            color="warning" 
            onClick={fixTables} 
            disabled={loading}
          >
            Fix Tables
          </Button>
        </Box>
      </Paper>
      
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Required Table Structure
        </Typography>
        
        <Typography variant="subtitle1" gutterBottom>
          Chats Table
        </Typography>
        <Box component="pre" sx={{ bgcolor: '#f5f5f5', p: 2, borderRadius: 1, overflow: 'auto' }}>
{`CREATE TABLE chats (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  title TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);`}
        </Box>
        
        <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
          Messages Table
        </Typography>
        <Box component="pre" sx={{ bgcolor: '#f5f5f5', p: 2, borderRadius: 1, overflow: 'auto' }}>
{`CREATE TABLE messages (
  id TEXT PRIMARY KEY,
  chat_id TEXT NOT NULL REFERENCES chats(id) ON DELETE CASCADE,
  role TEXT NOT NULL,
  content JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_messages_chat_id ON messages(chat_id);`}
        </Box>
      </Paper>
    </Container>
  );
}
