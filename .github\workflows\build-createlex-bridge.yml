name: Build CreateLex Bridge

on:
  push:
    branches: [ main, windows-working ]
    paths: 
      - 'createlex-bridge/**'
      - '.github/workflows/build-createlex-bridge.yml'
  pull_request:
    branches: [ main ]
    paths: 
      - 'createlex-bridge/**'
  release:
    types: [published]

env:
  NODE_VERSION: '18'

jobs:
  build-windows:
    runs-on: windows-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'createlex-bridge/package-lock.json'
        
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install PyInstaller
      run: pip install pyinstaller
        
    - name: Install dependencies
      working-directory: createlex-bridge
      run: npm ci
      
    - name: Build Windows app (Protected)
      working-directory: createlex-bridge
      run: npm run build-win-protected
      
    - name: Upload Windows installer
      uses: actions/upload-artifact@v4
      with:
        name: createlex-bridge-windows
        path: |
          createlex-bridge/dist/*.exe
          createlex-bridge/dist/*.exe.blockmap
          createlex-bridge/dist/latest.yml
        retention-days: 30

  build-macos:
    runs-on: macos-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'createlex-bridge/package-lock.json'
        
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install PyInstaller
      run: pip install pyinstaller
        
    - name: Install dependencies
      working-directory: createlex-bridge
      run: npm ci
      
    - name: Build macOS app (Protected)
      working-directory: createlex-bridge
      run: npm run build-mac-protected
      env:
        CSC_IDENTITY_AUTO_DISCOVERY: false
        
    - name: Upload macOS installer
      uses: actions/upload-artifact@v4
      with:
        name: createlex-bridge-macos
        path: |
          createlex-bridge/dist/*.dmg
          createlex-bridge/dist/*.dmg.blockmap
          createlex-bridge/dist/latest-mac.yml
        retention-days: 30

  build-linux:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'createlex-bridge/package-lock.json'
        
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install PyInstaller
      run: pip install pyinstaller
        
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y build-essential libnss3-dev libatk-bridge2.0-dev libdrm2 libxkbcommon0 libxss1 libasound2
        
    - name: Install dependencies
      working-directory: createlex-bridge
      run: npm ci
      
    - name: Build Linux app (Protected)
      working-directory: createlex-bridge
      run: npm run build-linux-protected
      
    - name: Upload Linux installer
      uses: actions/upload-artifact@v4
      with:
        name: createlex-bridge-linux
        path: |
          createlex-bridge/dist/*.AppImage
          createlex-bridge/dist/latest-linux.yml
        retention-days: 30

  create-release:
    if: github.event_name == 'release'
    needs: [build-windows, build-macos, build-linux]
    runs-on: ubuntu-latest
    
    steps:
    - name: Download all artifacts
      uses: actions/download-artifact@v4
      with:
        path: artifacts
        
    - name: Display structure of downloaded files
      run: ls -la artifacts/
      
    - name: Upload Windows installer to release
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ github.event.release.upload_url }}
        asset_path: artifacts/createlex-bridge-windows/CreateLex Bridge Setup 1.0.0.exe
        asset_name: CreateLex-Bridge-Setup-Windows.exe
        asset_content_type: application/octet-stream
        
    - name: Upload macOS installer to release
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ github.event.release.upload_url }}
        asset_path: artifacts/createlex-bridge-macos/CreateLex Bridge-1.0.0.dmg
        asset_name: CreateLex-Bridge-macOS.dmg
        asset_content_type: application/octet-stream
        
    - name: Upload Linux installer to release
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ github.event.release.upload_url }}
        asset_path: artifacts/createlex-bridge-linux/CreateLex Bridge-1.0.0.AppImage
        asset_name: CreateLex-Bridge-Linux.AppImage
        asset_content_type: application/octet-stream 