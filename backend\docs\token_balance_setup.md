# Token Balance Setup

This document explains how to set up the token balance tracking system in Supabase.

## Overview

The token balance system consists of two main tables:

1. `token_balance` - Stores the current token balance for each user
2. `token_transactions` - Records all token transactions (purchases, usage, etc.)

These tables work alongside the existing `token_usage` table to provide a complete token management system.

## Creating the Tables

### Option 1: Using the Supabase SQL Editor

1. Log in to your Supabase dashboard
2. Navigate to the SQL Editor
3. Copy the contents of `backend/migrations/supabase_token_balance_table.sql`
4. Paste into the SQL Editor and run the query

### Option 2: Using the Node.js Script

1. Ensure your `.env` file contains the following variables:
   ```
   SUPABASE_URL=your_supabase_url
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   ```

2. Run the script:
   ```
   cd backend
   node scripts/create-token-balance-table.js
   ```

### Option 3: Using the Direct PostgreSQL Connection

1. Ensure your `.env` file contains the following variable:
   ```
   DATABASE_URL=your_postgres_connection_string
   ```

2. Run the script:
   ```
   cd backend
   node scripts/create-token-balance-table-direct.js
   ```

## Table Structure

### token_balance

| Column     | Type      | Description                       |
|------------|-----------|-----------------------------------|
| id         | UUID      | Primary key                       |
| user_id    | UUID      | User ID (foreign key to auth.users) |
| balance    | INTEGER   | Current token balance             |
| created_at | TIMESTAMP | Record creation timestamp         |
| updated_at | TIMESTAMP | Record last update timestamp      |

### token_transactions

| Column          | Type      | Description                       |
|-----------------|-----------|-----------------------------------|
| id              | UUID      | Primary key                       |
| user_id         | UUID      | User ID (foreign key to auth.users) |
| amount          | INTEGER   | Transaction amount (positive for purchases, negative for usage) |
| type            | VARCHAR   | Transaction type (purchase, usage, etc.) |
| reference_id    | VARCHAR   | External reference ID (e.g., Stripe payment ID) |
| previous_balance| INTEGER   | Balance before transaction        |
| new_balance     | INTEGER   | Balance after transaction         |
| created_at      | TIMESTAMP | Transaction timestamp             |

## Usage

Once the tables are created, the backend API will automatically use them for:

1. Tracking token purchases
2. Checking token balances
3. Recording token usage

The frontend will display the token balance in the dashboard and allow users to purchase additional tokens.
