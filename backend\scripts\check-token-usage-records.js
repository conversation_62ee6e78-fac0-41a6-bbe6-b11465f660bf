require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials from environment
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

console.log('Supabase URL:', supabaseUrl ? 'Available' : 'Missing');
console.log('Supabase Key:', supabaseKey ? 'Available (first 10 chars): ' + supabaseKey.substring(0, 10) + '...' : 'Missing');

// Create a new Supabase client for testing
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function checkTokenUsageRecords() {
  try {
    console.log('Checking token_usage table for records...');
    
    // Get count of records
    const { data, error } = await supabase
      .from('token_usage')
      .select('*')
      .order('timestamp', { ascending: false });
    
    if (error) {
      console.error('Error querying token_usage table:', error);
      return;
    }
    
    console.log(`Found ${data.length} records in token_usage table`);
    
    if (data.length === 0) {
      console.log('No records found in token_usage table');
      return;
    }
    
    // Display the records
    data.forEach((record, index) => {
      console.log(`Record ${index + 1}:`);
      console.log(`  ID: ${record.id}`);
      console.log(`  User ID: ${record.user_id}`);
      console.log(`  Model: ${record.model_id}`);
      console.log(`  Prompt Tokens: ${record.prompt_tokens}`);
      console.log(`  Completion Tokens: ${record.completion_tokens}`);
      console.log(`  Total Tokens: ${record.total_tokens}`);
      console.log(`  Request Type: ${record.request_type}`);
      console.log(`  Subscription Plan: ${record.subscription_plan}`);
      console.log(`  Timestamp: ${record.timestamp}`);
      console.log('---');
    });
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

checkTokenUsageRecords();
