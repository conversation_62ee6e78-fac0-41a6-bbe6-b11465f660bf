const jwt = require('jsonwebtoken');
const { OAuth2Client } = require('google-auth-library');
const supabase = require('./supabaseClient');

// Initialize the Google OAuth client
const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

class SupabaseAuthService {
  constructor() {
    // For Supabase JWT verification, we need to use the JWT_SECRET from .env
    // This should be the same as the SUPABASE_JWT_SECRET or SUPABASE_ANON_KEY
    this.JWT_SECRET = process.env.JWT_SECRET;

    // Check if JWT_SECRET is set
    if (!process.env.JWT_SECRET) {
      console.warn('JWT_SECRET is not set. JWT verification will not work properly.');
    } else {
      console.log('JWT_SECRET is configured for token verification');
    }

    this.JWT_EXPIRY = '7d'; // Token expires in 7 days
  }

  // Verify Google token and create user if not exists
  async verifyGoogleToken(token) {
    try {
      const ticket = await client.verifyIdToken({
        idToken: token,
        audience: process.env.GOOGLE_CLIENT_ID
      });

      const payload = ticket.getPayload();
      const userId = payload.sub; // Google user ID

      // Check if user exists in Supabase
      const { data: existingUser, error: fetchError } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
        console.error('Error fetching user from Supabase:', fetchError);
        throw new Error('Database error when fetching user');
      }

      let user;

      if (!existingUser) {
        // Create new user in Supabase
        const { data: newUser, error: insertError } = await supabase
          .from('users')
          .insert([{
            id: userId,
            email: payload.email,
            name: payload.name,
            picture: payload.picture,
            subscription_status: 'none', // Default subscription status
            created_at: new Date().toISOString()
          }])
          .select()
          .single();

        if (insertError) {
          console.error('Error creating user in Supabase:', insertError);
          throw new Error('Database error when creating user');
        }

        user = {
          id: newUser.id,
          email: newUser.email,
          name: newUser.name,
          picture: newUser.picture,
          subscriptionStatus: newUser.subscription_status
        };
      } else {
        // Map from snake_case to camelCase for consistency
        user = {
          id: existingUser.id,
          email: existingUser.email,
          name: existingUser.name,
          picture: existingUser.picture,
          subscriptionStatus: existingUser.subscription_status,
          stripeCustomerId: existingUser.stripe_customer_id,
          subscriptionId: existingUser.subscription_id
        };
      }

      return user;
    } catch (error) {
      console.error('Error verifying Google token:', error);
      throw new Error('Invalid Google token');
    }
  }

  // Generate JWT token for authenticated user
  generateToken(user) {
    return jwt.sign(
      {
        id: user.id,
        email: user.email,
        subscriptionStatus: user.subscriptionStatus
      },
      this.JWT_SECRET,
      { expiresIn: this.JWT_EXPIRY }
    );
  }

  // Verify JWT token
  async verifyToken(token) {
    try {
      // Check if the token is valid
      if (!token || token === 'undefined' || token === 'null') {
        console.log('Invalid token provided:', token);
        throw new Error('Invalid token provided');
      }

      let decoded;

      try {
        // Try to verify the token with the JWT secret
        decoded = jwt.verify(token, this.JWT_SECRET);
        console.log('Token verified successfully');
      } catch (verifyError) {
        console.error('Error verifying token with JWT_SECRET:', verifyError.message);

        // For development purposes, decode the token without verification
        console.log('Falling back to decoding token without verification');
        decoded = jwt.decode(token);

        if (!decoded) {
          console.error('Failed to decode token');
          throw new Error('Invalid token format');
        }
      }

      // Extract user ID from token
      console.log('Decoded token:', JSON.stringify(decoded, null, 2));

      const userId = decoded.sub || decoded.id;

      if (!userId) {
        console.error('No user ID found in token');
        throw new Error('Invalid token: no user ID found');
      }

      console.log(`Verifying token for user ID: ${userId}`);

      // Get user from Supabase - using array approach instead of .single()
      console.log(`Querying Supabase for user with ID: ${userId}`);

      try {
        // First, try to find the user by ID
        const { data, error } = await supabase
          .from('users')
          .select('*')
          .eq('id', userId);

        if (error) {
          console.error('Error fetching user from Supabase:', error);
          throw new Error(`Database error: ${error.message}`);
        }

        console.log(`Query result: ${data ? data.length : 0} users found`);

        // Check if user exists
        if (!data || data.length === 0) {
          console.log(`No user found with ID: ${userId}`);

          // Check if this is a first-time login or a deleted user
          // For first-time login, we would typically not have email in the token yet
          // For deleted users, we would have email in the token

          // If we have an email in the token, we'll use it for the new user
          // No need to check if the user previously existed with this email

          console.log('Creating new user from token data');

          // Extract user info from token
          const email = decoded.email || '';
          const name = decoded.user_metadata?.full_name || decoded.user_metadata?.name || decoded.name || '';
          const picture = decoded.user_metadata?.avatar_url || decoded.user_metadata?.picture || '';

          console.log('User data for creation:', { email, name, picture });

          // Create new user
          const { data: insertData, error: insertError } = await supabase
            .from('users')
            .insert([{
              id: userId,
              email: email,
              name: name,
              picture: picture,
              subscription_status: 'inactive',
              created_at: new Date().toISOString()
            }]);

          if (insertError) {
            console.error('Error creating user in Supabase:', insertError);
            throw new Error('Database error when creating user');
          }

          console.log('User created successfully, fetching new user data');

          // Fetch the newly created user
          const { data: newUserData, error: fetchError } = await supabase
            .from('users')
            .select('*')
            .eq('id', userId);

          if (fetchError) {
            console.error('Error fetching new user from Supabase:', fetchError);
            throw new Error('User creation succeeded but could not retrieve user');
          }

          console.log(`Fetch result: ${newUserData ? newUserData.length : 0} users found`);

          if (!newUserData || newUserData.length === 0) {
            console.error('No user found after creation');
            throw new Error('User creation succeeded but user not found');
          }

          const newUser = newUserData[0];
          console.log('New user retrieved:', newUser);

          return {
            id: newUser.id,
            email: newUser.email,
            name: newUser.name,
            picture: newUser.picture,
            subscriptionStatus: newUser.subscription_status
          };
        }

        // User exists, return user data
        const user = data[0];
        console.log('Existing user found:', user);

        // Map from snake_case to camelCase for consistency
        return {
          id: user.id,
          email: user.email,
          name: user.name,
          picture: user.picture,
          subscriptionStatus: user.subscription_status,
          stripeCustomerId: user.stripe_customer_id,
          subscriptionId: user.subscription_id
        };
      } catch (queryError) {
        console.error('Error during user query/creation:', queryError);
        throw queryError;
      }
    } catch (error) {
      console.error('Error verifying token:', error);
      throw new Error(`Invalid token: ${error.message}`);
    }
  }

  // Get user by ID
  async getUserById(userId) {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId);

      if (error) {
        console.error('Error fetching user from Supabase:', error);
        return null;
      }

      if (!data || data.length === 0) {
        console.log(`No user found with ID: ${userId}`);
        return null;
      }

      const user = data[0];

      // Map from snake_case to camelCase for consistency
      return {
        id: user.id,
        email: user.email,
        name: user.name,
        picture: user.picture,
        subscriptionStatus: user.subscription_status,
        stripeCustomerId: user.stripe_customer_id,
        subscriptionId: user.subscription_id,
        createdAt: user.created_at,
        subscriptionUpdatedAt: user.subscription_updated_at,
        lastCheckoutSessionId: user.last_checkout_session_id
      };
    } catch (error) {
      console.error('Error getting user by ID:', error);
      return null;
    }
  }

  // Update user subscription status
  async updateSubscription(userId, status, subscriptionId = null) {
    try {
      const updates = {
        subscription_status: status,
        subscription_id: subscriptionId,
        subscription_updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', userId)
        .select();

      if (error) {
        console.error('Error updating subscription in Supabase:', error);
        return false;
      }

      console.log(`Updated subscription for user ${userId}:`, {
        status,
        subscriptionId,
        updatedAt: new Date().toISOString()
      });

      return true;
    } catch (error) {
      console.error('Error updating subscription:', error);
      return false;
    }
  }

  // Update user's Stripe customer ID
  async updateStripeCustomerId(userId, customerId) {
    try {
      const { data, error } = await supabase
        .from('users')
        .update({
          stripe_customer_id: customerId
        })
        .eq('id', userId)
        .select();

      if (error) {
        console.error('Error updating Stripe customer ID in Supabase:', error);
        return false;
      }

      console.log(`Updated Stripe customer ID for user ${userId} to ${customerId}`);
      return true;
    } catch (error) {
      console.error('Error updating Stripe customer ID:', error);
      return false;
    }
  }

  // Update user's last checkout session ID
  async updateLastCheckoutSessionId(userId, sessionId) {
    try {
      const { data, error } = await supabase
        .from('users')
        .update({
          last_checkout_session_id: sessionId
        })
        .eq('id', userId)
        .select();

      if (error) {
        console.error('Error updating last checkout session ID in Supabase:', error);
        return false;
      }

      console.log(`Updated last checkout session ID for user ${userId} to ${sessionId}`);
      return true;
    } catch (error) {
      console.error('Error updating last checkout session ID:', error);
      return false;
    }
  }

  // Check if user has active subscription
  async hasActiveSubscription(userId) {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('subscription_status')
        .eq('id', userId);

      if (error) {
        console.error('Error checking subscription status in Supabase:', error);
        return false;
      }

      if (!data || data.length === 0) {
        console.log(`No user found with ID: ${userId}`);
        return false;
      }

      const status = data[0].subscription_status;

      console.log(`Checking subscription status for user ${userId}: ${status}`);

      // User has an active subscription if status is 'active'
      return status === 'active';
    } catch (error) {
      console.error('Error checking subscription status:', error);
      return false;
    }
  }
}

module.exports = new SupabaseAuthService();
