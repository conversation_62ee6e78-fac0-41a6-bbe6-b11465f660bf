require('dotenv').config();
const supabase = require('./src/services/supabaseClient');

async function setupAdminTables() {
  console.log('Setting up admin dashboard tables...');
  
  try {
    // 1. Create usage_logs table
    console.log('Creating usage_logs table...');
    const { error: usageLogsError } = await supabase.query(`
      CREATE TABLE IF NOT EXISTS usage_logs (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES users(id),
        request_id TEXT,
        endpoint TEXT NOT NULL,
        token_count INTEGER NOT NULL,
        model TEXT NOT NULL,
        cost DECIMAL(10, 6) NOT NULL,
        timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        request_data JSONB,
        response_data JSONB,
        status TEXT,
        ip_address TEXT,
        user_agent TEXT
      )
    `);
    
    if (usageLogsError) {
      console.error('Error creating usage_logs table:', usageLogsError);
    } else {
      console.log('Successfully created usage_logs table');
    }
    
    // 2. Create api_keys table
    console.log('Creating api_keys table...');
    const { error: apiKeysError } = await supabase.query(`
      CREATE TABLE IF NOT EXISTS api_keys (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES users(id),
        key_name TEXT NOT NULL,
        api_key TEXT NOT NULL UNIQUE,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        last_used_at TIMESTAMPTZ,
        is_active BOOLEAN NOT NULL DEFAULT TRUE,
        rate_limit INTEGER DEFAULT 100,
        permissions JSONB
      )
    `);
    
    if (apiKeysError) {
      console.error('Error creating api_keys table:', apiKeysError);
    } else {
      console.log('Successfully created api_keys table');
    }
    
    // 3. Create billing_records table
    console.log('Creating billing_records table...');
    const { error: billingError } = await supabase.query(`
      CREATE TABLE IF NOT EXISTS billing_records (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES users(id),
        stripe_customer_id TEXT,
        stripe_invoice_id TEXT,
        amount DECIMAL(10, 2) NOT NULL,
        currency TEXT NOT NULL DEFAULT 'usd',
        status TEXT NOT NULL,
        billing_period_start TIMESTAMPTZ,
        billing_period_end TIMESTAMPTZ,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        metadata JSONB
      )
    `);
    
    if (billingError) {
      console.error('Error creating billing_records table:', billingError);
    } else {
      console.log('Successfully created billing_records table');
    }
    
    // 4. Add is_admin column to users table if it doesn't exist
    console.log('Adding is_admin column to users table...');
    const { error: alterError } = await supabase.query(`
      ALTER TABLE users ADD COLUMN IF NOT EXISTS is_admin BOOLEAN NOT NULL DEFAULT FALSE
    `);
    
    if (alterError) {
      console.error('Error adding is_admin column:', alterError);
    } else {
      console.log('Successfully added is_admin column');
    }
    
    // 5. Set admin privileges for specified users
    console.log('Setting admin privileges for specified users...');
    const { error: adminError } = await supabase
      .from('users')
      .update({ is_admin: true })
      .or('<EMAIL>,<EMAIL>');
      
    if (adminError) {
      console.error('Error setting admin privileges:', adminError);
    } else {
      console.log('Successfully set admin privileges');
    }
    
  } catch (error) {
    console.error('Exception in setup:', error);
  }
}

// Run the setup
setupAdminTables()
  .then(() => {
    console.log('Setup completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
