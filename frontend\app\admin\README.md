# Admin Dashboard

This admin dashboard provides tools for managing users, monitoring token usage, and handling billing for the CreateLex AI platform.

## Setup Instructions

### 1. Database Setup

Before using the admin dashboard, you need to set up the required database tables in Supabase:

1. Log in to the [Supabase Dashboard](https://app.supabase.com/)
2. Select your project
3. Go to the SQL Editor
4. Create a new query
5. Copy and paste the contents of `backend/migrations/admin-tables.sql` into the editor
6. Run the query

This will:
- Add an `is_admin` column to the users table
- Create tables for usage tracking, API keys, and billing records
- Set admin privileges for users with specific email addresses
- Create indexes and views for better performance and reporting

### 2. Admin Access

The admin dashboard is only accessible to users with admin privileges. By default, users with the following email addresses are granted admin access:

- <EMAIL>
- <EMAIL>

If you need to add more admin users, you can run the following SQL in the Supabase SQL Editor:

```sql
UPDATE users SET is_admin = TRUE WHERE email = '<EMAIL>';
```

### 3. Usage Tracking

The usage tracking system logs token usage for all API requests. This data is stored in the `usage_logs` table and can be viewed in the admin dashboard.

To enable usage tracking, make sure the `usageTracker` middleware is included in the backend server.

### 4. Stripe Integration

To enable metered billing with Stripe:

1. Set up a metered billing product in Stripe
2. Update the subscription service to report usage to Stripe
3. Configure webhooks to handle Stripe events

## Features

### User Management

- View all users
- Filter users by subscription status
- Search for users by name, email, or Stripe customer ID

### Token Usage Monitoring

- View token usage across all models
- Filter usage by date range, model, or user
- Export usage data for reporting

### Billing Management

- View billing records
- Monitor revenue and subscription statistics
- Configure billing rates for different models

### API Key Management

- Create and manage API keys
- Set rate limits for API usage
- Monitor API usage by key

## Development

To add new features to the admin dashboard:

1. Create new components in the `components/admin` directory
2. Add new pages in the `app/admin` directory
3. Update the admin sidebar to include links to new pages

All admin pages are protected by the admin layout, which checks if the current user has admin privileges.
