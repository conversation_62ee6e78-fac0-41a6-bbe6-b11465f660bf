# Token Purchase System - Admin Guide

This guide provides information for administrators managing the token purchase system in the CreateLex AI platform.

## Overview

The token purchase system allows users to buy additional tokens beyond their subscription limits. As an administrator, you need to monitor token purchases, troubleshoot issues, and occasionally perform manual interventions.

## Monitoring Token Purchases

### Viewing Token Transactions

1. Access the admin dashboard at `/admin`
2. Navigate to the "Token Transactions" section
3. You can filter transactions by:
   - User ID
   - Date range
   - Transaction status

### Monitoring Webhook Events

1. In the Stripe dashboard, go to "Developers" > "Webhooks"
2. Check the webhook delivery status for recent events
3. Look for any failed webhook deliveries

## Common Administrative Tasks

### Checking User Token Balance

1. Access the admin dashboard at `/admin`
2. Navigate to the "Users" section
3. Search for the user by email or ID
4. View their current token balance and usage

### Manually Adding Tokens

If a user's purchase was successful but tokens weren't added:

1. Verify the payment in the Stripe dashboard
2. In the admin dashboard, go to "Users" > [User ID] > "Token Balance"
3. Click "Add Tokens" and enter:
   - Token amount
   - Reason (e.g., "Manual recovery for failed webhook")
   - Reference ID (use the Stripe payment intent ID if available)
4. Click "Submit" to add the tokens

### Refunding a Token Purchase

1. In the Stripe dashboard, find the payment and issue a refund
2. In the admin dashboard, go to "Users" > [User ID] > "Token Balance"
3. Click "Adjust Tokens" and enter:
   - Token amount (negative value)
   - Reason (e.g., "Refund for payment pi_123")
   - Reference ID (use the Stripe refund ID)
4. Click "Submit" to deduct the tokens

## Troubleshooting

### User Reports Missing Tokens After Purchase

1. Ask for the user's email or ID
2. Check their token balance history in the admin dashboard
3. Verify the purchase in the Stripe dashboard
4. Check webhook logs for delivery status
5. If the purchase was successful but tokens weren't added:
   - Follow the "Manually Adding Tokens" procedure
   - Document the incident for future reference

### Webhook Failures

If webhooks are consistently failing:

1. Check the webhook configuration in the Stripe dashboard
2. Verify the webhook endpoint URL is correct
3. Check server logs for webhook receipt and processing errors
4. Verify the webhook secret is correct
5. Test the webhook endpoint with a test event from the Stripe dashboard

### Database Discrepancies

If token balances don't match expected values:

1. Check the token transaction history for the user
2. Compare with payment history in Stripe
3. Look for missing or duplicate transactions
4. Adjust the token balance manually if needed

## Reports and Analytics

### Token Purchase Reports

1. Access the admin dashboard at `/admin`
2. Navigate to the "Reports" section
3. Select "Token Purchases" from the report type dropdown
4. Set the date range and other filters
5. View or export the report

### Usage Analytics

1. Access the admin dashboard at `/admin`
2. Navigate to the "Analytics" section
3. View token usage trends by:
   - User
   - Subscription plan
   - Time period

## System Maintenance

### Updating Token Package Prices

1. In the Stripe dashboard, update the price of the product
2. Update the price in the frontend code:
   - `frontend/components/PurchaseTokensModal.js`

### Adding New Token Packages

1. Create a new product and price in the Stripe dashboard
2. Add the new package to the frontend code:
   - `frontend/components/PurchaseTokensModal.js`
3. Update the backend token amount mapping:
   - `backend/src/routes/tokens/purchase/simulate-webhook.js`
   - `backend/src/routes/tokens/purchase.js`

## Emergency Procedures

### Complete Webhook Failure

If webhooks completely stop working:

1. Check server status and logs
2. Verify Stripe service status
3. Implement manual processing:
   - Get a list of successful payments from Stripe
   - Compare with token transactions in the database
   - Manually add tokens for missing transactions

### Database Recovery

If the token balance database is corrupted:

1. Restore from the most recent backup
2. Reconcile with Stripe payment history:
   - Get all token purchases from Stripe
   - Compare with token transactions in the database
   - Add missing transactions
   - Recalculate token balances

## Contact Information

For urgent issues with the token purchase system:

- Technical Support: <EMAIL>
- Stripe Support: https://support.stripe.com
- System Administrator: <EMAIL>
