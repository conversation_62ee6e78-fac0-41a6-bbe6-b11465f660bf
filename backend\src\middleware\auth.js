const jwt = require('jsonwebtoken');
const authService = require('../services/authService');

// Middleware to authenticate JWT tokens with fallback for specific endpoints
const authenticateJWTWithFallback = async (req, res, next) => {
  // List of endpoints that should return mock data if authentication fails
  const mockDataEndpoints = [
    '/api/subscription/check',
    '/api/tokens/balance',
    '/api/tokens/packages'
  ];

  // Check if this is a mock data endpoint
  const isMockDataEndpoint = mockDataEndpoints.some(endpoint => req.originalUrl.includes(endpoint));

  // Check for mock user ID header
  if (req.headers['x-user-id'] === 'mock-user-id') {
    console.log(`[Auth] Using mock user ID from header for endpoint: ${req.originalUrl}`);

    // Create a mock user
    req.user = {
      id: 'mock-user-id',
      email: '<EMAIL>',
      name: 'Mock User',
      picture: '',
      subscriptionStatus: 'active',
      is_admin: false
    };

    return next();
  }

  // If this is a mock data endpoint and no user ID is provided, check for a real user ID
  if (isMockDataEndpoint && !req.query.userId) {
    console.log(`[Auth] Checking for real user ID for endpoint: ${req.originalUrl}`);

    // If we have a token, try to extract the user ID from it
    if (req.headers.authorization) {
      try {
        const token = req.headers.authorization.split(' ')[1];
        const decoded = jwt.decode(token);

        if (decoded && decoded.sub) {
          console.log(`[Auth] Found user ID in token: ${decoded.sub}`);
          req.query.userId = decoded.sub;
        }
      } catch (error) {
        console.error(`[Auth] Error extracting user ID from token: ${error.message}`);
      }
    }

    // If we still don't have a user ID, use a default one
    if (!req.query.userId) {
      console.log(`[Auth] No user ID found, using default user ID`);
      req.query.userId = '077f1533-9f81-429c-b1b1-52d9c83f146c'; // Use a real user ID
    }
  }

  // If a user ID is provided in the query, use it
  if (isMockDataEndpoint && req.query.userId) {
    console.log(`[Auth] Using user ID from query for endpoint: ${req.originalUrl}`);

    // Try to get the user from the database
    try {
      const user = await authService.getUserById(req.query.userId);

      if (user) {
        req.user = user;
        return next();
      } else {
        console.log(`[Auth] User not found with ID: ${req.query.userId}, using mock user`);

        // Create a mock user
        req.user = {
          id: req.query.userId,
          email: '<EMAIL>',
          name: 'Mock User',
          picture: '',
          subscriptionStatus: 'active',
          is_admin: false
        };

        return next();
      }
    } catch (error) {
      console.error(`[Auth] Error getting user by ID: ${error.message}`);

      // Create a mock user
      req.user = {
        id: req.query.userId,
        email: '<EMAIL>',
        name: 'Mock User',
        picture: '',
        subscriptionStatus: 'active',
        is_admin: false
      };

      return next();
    }
  }

  // Otherwise, try to authenticate normally
  try {
    await authenticateJWT(req, res, next);
  } catch (error) {
    console.error(`[Auth] Error in authenticateJWTWithFallback: ${error.message}`);
    return res.status(500).json({ error: 'Authentication error', message: error.message });
  }
};

// Middleware to authenticate JWT tokens
const authenticateJWT = async (req, res, next) => {
  // Check for bypass flag in development mode
  if (process.env.NODE_ENV === 'development' && process.env.BYPASS_AUTH === 'true') {
    console.log('[Auth] Bypassing authentication in development mode');

    // Use the user ID from the request if available, otherwise use the logged-in user ID from the token
    let devUserId = req.headers['x-user-id'];
    let devEmail = '<EMAIL>';
    let devName = 'Development User';

    // If we have an authorization header, try to extract the user ID from it
    if (!devUserId && req.headers.authorization) {
      try {
        const token = req.headers.authorization.split(' ')[1];
        const decoded = jwt.decode(token);

        if (decoded) {
          devUserId = decoded.sub || decoded.id;
          devEmail = decoded.email || decoded.user_metadata?.email || '<EMAIL>';
          devName = decoded.name || decoded.user_metadata?.name || 'Development User';
          console.log(`[Auth] Extracted user ID from token: ${devUserId}`);
        }
      } catch (error) {
        console.error(`[Auth] Error extracting user ID from token: ${error.message}`);
      }
    }

    // If we still don't have a user ID, use the one from the query
    if (!devUserId && req.query.userId) {
      devUserId = req.query.userId;
      console.log(`[Auth] Using user ID from query: ${devUserId}`);
    }

    // If we still don't have a user ID, use a default one
    if (!devUserId) {
      devUserId = '077f1533-9f81-429c-b1b1-52d9c83f146c'; // Default user ID
      console.log(`[Auth] Using default user ID: ${devUserId}`);
    }

    // Set admin flag for development
    const isAdmin = devEmail === '<EMAIL>' || devEmail === '<EMAIL>';

    // Create a mock user for development
    const mockUser = {
      id: devUserId,
      email: devEmail,
      name: devName,
      picture: '',
      subscriptionStatus: 'active',
      is_admin: isAdmin
    };

    req.user = mockUser;
    return next();
  }

  // Try to get the token from different sources
  let token = null;

  // 1. Check Authorization header (Bearer token)
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    token = authHeader.split(' ')[1];
  }

  // 2. Check x-auth-token header
  if (!token && req.headers['x-auth-token']) {
    token = req.headers['x-auth-token'];
  }

  // 3. Check query parameters
  if (!token && req.query && req.query.token) {
    token = req.query.token;
  }

  // 4. Check for user ID header as fallback
  const userId = req.headers['x-user-id'];

  if (!token && !userId) {
    console.log('[Auth] No token or user ID found in request');
    return res.status(401).json({ error: 'Authentication required' });
  }

  // If we have a user ID but no token, try to get the user directly
  if (!token && userId) {
    console.log(`[Auth] Using user ID ${userId} without token verification`);

    try {
      const user = await authService.getUserById(userId);

      if (!user) {
        console.error(`[Auth] User not found with ID: ${userId}`);
        return res.status(403).json({ error: 'User not found' });
      }

      req.user = user;
      return next();
    } catch (error) {
      console.error(`[Auth] Error getting user by ID: ${error.message}`);
      return res.status(500).json({ error: 'Internal server error' });
    }
  }

  // If we have a token, verify it
  if (token) {
    try {
      // Log the token for debugging
      console.log('[Auth] Token received:', token.substring(0, 20) + '...');

      // Decode the token to check if it's from Supabase
      const decoded = jwt.decode(token);
      console.log('[Auth] Decoded token payload:', decoded ? 'Valid' : 'Invalid');

      if (!decoded) {
        console.error('[Auth] Failed to decode token');
        return res.status(403).json({ error: 'Invalid token format' });
      }

      // Check if token is expired
      if (decoded.exp && decoded.exp < Math.floor(Date.now() / 1000)) {
        console.error('[Auth] Token is expired');
        return res.status(403).json({ error: 'Token expired' });
      }

      // Check if this is a Supabase token (has 'sub' claim)
      const isSupabaseToken = !!decoded.sub;
      console.log('[Auth] Is Supabase token:', isSupabaseToken);

      // Extract user ID from token
      const userId = decoded.sub || decoded.id || decoded.user_id;
      console.log('[Auth] User ID from token:', userId);

      if (!userId) {
        console.error('[Auth] No user ID found in token');
        return res.status(403).json({ error: 'Invalid token: no user ID found' });
      }

      // Check if user is an admin based on email
      const adminEmails = ['<EMAIL>', '<EMAIL>'];
      const userEmail = decoded.email || '';
      const isAdmin = adminEmails.includes(userEmail.toLowerCase());
      console.log('[Auth] User email:', userEmail);
      console.log('[Auth] Is admin:', isAdmin);

      // Create user object from token
      const user = {
        id: userId,
        email: userEmail,
        name: decoded.name || decoded.user_metadata?.name || '',
        picture: decoded.picture || decoded.user_metadata?.avatar_url || '',
        subscriptionStatus: decoded.subscriptionStatus || 'active',
        is_admin: isAdmin
      };

      // Set the user on the request object
      req.user = user;
      next();
    } catch (error) {
      console.error('[Auth] Token verification error:', error.message);

      // If token verification fails but we have a user ID, try to get the user directly
      if (userId) {
        console.log(`[Auth] Token verification failed, falling back to user ID: ${userId}`);

        try {
          const user = await authService.getUserById(userId);

          if (!user) {
            console.error(`[Auth] User not found with ID: ${userId}`);
            return res.status(403).json({ error: 'User not found' });
          }

          // Check if user is an admin based on email
          const adminEmails = ['<EMAIL>', '<EMAIL>'];
          const isAdmin = user.email && adminEmails.includes(user.email.toLowerCase());
          user.is_admin = isAdmin;

          req.user = user;
          return next();
        } catch (userError) {
          console.error(`[Auth] Error getting user by ID: ${userError.message}`);
          return res.status(500).json({ error: 'Internal server error' });
        }
      }

      return res.status(403).json({ error: 'Invalid or expired token' });
    }
  } else {
    res.status(401).json({ error: 'Authentication required' });
  }
};

module.exports = { authenticateJWT, authenticateJWTWithFallback };
