# 📥 Download CreateLex Bridge

The CreateLex Bridge is a desktop application that connects your local development environment with the CreateLex platform, enabling seamless MCP (Model Context Protocol) integration.

## 🚀 **Quick Download**

### **Windows** 
[![Download for Windows](https://img.shields.io/badge/Download-Windows-blue?style=for-the-badge&logo=windows)](https://github.com/AlexKissiJr/AiWebplatform/releases/latest/download/CreateLex-Bridge-Setup-Windows.exe)

**File:** `CreateLex-Bridge-Setup-Windows.exe` (356 MB)  
**Requirements:** Windows 10/11 (64-bit)

### **macOS**
[![Download for macOS](https://img.shields.io/badge/Download-macOS-black?style=for-the-badge&logo=apple)](https://github.com/AlexKissiJr/AiWebplatform/releases/latest/download/CreateLex-Bridge-macOS.dmg)

**File:** `CreateLex-Bridge-macOS.dmg` (~400 MB)  
**Requirements:** macOS 10.15+ (Intel & Apple Silicon)

### **Linux**
[![Download for Linux](https://img.shields.io/badge/Download-Linux-orange?style=for-the-badge&logo=linux)](https://github.com/AlexKissiJr/AiWebplatform/releases/latest/download/CreateLex-Bridge-Linux.AppImage)

**File:** `CreateLex-Bridge-Linux.AppImage` (~350 MB)  
**Requirements:** Ubuntu 18.04+, CentOS 7+, or equivalent

---

## 📋 **Installation Instructions**

### **Windows Installation**
1. Download `CreateLex-Bridge-Setup-Windows.exe`
2. Run the installer (Windows may show a security warning - click "More info" → "Run anyway")
3. Follow the installation wizard
4. Launch from Start Menu or Desktop shortcut

### **macOS Installation**
1. Download `CreateLex-Bridge-macOS.dmg`
2. Open the DMG file
3. Drag "CreateLex Bridge" to Applications folder
4. Right-click the app and select "Open" (first time only, due to Gatekeeper)
5. Launch from Applications or Launchpad

### **Linux Installation**
1. Download `CreateLex-Bridge-Linux.AppImage`
2. Make it executable: `chmod +x CreateLex-Bridge-Linux.AppImage`
3. Run: `./CreateLex-Bridge-Linux.AppImage`
4. Optional: Integrate with system using [AppImageLauncher](https://github.com/TheAssassin/AppImageLauncher)

---

## 🔐 **First Time Setup**

1. **Launch the app** - The CreateLex Bridge will start in system tray
2. **Sign in** - Click "Sign in with CreateLex" to authenticate
3. **Verify subscription** - Ensure you have an active CreateLex subscription
4. **Start MCP Server** - Click "Start Server" to begin using MCP features

---

## 🎯 **What's Included**

- **MCP Server** - Local Model Context Protocol server
- **Authentication** - Secure OAuth integration with CreateLex
- **Subscription Management** - Automatic subscription validation
- **Auto-Updates** - Automatic application updates
- **System Tray** - Runs quietly in background
- **Cross-Platform** - Works on Windows, Mac, and Linux

---

## 🔧 **System Requirements**

### **Minimum Requirements:**
- **RAM:** 4 GB
- **Storage:** 1 GB free space
- **Network:** Internet connection for authentication

### **Recommended:**
- **RAM:** 8 GB or more
- **Storage:** 2 GB free space
- **CPU:** Multi-core processor

---

## 🐛 **Troubleshooting**

### **Common Issues:**

#### **"App won't start"**
- **Windows:** Run as Administrator
- **Mac:** Check System Preferences → Security & Privacy
- **Linux:** Ensure executable permissions

#### **"Authentication failed"**
- Check internet connection
- Verify CreateLex account is active
- Try signing out and back in

#### **"Subscription required"**
- Ensure you have an active CreateLex subscription
- Visit [createlex.com/subscription](https://createlex.com/subscription) to subscribe

#### **"MCP Server won't start"**
- Check if port 9877 is available
- Restart the application
- Check system firewall settings

---

## 📞 **Support**

Need help? We're here for you:

- **📧 Email:** <EMAIL>
- **💬 Discord:** [discord.gg/createlex](https://discord.gg/createlex)
- **🐛 Issues:** [GitHub Issues](https://github.com/AlexKissiJr/AiWebplatform/issues)
- **📖 Documentation:** [docs.createlex.com](https://docs.createlex.com)

---

## 🔄 **Release Notes**

### **Version 1.0.0** (Current)
- ✅ Initial release
- ✅ Cross-platform support (Windows, Mac, Linux)
- ✅ OAuth authentication
- ✅ Subscription management
- ✅ MCP server integration
- ✅ Auto-updates
- ✅ System tray integration

---

## 🏗️ **For Developers**

Want to build from source? Check out our [Build Guide](BUILD_GUIDE.md).

**Repository:** https://github.com/AlexKissiJr/AiWebplatform  
**License:** MIT  
**Contributing:** Pull requests welcome!

---

**Last Updated:** June 19, 2025  
**Version:** 1.0.0 