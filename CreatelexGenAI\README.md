# CreateLex AI Studio for Unreal Engine

<p align="center">
  <strong>Professional AI Integration Plugin for Unreal Engine</strong><br/>
  Advanced AI tools, MCP support, and intelligent game development workflows
</p>

<p align="center">
  <a href="https://createlex.com/"><img src="https://img.shields.io/badge/Website-CreateLex-blue?style=for-the-badge" alt="Website"></a>
  <a href="https://docs.createlex.com/"><img src="https://img.shields.io/badge/Documentation-docs.createlex.com-green?style=for-the-badge" alt="Documentation"></a>
  <a href="https://support.createlex.com/"><img src="https://img.shields.io/badge/Support-support.createlex.com-orange?style=for-the-badge" alt="Support"></a>
</p>

---

## 🚀 **Overview**

**CreateLex AI Studio** is the most comprehensive AI integration plugin for Unreal Engine, designed for professional game developers who want to leverage cutting-edge AI technologies without the complexity of managing multiple APIs and integrations.

### ✨ **Key Features**

- 🤖 **25+ AI Models**: GPT-4, Claude Sonnet 4, Grok 3, DeepSeek R1, and more
- 🔧 **Model Control Protocol (MCP)**: Direct AI control of your Unreal Engine projects
- 🎮 **Game-Focused Tools**: AI-powered blueprint generation, scene creation, and asset management
- 🌐 **Multi-Platform**: Windows, macOS, Linux support
- 🔒 **Enterprise Security**: Secure API key management and subscription-based access
- 📱 **Multiple Integrations**: Claude Desktop, Cursor IDE, VS Code support
- ⚡ **Production Ready**: UE 4.26+ compatibility with enterprise-grade stability

---

## 🎯 **Who Is This For?**

- **🎮 Game Developers**: Accelerate development with AI-powered tools
- **🏢 Studios**: Enterprise-grade AI integration for production pipelines  
- **🎓 Educators**: Teaching AI integration in game development
- **🔬 Researchers**: Experimenting with AI in interactive experiences
- **🚀 Indie Developers**: Access to professional AI tools without complexity

---

## 🔥 **What Makes CreateLex AI Studio Different?**

### **🎯 Game Development Focus**
Unlike generic AI plugins, CreateLex AI Studio is built specifically for game developers with:
- Blueprint auto-generation from natural language
- Intelligent scene composition and object placement
- AI-powered material and texture creation
- Smart debugging and optimization suggestions

### **🔧 Model Control Protocol (MCP) Integration**
Revolutionary AI-to-engine communication allowing:
- Direct AI control of Unreal Engine through Claude Desktop
- Real-time scene manipulation via natural language
- Automated blueprint creation and modification
- Intelligent asset management and organization

### **🌐 Enterprise-Grade Infrastructure**
- Secure subscription-based access control
- Professional support and documentation
- Regular updates and new model integrations
- Scalable cloud deployment options

---

## 📊 **Supported AI Models & APIs**

### **🧠 Language Models**
| Provider | Models | Status |
|----------|--------|---------|
| **OpenAI** | GPT-4o, GPT-4o-mini, o1, o3-mini | ✅ Production |
| **Anthropic** | Claude Sonnet 4, Claude Opus 4, Claude 3.7 | ✅ Production |
| **XAI** | Grok 3, Grok 3 Mini | ✅ Production |
| **DeepSeek** | DeepSeek Chat, DeepSeek R1 Reasoning | ✅ Production |
| **Google** | Gemini 2.0 Flash, Gemini 2.5 Pro | 🔄 Coming Soon |
| **Meta** | Llama 4 Behemoth, Llama 4 Maverick | 🔄 Coming Soon |

### **🎨 Specialized APIs**
- **DALL-E 3**: AI image generation
- **Whisper**: Speech-to-text integration
- **Vision APIs**: Image analysis and processing
- **Structured Outputs**: JSON schema-based responses

---

## 🛠️ **MCP (Model Control Protocol) Features**

CreateLex AI Studio includes the most advanced MCP implementation for Unreal Engine:

### **🎮 Scene Control**
- Spawn and manipulate objects via natural language
- Intelligent material creation and application
- Dynamic lighting and environment setup
- Real-time scene composition

### **🔧 Blueprint Generation**
- Create complete Blueprint classes from descriptions
- Add components, variables, and functions automatically
- Generate complex node graphs and connections
- Compile and deploy Blueprints instantly

### **📁 Project Management**
- Create and organize project files and folders
- Manage assets and dependencies
- Automated project cleanup and optimization
- Version control integration

### **🐍 Python Integration**
- Execute Python scripts directly in Unreal Engine
- Access full Unreal Python API
- Custom tool development and automation
- Advanced scripting capabilities

---

## 🚀 **Quick Start Guide**

### **1. Installation**

#### **Option A: Git Submodule (Recommended)**
```bash
# Add to your Unreal project
git submodule add https://github.com/createlex/unreal-ai-studio Plugins/CreateLexAIStudio

# Generate project files
# Right-click your .uproject file → "Generate Visual Studio project files"
```

#### **Option B: Direct Download**
1. Download the latest release from [CreateLex Downloads](https://createlex.com/downloads)
2. Extract to your project's `Plugins/` folder
3. Regenerate project files

### **2. Enable the Plugin**
1. Open your Unreal project
2. Go to **Edit → Plugins**
3. Search for "CreateLex AI Studio"
4. Enable the plugin and restart the editor

### **3. Configure API Keys**

#### **For Development (Environment Variables)**
```bash
# Windows
setx CREATELEX_OPENAI_KEY "your-openai-key"
setx CREATELEX_ANTHROPIC_KEY "your-anthropic-key"
setx CREATELEX_XAI_KEY "your-xai-key"

# macOS/Linux
export CREATELEX_OPENAI_KEY="your-openai-key"
export CREATELEX_ANTHROPIC_KEY="your-anthropic-key"
export CREATELEX_XAI_KEY="your-xai-key"
```

#### **For Production (Secure Runtime)**
```cpp
// C++ Example
UCreateLexSecureKey::SetAPIKey(EAIProvider::OpenAI, "your-key");

// Blueprint Example - Use "Set CreateLex API Key" node
```

### **4. MCP Setup (Optional)**

#### **Claude Desktop Configuration**
Create or edit `claude_desktop_config.json`:
```json
{
  "mcpServers": {
    "createlex-unreal": {
      "command": "python",
      "args": ["<your_project_path>/Plugins/CreateLexAIStudio/Content/Python/mcp_server.py"],
      "env": {
        "UNREAL_HOST": "localhost",
        "UNREAL_PORT": "9877"
      }
    }
  }
}
```

#### **Cursor IDE Configuration**
Create `.cursor/mcp.json` in your project:
```json
{
  "mcpServers": {
    "createlex-unreal": {
      "command": "python", 
      "args": ["<your_project_path>/Plugins/CreateLexAIStudio/Content/Python/mcp_server.py"],
      "env": {
        "UNREAL_HOST": "localhost",
        "UNREAL_PORT": "9877"
      }
    }
  }
}
```

---

## 💡 **Usage Examples**

### **🧠 Basic AI Chat (C++)**
```cpp
#include "CreateLexAI/OpenAI/CreateLexOpenAIChat.h"

void AMyActor::QueryAI()
{
    FCreateLexChatSettings Settings;
    Settings.Model = "gpt-4o";
    Settings.MaxTokens = 500;
    Settings.Messages.Add({"system", "You are a helpful game development assistant."});
    Settings.Messages.Add({"user", "Create a simple enemy AI behavior description."});

    UCreateLexOpenAIChat::SendChatRequest(
        Settings,
        FOnCreateLexChatResponse::CreateLambda([this](const FString& Response, bool bSuccess)
        {
            if (bSuccess)
            {
                UE_LOG(LogTemp, Log, TEXT("AI Response: %s"), *Response);
            }
        })
    );
}
```

### **🎨 Blueprint AI Integration**
Use the **"CreateLex Chat Request"** Blueprint node:
1. Set your prompt and model
2. Connect the response to your game logic
3. Process AI responses in real-time

### **🔧 MCP Scene Control**
With Claude Desktop + MCP enabled:
```
"Create a medieval village scene with 5 houses, a well in the center, and appropriate lighting"
```
The AI will automatically:
- Spawn building meshes
- Position them intelligently  
- Add appropriate materials
- Set up lighting
- Organize the scene hierarchy

### **🎮 Advanced Blueprint Generation**
```
"Create a player controller blueprint with WASD movement, mouse look, and a jump action that plays a sound effect"
```
The AI will:
- Generate the Blueprint class
- Add input bindings
- Create movement logic
- Add sound components
- Wire everything together

---

## 🏢 **Enterprise Features**

### **🔒 Subscription Management**
- Secure token-based authentication
- Usage tracking and analytics
- Team collaboration features
- Priority support access

### **☁️ Cloud Deployment**
- Scalable MCP server hosting
- Global CDN distribution
- Enterprise SLA guarantees
- 24/7 monitoring and support

### **🔧 Custom Integrations**
- White-label solutions
- Custom AI model training
- Dedicated support teams
- On-premise deployment options

---

## 📚 **Documentation & Support**

- **📖 Full Documentation**: [docs.createlex.com](https://docs.createlex.com)
- **🎥 Video Tutorials**: [tutorials.createlex.com](https://tutorials.createlex.com)
- **💬 Community Forum**: [community.createlex.com](https://community.createlex.com)
- **🎫 Support Tickets**: [support.createlex.com](https://support.createlex.com)
- **📧 Enterprise Sales**: <EMAIL>

---

## 🔧 **System Requirements**

### **Unreal Engine**
- **Minimum**: Unreal Engine 4.26
- **Recommended**: Unreal Engine 5.1+
- **Tested**: UE 4.26, 4.27, 5.0, 5.1, 5.2, 5.3, 5.4, 5.5

### **Platforms**
- **Windows**: Windows 10/11 (64-bit)
- **macOS**: macOS 10.15+ (Intel/Apple Silicon)
- **Linux**: Ubuntu 18.04+ (64-bit)

### **Dependencies**
- **Python**: 3.8+ (for MCP features)
- **Visual Studio**: 2019/2022 (Windows)
- **Xcode**: 12+ (macOS)

---

## 🤝 **Contributing**

We welcome contributions from the community! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### **Development Setup**
```bash
# Clone the repository
git clone https://github.com/createlex/unreal-ai-studio.git

# Install development dependencies
pip install -r requirements-dev.txt

# Set up pre-commit hooks
pre-commit install
```

---

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

**Copyright © 2025 CreateLex Inc. All rights reserved.**

---

## 🌟 **Why Choose CreateLex?**

> *"CreateLex AI Studio transformed our development workflow. What used to take hours of manual Blueprint creation now happens in minutes with natural language descriptions."*
> 
> **— Senior Game Developer, AAA Studio**

> *"The MCP integration is revolutionary. Having Claude directly control our Unreal Engine scenes has accelerated our prototyping by 10x."*
> 
> **— Indie Game Developer**

> *"Enterprise-grade support and documentation made adoption seamless across our 50+ developer team."*
> 
> **— Technical Director, Game Studio**

---

<p align="center">
  <strong>Ready to transform your game development with AI?</strong><br/>
  <a href="https://createlex.com/get-started">Get Started Today</a> | 
  <a href="https://createlex.com/demo">Request Demo</a> | 
  <a href="https://createlex.com/pricing">View Pricing</a>
</p>

<p align="center">
  Made with ❤️ by the <a href="https://createlex.com/team">CreateLex Team</a>
</p> 