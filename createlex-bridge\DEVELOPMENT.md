# CreateLex Bridge Development Guide

## 🚀 Quick Start

The CreateLex Bridge now **automatically defaults to localhost development mode** when you run `npm start`, making development much easier!

### Simple Development Setup

```bash
# 1. Start backend (Terminal 1)
cd backend
npm run dev

# 2. Start frontend (Terminal 2) 
cd frontend
npm run dev

# 3. Start bridge (Terminal 3)
cd createlex-bridge
npm start  # 🎉 Now automatically uses localhost!
```

## 📋 Available Commands

| Command | Description | Environment |
|---------|-------------|-------------|
| `npm start` | **Default development mode** - Auto-detects and uses localhost:3000 | Development |
| `npm run start:dev` | Explicit development mode with localhost | Development |
| `npm run start:prod` | Production mode with createlex.com | Production |
| `npm run dev` | Windows development script (legacy) | Development |
| `npm run dev:mac` | Mac development script (legacy) | Development |
| `./start-dev.sh` | Unix shell script (legacy) | Development |

## 🔧 Environment Variables

When you run `npm start`, the bridge automatically sets:

```bash
NODE_ENV=development
CREATELEX_BASE_URL=http://localhost:3000
API_BASE_URL=http://localhost:5001/api
DEV_MODE=true
```

## 🎯 What You'll See

### Development Mode (npm start)
```
🚀 CreateLex Bridge - Development Mode
📍 Base URL: http://localhost:3000
🔗 API URL: http://localhost:5001/api

=== OAuth Flow Configuration ===
Base URL: http://localhost:3000
Opening browser for authentication: http://localhost:3000/login?redirect=...
Frontend callback will be at: http://localhost:3000/bridge-callback
================================
```

### Production Mode (npm run start:prod)
```
🚀 CreateLex Bridge - Production Mode
📍 Base URL: https://createlex.com

=== OAuth Flow Configuration ===
Base URL: https://createlex.com
Opening browser for authentication: https://createlex.com/login?redirect=...
================================
```

## 🔀 Override Environment Variables

You can still override the defaults if needed:

```bash
# Use different ports
CREATELEX_BASE_URL=http://localhost:3001 npm start

# Force production mode
NODE_ENV=production npm start

# Custom API endpoint
API_BASE_URL=http://localhost:5002/api npm start
```

## 🐛 Troubleshooting

### Bridge points to production instead of localhost
- Make sure you're using `npm start` (not `electron .`)
- Check that no `NODE_ENV=production` is set in your environment
- Try `npm run start:dev` to force development mode

### Authentication fails
- Ensure frontend is running on `http://localhost:3000`
- Ensure backend is running on `http://localhost:5001`
- Check that Supabase redirect URLs include `http://localhost:3000/bridge-callback`

### Subscription validation issues
- Make sure `NEXT_PUBLIC_BYPASS_SUBSCRIPTION=false` in frontend
- Ensure no `ALWAYS_SUBSCRIBED=true` in backend
- Check that Stripe credentials are configured correctly 