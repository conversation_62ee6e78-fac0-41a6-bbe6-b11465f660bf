<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- Allow JIT compilation for V8 -->
    <key>com.apple.security.cs.allow-jit</key>
    <true/>
    
    <!-- Allow unsigned executable memory -->
    <key>com.apple.security.cs.allow-unsigned-executable-memory</key>
    <true/>
    
    <!-- Disable library validation -->
    <key>com.apple.security.cs.disable-library-validation</key>
    <true/>
    
    <!-- Network access for MCP server and API calls -->
    <key>com.apple.security.network.client</key>
    <true/>
    
    <!-- Allow network server for MCP bridge -->
    <key>com.apple.security.network.server</key>
    <true/>
    
    <!-- File access for MCP server and user data -->
    <key>com.apple.security.files.user-selected.read-write</key>
    <true/>
    
    <!-- Access to user data directory -->
    <key>com.apple.security.files.user-selected.read-only</key>
    <true/>
    
    <!-- Allow subprocess execution for Python MCP server -->
    <key>com.apple.security.cs.allow-dyld-environment-variables</key>
    <true/>
</dict>
</plist> 