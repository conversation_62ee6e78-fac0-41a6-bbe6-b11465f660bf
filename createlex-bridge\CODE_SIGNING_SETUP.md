# Code Signing Setup for CreateLex Bridge

## Why Code Signing?
Code signing prevents the "Apple could not verify" security warning that users see when downloading your app. Without code signing, every user has to manually override macOS security settings.

## Prerequisites

### 1. Apple Developer Account
- Sign up at [developer.apple.com](https://developer.apple.com/programs/)
- Cost: $99/year
- Provides access to code signing certificates

### 2. Install Certificates

After getting your Apple Developer account:

1. **Download certificates from Apple Developer portal:**
   - Go to Certificates, Identifiers & Profiles
   - Create a "Developer ID Application" certificate
   - Download and install in Keychain Access

2. **Find your Team ID:**
   - Go to Apple Developer portal → Membership
   - Copy your Team ID (10-character string)

## Configuration

### 1. Update package.json

Replace the placeholder values in `package.json`:

```json
"mac": {
  "identity": "Developer ID Application: Your Company Name (YOUR_TEAM_ID)",
  "notarize": {
    "teamId": "YOUR_TEAM_ID"
  }
}
```

**Example:**
```json
"mac": {
  "identity": "Developer ID Application: CreateLex Inc (ABC123DEFG)",
  "notarize": {
    "teamId": "ABC123DEFG"
  }
}
```

### 2. Environment Variables

Set these environment variables for notarization:

```bash
export APPLE_ID="<EMAIL>"
export APPLE_ID_PASSWORD="app-specific-password"
export APPLE_TEAM_ID="YOUR_TEAM_ID"
```

### 3. App-Specific Password

1. Go to [appleid.apple.com](https://appleid.apple.com)
2. Sign in → App-Specific Passwords
3. Generate a password for "CreateLex Bridge"
4. Use this password in `APPLE_ID_PASSWORD`

## Building Signed App

### Method 1: Command Line
```bash
# Set environment variables
export APPLE_ID="<EMAIL>"
export APPLE_ID_PASSWORD="your-app-specific-password"
export APPLE_TEAM_ID="YOUR_TEAM_ID"

# Build signed app
npm run build-mac-protected
```

### Method 2: CI/CD (GitHub Actions)

Add these secrets to your GitHub repository:
- `APPLE_ID`
- `APPLE_ID_PASSWORD` 
- `APPLE_TEAM_ID`
- `CSC_LINK` (base64 encoded .p12 certificate)
- `CSC_KEY_PASSWORD` (certificate password)

## Verification

After building, verify the signature:

```bash
# Check signature
codesign -dv --verbose=4 "dist/CreateLex Bridge.app"

# Verify notarization
spctl -a -t exec -vv "dist/CreateLex Bridge.app"
```

## Troubleshooting

### Common Issues:

1. **"No identity found"**
   - Certificate not installed in Keychain
   - Wrong identity name in package.json

2. **"Notarization failed"**
   - Wrong Apple ID or password
   - App not properly signed first

3. **"Team ID not found"**
   - Wrong team ID in environment variables
   - Not member of Apple Developer Program

### Quick Fix for Development

If you don't want to set up code signing immediately, you can:

1. **Remove quarantine from built DMG:**
   ```bash
   xattr -dr com.apple.quarantine "CreateLex-Bridge-1.0.0-arm64.dmg"
   ```

2. **Disable Gatekeeper temporarily:**
   ```bash
   sudo spctl --master-disable
   # Build and test
   sudo spctl --master-enable
   ```

## Alternative: Self-Signed Certificate

For internal testing, create a self-signed certificate:

```bash
# Create self-signed certificate
security create-certificate -c "CreateLex Bridge" -P -p codesigning -k ~/Library/Keychains/login.keychain

# Update package.json
"identity": "CreateLex Bridge"
```

**Note:** Self-signed certificates still show warnings but are easier to set up.

## Production Checklist

- [ ] Apple Developer account active
- [ ] Developer ID Application certificate installed
- [ ] Team ID added to package.json
- [ ] Environment variables set
- [ ] App-specific password generated
- [ ] Build process includes notarization
- [ ] Signed app verified with `spctl`

Once properly code signed and notarized, users will be able to run your app without any security warnings. 