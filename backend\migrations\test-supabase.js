require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: SUPABASE_URL and SUPABASE_SERVICE_KEY must be set in .env file');
  process.exit(1);
}

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key (first 10 chars):', supabaseKey.substring(0, 10) + '...');

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testSupabase() {
  try {
    // Test connection by getting the server timestamp
    console.log('Testing Supabase connection...');
    
    const { data, error } = await supabase.from('_test_connection').select('*').limit(1);
    
    if (error) {
      console.log('Error testing connection, but this is expected if the table does not exist:', error.message);
      console.log('This is not necessarily a problem, just confirming we can connect to Supabase.');
    } else {
      console.log('Successfully connected to Supabase!');
      console.log('Data:', data);
    }
    
    // Create a test table
    console.log('\nCreating test table...');
    const { error: createError } = await supabase.rpc('create_test_table');
    
    if (createError) {
      console.error('Error creating test table:', createError);
      
      // Try a different approach - create the table with SQL
      console.log('Trying to create test table with SQL...');
      const { error: sqlError } = await supabase.rpc('execute_sql', {
        sql: `
          CREATE TABLE IF NOT EXISTS public.test_table (
            id SERIAL PRIMARY KEY,
            name TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      });
      
      if (sqlError) {
        console.error('Error creating test table with SQL:', sqlError);
      } else {
        console.log('Successfully created test table with SQL!');
      }
    } else {
      console.log('Successfully created test table!');
    }
    
    // Insert a test record
    console.log('\nInserting test record...');
    const { data: insertData, error: insertError } = await supabase
      .from('test_table')
      .insert([{ name: 'Test Record' }])
      .select();
    
    if (insertError) {
      console.error('Error inserting test record:', insertError);
    } else {
      console.log('Successfully inserted test record!');
      console.log('Inserted data:', insertData);
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

testSupabase();
