{"id": "9ea87331-4108-40dd-8ac1-32fb1d2f1149", "prevId": "c25dbd1f-846e-4ca4-b2f3-d24f70977d6f", "version": "7", "dialect": "postgresql", "tables": {"public.chats": {"name": "chats", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "default": "'New Chat'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.messages": {"name": "messages", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "chat_id": {"name": "chat_id", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "reasoning": {"name": "reasoning", "type": "text", "primaryKey": false, "notNull": false}, "tool_calls": {"name": "tool_calls", "type": "json", "primaryKey": false, "notNull": false}, "tool_results": {"name": "tool_results", "type": "json", "primaryKey": false, "notNull": false}, "has_tool_use": {"name": "has_tool_use", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {"messages_chat_id_chats_id_fk": {"name": "messages_chat_id_chats_id_fk", "tableFrom": "messages", "tableTo": "chats", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.steps": {"name": "steps", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "message_id": {"name": "message_id", "type": "text", "primaryKey": false, "notNull": true}, "step_type": {"name": "step_type", "type": "text", "primaryKey": false, "notNull": true}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": false}, "reasoning": {"name": "reasoning", "type": "text", "primaryKey": false, "notNull": false}, "finish_reason": {"name": "finish_reason", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "tool_calls": {"name": "tool_calls", "type": "json", "primaryKey": false, "notNull": false}, "tool_results": {"name": "tool_results", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"steps_message_id_messages_id_fk": {"name": "steps_message_id_messages_id_fk", "tableFrom": "steps", "tableTo": "messages", "columnsFrom": ["message_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}