
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CreateLex Bridge - Dashboard</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 40px;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }
    h1 { color: #333; margin-bottom: 20px; }
    .status { padding: 12px; border-radius: 6px; margin: 20px 0; }
    .success { background: #f0fdf4; border: 1px solid #bbf7d0; color: #16a34a; }
    .info { background: #eff6ff; border: 1px solid #bfdbfe; color: #1d4ed8; }
    .warning { background: #fffbeb; border: 1px solid #fde68a; color: #d97706; }
    .error { background: #fef2f2; border: 1px solid #fecaca; color: #dc2626; }
    button {
      background: #667eea;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      margin: 8px;
      transition: all 0.2s ease;
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }
    button:hover:not(:disabled) { opacity: 0.9; }
    button:disabled {
      background: #9ca3af;
      cursor: not-allowed;
      opacity: 0.6;
    }
    .subscription-status {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      background: #f9fafb;
      border-radius: 8px;
      margin: 20px 0;
      border: 1px solid #e5e7eb;
    }
    .subscription-badge {
      display: inline-block;
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 14px;
      font-weight: 500;
    }
    .subscription-active {
      background: #d1fae5;
      color: #065f46;
    }
    .subscription-inactive {
      background: #fee2e2;
      color: #991b1b;
    }
    .spinner {
      animation: spin 1s linear infinite;
    }
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>CreateLex Bridge Dashboard</h1>
    
    <div class="status success">
      ✅ Bridge authentication successful
    </div>

    <!-- Subscription Status -->
    <div class="subscription-status">
      <div>
        <strong>Subscription Status:</strong>
        <span id="subscriptionText">Checking...</span>
      </div>
      <span id="subscriptionBadge" class="subscription-badge">Loading...</span>
    </div>

    <div id="subscriptionWarning" class="status warning" style="display: none;">
      ⚠️ <strong>No Active Subscription</strong><br>
      MCP Server features require an active subscription. 
      <a href="https://createlex.com/subscription" target="_blank">Upgrade your plan</a> to enable MCP functionality.
    </div>

    <div class="status info">
      📡 This is a simplified dashboard for the CreateLex Bridge. 
      For full functionality, please visit <a href="https://createlex.com/dashboard" target="_blank">createlex.com/dashboard</a>
    </div>

    <div>
      <button id="startMcpBtn" onclick="startMCP()" disabled>
        <span id="startMcpBtnText">Start MCP Server</span>
        <span id="startMcpBtnSpinner" style="display: none;">
          <svg class="spinner" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" stroke-opacity="0.25"></circle>
            <path fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Starting MCP Server...
        </span>
      </button>
      <button id="stopMcpBtn" onclick="stopMCP()" disabled>
        <span id="stopMcpBtnText">Stop MCP Server</span>
        <span id="stopMcpBtnSpinner" style="display: none;">
          <svg class="spinner" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" stroke-opacity="0.25"></circle>
            <path fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Stopping MCP Server...
        </span>
      </button>
      <button onclick="checkStatus()">Check Status</button>
      <button onclick="refreshSubscription()">Refresh Subscription</button>
      <button onclick="logout()">Logout</button>
    </div>

    <div id="status"></div>
  </div>

  <script>
    const isElectron = typeof window !== 'undefined' && window.electronAPI;
    const statusDiv = document.getElementById('status');
    let hasActiveSubscription = false;

    // Check subscription status on load
    async function checkSubscription() {
      if (!isElectron) return;

      try {
        const result = await window.electronAPI.checkSubscription();
        const subscriptionText = document.getElementById('subscriptionText');
        const subscriptionBadge = document.getElementById('subscriptionBadge');
        const subscriptionWarning = document.getElementById('subscriptionWarning');
        const startBtn = document.getElementById('startMcpBtn');
        const stopBtn = document.getElementById('stopMcpBtn');
        
        if (result.success && result.valid) {
          hasActiveSubscription = true;
          subscriptionText.textContent = 'Active subscription detected';
          subscriptionBadge.textContent = 'PRO';
          subscriptionBadge.className = 'subscription-badge subscription-active';
          subscriptionWarning.style.display = 'none';
          
          // Enable MCP buttons
          startBtn.disabled = false;
          stopBtn.disabled = false;
        } else {
          hasActiveSubscription = false;
          subscriptionText.textContent = 'No active subscription';
          subscriptionBadge.textContent = 'FREE';
          subscriptionBadge.className = 'subscription-badge subscription-inactive';
          subscriptionWarning.style.display = 'block';
          
          // Disable MCP buttons
          startBtn.disabled = true;
          stopBtn.disabled = true;
        }
      } catch (err) {
        console.error('Error checking subscription:', err);
        document.getElementById('subscriptionText').textContent = 'Unable to verify subscription';
        document.getElementById('subscriptionBadge').textContent = 'ERROR';
      }
    }

    async function startMCP() {
      if (!isElectron) {
        statusDiv.innerHTML = '<div class="status info">MCP controls only available in desktop app</div>';
        return;
      }

      if (!hasActiveSubscription) {
        statusDiv.innerHTML = '<div class="status warning">⚠️ MCP Server requires an active subscription</div>';
        return;
      }

      // Show loading spinner
      const startBtn = document.getElementById('startMcpBtn');
      const startBtnText = document.getElementById('startMcpBtnText');
      const startBtnSpinner = document.getElementById('startMcpBtnSpinner');
      
      startBtn.disabled = true;
      startBtnText.style.display = 'none';
      startBtnSpinner.style.display = 'inline-flex';

      try {
        const result = await window.electronAPI.startMcp();
        statusDiv.innerHTML = `<div class="status ${result.success ? 'success' : 'error'}">
          ${result.success ? '✅ MCP Server started' : '❌ Failed to start MCP Server: ' + result.error}
        </div>`;
        if (result.success) {
          checkStatus(); // Update status display
        }
      } catch (err) {
        statusDiv.innerHTML = '<div class="status error">❌ Error: ' + err.message + '</div>';
      } finally {
        // Hide loading spinner
        startBtnText.style.display = 'inline';
        startBtnSpinner.style.display = 'none';
        startBtn.disabled = false;
      }
    }

    async function stopMCP() {
      if (!isElectron) return;

      // Show loading spinner
      const stopBtn = document.getElementById('stopMcpBtn');
      const stopBtnText = document.getElementById('stopMcpBtnText');
      const stopBtnSpinner = document.getElementById('stopMcpBtnSpinner');
      
      stopBtn.disabled = true;
      stopBtnText.style.display = 'none';
      stopBtnSpinner.style.display = 'inline-flex';

      try {
        const result = await window.electronAPI.stopMcp();
        statusDiv.innerHTML = `<div class="status ${result.success ? 'success' : 'error'}">
          ${result.success ? '✅ MCP Server stopped' : '❌ Failed to stop MCP Server: ' + result.error}
        </div>`;
        if (result.success) {
          checkStatus(); // Update status display
        }
      } catch (err) {
        statusDiv.innerHTML = '<div class="status error">❌ Error: ' + err.message + '</div>';
      } finally {
        // Hide loading spinner
        stopBtnText.style.display = 'inline';
        stopBtnSpinner.style.display = 'none';
        stopBtn.disabled = false;
      }
    }

    async function checkStatus() {
      if (!isElectron) return;

      try {
        // First, re-check subscription status to ensure it's up to date
        await checkSubscription();
        
        // Then check MCP server status
        const status = await window.electronAPI.getMcpStatus();
        const isRunning = status.isRunning || status.running;
        statusDiv.innerHTML = `<div class="status info">
          📊 MCP Server Status: ${isRunning ? '🟢 Running' : '🔴 Stopped'}<br/>
          Port: ${status.port || 'N/A'}<br/>
          PID: ${status.pythonProcessId || status.pid || 'N/A'}
        </div>`;
        
        // Update button states based on server status AND subscription
        const startBtn = document.getElementById('startMcpBtn');
        const stopBtn = document.getElementById('stopMcpBtn');
        
        if (hasActiveSubscription) {
          // User has subscription - enable/disable based on server status
          startBtn.disabled = isRunning;
          stopBtn.disabled = !isRunning;
        } else {
          // User has no subscription - disable both buttons
          startBtn.disabled = true;
          stopBtn.disabled = true;
        }
      } catch (err) {
        statusDiv.innerHTML = '<div class="status error">❌ Error: ' + err.message + '</div>';
      }
    }

    async function refreshSubscription() {
      if (!isElectron) return;
      
      try {
        statusDiv.innerHTML = '<div class="status info">🔄 Refreshing subscription status...</div>';
        await checkSubscription();
        await checkStatus(); // Also update MCP status
        statusDiv.innerHTML = '<div class="status success">✅ Subscription status refreshed</div>';
      } catch (err) {
        statusDiv.innerHTML = '<div class="status error">❌ Error refreshing subscription: ' + err.message + '</div>';
      }
    }

    async function logout() {
      if (!isElectron) return;

      if (confirm('Are you sure you want to logout? This will automatically stop the MCP server.')) {
        try {
          statusDiv.innerHTML = '<div class="status info">🔄 Logging out and stopping MCP server...</div>';
          await window.electronAPI.logout();
          statusDiv.innerHTML = '<div class="status success">✅ Logged out successfully. MCP server has been stopped.</div>';
        } catch (err) {
          statusDiv.innerHTML = '<div class="status error">❌ Error: ' + err.message + '</div>';
        }
      }
    }

    // Initialize on load
    if (isElectron) {
      checkSubscription().then(() => {
        checkStatus();
      });
      
      // Set up event listeners for subscription changes
      window.electronAPI.onSubscriptionInvalid((data) => {
        hasActiveSubscription = false;
        checkSubscription();
        statusDiv.innerHTML = `<div class="status error">❌ ${data.message}</div>`;
      });

      window.electronAPI.onSubscriptionWarning((data) => {
        statusDiv.innerHTML = `<div class="status warning">⚠️ ${data.message}</div>`;
      });
    }
  </script>
</body>
</html>
      