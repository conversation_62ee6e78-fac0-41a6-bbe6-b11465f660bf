// Script to add tokens to a user's account
require('dotenv').config();
const tokenPurchaseService = require('./src/services/tokenPurchaseService');

// User ID and token amount
const userId = '459f3de9-f399-4109-9dad-c39e885953c2';
const tokens = 100000;
const transactionId = `manual-${Date.now()}`;

async function addTokens() {
  try {
    console.log(`Adding ${tokens} tokens to user ${userId}`);

    // Add tokens to user's account
    const result = await tokenPurchaseService.addTokensToUser(userId, tokens, transactionId);

    console.log('Result:', JSON.stringify(result, null, 2));
    console.log(`Successfully added ${tokens} tokens to user ${userId}`);

    process.exit(0);
  } catch (error) {
    console.error('Error adding tokens:', error);
    process.exit(1);
  }
}

// Run the function
addTokens();
