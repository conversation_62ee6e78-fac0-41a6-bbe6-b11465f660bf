const { PythonShell } = require('python-shell');
const path = require('path');
const { EventEmitter } = require('events');
const fs = require('fs');

class MCPServer extends EventEmitter {
  constructor(options = {}) {
    super();
    this.port = options.port || 9877;
    this.pythonProcess = null;
  }

  start() {
    if (this.pythonProcess) {
      console.log('MCP server already running');
      return Promise.resolve();
    }

    let scriptPath;
    const bundledPath = path.join(__dirname, '..', 'python', 'mcp_server_protected.py');
    if (fs.existsSync(bundledPath)) {
      scriptPath = bundledPath;
    } else {
      // Fallback to new or legacy source directory for dev
      const newSourcePath = path.resolve(__dirname, '..', '..', '..', 'mcp-server-createlexgenai', 'mcp_server_protected.py');
      const legacySourcePath = path.resolve(__dirname, '..', '..', '..', 'UnrealGenAISupport_with_server', 'server', 'mcp_server_protected.py');
      if (fs.existsSync(newSourcePath)) {
        scriptPath = newSourcePath;
      } else {
        scriptPath = legacySourcePath;
      }
    }

    console.log('Starting MCP server with script:', scriptPath);

    const pythonOptions = {
      mode: 'text',
      pythonOptions: ['-u'], // unbuffered stdout
      scriptPath: path.dirname(scriptPath),
      args: [this.port.toString()],
      env: {
        ...process.env,
        AUTH_TOKEN: process.env.AUTH_TOKEN || '',
        PYTHONIOENCODING: 'utf-8',
        DEV_MODE: 'true',
        NODE_ENV: 'development',
      },
    };

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('MCP server start timeout after 30 seconds'));
      }, 30000);

      try {
        this.pythonProcess = new PythonShell(path.basename(scriptPath), pythonOptions);
        this.pythonProcess.on('message', (message) => {
          if (message.startsWith('MCP_READY')) {
            clearTimeout(timeout);
            this.emit('ready');
            resolve();
          }
          this.emit('message', message);
        });
        this.pythonProcess.on('close', (code) => {
          clearTimeout(timeout);
          console.log(`[MCP-Python] Process closed with code: ${code}`);
          this.emit('close', code);
          this.pythonProcess = null;
          if (code !== 0) {
            reject(new Error(`Python process exited with code ${code}. Check Python installation and dependencies.`));
          }
        });
        this.pythonProcess.on('error', (err) => {
          clearTimeout(timeout);
          console.error('[MCP-Python] Process error:', err);
          this.emit('error', err);
          reject(err);
        });
        this.pythonProcess.on('stderr', (stderr) => {
          console.error('[MCP-Python]', stderr);
        });
        this.pythonProcess.on('stdout', (stdout) => {
          console.log('[MCP-Python]', stdout);
        });
      } catch (err) {
        clearTimeout(timeout);
        reject(err);
      }
    });
  }

  stop() {
    if (this.pythonProcess) {
      this.pythonProcess.terminate();
      this.pythonProcess = null;
    }
  }

  getStatus() {
    return {
      isRunning: !!this.pythonProcess,
      port: this.port,
    };
  }
}

module.exports = { MCPServer }; 