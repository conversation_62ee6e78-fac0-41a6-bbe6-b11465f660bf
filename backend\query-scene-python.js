const McpBridge = require('./src/services/mcpBridge');

async function querySceneWithPython() {
  const bridge = McpBridge.getInstance();
  
  console.log('Sending Python script to query scene...');
  
  const pythonScript = `
import unreal

# Get all actors in the current level
actors = unreal.EditorLevelLibrary.get_all_level_actors()

# Format the result
result = {
    "success": True,
    "actors": []
}

for actor in actors:
    actor_info = {
        "name": actor.get_actor_label(),
        "class": actor.get_class().get_name(),
        "location": [actor.get_actor_location().x, actor.get_actor_location().y, actor.get_actor_location().z],
        "rotation": [actor.get_actor_rotation().roll, actor.get_actor_rotation().pitch, actor.get_actor_rotation().yaw],
        "scale": [actor.get_actor_scale3d().x, actor.get_actor_scale3d().y, actor.get_actor_scale3d().z]
    }
    result["actors"].append(actor_info)

print(f"Found {len(result['actors'])} actors in the level")
`;
  
  bridge.sendCommand({
    command: 'execute_python',
    params: {
      script: pythonScript
    }
  }, (err, res) => {
    if (err) {
      console.error('Error executing Python script:', err);
    } else {
      console.log('Python script result:');
      console.log(JSON.stringify(res, null, 2));
    }
    
    // Exit after 2 seconds to allow any async operations to complete
    setTimeout(() => process.exit(), 2000);
  });
}

querySceneWithPython();
