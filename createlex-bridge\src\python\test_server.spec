# -*- mode: python ; coding: utf-8 -*-
import sys
sys.setrecursionlimit(5000)

a = Analysis(
    ['test_dist/obfuscated/mcp_server_test.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('test_dist/obfuscated/pyarmor_runtime_000000', 'pyarmor_runtime_000000'),
    ],
    hiddenimports=[
        'fastmcp',
        'requests',
        'json',
        'os',
        'sys',
        'time',
        'pathlib',
        'asyncio',
        'urllib.parse',
        'urllib.request'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='unrealgenai_mcp_server_test',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
) 