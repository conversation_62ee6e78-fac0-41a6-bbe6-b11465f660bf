const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');
const path = require('path');
const fs = require('fs');

// Always run in development mode
const dev = true;
process.env.NODE_ENV = 'development';
console.log('Running in development mode');

// Configure Next.js app
const app = next({
  dev
});

const handle = app.getRequestHandler();
const PORT = process.env.PORT || 3000;

app.prepare()
  .then(() => {
    console.log('Next.js app prepared successfully');

    createServer((req, res) => {
      // Add dynamic=true query parameter to force dynamic rendering
      const parsedUrl = parse(req.url, true);
      parsedUrl.query.dynamic = 'true';

      // Handle the request
      handle(req, res, parsedUrl);
    }).listen(PORT, (err) => {
      if (err) throw err;
      console.log(`> Ready on http://localhost:${PORT}`);
    });
  })
  .catch(err => {
    console.error('Error preparing Next.js app:', err);
    process.exit(1);
  });
