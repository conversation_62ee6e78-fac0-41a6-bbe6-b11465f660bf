const fs = require('fs-extra');
const path = require('path');
const child_process = require('child_process');

(async () => {
  try {
    const rootDir = path.resolve(__dirname, '..', '..'); // workspace root
    // Primary location for MCP server source (after repo restructure)
    const newSourceDir = path.join(rootDir, 'mcp-server-createlexgenai');
    // Legacy location kept for backward compatibility
    const legacySourceDir = path.join(rootDir, 'CreatelexGenAI_with_server', 'server');

    // Determine which source directory actually exists
    let sourceDir;
    if (fs.existsSync(newSourceDir)) {
      sourceDir = newSourceDir;
    } else if (fs.existsSync(legacySourceDir)) {
      sourceDir = legacySourceDir;
    } else {
      throw new Error(`Neither new (${newSourceDir}) nor legacy (${legacySourceDir}) MCP server source directories were found.`);
    }
    const destDir = path.join(__dirname, '..', 'src', 'python');

    console.log('🔄 Copying MCP server files…');
    
    // Check if local files already exist and are newer/modified
    const localProtectedFile = path.join(destDir, 'mcp_server_protected.py');
    const localValidatorFile = path.join(destDir, 'subscription_validator.py');
    
    let preserveLocal = false;
    if (await fs.pathExists(localProtectedFile) && await fs.pathExists(localValidatorFile)) {
      // Check if local files have debug logging (indicating they're modified)
      const protectedContent = await fs.readFile(localProtectedFile, 'utf8');
      const validatorContent = await fs.readFile(localValidatorFile, 'utf8');
      
      if (protectedContent.includes('[DEBUG]') || validatorContent.includes('[DEBUG]')) {
        console.log('🔧 Local debug versions detected - preserving local changes');
        preserveLocal = true;
      }
    }
    
    if (!preserveLocal) {
      // Normal copy process
      await fs.remove(destDir);
      await fs.copy(sourceDir, destDir);
      console.log('✅ MCP server copied from', sourceDir, 'to', destDir);
    } else {
      // Preserve local changes, only copy missing files
      console.log('✅ Using existing local MCP server files with debug logging');
      
      // Copy any missing files from source that don't exist locally
      const sourceFiles = await fs.readdir(sourceDir);
      for (const file of sourceFiles) {
        const sourcePath = path.join(sourceDir, file);
        const destPath = path.join(destDir, file);
        
        if (!(await fs.pathExists(destPath))) {
          console.log(`📄 Copying missing file: ${file}`);
          await fs.copy(sourcePath, destPath);
        }
      }
    }

    // Ensure mcp_server_protected.py is executable on *nix
    if (process.platform !== 'win32') {
      child_process.execSync(`chmod +x ${path.join(destDir, 'mcp_server_protected.py')}`);
    }
  } catch (err) {
    console.error('❌ Failed to copy MCP server', err);
    process.exit(1);
  }
})(); 