'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DollarSign } from 'lucide-react';
import { fetchWithAuth, getApiUrl } from '@/lib/auth-utils';
import { Skeleton } from '@/components/ui/skeleton';

export default function BillingStatsCard() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalRevenue, setTotalRevenue] = useState('0.00');

  useEffect(() => {
    const fetchData = async () => {
      try {
        const apiUrl = getApiUrl();
        const requestUrl = `${apiUrl}/api/admin/token-usage/statistics?period=month`;

        const response = await fetchWithAuth(requestUrl);

        if (!response.ok) {
          throw new Error(`Failed to fetch token usage statistics: ${response.status}`);
        }

        const data = await response.json();
        setTotalRevenue(data?.revenue?.totalRevenue || '0.00');
      } catch (error) {
        console.error('Error fetching revenue data:', error);
        setError('Failed to load revenue data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
        <DollarSign className="h-4 w-4 text-gray-500" />
      </CardHeader>
      <CardContent>
        {loading ? (
          <Skeleton className="h-8 w-24" />
        ) : error ? (
          <div className="text-sm text-red-500">Error loading data</div>
        ) : (
          <>
            <div className="text-2xl font-bold">${totalRevenue}</div>
            <p className="text-xs text-gray-500 mt-1">
              Total revenue this month
            </p>
          </>
        )}
      </CardContent>
    </Card>
  );
}
