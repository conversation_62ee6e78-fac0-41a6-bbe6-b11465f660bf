#!/bin/bash

# Exit on error
set -e

# Configuration
APP_NAME="createlex-frontend"
BUILD_DIR=".next"
STANDALONE_DIR=".next/standalone"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Starting deployment process for ${APP_NAME}...${NC}"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo -e "${RED}Node.js is not installed. Please install Node.js and try again.${NC}"
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo -e "${RED}npm is not installed. Please install npm and try again.${NC}"
    exit 1
fi

# Install dependencies
echo -e "${YELLOW}Installing dependencies...${NC}"
npm install

# Install PM2 globally if not already installed
if ! command -v pm2 &> /dev/null; then
    echo -e "${YELLOW}Installing PM2 globally...${NC}"
    npm install -g pm2
fi

# Install dotenv for environment variable loading
echo -e "${YELLOW}Installing dotenv...${NC}"
npm install --save dotenv

# Build the application
echo -e "${YELLOW}Building the application...${NC}"
NODE_ENV=production npm run build

# Check if build was successful
if [ ! -d "$BUILD_DIR" ]; then
    echo -e "${RED}Build failed. The $BUILD_DIR directory does not exist.${NC}"
    exit 1
fi

# Check if standalone build was created
if [ -d "$STANDALONE_DIR" ]; then
    echo -e "${GREEN}Standalone build detected.${NC}"
else
    echo -e "${YELLOW}No standalone build detected. Using regular build.${NC}"
fi

# Stop the existing PM2 process if it exists
echo -e "${YELLOW}Stopping existing PM2 process if it exists...${NC}"
pm2 stop $APP_NAME 2>/dev/null || true
pm2 delete $APP_NAME 2>/dev/null || true

# Start the application with PM2
echo -e "${YELLOW}Starting the application with PM2...${NC}"
pm2 start ecosystem.config.js --env production

# Save the PM2 process list
echo -e "${YELLOW}Saving the PM2 process list...${NC}"
pm2 save

# Setup PM2 to start on system boot
echo -e "${YELLOW}Setting up PM2 to start on system boot...${NC}"
pm2 startup

echo -e "${GREEN}Deployment completed successfully!${NC}"
echo -e "${GREEN}The application is now running at http://localhost:3000${NC}"
echo -e "${YELLOW}To check the application logs, run: pm2 logs $APP_NAME${NC}"
echo -e "${YELLOW}To monitor the application, run: pm2 monit${NC}"
