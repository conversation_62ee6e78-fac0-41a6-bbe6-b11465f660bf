#!/usr/bin/env python3
"""
Build script for creating protected MCP server distribution
Combines obfuscation, compilation, and subscription validation
"""

import os
import shutil
import subprocess
import sys
from pathlib import Path

def install_requirements():
    """Install required build tools"""
    requirements = [
        'pyarmor',
        'pyinstaller',
        'nuitka',
        'requests'
    ]
    
    for req in requirements:
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', req], check=True)
            print(f"✅ Installed {req}")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {req}")

def create_protected_version():
    """Create protected version with subscription validation"""
    
    # 1. Create protected source with subscription checks
    protected_source = """
# Protected MCP Server with Subscription Validation
import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from subscription_validator import check_subscription

# Validate subscription on startup
if not check_subscription():
    print("Invalid subscription. Exiting.")
    sys.exit(1)

# Import original server after validation
from mcp_server_stdio import *

if __name__ == "__main__":
    print("Protected UnrealGenAI MCP Server")
    print("Subscription validated")
    main()
"""
    
    with open('mcp_server_protected.py', 'w', encoding='utf-8') as f:
        f.write(protected_source)
    
    print("✅ Created protected source")

def obfuscate_code():
    """Obfuscate the code using PyArmor"""
    try:
        # Clean previous builds
        if os.path.exists('dist'):
            shutil.rmtree('dist')
        
        # Obfuscate main files
        subprocess.run([
            'pyarmor', 'gen', 
            '--output', 'dist/obfuscated',
            'mcp_server_protected.py',
            'subscription_validator.py',
            'mcp_server_stdio.py'
        ], check=True)
        
        print("✅ Code obfuscated successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Obfuscation failed")
        return False

def compile_to_executable():
    """Compile to standalone executable"""
    try:
        # Create spec file for PyInstaller
        spec_content = """
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['mcp_server_protected.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=['fastmcp', 'requests', 'websockets'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='unrealgenai_mcp_server',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
"""
        
        with open('server.spec', 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        # Build executable
        subprocess.run([
            'pyinstaller', 
            '--clean',
            '--noconfirm',
            'server.spec'
        ], check=True)
        
        print("✅ Executable compiled successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Compilation failed")
        return False

def create_installer():
    """Create installer package"""
    installer_script = """
import os
import shutil
import json
from pathlib import Path

def install_unrealgenai_mcp():
    print("Installing UnrealGenAI MCP Server...")
    
    # Create installation directory
    install_dir = Path.home() / ".unrealgenai" / "mcp_server"
    install_dir.mkdir(parents=True, exist_ok=True)
    
    # Copy executable
    exe_path = install_dir / "unrealgenai_mcp_server.exe"
    shutil.copy("unrealgenai_mcp_server.exe", exe_path)
    
    # Create Claude Desktop config
    claude_config = {
        "mcpServers": {
            "unrealgenai": {
                "command": str(exe_path),
                "args": []
            }
        }
    }
    
    config_dir = Path.home() / "AppData" / "Roaming" / "Claude"
    config_dir.mkdir(parents=True, exist_ok=True)
    
    with open(config_dir / "claude_desktop_config.json", "w") as f:
        json.dump(claude_config, f, indent=2)
    
    print("Installation complete!")
    print(f"Installed to: {install_dir}")
    print("Please restart Claude Desktop")

if __name__ == "__main__":
    install_unrealgenai_mcp()
"""
    
    with open('dist/install.py', 'w', encoding='utf-8') as f:
        f.write(installer_script)
    
    print("✅ Installer created")

def build_all():
    """Build complete protected distribution"""
    print("🔨 Building Protected UnrealGenAI MCP Server")
    print("=" * 50)
    
    # Step 1: Install requirements
    print("\n1️⃣ Installing build requirements...")
    install_requirements()
    
    # Step 2: Create protected version
    print("\n2️⃣ Creating protected version...")
    create_protected_version()
    
    # Step 3: Obfuscate (optional)
    print("\n3️⃣ Obfuscating code...")
    obfuscate_success = obfuscate_code()
    
    # Step 4: Compile to executable
    print("\n4️⃣ Compiling to executable...")
    compile_success = compile_to_executable()
    
    # Step 5: Create installer
    print("\n5️⃣ Creating installer...")
    create_installer()
    
    print("\n" + "=" * 50)
    if compile_success:
        print("✅ BUILD SUCCESSFUL!")
        print(f"📦 Executable: dist/unrealgenai_mcp_server.exe")
        print(f"💾 Installer: dist/install.py")
        print("\n🔒 Protection features:")
        print("  ✅ Subscription validation")
        print("  ✅ Compiled to executable")
        if obfuscate_success:
            print("  ✅ Code obfuscation")
        print("  ✅ Machine ID binding")
        print("  ✅ License server validation")
    else:
        print("❌ BUILD FAILED!")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(build_all()) 