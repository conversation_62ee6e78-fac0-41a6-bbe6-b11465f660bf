'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function DebugApiPage() {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const addResult = (test: string, result: any) => {
    setTestResults(prev => [...prev, { test, result, timestamp: new Date().toISOString() }]);
  };

  const testApiRoutes = async () => {
    setLoading(true);
    setTestResults([]);

    try {
      // Test 1: Test admin route
      console.log('🧪 Testing /api/test-admin');
      try {
        const response1 = await fetch('/api/test-admin');
        const data1 = await response1.json();
        addResult('Test Admin Route', {
          status: response1.status,
          data: data1
        });
      } catch (error) {
        addResult('Test Admin Route', {
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }

      // Test 2: Test API keys route
      console.log('🧪 Testing /api/admin/api-keys');
      try {
        const response2 = await fetch('/api/admin/api-keys');
        const data2 = await response2.json();
        addResult('API Keys Route', {
          status: response2.status,
          data: data2
        });
      } catch (error) {
        addResult('API Keys Route', {
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }

      // Test 3: Check environment variables
      addResult('Environment Check', {
        NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
        NODE_ENV: process.env.NODE_ENV,
        NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Not set'
      });

      // Test 4: Check current URL
      addResult('Current URL Info', {
        origin: window.location.origin,
        pathname: window.location.pathname,
        href: window.location.href
      });

    } catch (error) {
      addResult('General Error', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>🔧 API Debug Tool</CardTitle>
        </CardHeader>
        <CardContent>
          <Button onClick={testApiRoutes} disabled={loading}>
            {loading ? 'Testing...' : 'Run API Tests'}
          </Button>
        </CardContent>
      </Card>

      {testResults.length > 0 && (
        <div className="space-y-4">
          {testResults.map((result, index) => (
            <Card key={index}>
              <CardHeader>
                <CardTitle className="text-sm">{result.test}</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto">
                  {JSON.stringify(result.result, null, 2)}
                </pre>
                <div className="text-xs text-gray-500 mt-2">
                  {result.timestamp}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle>📋 Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <p><strong>Expected Results:</strong></p>
          <ul className="list-disc list-inside space-y-1">
            <li>Test Admin Route: Should return 200 with user info</li>
            <li>API Keys Route: Should return 200 or proper error with admin check</li>
            <li>Environment Check: Should show NEXT_PUBLIC_API_URL as empty or relative</li>
            <li>Current URL: Should show createlex.com domain</li>
          </ul>
          <p className="mt-4"><strong>Access this page at:</strong> <code>/debug-api</code></p>
        </CardContent>
      </Card>
    </div>
  );
}
