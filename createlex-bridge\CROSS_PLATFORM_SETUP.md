# 🌍 Cross-Platform Setup Guide

This guide helps you set up the CreateLex Bridge on **macOS** and **Linux** systems.

## 📋 Prerequisites

### **All Platforms:**
- **Node.js** 16+ (recommended: 18+)
- **npm** or **yarn**
- **Git**

### **Python Requirements:**
- **Python 3.8+** (recommended: 3.10+)
- **pip** (Python package manager)
- **PyInstaller** (for building protected executables)

---

## 🍎 macOS Setup

### **1. Install Prerequisites**

```bash
# Install Homebrew (if not already installed)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install Node.js and Python
brew install node python@3.11

# Verify installations
node --version
python3 --version
pip3 --version
```

### **2. Install PyInstaller**

```bash
# Install PyInstaller for building executables
pip3 install pyinstaller

# Verify installation
pyinstaller --version
```

### **3. <PERSON><PERSON> and <PERSON>up**

```bash
# Clone the repository
git clone https://github.com/AlexKissiJr/AiWebplatform.git
cd AiWebplatform/createlex-bridge

# Install dependencies
npm install

# Test cross-platform compatibility
npm run test-cross-platform
```

### **4. Build for macOS**

```bash
# Build protected macOS version (recommended)
npm run build-mac-protected

# Alternative: Build simple version (development only)
npm run build-mac-simple
```

### **5. macOS-Specific Notes**

- **Security:** macOS may block the executable. Go to **System Preferences > Security & Privacy** and allow the app
- **Executable Name:** `mcp_server_mac`
- **Python Command:** Uses `python3` by default
- **Output:** `.dmg` file in `dist/` directory

---

## 🐧 Linux Setup

### **1. Install Prerequisites**

#### **Ubuntu/Debian:**
```bash
# Update package list
sudo apt update

# Install Node.js and Python
sudo apt install nodejs npm python3 python3-pip git

# Verify installations
node --version
python3 --version
pip3 --version
```

#### **CentOS/RHEL/Fedora:**
```bash
# Install Node.js and Python
sudo dnf install nodejs npm python3 python3-pip git

# Or for older versions:
# sudo yum install nodejs npm python3 python3-pip git
```

#### **Arch Linux:**
```bash
# Install Node.js and Python
sudo pacman -S nodejs npm python python-pip git
```

### **2. Install PyInstaller**

```bash
# Install PyInstaller for building executables
pip3 install pyinstaller

# Verify installation
pyinstaller --version
```

### **3. Clone and Setup**

```bash
# Clone the repository
git clone https://github.com/AlexKissiJr/AiWebplatform.git
cd AiWebplatform/createlex-bridge

# Install dependencies
npm install

# Test cross-platform compatibility
npm run test-cross-platform
```

### **4. Build for Linux**

```bash
# Build protected Linux version (recommended)
npm run build-linux-protected

# Alternative: Build simple version (development only)
npm run build-linux-simple
```

### **5. Linux-Specific Notes**

- **Permissions:** You may need to make the executable file executable: `chmod +x mcp_server_linux`
- **Executable Name:** `mcp_server_linux`
- **Python Command:** Uses `python3` by default
- **Output:** `.AppImage` file in `dist/` directory

---

## 🔧 Troubleshooting

### **Common Issues:**

#### **1. Python Not Found**
```bash
# Check available Python commands
which python
which python3
which py

# Install Python if missing
# macOS: brew install python@3.11
# Ubuntu: sudo apt install python3
```

#### **2. PyInstaller Not Found**
```bash
# Install PyInstaller
pip3 install pyinstaller

# Or try:
python3 -m pip install pyinstaller
```

#### **3. Permission Denied (Linux)**
```bash
# Make executable file executable
chmod +x dist/mcp_server_linux

# Or for the source file:
chmod +x src/python-protected/mcp_server_linux
```

#### **4. Security Warning (macOS)**
```bash
# Allow the app in System Preferences
# Or use command line:
sudo spctl --master-disable  # Disable Gatekeeper (not recommended)
sudo spctl --master-enable   # Re-enable Gatekeeper
```

#### **5. Build Fails**
```bash
# Clean and rebuild
rm -rf node_modules dist build
npm install
npm run test-cross-platform
npm run build-mac-protected  # or build-linux-protected
```

---

## 🧪 Testing Your Setup

### **Run the Cross-Platform Test:**
```bash
npm run test-cross-platform
```

**Expected Output:**
```
✅ Platform darwin/linux is supported
✅ Expected executable name: mcp_server_mac/mcp_server_linux
✅ Using Python command: python3
✅ PyInstaller available: 6.x.x
✅ System appears ready for cross-platform build!
```

### **Test the Build:**
```bash
# Test MCP executable build only
npm run build:mcp-exe

# Check if executable was created
ls -la src/python-protected/
```

---

## 📦 Build Outputs

### **macOS:**
- **Installer:** `dist/CreateLex Bridge-1.0.0.dmg`
- **Executable:** `src/python-protected/mcp_server_mac`
- **Size:** ~400 MB

### **Linux:**
- **Installer:** `dist/CreateLex Bridge-1.0.0.AppImage`
- **Executable:** `src/python-protected/mcp_server_linux`
- **Size:** ~350 MB

---

## 🚀 Running the Application

### **Development Mode:**
```bash
# macOS
npm run dev:mac

# Linux (same as macOS)
npm run dev:mac
```

### **Production Mode:**
```bash
# Install the built package
# macOS: Open the .dmg file and drag to Applications
# Linux: Make AppImage executable and run: ./CreateLex-Bridge-Linux.AppImage
```

---

## 📝 Platform Differences

| Feature | Windows | macOS | Linux |
|---------|---------|-------|-------|
| **Python Command** | `python` | `python3` | `python3` |
| **Executable Name** | `mcp_server.exe` | `mcp_server_mac` | `mcp_server_linux` |
| **Package Format** | `.exe` (NSIS) | `.dmg` | `.AppImage` |
| **Security** | Windows Defender | Gatekeeper | File permissions |

---

## 🆘 Getting Help

If you encounter issues:

1. **Run the test:** `npm run test-cross-platform`
2. **Check the logs:** Look for error messages in the terminal
3. **Verify dependencies:** Ensure Python and PyInstaller are installed
4. **File an issue:** [GitHub Issues](https://github.com/AlexKissiJr/AiWebplatform/issues)

---

**Happy Building! 🎉** 