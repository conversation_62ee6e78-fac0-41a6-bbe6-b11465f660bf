# macOS MCP Server Fix Guide 🍎

## 🎯 Problem Identified

The macOS MCP server (`mcp_server_mac`) fails to start due to missing FastMCP dependencies that weren't properly bundled by PyInstaller. This is a common issue when building cross-platform Python executables.

## 🔧 Solution Steps

### Step 1: Test Current Dependencies
```bash
# Run the dependency test to see what's missing
npm run test-macos-deps
```

### Step 2: Install Missing Python Modules
```bash
# Install all required modules
pip3 install fastmcp aiohttp requests websockets uvicorn flask pydantic python-dotenv

# Or install them one by one if needed:
pip3 install fastmcp
pip3 install aiohttp
pip3 install requests
pip3 install websockets
pip3 install uvicorn
pip3 install flask
pip3 install pydantic
pip3 install python-dotenv
```

### Step 3: Rebuild MCP Server with Enhanced Dependencies
```bash
# Rebuild the MCP executable with comprehensive imports
npm run build:mcp-exe
```

### Step 4: Test the Fixed Executable
```bash
# Test the rebuilt MCP server
npm run test-macos-mcp

# Or test manually
./src/python-protected/mcp_server_mac
```

### Step 5: Verify Claude Desktop Integration
```bash
# Update your Claude Desktop config
nano ~/.config/claude-desktop/claude_desktop_config.json
```

Add this configuration:
```json
{
  "mcpServers": {
    "createlex-bridge-mac": {
      "command": "/path/to/your/mcp_server_mac",
      "args": [],
      "env": {
        "NODE_ENV": "production"
      }
    }
  }
}
```

## 🔍 What We Fixed

### Enhanced PyInstaller Configuration
The build script now includes:

1. **Complete FastMCP Module Tree**:
   - `fastmcp.core`
   - `fastmcp.server`
   - `fastmcp.tools`
   - `fastmcp.types`
   - `fastmcp.utilities`
   - `fastmcp.exceptions`

2. **MCP Protocol Dependencies**:
   - `mcp.server.stdio`
   - `mcp.server.session`
   - `mcp.types`

3. **Comprehensive Async Support**:
   - All `asyncio` submodules
   - `aiohttp` complete module tree
   - WebSocket protocol handlers

4. **macOS-Specific Optimizations**:
   - ARM64 architecture targeting
   - UPX compression disabled for compatibility
   - macOS Foundation framework imports

## 🧪 Testing Commands

### Quick Test
```bash
# Test if MCP server starts (should show subscription message)
./src/python-protected/mcp_server_mac
```

### Comprehensive Test
```bash
# Run full dependency and functionality test
npm run test-macos-deps
```

### Integration Test
```bash
# Test with actual MCP protocol
echo '{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {}, "clientInfo": {"name": "test", "version": "1.0.0"}}}' | ./src/python-protected/mcp_server_mac
```

## 🎯 Expected Results

### ✅ Successful Startup
```
❌ Invalid or expired subscription!
Please contact support or renew your license.
Invalid subscription. Exiting.
```

This is **correct behavior** - it means the MCP server is working and the subscription validation is functioning.

### ❌ Import Errors (Fixed)
If you previously saw errors like:
```
ImportError: No module named 'fastmcp'
ModuleNotFoundError: No module named 'aiohttp'
```

These should now be resolved with the enhanced build configuration.

## 🔒 macOS Security Notes

### Allow Executable
macOS may block the executable. To allow it:

**Option 1: System Preferences**
1. Go to System Preferences > Security & Privacy
2. Click "Allow" when prompted about `mcp_server_mac`

**Option 2: Terminal Command**
```bash
xattr -d com.apple.quarantine ./src/python-protected/mcp_server_mac
```

**Option 3: Make Executable**
```bash
chmod +x ./src/python-protected/mcp_server_mac
```

## 🚀 Final Validation

Once everything is working:

1. ✅ MCP server starts without import errors
2. ✅ Shows subscription validation message
3. ✅ Claude Desktop recognizes the server
4. ✅ All CreateLex tools appear in Claude Desktop interface

## 🔄 Troubleshooting

### If Dependencies Still Missing
```bash
# Check Python environment
python3 --version
pip3 list | grep -E "(fastmcp|aiohttp|uvicorn)"

# Reinstall in user space if needed
pip3 install --user fastmcp aiohttp uvicorn websockets flask pydantic python-dotenv
```

### If Build Still Fails
```bash
# Clean rebuild
rm -rf build/mcp-exe
rm -rf src/python-protected/mcp_server_mac
npm run build:mcp-exe
```

### If Claude Desktop Doesn't Recognize
1. Check config file path: `~/.config/claude-desktop/claude_desktop_config.json`
2. Verify executable path is absolute
3. Restart Claude Desktop application
4. Check Claude Desktop logs for errors

## 🎉 Success Indicators

When everything works correctly:
- MCP server starts and shows subscription message ✅
- No import or module errors ✅
- Claude Desktop shows CreateLex tools in interface ✅
- All cross-platform builds working (Windows, Linux, macOS) ✅

The enhanced PyInstaller configuration should resolve all FastMCP dependency issues on macOS! 