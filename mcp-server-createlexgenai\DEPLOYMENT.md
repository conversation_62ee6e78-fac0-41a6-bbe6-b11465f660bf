# DigitalOcean Deployment Guide

This guide will help you deploy the Unreal Engine MCP Server to DigitalOcean or any other cloud provider.

## Prerequisites

- DigitalOcean account (or other cloud provider)
- Basic knowledge of Linux/Docker
- Unreal Engine project with the GenerativeAISupport plugin

## Step 1: Create a Droplet

1. **Log into DigitalOcean** and create a new Droplet
2. **Choose an image**: Ubuntu 22.04 LTS with <PERSON><PERSON> pre-installed
3. **Choose a plan**: Basic plan with at least 1GB RAM (2GB recommended)
4. **Choose a datacenter region**: Select the region closest to your Unreal Engine instance
5. **Add SSH keys** for secure access
6. **Create the Droplet**

## Step 2: Upload Server Files

### Option A: Using SCP
```bash
# From your local machine, upload the server folder
scp -r server/ root@your-droplet-ip:/opt/unreal-mcp-server/
```

### Option B: Using Git
```bash
# SSH into your droplet
ssh root@your-droplet-ip

# Clone your repository
git clone https://github.com/your-username/your-repo.git
cd your-repo/UnrealGenAISupport_with_server/server/
```

### Option C: Manual Upload
1. Use FileZilla, WinSCP, or similar tool
2. Upload the entire `server/` folder to `/opt/unreal-mcp-server/`

## Step 3: Configure Environment

```bash
# SSH into your droplet
ssh root@your-droplet-ip

# Navigate to the server directory
cd /opt/unreal-mcp-server/

# Copy environment template
cp env.example .env

# Edit configuration
nano .env
```

Update the `.env` file with your Unreal Engine details:
```bash
# Your Unreal Engine host (where UE is running)
UNREAL_HOST=your-unreal-engine-ip
UNREAL_PORT=9877

# MCP Server settings (usually don't need to change)
MCP_SERVER_HOST=0.0.0.0
MCP_SERVER_PORT=8000
```

## Step 4: Deploy the Server

```bash
# Make deploy script executable
chmod +x deploy.sh

# Deploy the server
./deploy.sh production
```

## Step 5: Configure Firewall

```bash
# Allow MCP server port
ufw allow 8000

# Allow Unreal Engine communication port
ufw allow 9877

# Enable firewall
ufw enable
```

## Step 6: Verify Deployment

```bash
# Check if container is running
docker ps

# Check logs
docker-compose logs -f

# Test server status
curl http://localhost:8000
```

## Step 7: Configure Unreal Engine

Update your Unreal Engine configuration to point to the deployed server:

### In DefaultEngine.ini:
```ini
[MCP]
mcpServers=(
    Name="CreateLex",
    Host="your-droplet-ip",
    Port=8000,
    AuthToken=""
)
```

### Or via Plugin Settings:
1. Open Unreal Engine
2. Go to Edit → Project Settings
3. Find "Generative AI Support" plugin settings
4. Set MCP Server Host to your droplet IP
5. Set MCP Server Port to 8000

## Step 8: Configure Claude Desktop

Update your Claude Desktop configuration:

```json
{
  "mcpServers": {
    "unreal-ai-support": {
      "command": "curl",
      "args": [
        "-X", "POST",
        "-H", "Content-Type: application/json",
        "-d", "@-",
        "http://your-droplet-ip:8000/mcp"
      ]
    }
  }
}
```

## Monitoring and Maintenance

### Check Server Status
```bash
# View running containers
docker ps

# View logs
docker-compose logs -f unreal-mcp-server

# Restart server
docker-compose restart

# Stop server
docker-compose down

# Update and restart
git pull
docker-compose down
docker-compose up --build -d
```

### Monitor Resources
```bash
# Check system resources
htop

# Check disk usage
df -h

# Check Docker stats
docker stats
```

### Backup Configuration
```bash
# Backup your .env file
cp .env .env.backup

# Backup logs (optional)
tar -czf logs-backup-$(date +%Y%m%d).tar.gz logs/
```

## Troubleshooting

### Server Won't Start
1. Check Docker logs: `docker-compose logs`
2. Verify environment variables: `cat .env`
3. Test port availability: `netstat -tlnp | grep 8000`

### Can't Connect to Unreal Engine
1. Verify Unreal Engine is running and listening on port 9877
2. Check firewall settings on both machines
3. Test connection: `telnet your-unreal-ip 9877`

### MCP Tools Not Working
1. Check server status tool: Use the `server_status` MCP tool
2. Verify Unreal Engine plugin is loaded
3. Check Unreal Engine logs for MCP-related messages

## Security Considerations

### Basic Security
```bash
# Update system packages
apt update && apt upgrade -y

# Configure automatic security updates
dpkg-reconfigure -plow unattended-upgrades

# Change default SSH port (optional)
nano /etc/ssh/sshd_config
# Change Port 22 to Port 2222
systemctl restart ssh
```

### SSL/TLS (Production)
For production deployments, consider:
1. Setting up a reverse proxy with Nginx
2. Using Let's Encrypt for SSL certificates
3. Implementing authentication tokens

### Firewall Rules
```bash
# Only allow necessary ports
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 8000
ufw allow 9877
ufw enable
```

## Scaling Considerations

### Multiple Unreal Instances
If you have multiple Unreal Engine instances:
1. Deploy multiple MCP server containers
2. Use different ports for each (8001, 8002, etc.)
3. Configure load balancing if needed

### High Availability
For production environments:
1. Use Docker Swarm or Kubernetes
2. Set up health checks and auto-restart
3. Implement monitoring with Prometheus/Grafana

## Cost Optimization

### DigitalOcean Droplet Sizing
- **Development**: 1GB RAM ($6/month)
- **Production**: 2GB RAM ($12/month)
- **High Load**: 4GB RAM ($24/month)

### Auto-scaling
Consider using DigitalOcean's managed Kubernetes for auto-scaling based on load.

## Support

If you encounter issues:
1. Check the server logs first
2. Verify network connectivity
3. Test with the included test script: `python test_server.py`
4. Review the main README.md for additional troubleshooting 