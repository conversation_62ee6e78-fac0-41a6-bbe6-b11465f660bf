{"name": "frontend", "version": "0.1.0", "private": true, "engines": {"node": "18.x"}, "scripts": {"dev": "node dev-server.js", "dev:next": "next dev", "build": "next build", "build:standalone": "next build", "export": "next build --config-file=next.config.bridge.js", "start": "NODE_ENV=production node server.js", "start:dev": "node dev-server.js", "start:next": "next start", "start:prod": "NODE_ENV=production node server.js", "deploy": "chmod +x deploy.sh && ./deploy.sh", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.10", "@ai-sdk/cohere": "^1.2.9", "@ai-sdk/deepseek": "^0.2.14", "@ai-sdk/google": "^1.2.12", "@ai-sdk/groq": "^1.2.8", "@ai-sdk/openai": "^1.3.16", "@ai-sdk/react": "^1.2.9", "@ai-sdk/xai": "^1.2.14", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.0.1", "@mui/material": "^7.0.2", "@neondatabase/serverless": "^1.0.0", "@radix-ui/react-accordion": "^1.2.7", "@radix-ui/react-avatar": "^1.1.6", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.10", "@radix-ui/react-scroll-area": "^1.2.5", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.11", "@radix-ui/react-tooltip": "^1.2.3", "@stripe/stripe-js": "^2.4.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.74.4", "@types/node": "^20.17.25", "@types/react": "^18.3.19", "@types/react-dom": "^18", "@vercel/otel": "^1.11.0", "ai": "^4.3.9", "autoprefixer": "^10.4.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.42.0", "fast-deep-equal": "^3.1.3", "framer-motion": "^12.7.4", "groq-sdk": "^0.19.0", "gsap": "^3.12.5", "lucide-react": "^0.488.0", "motion": "^12.7.3", "nanoid": "^5.1.5", "next": "^14.2.0", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "or": "^0.2.0", "pg": "^8.14.1", "postcss": "^8.4.31", "react": "^18", "react-day-picker": "^9.6.7", "react-dom": "^18", "react-hook-form": "^7.56.2", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-type-animation": "^3.2.0", "recharts": "^2.15.3", "remark-gfm": "^4.0.1", "socket.io-client": "^4.7.4", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^3.3.0", "tailwindcss-animate": "^1.0.7", "three": "^0.162.0", "typescript": "^5", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.4", "@types/pg": "^8.11.13", "@types/socket.io-client": "^3.0.0", "@types/three": "^0.162.0", "dotenv": "^16.5.0", "drizzle-kit": "^0.31.0", "eslint": "^8", "eslint-config-next": "14.2.0", "pg-pool": "^3.8.0"}}