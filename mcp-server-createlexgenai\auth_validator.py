#!/usr/bin/env python3
"""
Authentication Validator for UnrealGenAI MCP Server
Provides authentication validation utilities
"""

import jwt
import time
import logging
from typing import Dict, Any, Optional, Tuple

logger = logging.getLogger(__name__)

class AuthValidator:
    def __init__(self, jwt_secret: str = None):
        self.jwt_secret = jwt_secret or "default-secret"
        
    def validate_jwt_token(self, token: str) -> <PERSON><PERSON>[bool, Dict[str, Any]]:
        """
        Validate a JWT token and extract user information
        
        Args:
            token: JWT token string
            
        Returns:
            Tuple of (is_valid, user_data)
        """
        try:
            # Decode without verification first to check structure
            unverified_payload = jwt.decode(token, options={"verify_signature": False})
            
            # Check if token has expired
            if 'exp' in unverified_payload:
                if time.time() > unverified_payload['exp']:
                    logger.warning("JWT token has expired")
                    return False, {'error': 'token_expired'}
            
            # For development, we can skip signature verification
            # In production, you should verify the signature
            try:
                verified_payload = jwt.decode(token, self.jwt_secret, algorithms=["HS256"])
                logger.info(f"JWT token validated for user: {verified_payload.get('id', 'unknown')}")
                return True, verified_payload
            except jwt.InvalidSignatureError:
                # For development flexibility, still return the payload but log the issue
                logger.warning("JWT signature verification failed, but continuing in development mode")
                return True, unverified_payload
            except jwt.DecodeError:
                logger.error("JWT token decode error")
                return False, {'error': 'invalid_token'}
                
        except Exception as e:
            logger.error(f"JWT validation error: {e}")
            return False, {'error': 'validation_failed', 'details': str(e)}
    
    def extract_user_id(self, token: str) -> Optional[str]:
        """Extract user ID from JWT token"""
        is_valid, payload = self.validate_jwt_token(token)
        if is_valid:
            return payload.get('id') or payload.get('user_id') or payload.get('sub')
        return None
    
    def extract_user_email(self, token: str) -> Optional[str]:
        """Extract user email from JWT token"""
        is_valid, payload = self.validate_jwt_token(token)
        if is_valid:
            return payload.get('email') or payload.get('user_email')
        return None
    
    def is_token_expired(self, token: str) -> bool:
        """Check if JWT token is expired"""
        try:
            payload = jwt.decode(token, options={"verify_signature": False})
            if 'exp' in payload:
                return time.time() > payload['exp']
            return False
        except Exception:
            return True
    
    def get_token_info(self, token: str) -> Dict[str, Any]:
        """Get comprehensive token information"""
        is_valid, payload = self.validate_jwt_token(token)
        
        info = {
            'valid': is_valid,
            'expired': self.is_token_expired(token),
            'user_id': None,
            'user_email': None,
            'issued_at': None,
            'expires_at': None
        }
        
        if is_valid:
            info.update({
                'user_id': self.extract_user_id(token),
                'user_email': self.extract_user_email(token),
                'issued_at': payload.get('iat'),
                'expires_at': payload.get('exp'),
                'payload': payload
            })
        
        return info

# Global instance
auth_validator = AuthValidator()

def validate_auth_token(token: str) -> Tuple[bool, Dict[str, Any]]:
    """Global function to validate authentication token"""
    return auth_validator.validate_jwt_token(token) 