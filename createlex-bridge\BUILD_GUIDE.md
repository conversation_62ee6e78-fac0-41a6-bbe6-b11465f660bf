# CreateLex Bridge - Build Guide

## 🏗️ Building the CreateLex Bridge Application

This guide explains how to build the CreateLex Bridge desktop application for Windows, Mac, and Linux.

## ✅ **Successful Builds**

### **Windows ✅**
- **Built on:** Windows 10/11
- **Installer:** `CreateLex Bridge Setup 1.0.0.exe` (356 MB)
- **Type:** NSIS installer
- **Command:** `npm run build-win-simple`

## 📋 **Prerequisites**

### **All Platforms:**
```bash
# Install dependencies
npm install

# Install Electron Builder globally (optional)
npm install -g electron-builder
```

### **Platform-Specific Requirements:**

#### **Windows:**
- Node.js 18+
- Python 3.8+
- Visual Studio Build Tools (for native modules)

#### **Mac:**
- macOS 10.15+
- Xcode Command Line Tools
- Node.js 18+
- Python 3.8+

#### **Linux:**
- Ubuntu 18.04+ or equivalent
- Node.js 18+
- Python 3.8+
- Build essentials: `sudo apt-get install build-essential`

## 🚀 **Build Commands**

### **Simple Build (Recommended)**
Uses pre-built static HTML files instead of building Next.js:

```bash
# Windows
npm run build-win-simple

# Mac (must run on macOS)
npm run build-mac-simple

# Linux (can run on Linux or with Docker)
npm run build-linux-simple
```

### **Full Build (Advanced)**
Builds the complete Next.js frontend:

```bash
# Windows
npm run build-win

# Mac (must run on macOS)  
npm run build-mac

# Linux
npm run build-linux
```

## 📦 **Output Files**

### **Windows:**
- **Installer:** `dist/CreateLex Bridge Setup 1.0.0.exe`
- **Portable:** `dist/win-unpacked/CreateLex Bridge.exe`
- **Size:** ~356 MB

### **Mac:**
- **DMG:** `dist/CreateLex Bridge-1.0.0.dmg`
- **App Bundle:** `dist/mac/CreateLex Bridge.app`
- **Size:** ~400 MB (estimated)

### **Linux:**
- **AppImage:** `dist/CreateLex Bridge-1.0.0.AppImage`
- **Portable:** `dist/linux-unpacked/createlex-bridge`
- **Size:** ~350 MB (estimated)

## 🔧 **Cross-Platform Build Strategy**

### **Option 1: Platform-Specific Builds**
Build each platform on its native OS:

1. **Windows builds:** Use Windows machine
2. **Mac builds:** Use macOS machine  
3. **Linux builds:** Use Linux machine or Docker

### **Option 2: GitHub Actions (Recommended)**
Set up automated builds using GitHub Actions:

```yaml
# .github/workflows/build.yml
name: Build CreateLex Bridge
on: [push, pull_request]

jobs:
  build-windows:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm install
      - run: npm run build-win-simple
      
  build-mac:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm install
      - run: npm run build-mac-simple
      
  build-linux:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm install
      - run: npm run build-linux-simple
```

### **Option 3: Docker Builds**
Use Docker for consistent Linux builds:

```bash
# Build Linux version in Docker
docker run --rm -v $(pwd):/app -w /app node:18 npm run build-linux-simple
```

## 🎯 **Distribution Strategy**

### **Release Channels:**
1. **GitHub Releases:** Attach installers to GitHub releases
2. **Direct Download:** Host on createlex.com/download
3. **Package Managers:** 
   - Windows: Microsoft Store, Chocolatey
   - Mac: Homebrew Cask
   - Linux: Snap Store, AppImage Hub

### **Auto-Updates:**
The app includes Electron's auto-updater. Configure update server:

```javascript
// In main.js
const { autoUpdater } = require('electron-updater');
autoUpdater.setFeedURL('https://createlex.com/updates/');
```

## 🔍 **Testing Builds**

### **Before Release:**
1. **Install test:** Install on clean systems
2. **Authentication:** Test OAuth flow
3. **Subscription:** Test with active/inactive users
4. **MCP Server:** Test server start/stop
5. **Updates:** Test auto-update mechanism

### **Test Matrix:**
- Windows 10/11 (x64)
- macOS 10.15+ (Intel + Apple Silicon)
- Ubuntu 18.04+, CentOS 7+, Debian 10+

## 📋 **Release Checklist**

- [ ] Update version in `package.json`
- [ ] Test builds on all platforms
- [ ] Update CHANGELOG.md
- [ ] Create GitHub release
- [ ] Upload installers
- [ ] Update download page
- [ ] Test auto-updates
- [ ] Announce release

## 🐛 **Troubleshooting**

### **Common Issues:**

#### **"Python not found"**
```bash
# Windows
npm config set python python3

# Mac/Linux  
which python3
```

#### **"Build tools missing"**
```bash
# Windows
npm install --global windows-build-tools

# Mac
xcode-select --install

# Linux
sudo apt-get install build-essential
```

#### **"Permission denied" (Linux)**
```bash
# Run with elevated permissions
sudo npm run build-linux-simple
```

#### **"Code signing failed" (Mac)**

**For Production Builds (Signed):**
1. Install the CreateLex certificate:
   ```bash
   cd certificates
   ./install-certificate.sh
   ```

2. Build with signing:
   ```bash
   npm run build-mac-protected
   ```

**For Development Builds (Unsigned):**
```bash
# Skip code signing for development
export CSC_IDENTITY_AUTO_DISCOVERY=false
npm run build-mac-simple
```

See `certificates/README.md` for detailed certificate setup instructions.

## 📞 **Support**

For build issues:
- **GitHub Issues:** https://github.com/AlexKissiJr/AiWebplatform/issues
- **Email:** <EMAIL>
- **Discord:** https://discord.gg/createlex

---

**Last Updated:** June 19, 2025  
**Version:** 1.0.0 