import { model, type modelID } from "@/ai/providers";
import { streamText, type UIMessage } from "ai";
import { appendResponseMessages } from 'ai';
import { saveChat, saveMessages, convertToDBMessages } from '@/lib/chat-store';
import { nanoid } from 'nanoid';
import { db } from '@/lib/db';
import { chats } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { trackTokenUsage } from '@/lib/backend-chat';
import { getSupabaseAdminClient } from '@/lib/supabase-singleton';

import { experimental_createMCPClient as createMCPClient, MCPTransport } from 'ai';
import { Experimental_StdioMCPTransport as StdioMCPTransport } from 'ai/mcp-stdio';
import { spawn } from "child_process";

// Allow streaming responses up to 30 seconds
export const maxDuration = 120;

interface KeyValuePair {
  key: string;
  value: string;
}

interface MCPServerConfig {
  url: string;
  type: 'sse' | 'stdio';
  command?: string;
  args?: string[];
  env?: KeyValuePair[];
  headers?: KeyValuePair[];
}

export async function POST(req: Request) {
  const {
    messages,
    chatId,
    selectedModel,
    userId,
    mcpServers = [],
    authToken,
  }: {
    messages: UIMessage[];
    chatId?: string;
    selectedModel: modelID;
    userId: string;
    mcpServers?: MCPServerConfig[];
    authToken?: string;
  } = await req.json();

  if (!userId) {
    return new Response(
      JSON.stringify({ error: "User ID is required" }),
      { status: 400, headers: { "Content-Type": "application/json" } }
    );
  }

  const id = chatId || nanoid();

  // Check if chat already exists for the given ID
  // If not, we'll create it in onFinish
  let isNewChat = false;
  if (chatId) {
    try {
      // Use an even simpler approach to avoid issues with the dummy client
      // First check with just the ID
      let existingChat = null;
      let error = null;

      try {
        const result = await getSupabaseAdminClient()
          .from('chats')
          .select('id')
          .eq('id', chatId)
          .maybeSingle();

        existingChat = result.data;
        error = result.error;
      } catch (queryError) {
        console.error("Error in first query:", queryError);
        // Try a different approach if the first one fails
        try {
          // Just assume the chat exists if we can't check properly
          console.log("Using fallback approach for chat existence check");
          existingChat = { id: chatId };
          error = null;
        } catch (fallbackError) {
          console.error("Error in fallback approach:", fallbackError);
        }
      }

      isNewChat = !existingChat || error;
    } catch (error) {
      console.error("Error checking for existing chat:", error);
      // Continue anyway, we'll create the chat in onFinish
      isNewChat = true;
    }
  } else {
    // No ID provided, definitely new
    isNewChat = true;
  }

  // Initialize tools
  let tools = {};
  const mcpClients: any[] = [];

  // Process each MCP server configuration
  for (const mcpServer of mcpServers) {
    try {
      // Create appropriate transport based on type
      let transport: MCPTransport | { type: 'sse', url: string, headers?: Record<string, string> };

      if (mcpServer.type === 'sse') {
        // Convert headers array to object for SSE transport
        const headers: Record<string, string> = {};
        if (mcpServer.headers && mcpServer.headers.length > 0) {
          mcpServer.headers.forEach(header => {
            if (header.key) headers[header.key] = header.value || '';
          });
        }

        transport = {
          type: 'sse' as const,
          url: mcpServer.url,
          headers: Object.keys(headers).length > 0 ? headers : undefined
        };
      } else if (mcpServer.type === 'stdio') {
        // For stdio transport, we need command and args
        if (!mcpServer.command || !mcpServer.args || mcpServer.args.length === 0) {
          console.warn("Skipping stdio MCP server due to missing command or args");
          continue;
        }

        // Convert env array to object for stdio transport
        const env: Record<string, string> = {};
        if (mcpServer.env && mcpServer.env.length > 0) {
          mcpServer.env.forEach(envVar => {
            if (envVar.key) env[envVar.key] = envVar.value || '';
          });
        }

        // Check for uvx pattern and transform to python3 -m uv run
        if (mcpServer.command === 'uvx') {
          // install uv
          const subprocess = spawn('pip3', ['install', 'uv']);
          subprocess.on('close', (code: number) => {
            if (code !== 0) {
              console.error(`Failed to install uv: ${code}`);
            }
          });
          // wait for the subprocess to finish
          await new Promise((resolve) => {
            subprocess.on('close', resolve);
            console.log("installed uv");
          });
          console.log("Detected uvx pattern, transforming to python3 -m uv run");
          mcpServer.command = 'python3';
          // Get the tool name (first argument)
          const toolName = mcpServer.args[0];
          // Replace args with the new pattern
          mcpServer.args = ['-m', 'uv', 'run', toolName, ...mcpServer.args.slice(1)];
        }
        // if python is passed in the command, install the python package mentioned in args after -m with subprocess or use regex to find the package name
        else if (mcpServer.command.includes('python3')) {
          const packageName = mcpServer.args[mcpServer.args.indexOf('-m') + 1];
          console.log("installing python package", packageName);
          const subprocess = spawn('pip3', ['install', packageName]);
          subprocess.on('close', (code: number) => {
            if (code !== 0) {
              console.error(`Failed to install python package: ${code}`);
            }
          });
          // wait for the subprocess to finish
          await new Promise((resolve) => {
            subprocess.on('close', resolve);
            console.log("installed python package", packageName);
          });
        }

        transport = new StdioMCPTransport({
          command: mcpServer.command,
          args: mcpServer.args,
          env: Object.keys(env).length > 0 ? env : undefined
        });
      } else {
        console.warn(`Skipping MCP server with unsupported transport type: ${mcpServer.type}`);
        continue;
      }

      const mcpClient = await createMCPClient({ transport });
      mcpClients.push(mcpClient);

      const mcptools = await mcpClient.tools();

      console.log(`MCP tools from ${mcpServer.type} transport:`, Object.keys(mcptools));

      // Add MCP tools to tools object
      tools = { ...tools, ...mcptools };
    } catch (error) {
      console.error("Failed to initialize MCP client:", error);
      // Continue with other servers instead of failing the entire request
    }
  }

  // Register cleanup for all clients
  if (mcpClients.length > 0) {
    req.signal.addEventListener('abort', async () => {
      for (const client of mcpClients) {
        try {
          await client.close();
        } catch (error) {
          console.error("Error closing MCP client:", error);
        }
      }
    });
  }

  console.log("messages", messages);
  console.log("parts", messages.map(m => m.parts.map(p => p)));

  // If there was an error setting up MCP clients but we at least have composio tools, continue
  const result = streamText({
    model: model.languageModel(selectedModel),
    system: `You are a helpful assistant with access to a variety of tools.

    Today's date is ${new Date().toISOString().split('T')[0]}.

    The tools are very powerful, and you can use them to answer the user's question.
    So choose the tool that is most relevant to the user's question.

    If tools are not available, say you don't know or if the user wants a tool they can add one from the server icon in bottom left corner in the sidebar.

    You can use multiple tools in a single response.
    Always respond after using the tools for better user experience.
    You can run multiple steps using all the tools!!!!
    Make sure to use the right tool to respond to the user's question.

    Multiple tools can be used in a single response and multiple steps can be used to answer the user's question.

    ## Response Format
    - Markdown is supported.
    - Respond according to tool's response.
    - Use the tools to answer the user's question.
    - If you don't know the answer, use the tools to find the answer or say you don't know.
    `,
    messages,
    tools,
    maxSteps: 20,
    providerOptions: {
      google: {
        thinkingConfig: {
          thinkingBudget: 2048,
        },
      },
      anthropic: {
        thinking: {
          type: 'enabled',
          budgetTokens: 12000
        },
      }
    },
    onError: (error) => {
      console.error(JSON.stringify(error, null, 2));
    },
    async onFinish({ response }) {
      try {
        const allMessages = appendResponseMessages({
          messages,
          responseMessages: response.messages,
        });

        // Include the auth token when saving the chat
        await saveChat({
          id,
          userId,
          messages: allMessages,
          authToken,
        });

        const dbMessages = convertToDBMessages(allMessages, id);
        await saveMessages({ messages: dbMessages });

        // Track token usage with the backend
        try {
          console.log('=== TOKEN TRACKING START ===');

          // Estimate token counts based on message length
          const userMessages = messages.filter(m => m.role === 'user');
          const assistantMessages = response.messages.filter(m => m.role === 'assistant');

          console.log('User messages:', userMessages.length);
          console.log('Assistant messages:', assistantMessages.length);

          // Simple estimation: 4 characters per token
          const promptText = userMessages.map(m => {
            // Check if message has content property (standard format)
            if (m.content) {
              return typeof m.content === 'string' ? m.content : '';
            }
            // Fallback for messages with parts
            if ('parts' in m && Array.isArray((m as any).parts)) {
              return (m as any).parts
                .filter((p: any) => p.type === 'text')
                .map((p: any) => p.text)
                .join(' ');
            }
            // Final fallback
            return '';
          }).join(' ');

          const completionText = assistantMessages.map(m => {
            // Check if message has content property (standard format)
            if (m.content) {
              return typeof m.content === 'string' ? m.content : '';
            }
            // Fallback for messages with parts
            if ('parts' in m && Array.isArray((m as any).parts)) {
              return (m as any).parts
                .filter((p: any) => p.type === 'text')
                .map((p: any) => p.text)
                .join(' ');
            }
            // Final fallback
            return '';
          }).join(' ');

          console.log('Prompt text length:', promptText.length);
          console.log('Completion text length:', completionText.length);

          const promptTokens = Math.ceil(promptText.length / 4);
          const completionTokens = Math.ceil(completionText.length / 4);

          console.log(`Estimated tokens - Prompt: ${promptTokens}, Completion: ${completionTokens}`);

          try {
            console.log('Attempting to track token usage with backend...');
            console.log('User ID:', userId);
            console.log('Model ID:', selectedModel || 'gemini-flash');
            console.log('Prompt tokens:', promptTokens);
            console.log('Completion tokens:', completionTokens);

            // Track token usage with the backend using the test endpoint
            console.log('Making fetch request to token tracking endpoint...');

            const tokenResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001'}/api/test/token-usage/track`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
              },
              body: JSON.stringify({
                userId,
                modelId: selectedModel || 'gemini-flash',
                promptTokens,
                completionTokens,
                requestType: 'chat'
              })
            });

            console.log('Token tracking response status:', tokenResponse.status);

            if (tokenResponse.ok) {
              const data = await tokenResponse.json();
              console.log('Token usage tracked successfully:', data);
            } else {
              const errorText = await tokenResponse.text();
              console.error('Failed to track token usage:', errorText);
            }
          } catch (fetchError) {
            console.error('Error making token tracking request:', fetchError);
            // Check if fetchError is an Error object before accessing its properties
            if (fetchError instanceof Error) {
              console.error('Error details:', fetchError.message);
              console.error('Error stack:', fetchError.stack);
            } else {
              console.error('Unknown error type:', typeof fetchError);
            }
          }

          console.log('=== TOKEN TRACKING END ===');
        } catch (trackingError) {
          console.error('Error tracking token usage:', trackingError);
          // Check if trackingError is an Error object before accessing its properties
          if (trackingError instanceof Error) {
            console.error('Error details:', trackingError.message);
            console.error('Error stack:', trackingError.stack);
          } else {
            console.error('Unknown error type:', typeof trackingError);
          }
          // Don't fail the request if token tracking fails
        }

        // close all mcp clients
        // for (const client of mcpClients) {
        //   await client.close();
        // }
      } catch (error) {
        console.error("Error in onFinish handler:", error);
        // Don't rethrow the error, just log it
        // This prevents the stream from being interrupted
      }
    }
  });

  try {
    result.consumeStream();
    return result.toDataStreamResponse({
      sendReasoning: true,
      getErrorMessage: (error) => {
        if (error instanceof Error) {
          if (error.message.includes("Rate limit")) {
            return "Rate limit exceeded. Please try again later.";
          }
        }
        console.error("Stream error:", error);
        return "An error occurred.";
      },
    });
  } catch (error) {
    console.error("Error in stream response:", error);
    return new Response(JSON.stringify({ error: "An error occurred while streaming the response" }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
}
