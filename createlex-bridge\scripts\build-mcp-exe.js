const fs = require('fs-extra');
const path = require('path');
const { execSync } = require('child_process');
const os = require('os');

async function buildMCPExecutable() {
  const pythonDir = path.join(__dirname, '..', 'src', 'python');
  const buildDir = path.join(__dirname, '..', 'build', 'mcp-exe');
  const distDir = path.join(__dirname, '..', 'src', 'python-protected');
  
  // Detect platform
  const platform = os.platform();
  const isWindows = platform === 'win32';
  const isMac = platform === 'darwin';
  const isLinux = platform === 'linux';
  // Allow override for cross-arch building (e.g. TARGET_ARCH=x64 on Apple Silicon)
  const targetArchEnv = process.env.TARGET_ARCH || '';
  const isMacX64Build = isMac && targetArchEnv === 'x64';
  const macTargetArch = isMacX64Build ? 'x86_64' : 'arm64';
  
  // Set executable name based on platform
  let executableName = 'mcp_server';
  if (isWindows) {
    executableName = 'mcp_server.exe';
  } else if (isMac) {
    executableName = 'mcp_server_mac';
  } else if (isLinux) {
    executableName = 'mcp_server_linux';
  }
  
  console.log(`🔨 Building protected MCP server executable for ${platform}...`);
  console.log(`📦 Target executable: ${executableName}`);
  
  try {
    // Clean build directories
    await fs.remove(buildDir);
    await fs.remove(distDir);
    await fs.ensureDir(buildDir);
    await fs.ensureDir(distDir);
    
    // Copy Python files to build directory
    await fs.copy(pythonDir, buildDir);
    
    // Create a main entry point that includes all dependencies
    const mainScript = `#!/usr/bin/env python3
"""
Protected MCP Server Bundle
This bundles all MCP server code into a single executable
"""
import sys
import os

# Add the script directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import and run the protected server
from mcp_server_protected import *

if __name__ == "__main__":
    # The mcp_server_protected will handle everything
    pass
`;
    
    await fs.writeFile(path.join(buildDir, 'mcp_bundle.py'), mainScript);
    
    // Create PyInstaller spec file with all hidden imports
    const specContent = `# -*- mode: python ; coding: utf-8 -*-
import os
import sys

block_cipher = None

# Comprehensive hidden imports for all platforms
hidden_imports = [
    # Core MCP server modules
    'subscription_validator',
    'subscription_validator_secure',
    'mcp_server_stdio',
    
    # FastMCP - Complete module tree
    'fastmcp',
    'fastmcp.core',
    'fastmcp.server',
    'fastmcp.tools',
    'fastmcp.types',
    'fastmcp.utilities',
    'fastmcp.exceptions',
    'fastmcp.__init__',
    
    # MCP Protocol
    'mcp',
    'mcp.server',
    'mcp.server.stdio',
    'mcp.server.session',
    'mcp.types',
    
    # Async and networking
    'asyncio',
    'asyncio.streams',
    'asyncio.subprocess',
    'asyncio.events',
    'asyncio.protocols',
    'asyncio.transports',
    'asyncio.futures',
    'asyncio.tasks',
    'asyncio.coroutines',
    
    # HTTP and web frameworks
    'aiohttp',
    'aiohttp.web',
    'aiohttp.client',
    'aiohttp.connector',
    'aiohttp.helpers',
    'requests',
    'requests.adapters',
    'requests.auth',
    'requests.exceptions',
    'urllib3',
    'urllib3.util',
    'urllib3.poolmanager',
    
    # WebSocket support
    'websockets',
    'websockets.server',
    'websockets.client',
    'websockets.protocol',
    'websockets.exceptions',
    
    # Flask framework
    'flask',
    'flask.app',
    'flask.blueprints',
    'flask.json',
    'flask.helpers',
    'werkzeug',
    'werkzeug.serving',
    'werkzeug.utils',
    
    # Uvicorn ASGI server
    'uvicorn',
    'uvicorn.main',
    'uvicorn.server',
    'uvicorn.config',
    'uvicorn.protocols',
    'uvicorn.protocols.http',
    'uvicorn.protocols.websockets',
    
    # Pydantic (often used with FastMCP)
    'pydantic',
    'pydantic.main',
    'pydantic.fields',
    'pydantic.types',
    'pydantic.validators',
    
    # Standard library essentials
    'json',
    'socket',
    'ssl',
    'typing',
    'typing_extensions',
    'datetime',
    'hashlib',
    'hmac',
    'platform',
    'uuid',
    'pathlib',
    'atexit',
    'logging',
    'logging.handlers',
    'multiprocessing',
    'threading',
    'queue',
    'sys',
    'os',
    'io',
    'traceback',
    'subprocess',
    'shutil',
    'tempfile',
    'time',
    'signal',
    'base64',
    'binascii',
    'struct',
    'collections',
    'collections.abc',
    'functools',
    'itertools',
    'contextlib',
    
    # Environment and configuration
    'dotenv',
    'python_dotenv',
    'configparser',
    'argparse',
    
    # macOS specific imports
    'Foundation',
    'objc',
    
    # Additional network and crypto
    'cryptography',
    'cryptography.fernet',
    'cryptography.hazmat',
    'certifi',
    'charset_normalizer',
]

a = Analysis(
    ['mcp_server_protected.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('*.py', '.'),  # Include all Python files
    ],
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['tkinter', 'matplotlib', 'numpy', 'pandas', 'scipy'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='${executableName.replace('.exe', '')}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=${isMac ? 'False' : 'True'},  # Disable UPX on macOS for compatibility
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # Keep console for stdio communication
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=${isMac ? `'${macTargetArch}'` : 'None'},  # Set arch for macOS build
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
`;
    
    const specFile = path.join(buildDir, 'mcp_server.spec');
    await fs.writeFile(specFile, specContent);
    
    console.log('📦 Running PyInstaller...');
    
    // Determine best Python command based on platform and version requirements
    let pythonCmd = 'python';
    // If building mac x64 on Apple Silicon, force Rosetta
    const archPrefix = isMacX64Build ? 'arch -x86_64 ' : '';
    
    if (isMac || isLinux) {
      // On Unix systems, try to find the best Python version
      // FastMCP requires Python 3.10+, so prioritize newer versions
      const pythonVersions = [
        'python3.12',
        'python3.11', 
        'python3.10',
        'python3',
        'python'
      ];
      
      for (const cmd of pythonVersions) {
        try {
          const versionOutput = execSync(`${cmd} --version`, { stdio: 'pipe' }).toString();
          const versionMatch = versionOutput.match(/Python (\d+)\.(\d+)/);
          
          if (versionMatch) {
            const major = parseInt(versionMatch[1]);
            const minor = parseInt(versionMatch[2]);
            
            console.log(`🐍 Found ${cmd}: Python ${major}.${minor}`);
            
            // For macOS, prefer Python 3.10+ for FastMCP compatibility
            if (isMac && major === 3 && minor >= 10) {
              pythonCmd = cmd;
              console.log(`✅ Using ${cmd} for FastMCP compatibility (requires Python 3.10+)`);
              break;
            }
            // For Linux, Python 3.8+ is usually fine
            else if (isLinux && major === 3 && minor >= 8) {
              pythonCmd = cmd;
              console.log(`✅ Using ${cmd} for Linux build`);
              break;
            }
            // Fallback for older versions
            else if (major === 3) {
              pythonCmd = cmd;
              console.log(`⚠️  Using ${cmd} (Python ${major}.${minor}) - may have compatibility issues`);
              if (isMac) {
                console.log(`💡 Consider installing Python 3.10+ for better FastMCP support:`);
                console.log(`   brew install python@3.11`);
              }
              break;
            }
          }
        } catch (e) {
          // Command not found, continue to next version
          continue;
        }
      }
    } else {
      // Windows - try python first, then python3
      try {
        execSync('python --version', { stdio: 'pipe' });
        pythonCmd = 'python';
      } catch (e) {
        try {
          execSync('python3 --version', { stdio: 'pipe' });
          pythonCmd = 'python3';
        } catch (e2) {
          console.log('⚠️  No Python found, using default "python" command');
        }
      }
    }
    
    // Check if PyInstaller is available and determine the best command to use
    let pyinstallerCmd;
    try {
      // Try python module first (preferred method)
      execSync(`${pythonCmd} -m PyInstaller --version`, { stdio: 'pipe' });
      pyinstallerCmd = `${archPrefix}${pythonCmd} -m PyInstaller --distpath "${distDir}" "${specFile}"`;
      console.log(`✅ Using PyInstaller via Python module: ${pythonCmd} -m PyInstaller`);
    } catch (e) {
      console.log('⚠️  PyInstaller not found via python module, trying direct command...');
      try {
        execSync('pyinstaller --version', { stdio: 'pipe' });
        pyinstallerCmd = `${archPrefix}pyinstaller --distpath "${distDir}" "${specFile}"`;
        console.log(`✅ Using direct PyInstaller command`);
      } catch (e2) {
        throw new Error(`PyInstaller not found. Please install it with: ${pythonCmd} -m pip install pyinstaller`);
      }
    }
    
    // Run PyInstaller with the determined command
    console.log(`🔧 Running: ${pyinstallerCmd}`);
    
    execSync(pyinstallerCmd, {
      cwd: buildDir,
      stdio: 'inherit',
      env: {
        ...process.env,
        PYTHONIOENCODING: 'utf-8'
      }
    });
    
    // Clean up build artifacts
    await fs.remove(path.join(buildDir, 'build'));
    await fs.remove(path.join(buildDir, '__pycache__'));
    
    console.log('✅ MCP server compiled to executable!');
    console.log(`📍 Output: ${path.join(distDir, executableName)}`);
    
    // Platform-specific instructions
    if (isWindows) {
      console.log('📝 Windows: Update bridge-server.js to use mcp_server.exe');
    } else if (isMac) {
      console.log('📝 macOS: Update bridge-server.js to use mcp_server_mac');
      console.log('🔒 macOS: You may need to allow the executable in System Preferences > Security');
    } else if (isLinux) {
      console.log('📝 Linux: Update bridge-server.js to use mcp_server_linux');
      console.log('🔒 Linux: You may need to make the executable file executable: chmod +x mcp_server_linux');
    }
    
  } catch (error) {
    console.error('❌ Failed to build MCP executable:', error);
    
    // Platform-specific troubleshooting
    if (error.message.includes('PyInstaller')) {
      console.error('💡 Try installing PyInstaller:');
      if (isWindows) {
        console.error('   pip install pyinstaller');
      } else {
        console.error('   pip3 install pyinstaller');
        console.error('   or: python3 -m pip install pyinstaller');
      }
    }
    
    process.exit(1);
  }
}

buildMCPExecutable(); 