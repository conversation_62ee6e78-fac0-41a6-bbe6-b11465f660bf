const express = require('express');
const router = express.Router();
const UnrealAdapter = require('../services/unrealAdapter');
const { isAuthenticated } = require('../middleware/auth');

// Create a singleton instance of UnrealAdapter
let unrealAdapter = null;

// Get or create the UnrealAdapter instance
const getUnrealAdapter = async () => {
  if (!unrealAdapter) {
    unrealAdapter = new UnrealAdapter();
    try {
      await unrealAdapter.connect();
      console.log('Connected to Unreal Engine');
      
      // Listen for events from Unreal Engine
      unrealAdapter.on('error', (err) => {
        console.error('Unreal adapter error:', err.message);
      });
      
      unrealAdapter.on('disconnected', () => {
        console.log('Disconnected from Unreal Engine');
      });
      
      unrealAdapter.on('reconnected', () => {
        console.log('Reconnected to Unreal Engine');
      });
      
      // Register for common events
      await unrealAdapter.registerEvent('object_spawned');
      await unrealAdapter.registerEvent('object_deleted');
      await unrealAdapter.registerEvent('editor_state_changed');
    } catch (err) {
      console.error('Failed to connect to Unreal Engine:', err.message);
    }
  }
  return unrealAdapter;
};

// Middleware to check connection to Unreal Engine
const checkUnrealConnection = async (req, res, next) => {
  try {
    const adapter = await getUnrealAdapter();
    if (!adapter.isConnected) {
      return res.status(503).json({ 
        success: false, 
        message: 'Not connected to Unreal Engine. Please start the Unreal Engine editor.' 
      });
    }
    req.unrealAdapter = adapter;
    next();
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};

// Routes

/**
 * @route   GET /api/unreal/status
 * @desc    Check connection status with Unreal Engine
 * @access  Protected
 */
router.get('/status', isAuthenticated, async (req, res) => {
  try {
    const adapter = await getUnrealAdapter();
    res.json({
      success: true,
      connected: adapter.isConnected
    });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
});

/**
 * @route   POST /api/unreal/spawn
 * @desc    Spawn an object in Unreal Engine
 * @access  Protected
 */
router.post('/spawn', isAuthenticated, checkUnrealConnection, async (req, res) => {
  try {
    const { 
      actorClass = 'Cube', 
      location = [0, 0, 100], 
      rotation = [0, 0, 0], 
      scale = [1, 1, 1], 
      actorLabel 
    } = req.body;
    
    const response = await req.unrealAdapter.spawnObject({
      actorClass,
      location,
      rotation,
      scale,
      actorLabel: actorLabel || `${req.user.username}_${actorClass}_${Date.now()}`
    });
    
    res.json({
      success: true,
      message: 'Object spawned successfully',
      data: response
    });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
});

/**
 * @route   POST /api/unreal/material
 * @desc    Create a material in Unreal Engine
 * @access  Protected
 */
router.post('/material', isAuthenticated, checkUnrealConnection, async (req, res) => {
  try {
    const { 
      materialName, 
      color = [1, 1, 1] 
    } = req.body;
    
    if (!materialName) {
      return res.status(400).json({ success: false, message: 'Material name is required' });
    }
    
    const response = await req.unrealAdapter.createMaterial({
      materialName,
      color
    });
    
    res.json({
      success: true,
      message: 'Material created successfully',
      data: response
    });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
});

/**
 * @route   POST /api/unreal/set-material
 * @desc    Set material of an object in Unreal Engine
 * @access  Protected
 */
router.post('/set-material', isAuthenticated, checkUnrealConnection, async (req, res) => {
  try {
    const { actorName, materialPath } = req.body;
    
    if (!actorName || !materialPath) {
      return res.status(400).json({ 
        success: false, 
        message: 'Actor name and material path are required' 
      });
    }
    
    const response = await req.unrealAdapter.setObjectMaterial({
      actorName,
      materialPath
    });
    
    res.json({
      success: true,
      message: 'Material set successfully',
      data: response
    });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
});

/**
 * @route   POST /api/unreal/command
 * @desc    Send a custom command to Unreal Engine
 * @access  Protected (Admin only)
 */
router.post('/command', isAuthenticated, checkUnrealConnection, async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ 
        success: false, 
        message: 'Only administrators can send custom commands' 
      });
    }
    
    const { command } = req.body;
    
    if (!command || !command.type) {
      return res.status(400).json({ 
        success: false, 
        message: 'Valid command object with type is required' 
      });
    }
    
    const response = await req.unrealAdapter.sendCommand(command);
    
    res.json({
      success: true,
      message: 'Command executed successfully',
      data: response
    });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
});

module.exports = router;