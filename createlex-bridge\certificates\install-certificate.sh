#!/bin/bash

# CreateLex Bridge Certificate Installation Script
# This script installs the Developer ID Application certificate on a new Mac

set -e  # Exit on any error

echo "🔐 CreateLex Bridge Certificate Installer"
echo "========================================"

# Check if p12 file exists
P12_FILE="createlex-developer-id.p12"
if [ ! -f "$P12_FILE" ]; then
    echo "❌ Error: $P12_FILE not found in current directory"
    echo "Make sure you're running this script from the certificates directory"
    exit 1
fi

echo "📁 Found certificate file: $P12_FILE"

# Prompt for password
echo ""
echo "🔑 Enter the certificate password:"
read -s CERT_PASSWORD

if [ -z "$CERT_PASSWORD" ]; then
    echo "❌ Error: Password cannot be empty"
    exit 1
fi

echo ""
echo "📥 Installing certificate to login keychain..."

# Import the certificate
if security import "$P12_FILE" -k ~/Library/Keychains/login.keychain-db -P "$CERT_PASSWORD" -T /usr/bin/codesign; then
    echo "✅ Certificate imported successfully!"
else
    echo "❌ Failed to import certificate. Please check the password and try again."
    exit 1
fi

echo ""
echo "🔍 Verifying installation..."

# Verify the certificate is installed and usable
if security find-identity -v -p codesigning | grep -q "CREATELEX"; then
    echo "✅ Certificate verification successful!"
    echo ""
    echo "📋 Available CreateLex certificates:"
    security find-identity -v -p codesigning | grep "CREATELEX"
else
    echo "❌ Certificate not found. Installation may have failed."
    exit 1
fi

echo ""
echo "🎉 Installation complete!"
echo ""
echo "You can now build signed CreateLex Bridge apps by running:"
echo "  cd ../  # Go back to createlex-bridge directory"
echo "  npm run build-mac-protected"
echo ""
echo "🔒 Security reminder: Keep the certificate password secure and never commit it to git." 