import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check if chats table exists
    const { data: chatTables, error: chatTablesError } = await supabase
      .from('chats')
      .select('id')
      .limit(1);
      
    if (chatTablesError) {
      console.error('Error checking chats table:', chatTablesError);
      return res.status(500).json({ error: 'Error checking chats table', details: chatTablesError });
    }
    
    // Check if messages table exists
    const { data: messageTables, error: messageTablesError } = await supabase
      .from('messages')
      .select('id')
      .limit(1);
      
    if (messageTablesError) {
      console.error('Error checking messages table:', messageTablesError);
      return res.status(500).json({ error: 'Error checking messages table', details: messageTablesError });
    }
    
    // Check content column type
    const { data: contentType, error: contentTypeError } = await supabase
      .rpc('check_column_type', {
        table_name: 'messages',
        column_name: 'content',
        expected_type: 'jsonb'
      });
      
    if (contentTypeError) {
      console.error('Error checking content column type:', contentTypeError);
      return res.status(500).json({ error: 'Error checking content column type', details: contentTypeError });
    }
    
    return res.status(200).json({
      chats_table_exists: true,
      messages_table_exists: true,
      content_column_type: contentType
    });
  } catch (error) {
    console.error('Error checking tables:', error);
    return res.status(500).json({ error: 'Error checking tables', details: error });
  }
}
