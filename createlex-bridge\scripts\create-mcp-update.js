const fs = require('fs-extra');
const path = require('path');
const crypto = require('crypto');
const AdmZip = require('adm-zip');

/**
 * Create an MCP server update package
 * Usage: node scripts/create-mcp-update.js <version> [source-dir]
 */

async function createUpdatePackage() {
  const args = process.argv.slice(2);
  
  if (args.length < 1) {
    console.error('Usage: node create-mcp-update.js <version> [source-dir]');
    console.error('Example: node create-mcp-update.js 1.1.0');
    process.exit(1);
  }

  const version = args[0];
  const sourceDir = args[1] || (fs.existsSync(path.join(__dirname, '..', '..', 'mcp-server-createlexgenai'))
  ? path.join(__dirname, '..', '..', 'mcp-server-createlexgenai')
  : path.join(__dirname, '..', '..', 'CreatelexGenAI_with_server', 'server'));
  const outputDir = path.join(__dirname, '..', 'updates');
  
  console.log(`📦 Creating MCP server update package v${version}`);
  console.log(`📁 Source directory: ${sourceDir}`);
  console.log(`📁 Output directory: ${outputDir}`);

  try {
    // Ensure source directory exists
    if (!(await fs.pathExists(sourceDir))) {
      throw new Error(`Source directory not found: ${sourceDir}`);
    }

    // Ensure output directory exists
    await fs.ensureDir(outputDir);

    // Create temporary directory for packaging
    const tempDir = path.join(__dirname, '..', 'temp-package');
    await fs.ensureDir(tempDir);

    try {
      console.log('🔄 Copying MCP server files...');
      
      // Copy all Python files and dependencies
      const filesToCopy = [
        'mcp_server_protected.py',
        'mcp_server_stdio.py',
        'mcp_server.py',
        'subscription_validator.py',
        'fastmcp.py',
        'requirements.txt',
        'requirements-cloud.txt'
      ];

      // Copy essential files
      for (const file of filesToCopy) {
        const srcFile = path.join(sourceDir, file);
        const destFile = path.join(tempDir, file);
        
        if (await fs.pathExists(srcFile)) {
          await fs.copy(srcFile, destFile);
          console.log(`✅ Copied ${file}`);
        } else {
          console.warn(`⚠️ File not found: ${file}`);
        }
      }

      // Copy all tool files (they usually start with specific patterns)
      const allFiles = await fs.readdir(sourceDir);
      const toolFiles = allFiles.filter(file => 
        file.endsWith('.py') && 
        !filesToCopy.includes(file) &&
        !file.startsWith('test_') &&
        !file.startsWith('build_')
      );

      for (const file of toolFiles) {
        const srcFile = path.join(sourceDir, file);
        const destFile = path.join(tempDir, file);
        await fs.copy(srcFile, destFile);
        console.log(`✅ Copied tool file: ${file}`);
      }

      // Create version.json with update information
      const versionInfo = {
        version: version,
        build_date: new Date().toISOString().split('T')[0],
        build_number: Date.now(),
        description: `CreateLex MCP Server v${version}`,
        features: [
          "Unreal Engine project management",
          "Asset creation and import",
          "Blueprint creation and editing",
          "Level design tools",
          "Animation and rigging support",
          "Lighting and rendering configuration",
          "Gameplay programming helpers",
          "Material creation and editing",
          "Component management",
          "Input binding setup",
          "UI widget creation"
        ],
        compatibility: {
          unreal_engine: "5.0+",
          python: "3.8+",
          platforms: ["Windows", "macOS", "Linux"]
        }
      };

      await fs.writeJson(path.join(tempDir, 'version.json'), versionInfo, { spaces: 2 });
      console.log('✅ Created version.json');

      // Create ZIP package
      console.log('📦 Creating ZIP package...');
      const zip = new AdmZip();
      
      // Add all files from temp directory to ZIP
      const tempFiles = await fs.readdir(tempDir);
      for (const file of tempFiles) {
        const filePath = path.join(tempDir, file);
        const stats = await fs.stat(filePath);
        
        if (stats.isFile()) {
          zip.addLocalFile(filePath);
        }
      }

      // Write ZIP file
      const zipFileName = `mcp-server-v${version}.zip`;
      const zipFilePath = path.join(outputDir, zipFileName);
      zip.writeZip(zipFilePath);

      // Calculate checksum
      const zipBuffer = await fs.readFile(zipFilePath);
      const checksum = crypto.createHash('sha256').update(zipBuffer).digest('hex');

      // Create metadata file
      const metadata = {
        version: version,
        filename: zipFileName,
        size: zipBuffer.length,
        checksum: checksum,
        created: new Date().toISOString(),
        files_count: tempFiles.length
      };

      const metadataPath = path.join(outputDir, `mcp-server-v${version}.json`);
      await fs.writeJson(metadataPath, metadata, { spaces: 2 });

      console.log('\n🎉 Update package created successfully!');
      console.log(`📦 Package: ${zipFilePath}`);
      console.log(`📋 Metadata: ${metadataPath}`);
      console.log(`📏 Size: ${(zipBuffer.length / 1024).toFixed(2)} KB`);
      console.log(`🔐 SHA256: ${checksum}`);
      console.log(`📁 Files: ${tempFiles.length}`);

      // Create upload instructions
      const uploadInstructions = `
# Upload Instructions for MCP Server v${version}

## Files to upload to createlex.com/api/mcp-updates/:

1. **Update Package**: ${zipFileName}
   - Upload to: /api/mcp-updates/download/${version}
   - Set header: x-checksum: ${checksum}

2. **Metadata**: ${JSON.stringify(metadata, null, 2)}
   - Add to version database
   - Update /api/mcp-updates/check endpoint

## API Endpoints to update:

### GET /api/mcp-updates/check?current_version=X.X.X
Response:
\`\`\`json
{
  "hasUpdate": true,
  "latestVersion": "${version}",
  "updateInfo": {
    "size": ${zipBuffer.length},
    "description": "CreateLex MCP Server v${version}",
    "releaseNotes": "Add your release notes here"
  }
}
\`\`\`

### GET /api/mcp-updates/download/${version}
- Return the ZIP file with x-checksum header
- Content-Type: application/zip
- x-checksum: ${checksum}
`;

      await fs.writeFile(path.join(outputDir, `upload-instructions-v${version}.md`), uploadInstructions);
      console.log(`📝 Upload instructions: upload-instructions-v${version}.md`);

    } finally {
      // Clean up temp directory
      if (await fs.pathExists(tempDir)) {
        await fs.remove(tempDir);
      }
    }

  } catch (error) {
    console.error('❌ Failed to create update package:', error.message);
    process.exit(1);
  }
}

// Run the script
createUpdatePackage(); 