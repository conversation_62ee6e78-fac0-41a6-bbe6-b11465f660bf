FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies including curl
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first (for better caching)
COPY requirements-cloud.txt .

# Install dependencies
RUN pip install --no-cache-dir -r requirements-cloud.txt

# Copy the MCP wrapper and bridge servers
COPY cloud_mcp_wrapper.py .
COPY cloud_mcp_bridge.py .
COPY cloud_mcp_client.py .
COPY mcp_server.py .

# Create a health check script
RUN echo '#!/bin/bash\ncurl -f http://localhost:8000/health || exit 1' > /app/healthcheck.sh
RUN chmod +x /app/healthcheck.sh

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD /app/healthcheck.sh || exit 1

# Expose port
EXPOSE 8000

# Use the cloud MCP wrapper
CMD ["python", "cloud_mcp_wrapper.py"] 