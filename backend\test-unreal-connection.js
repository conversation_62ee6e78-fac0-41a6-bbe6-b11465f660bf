// Simple test script to verify Unreal Engine connection
const TcpBridge = require('./src/services/tcpBridge');

// Create a new TcpBridge instance
const tcpBridge = new TcpBridge();

// Wait for connection to establish
setTimeout(() => {
  console.log('Sending test handshake command...');
  
  // Test handshake command
  tcpBridge.sendCommand({ 
    command: 'test_unreal_connection' 
  }, (result) => {
    console.log('Handshake result:', result);
    
    if (result.success) {
      console.log('Handshake successful, now trying to spawn a cube...');
      
      // Try to spawn a cube
      tcpBridge.sendCommand({ 
        command: 'spawn_object',
        params: {
          actor_class: 'Cube',
          location: [0, 0, 100],
          rotation: [0, 0, 0],
          scale: [1, 1, 1],
          actor_label: 'TestCube_' + Date.now()
        }
      }, (spawnResult) => {
        console.log('Spawn result:', spawnResult);
        
        if (spawnResult.success) {
          console.log('<PERSON><PERSON> spawned successfully, now trying to create a red material...');
          
          const materialName = 'RedMaterial_' + Date.now();
          
          // Create a red material
          tcpBridge.sendCommand({
            command: 'create_material',
            params: {
              material_name: materialName,
              color: [1, 0, 0]
            }
          }, (materialResult) => {
            console.log('Material creation result:', materialResult);
            
            if (materialResult.success) {
              console.log('Material created successfully, now trying to apply it to the cube...');
              
              // Apply the material to the cube
              tcpBridge.sendCommand({
                command: 'modify_object',
                params: {
                  actor_name: spawnResult.actor_label || 'TestCube',
                  property_type: 'material',
                  value: `/Game/Materials/${materialName}`
                }
              }, (applyResult) => {
                console.log('Material application result:', applyResult);
                console.log('Test complete!');
                process.exit(0);
              });
            } else {
              console.log('Failed to create material, test incomplete.');
              process.exit(1);
            }
          });
        } else {
          console.log('Failed to spawn cube, test incomplete.');
          process.exit(1);
        }
      });
    } else {
      console.log('Handshake failed, test incomplete.');
      process.exit(1);
    }
  });
}, 2000); // Wait 2 seconds for connection to establish
