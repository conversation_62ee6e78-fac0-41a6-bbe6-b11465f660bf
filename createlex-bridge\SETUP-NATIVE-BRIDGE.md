# CreateLex Native Bridge Setup

## What Changed

Your Claude Desktop now connects **through** the native CreateLex Bridge app instead of directly to the Python server. This gives you centralized control over all MCP connections.

## New Architecture Flow

```
Claude Desktop → claude-bridge-native.js → Native Bridge App (localhost:9877) → Python MCP Server → Unreal Engine
```

## Setup Steps

### 1. Update Claude Desktop Configuration

Your `claude_desktop_config.json` has been updated to use the new native bridge:

```json
{
  "mcpServers": {
    "createlex-unreal": {
      "command": "node",
      "args": ["C:\\Dev\\AiWebplatform\\createlex-bridge\\claude-bridge-native.js"],
      "env": {}
    }
  }
}
```

### 2. Start the Native Bridge App

**IMPORTANT**: You must now start the native app and enable the MCP server:

```bash
cd createlex-bridge
npm start
```

1. Login with your CreateLex credentials
2. Click "🌐 Enable Native MCP Button" in the dashboard
3. Verify the system tray shows "MCP Server Running (Port 9877)"

### 3. Test the Connection

Test that everything works:

```bash
npm run test-bridge
```

This should show:
- ✅ Successfully connected to native bridge on localhost:9877
- 📥 Received response from bridge

### 4. Use Claude Desktop

Now when you open Claude Desktop, it will:
1. Launch `claude-bridge-native.js`
2. Connect to your running native bridge app on port 9877
3. Access all 21+ Unreal Engine tools through the centralized bridge

## Benefits of Native Bridge

- **Centralized Control**: All IDE connections go through one managed server
- **Visual Management**: GUI dashboard to start/stop/monitor the MCP server
- **System Tray**: Background operation with easy controls
- **Connection Status**: See how many clients are connected
- **Unified Logging**: All MCP activity visible in one place

## Troubleshooting

### Claude Desktop Shows Connection Errors

1. **Check Native App**: Ensure CreateLex Bridge is running (`npm start`)
2. **Enable MCP**: Click the "🌐 Enable Native MCP Button" in dashboard
3. **Check Port**: Verify system tray shows "MCP Server Running (Port 9877)"
4. **Test Connection**: Run `npm run test-bridge` to verify bridge works

### Bridge Connection Refused

```
❌ Connection refused - Native bridge app is not running
```

**Solution**: 
1. Start the native app: `npm start`
2. Login and enable MCP server in dashboard
3. Wait for "MCP Server Running" status in system tray

### Python Server Issues

Check the Electron app console for Python server errors:
- Unicode encoding issues
- Missing dependencies
- Port conflicts

## File Changes Made

- ✅ **claude-bridge-native.js**: New bridge that connects to native app
- ✅ **src/mcp/bridge-server.js**: New bridge server handling TCP + stdio
- ✅ **main.js**: Updated to use bridge server
- ✅ **claude_desktop_config.json**: Updated to use native bridge
- ✅ **test-native-bridge.js**: Test script for connection verification

## Next Steps

1. **Start the native app**: `npm start`
2. **Enable MCP server**: Click the button in dashboard
3. **Test Claude Desktop**: Should now work through native bridge
4. **Monitor connections**: Check system tray and dashboard for status

The native bridge is now the central hub for all your AI IDE connections to Unreal Engine tools! 