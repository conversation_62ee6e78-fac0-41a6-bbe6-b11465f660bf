'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';

// Type declaration for bridge API
declare global {
  interface Window {
    bridgeApi?: {
      startMCP: (authData?: { token: string }) => Promise<{ success: boolean; error?: string; status?: any }>;
      stopMCP: () => Promise<{ success: boolean; error?: string; status?: any }>;
      getMCPStatus: () => Promise<{ isRunning: boolean }>;
    };
  }
}

interface UnrealControlPanelProps {
  hasActiveSubscription: boolean;
  isRefreshing?: boolean;
}

export default function UnrealControlPanel({ hasActiveSubscription, isRefreshing }: UnrealControlPanelProps) {
  const { token } = useAuth();
  const [serverStatus, setServerStatus] = useState<'stopped' | 'starting' | 'running' | 'error'>('stopped');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isBridgeMode, setIsBridgeMode] = useState(false);

  // Check if we're in bridge mode (Atom app)
  useEffect(() => {
    const checkBridgeMode = () => {
      const hasBridgeApi = typeof window !== 'undefined' && 
                          window.bridgeApi && 
                          typeof window.bridgeApi.startMCP === 'function';
      setIsBridgeMode(!!hasBridgeApi);
      
      if (hasBridgeApi) {
        // Get initial server status in bridge mode
        checkServerStatus();
      }
    };

    checkBridgeMode();
  }, []);

  const checkServerStatus = async () => {
    if (!isBridgeMode || !window.bridgeApi) return;
    
    try {
      const status = await window.bridgeApi.getMCPStatus();
      setServerStatus(status.isRunning ? 'running' : 'stopped');
    } catch (error) {
      console.error('Failed to check server status:', error);
      setServerStatus('error');
    }
  };

  const handleStartServer = async () => {
    if (!hasActiveSubscription) {
      setError('Active subscription required to start MCP server');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      if (isBridgeMode && window.bridgeApi) {
        // Bridge mode - real server control
        setServerStatus('starting');
        const authData = token ? { token } : undefined;
        console.log('🚀 Starting MCP server with auth data:', authData ? 'Token provided' : 'No token');
        const result = await window.bridgeApi.startMCP(authData);
        
        if (result.success) {
          setServerStatus('running');
          console.log('✅ MCP server started successfully');
        } else {
          setServerStatus('error');
          setError(result.error || 'Failed to start server');
          console.error('❌ MCP server start failed:', result.error);
        }
      } else {
        // Web mode - simulated behavior
        setServerStatus('starting');
        await new Promise(resolve => setTimeout(resolve, 2000));
        setServerStatus('running');
      }
    } catch (error) {
      console.error('Failed to start MCP server:', error);
      setServerStatus('error');
      setError(error instanceof Error ? error.message : 'Failed to start server');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStopServer = async () => {
    setIsLoading(true);
    setError(null);

    try {
      if (isBridgeMode && window.bridgeApi) {
        // Bridge mode - real server control
        const result = await window.bridgeApi.stopMCP();
        
        if (result.success) {
          setServerStatus('stopped');
        } else {
          setError(result.error || 'Failed to stop server');
        }
      } else {
        // Web mode - simulated behavior
        await new Promise(resolve => setTimeout(resolve, 1000));
        setServerStatus('stopped');
      }
    } catch (error) {
      console.error('Failed to stop MCP server:', error);
      setError(error instanceof Error ? error.message : 'Failed to stop server');
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = () => {
    switch (serverStatus) {
      case 'running': return 'text-green-600';
      case 'starting': return 'text-yellow-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusText = () => {
    switch (serverStatus) {
      case 'running': return 'Running';
      case 'starting': return 'Starting...';
      case 'error': return 'Error';
      default: return 'Stopped';
    }
  };

  const getStatusIcon = () => {
    switch (serverStatus) {
      case 'running':
        return (
          <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
        );
      case 'starting':
        return (
          <div className="h-2 w-2 bg-yellow-500 rounded-full animate-pulse"></div>
        );
      case 'error':
        return (
          <div className="h-2 w-2 bg-red-500 rounded-full"></div>
        );
      default:
        return (
          <div className="h-2 w-2 bg-gray-400 rounded-full"></div>
        );
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div className="p-6">
        <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center text-blue-600 mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </div>
        
        <h3 className="text-lg font-semibold mb-2">Unreal Engine Control Panel</h3>
        <p className="text-gray-600 mb-4">
          {isBridgeMode 
            ? "Control your MCP server for Unreal Engine integration."
            : "MCP server control (requires desktop app for full functionality)."
          }
        </p>

        {/* Subscription Status */}
        <div className="mb-4 p-3 rounded-lg border">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Subscription Status</span>
            {isRefreshing ? (
              <div className="flex items-center text-yellow-600">
                <svg className="animate-spin h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span className="text-sm">Checking...</span>
              </div>
            ) : hasActiveSubscription ? (
              <div className="flex items-center text-green-600">
                <div className="h-2 w-2 bg-green-500 rounded-full mr-2"></div>
                <span className="text-sm">Active</span>
              </div>
            ) : (
              <div className="flex items-center text-red-600">
                <div className="h-2 w-2 bg-red-500 rounded-full mr-2"></div>
                <span className="text-sm">Expired</span>
              </div>
            )}
          </div>
        </div>

        {/* Server Status */}
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">MCP Server Status</span>
            <div className="flex items-center">
              {getStatusIcon()}
              <span className={`text-sm ml-2 ${getStatusColor()}`}>
                {getStatusText()}
              </span>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-700">{error}</p>
          </div>
        )}

        {/* Control Buttons */}
        <div className="flex space-x-2">
          {serverStatus === 'stopped' || serverStatus === 'error' ? (
            <button
              onClick={handleStartServer}
              disabled={isLoading || !hasActiveSubscription || isRefreshing}
              className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                hasActiveSubscription && !isRefreshing
                  ? 'bg-green-600 text-white hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed'
                  : 'bg-gray-300 text-gray-600 cursor-not-allowed'
              }`}
            >
              {isLoading ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Starting MCP Server...
                </span>
              ) : (
                'Start MCP Server'
              )}
            </button>
          ) : (
            <button
              onClick={handleStopServer}
              disabled={isLoading}
              className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700 disabled:opacity-50 transition-colors"
            >
              {isLoading ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Stopping...
                </span>
              ) : (
                'Stop Server'
              )}
            </button>
          )}
        </div>

        {/* Subscription Required Message */}
        {!hasActiveSubscription && !isRefreshing && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm text-yellow-800">
              An active subscription is required to use the MCP server.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}