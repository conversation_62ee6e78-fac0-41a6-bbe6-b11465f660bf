const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const authService = require('./supabaseAuthService');
const supabase = require('./supabaseClient');

class SupabaseSubscriptionService {
  constructor() {
    // Price IDs for different tiers
    this.PRICE_ID_BASIC = process.env.STRIPE_PRICE_ID_BASIC; // $20/month tier
    this.PRICE_ID_PRO = process.env.STRIPE_PRICE_ID_PRO; // $30/month tier
    this.PRICE_ID = process.env.STRIPE_PRICE_ID; // Default price ID (for backward compatibility)

    this.PRODUCT_NAME = 'AI Unreal Engine Assistant';
    this.CURRENCY = 'usd';

    // Plan details
    this.PLANS = {
      basic: {
        name: 'Basic',
        priceId: this.PRICE_ID_BASIC,
        amount: 2000, // $20.00
        features: ['Basic AI assistance', 'Limited API calls', 'Standard support']
      },
      pro: {
        name: 'Pro',
        priceId: this.PRICE_ID_PRO,
        amount: 3000, // $30.00
        features: ['Advanced AI assistance', 'Unlimited API calls', 'Priority support', 'Custom blueprints']
      }
    };

    // Initialize Stripe
    console.log('Stripe initialized with secret key:', process.env.STRIPE_SECRET_KEY ? process.env.STRIPE_SECRET_KEY.substring(0, 10) + '...' : 'Not set');
    console.log('Subscription service initialized with plans:', {
      basic: {
        priceId: this.PLANS.basic.priceId,
        amount: this.PLANS.basic.amount
      },
      pro: {
        priceId: this.PLANS.pro.priceId,
        amount: this.PLANS.pro.amount
      },
      default: this.PRICE_ID
    });
  }

  // Find a Stripe customer by email
  async findCustomerByEmail(email) {
    try {
      if (!email) {
        console.log('No email provided to find customer');
        return null;
      }

      console.log(`Searching for Stripe customer with email: ${email}`);

      const customers = await stripe.customers.list({
        email: email,
        limit: 1
      });

      if (customers.data.length > 0) {
        console.log(`Found Stripe customer with ID: ${customers.data[0].id}`);
        return customers.data[0];
      }

      console.log(`No Stripe customer found with email: ${email}`);
      return null;
    } catch (error) {
      console.error('Error finding Stripe customer by email:', error);
      return null;
    }
  }

  // Find active subscriptions for a customer
  async findActiveSubscriptions(customerId) {
    try {
      if (!customerId) {
        console.log('No customer ID provided to find subscriptions');
        return [];
      }

      console.log(`Searching for active subscriptions for customer: ${customerId}`);

      const subscriptions = await stripe.subscriptions.list({
        customer: customerId,
        status: 'active',
        limit: 10
      });

      console.log(`Found ${subscriptions.data.length} active subscriptions for customer ${customerId}`);
      return subscriptions.data;
    } catch (error) {
      console.error('Error finding active subscriptions:', error);
      return [];
    }
  }

  // Create a Stripe customer for a user
  async createCustomer(user) {
    try {
      // Always use the actual user information from authentication
      // with fallbacks only if the data is missing
      const email = user.email || '<EMAIL>';
      const name = user.name || 'Unknown User';

      console.log(`Creating Stripe customer for user:`, {
        id: user.id,
        email: email,
        name: name
      });

      const customer = await stripe.customers.create({
        email: email,
        name: name,
        metadata: {
          userId: user.id
        }
      });

      console.log(`Stripe customer created successfully:`, {
        customerId: customer.id,
        email: customer.email,
        name: customer.name
      });

      return customer;
    } catch (error) {
      console.error('Error creating Stripe customer:', error);
      throw new Error(`Failed to create customer: ${error.message}`);
    }
  }

  // Create a checkout session for subscription
  async createCheckoutSession(userId, successUrl, cancelUrl, planType = 'pro', couponCode = null) {
    try {
      if (!userId) {
        console.error('No user ID provided to create checkout session');
        throw new Error('User ID is required');
      }

      console.log(`Creating checkout session for user ${userId} with plan: ${planType}`);
      console.log(`Success URL: ${successUrl}`);
      console.log(`Cancel URL: ${cancelUrl}`);
      if (couponCode) {
        console.log(`Coupon Code: ${couponCode}`);
      }

      const user = await authService.getUserById(userId);

      if (!user) {
        console.error(`User not found with ID: ${userId}`);
        throw new Error('User not found');
      }

      console.log(`Found user: ${user.email}`);

      // Always check for existing customer in Stripe by email first
      let customerId = null;
      let stripeCustomer = null;

      if (user.email) {
        console.log(`Checking for existing Stripe customer with email: ${user.email}`);
        stripeCustomer = await this.findCustomerByEmail(user.email);

        if (stripeCustomer) {
          console.log(`Found existing Stripe customer with ID: ${stripeCustomer.id} for email: ${user.email}`);
          customerId = stripeCustomer.id;

          // Update user with the found Stripe customer ID
          await authService.updateStripeCustomerId(userId, customerId);
        }
      }

      // If no customer found by email, create a new one
      if (!customerId) {
        console.log(`No existing Stripe customer found, creating new customer for user ${userId}`);
        stripeCustomer = await this.createCustomer(user);
        customerId = stripeCustomer.id;

        console.log(`Created new Stripe customer with ID: ${customerId}`);

        // Update user with Stripe customer ID
        await authService.updateStripeCustomerId(userId, customerId);
      } else {
        console.log(`Using existing Stripe customer with ID: ${customerId}`);
      }

      // Check for any existing subscriptions for this customer
      try {
        console.log(`Checking for existing subscriptions for customer ${customerId}`);
        const subscriptions = await stripe.subscriptions.list({
          customer: customerId,
          limit: 10
        });

        // If there are any subscriptions, check their status
        if (subscriptions.data.length > 0) {
          console.log(`Found ${subscriptions.data.length} subscriptions for customer ${customerId}`);

          // Check if any of them are active
          const activeSubscription = subscriptions.data.find(sub => sub.status === 'active');
          if (activeSubscription) {
            console.log(`Customer ${customerId} has an active subscription ${activeSubscription.id}`);

            // Update user with subscription ID and status
            await authService.updateSubscription(userId, 'active', activeSubscription.id);

            // Return the active subscription details
            return {
              url: successUrl,
              alreadySubscribed: true,
              message: 'User already has an active subscription'
            };
          }

          // If there are canceled subscriptions, we can proceed with creating a new one
          console.log(`No active subscriptions found for customer ${customerId}, proceeding with checkout`);
        } else {
          console.log(`No subscriptions found for customer ${customerId}, proceeding with checkout`);
        }
      } catch (error) {
        console.error(`Error checking existing subscriptions:`, error);
        // Continue with creating a new checkout session
      }

      // Determine which plan to use
      const plan = planType === 'basic' ? this.PLANS.basic : this.PLANS.pro;
      const priceId = plan.priceId;

      if (!priceId) {
        console.error(`No price ID found for plan type: ${planType}`);
        throw new Error(`Invalid plan type: ${planType}`);
      }

      // Validate coupon code if provided
      let discounts = [];
      if (couponCode) {
        try {
          // Verify the coupon exists and is valid
          const coupon = await stripe.coupons.retrieve(couponCode);
          console.log(`Coupon found: ${couponCode}`, coupon);

          // Add the coupon to the checkout session
          discounts.push({
            coupon: couponCode
          });
        } catch (error) {
          console.error(`Error retrieving coupon ${couponCode}:`, error);
          // Continue without the coupon if it's invalid
        }
      }

      // Create checkout session
      console.log(`Creating Stripe checkout session with:`, {
        customer: customerId,
        mode: 'subscription',
        plan: planType,
        priceId: priceId,
        amount: plan.amount,
        currency: this.CURRENCY,
        productName: this.PRODUCT_NAME,
        coupon: couponCode || 'none'
      });

      // Prepare checkout session parameters
      const sessionParams = {
        payment_method_types: ['card'],
        line_items: [
          {
            price: priceId,
            quantity: 1
          }
        ],
        mode: 'subscription',
        success_url: successUrl,
        cancel_url: cancelUrl,
        customer: customerId,
        client_reference_id: userId,
        metadata: {
          planType: planType
        },
        allow_promotion_codes: true // Allow users to enter promo codes on the checkout page
      };

      // Add discounts if we have a valid coupon
      if (discounts.length > 0) {
        sessionParams.discounts = discounts;
      }

      const session = await stripe.checkout.sessions.create(sessionParams);

      console.log(`Checkout session created successfully:`, {
        sessionId: session.id,
        url: session.url,
        planType: planType,
        couponApplied: discounts.length > 0
      });

      // Store the checkout session ID and plan type for reference
      await authService.updateLastCheckoutSessionId(userId, session.id);

      // Store the selected plan type
      try {
        const { error } = await supabase
          .from('users')
          .update({ selected_plan_type: planType })
          .eq('id', userId);

        if (error) {
          console.error(`Error updating selected plan type:`, error);
        }
      } catch (error) {
        console.error(`Error updating selected plan type:`, error);
      }

      return session;
    } catch (error) {
      console.error('Error creating checkout session:', error);
      throw new Error(`Failed to create checkout session: ${error.message}`);
    }
  }

  // Handle webhook events from Stripe
  async handleWebhookEvent(event) {
    try {
      console.log(`Received Stripe webhook event: ${event.type}`);

      switch (event.type) {
        case 'checkout.session.completed':
          return await this.handleCheckoutCompleted(event.data.object);

        case 'customer.subscription.created':
          return await this.handleSubscriptionCreated(event.data.object);

        case 'customer.subscription.updated':
          return await this.handleSubscriptionUpdated(event.data.object);

        case 'customer.subscription.deleted':
          return await this.handleSubscriptionDeleted(event.data.object);

        case 'customer.subscription.paused':
          return await this.handleSubscriptionPaused(event.data.object);

        case 'customer.subscription.resumed':
          return await this.handleSubscriptionResumed(event.data.object);

        case 'customer.subscription.trial_will_end':
          return await this.handleSubscriptionTrialWillEnd(event.data.object);

        case 'invoice.payment_succeeded':
          return await this.handleInvoicePaymentSucceeded(event.data.object);

        case 'invoice.payment_failed':
          return await this.handleInvoicePaymentFailed(event.data.object);

        default:
          console.log(`Unhandled event type: ${event.type}`);
          return { status: 'ignored', event: event.type };
      }
    } catch (error) {
      console.error('Error handling webhook event:', error);
      throw new Error('Failed to process webhook event');
    }
  }

  // Handle checkout.session.completed event
  async handleCheckoutCompleted(session) {
    const userId = session.client_reference_id;
    const subscriptionId = session.subscription;
    const customerId = session.customer;

    console.log(`Handling checkout completed for user ${userId}:`, {
      subscriptionId,
      customerId
    });

    if (!userId) {
      console.error('No user ID in checkout session');
      return {
        status: 'failed',
        reason: 'missing_user_id',
        session_id: session.id
      };
    }

    // Get the user
    const user = await authService.getUserById(userId);

    if (!user) {
      console.error(`User not found with ID: ${userId}`);
      return {
        status: 'failed',
        reason: 'user_not_found',
        userId
      };
    }

    console.log(`Found user: ${user.email}`);

    // Update user's Stripe customer ID if it's different
    if (customerId && user.stripeCustomerId !== customerId) {
      console.log(`Updating Stripe customer ID for user ${userId} from ${user.stripeCustomerId || 'none'} to ${customerId}`);
      await authService.updateStripeCustomerId(userId, customerId);
    }

    // Update user subscription status
    console.log(`Updating subscription status for user ${userId} to active with subscription ID ${subscriptionId}`);
    const updated = await authService.updateSubscription(userId, 'active', subscriptionId);

    return {
      status: updated ? 'success' : 'failed',
      userId,
      subscriptionId,
      customerId
    };
  }

  // Find user by subscription ID or customer ID in Supabase
  async findUserBySubscriptionOrCustomer(subscriptionId, customerId) {
    try {
      let user = null;

      // First try to find user with this subscription ID
      if (subscriptionId) {
        const { data: userBySubscription, error: subError } = await supabase
          .from('users')
          .select('*')
          .eq('subscription_id', subscriptionId)
          .single();

        if (!subError && userBySubscription) {
          console.log(`Found user ${userBySubscription.id} by subscription ID ${subscriptionId}`);
          return {
            id: userBySubscription.id,
            email: userBySubscription.email,
            name: userBySubscription.name,
            picture: userBySubscription.picture,
            subscriptionStatus: userBySubscription.subscription_status,
            stripeCustomerId: userBySubscription.stripe_customer_id,
            subscriptionId: userBySubscription.subscription_id
          };
        }
      }

      // If no user found by subscription ID, try to find by customer ID
      if (customerId) {
        const { data: userByCustomer, error: custError } = await supabase
          .from('users')
          .select('*')
          .eq('stripe_customer_id', customerId)
          .single();

        if (!custError && userByCustomer) {
          console.log(`Found user ${userByCustomer.id} by customer ID ${customerId}`);

          // If found by customer ID but subscription ID doesn't match, update it
          if (subscriptionId && userByCustomer.subscription_id !== subscriptionId) {
            console.log(`Updating subscription ID to ${subscriptionId}`);
            await authService.updateSubscription(userByCustomer.id, userByCustomer.subscription_status, subscriptionId);
          }

          return {
            id: userByCustomer.id,
            email: userByCustomer.email,
            name: userByCustomer.name,
            picture: userByCustomer.picture,
            subscriptionStatus: userByCustomer.subscription_status,
            stripeCustomerId: userByCustomer.stripe_customer_id,
            subscriptionId: userByCustomer.subscription_id
          };
        }
      }

      // If still no user found, try to find by email using Stripe customer
      if (customerId) {
        try {
          const customer = await stripe.customers.retrieve(customerId);
          if (customer && customer.email) {
            const { data: userByEmail, error: emailError } = await supabase
              .from('users')
              .select('*')
              .eq('email', customer.email)
              .single();

            if (!emailError && userByEmail) {
              console.log(`Found user ${userByEmail.id} by email ${customer.email}`);

              // Update customer ID and subscription ID if needed
              if (userByEmail.stripe_customer_id !== customerId) {
                console.log(`Updating Stripe customer ID to ${customerId}`);
                await authService.updateStripeCustomerId(userByEmail.id, customerId);
              }

              if (subscriptionId && userByEmail.subscription_id !== subscriptionId) {
                console.log(`Updating subscription ID to ${subscriptionId}`);
                await authService.updateSubscription(userByEmail.id, userByEmail.subscription_status, subscriptionId);
              }

              return {
                id: userByEmail.id,
                email: userByEmail.email,
                name: userByEmail.name,
                picture: userByEmail.picture,
                subscriptionStatus: userByEmail.subscription_status,
                stripeCustomerId: customerId,
                subscriptionId: subscriptionId || userByEmail.subscription_id
              };
            }
          }
        } catch (error) {
          console.error(`Error retrieving customer ${customerId}:`, error);
        }
      }

      return null;
    } catch (error) {
      console.error('Error finding user by subscription or customer:', error);
      return null;
    }
  }

  // Handle customer.subscription.updated event
  async handleSubscriptionUpdated(subscription) {
    const customerId = subscription.customer;
    const subscriptionId = subscription.id;

    console.log(`Handling subscription updated:`, {
      subscriptionId,
      customerId,
      status: subscription.status
    });

    const user = await this.findUserBySubscriptionOrCustomer(subscriptionId, customerId);

    if (!user) {
      console.log(`No user found for subscription ${subscriptionId} with customer ${customerId}`);
      return { status: 'ignored', reason: 'user_not_found' };
    }

    // Update subscription status
    const status = subscription.status === 'active' ? 'active' : 'inactive';
    const updated = await authService.updateSubscription(user.id, status, subscription.id);

    return {
      status: updated ? 'success' : 'failed',
      userId: user.id,
      subscriptionStatus: status,
      customerId
    };
  }

  // Handle customer.subscription.created event
  async handleSubscriptionCreated(subscription) {
    const customerId = subscription.customer;
    const subscriptionId = subscription.id;
    const status = subscription.status;

    console.log(`Handling subscription created:`, {
      subscriptionId,
      customerId,
      status
    });

    const user = await this.findUserBySubscriptionOrCustomer(subscriptionId, customerId);

    if (!user) {
      console.log(`No user found for subscription ${subscriptionId} with customer ${customerId}`);
      return { status: 'ignored', reason: 'user_not_found' };
    }

    // Update user subscription status
    const subscriptionStatus = status === 'active' ? 'active' : 'inactive';
    console.log(`Updating subscription status for user ${user.id} to ${subscriptionStatus}`);
    const updated = await authService.updateSubscription(user.id, subscriptionStatus, subscriptionId);

    return {
      status: updated ? 'success' : 'failed',
      userId: user.id,
      subscriptionId,
      customerId,
      subscriptionStatus
    };
  }

  // Handle customer.subscription.deleted event
  async handleSubscriptionDeleted(subscription) {
    const customerId = subscription.customer;
    const subscriptionId = subscription.id;

    console.log(`Handling subscription deleted:`, {
      subscriptionId,
      customerId
    });

    const user = await this.findUserBySubscriptionOrCustomer(subscriptionId, customerId);

    if (!user) {
      console.log(`No user found for subscription ${subscriptionId} with customer ${customerId}`);
      return { status: 'ignored', reason: 'user_not_found' };
    }

    // Update subscription status
    const updated = await authService.updateSubscription(user.id, 'canceled', null);

    return {
      status: updated ? 'success' : 'failed',
      userId: user.id,
      subscriptionStatus: 'canceled',
      customerId
    };
  }

  // Handle customer.subscription.paused event
  async handleSubscriptionPaused(subscription) {
    const customerId = subscription.customer;
    const subscriptionId = subscription.id;

    console.log(`Handling subscription paused:`, {
      subscriptionId,
      customerId
    });

    const user = await this.findUserBySubscriptionOrCustomer(subscriptionId, customerId);

    if (!user) {
      console.log(`No user found for subscription ${subscriptionId} with customer ${customerId}`);
      return { status: 'ignored', reason: 'user_not_found' };
    }

    // Update user subscription status
    console.log(`Updating subscription status for user ${user.id} to paused`);
    // We'll use 'inactive' for paused subscriptions in our system
    const updated = await authService.updateSubscription(user.id, 'inactive', subscriptionId);

    return {
      status: updated ? 'success' : 'failed',
      userId: user.id,
      subscriptionId,
      customerId
    };
  }

  // Handle customer.subscription.resumed event
  async handleSubscriptionResumed(subscription) {
    const customerId = subscription.customer;
    const subscriptionId = subscription.id;

    console.log(`Handling subscription resumed:`, {
      subscriptionId,
      customerId
    });

    const user = await this.findUserBySubscriptionOrCustomer(subscriptionId, customerId);

    if (!user) {
      console.log(`No user found for subscription ${subscriptionId} with customer ${customerId}`);
      return { status: 'ignored', reason: 'user_not_found' };
    }

    // Update user subscription status
    console.log(`Updating subscription status for user ${user.id} to active`);
    const updated = await authService.updateSubscription(user.id, 'active', subscriptionId);

    return {
      status: updated ? 'success' : 'failed',
      userId: user.id,
      subscriptionId,
      customerId
    };
  }

  // Handle customer.subscription.trial_will_end event
  async handleSubscriptionTrialWillEnd(subscription) {
    const customerId = subscription.customer;
    const subscriptionId = subscription.id;
    const trialEnd = subscription.trial_end;

    console.log(`Handling subscription trial will end:`, {
      subscriptionId,
      customerId,
      trialEnd: new Date(trialEnd * 1000).toISOString()
    });

    const user = await this.findUserBySubscriptionOrCustomer(subscriptionId, customerId);

    if (!user) {
      console.log(`No user found for subscription ${subscriptionId} with customer ${customerId}`);
      return { status: 'ignored', reason: 'user_not_found' };
    }

    // We could send an email notification here
    // For now, just log it
    console.log(`Trial ending soon for user ${user.id}, subscription ${subscriptionId}`);

    return {
      status: 'success',
      userId: user.id,
      subscriptionId,
      customerId,
      trialEnd: new Date(trialEnd * 1000).toISOString()
    };
  }

  // Handle invoice.payment_succeeded event
  async handleInvoicePaymentSucceeded(invoice) {
    const customerId = invoice.customer;
    const subscriptionId = invoice.subscription;

    if (!subscriptionId) {
      console.log(`Invoice ${invoice.id} is not for a subscription`);
      return { status: 'ignored', reason: 'not_subscription_invoice' };
    }

    console.log(`Handling invoice payment succeeded:`, {
      invoiceId: invoice.id,
      customerId,
      subscriptionId,
      amount: invoice.amount_paid
    });

    const user = await this.findUserBySubscriptionOrCustomer(subscriptionId, customerId);

    if (!user) {
      console.log(`No user found for subscription ${subscriptionId} with customer ${customerId}`);
      return { status: 'ignored', reason: 'user_not_found' };
    }

    // Update user subscription status to active
    console.log(`Updating subscription status for user ${user.id} to active`);
    const updated = await authService.updateSubscription(user.id, 'active', subscriptionId);

    return {
      status: updated ? 'success' : 'failed',
      userId: user.id,
      subscriptionId,
      customerId,
      invoiceId: invoice.id
    };
  }

  // Handle invoice.payment_failed event
  async handleInvoicePaymentFailed(invoice) {
    const customerId = invoice.customer;
    const subscriptionId = invoice.subscription;

    if (!subscriptionId) {
      console.log(`Invoice ${invoice.id} is not for a subscription`);
      return { status: 'ignored', reason: 'not_subscription_invoice' };
    }

    console.log(`Handling invoice payment failed:`, {
      invoiceId: invoice.id,
      customerId,
      subscriptionId,
      attemptCount: invoice.attempt_count
    });

    const user = await this.findUserBySubscriptionOrCustomer(subscriptionId, customerId);

    if (!user) {
      console.log(`No user found for subscription ${subscriptionId} with customer ${customerId}`);
      return { status: 'ignored', reason: 'user_not_found' };
    }

    // We don't immediately mark the subscription as inactive because Stripe will retry the payment
    // But we could send an email notification here
    console.log(`Payment failed for user ${user.id}, subscription ${subscriptionId}`);

    // If this is the final attempt, mark the subscription as inactive
    if (invoice.attempt_count >= 3) {
      console.log(`Final payment attempt failed for user ${user.id}, marking subscription as inactive`);
      const updated = await authService.updateSubscription(user.id, 'inactive', subscriptionId);

      return {
        status: updated ? 'success' : 'failed',
        userId: user.id,
        subscriptionId,
        customerId,
        invoiceId: invoice.id,
        action: 'marked_inactive'
      };
    }

    return {
      status: 'success',
      userId: user.id,
      subscriptionId,
      customerId,
      invoiceId: invoice.id,
      attemptCount: invoice.attempt_count,
      action: 'notification_only'
    };
  }

  // Cancel a subscription in Stripe
  async cancelSubscription(subscriptionId) {
    try {
      if (!subscriptionId) {
        console.error('No subscription ID provided to cancel');
        throw new Error('Subscription ID is required');
      }

      console.log(`Canceling Stripe subscription: ${subscriptionId}`);

      // Cancel the subscription at period end to avoid prorated refunds
      const subscription = await stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: true
      });

      console.log(`Subscription ${subscriptionId} will be canceled at period end`);

      // For immediate cancellation, uncomment this:
      // const subscription = await stripe.subscriptions.cancel(subscriptionId);
      // console.log(`Subscription ${subscriptionId} canceled immediately`);

      return {
        success: true,
        status: subscription.status,
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        currentPeriodEnd: new Date(subscription.current_period_end * 1000).toISOString()
      };
    } catch (error) {
      console.error(`Error canceling subscription ${subscriptionId}:`, error);
      throw error;
    }
  }

  // Check if a user has an active subscription
  async checkSubscription(userId) {
    try {
      if (!userId) {
        console.error('No user ID provided to check subscription');
        return false;
      }

      console.log(`Checking subscription for user ID: ${userId}`);
      const user = await authService.getUserById(userId);

      if (!user) {
        console.error(`User not found with ID: ${userId}`);
        return false;
      }

      console.log(`Found user: ${user.email}`);

      // First check: If user has a subscription ID, check that subscription in Stripe
      if (user.subscriptionId) {
        console.log(`Checking Stripe subscription status for user ${userId} with subscription ID ${user.subscriptionId}`);

        try {
          const subscription = await stripe.subscriptions.retrieve(user.subscriptionId);

          // Update local subscription status if it's different
          const stripeStatus = subscription.status === 'active' ? 'active' : 'inactive';
          if (user.subscriptionStatus !== stripeStatus) {
            console.log(`Updating subscription status for user ${userId} from ${user.subscriptionStatus} to ${stripeStatus}`);
            await authService.updateSubscription(userId, stripeStatus, user.subscriptionId);
          }

          return subscription.status === 'active';
        } catch (stripeError) {
          console.error(`Error retrieving subscription from Stripe:`, stripeError);
          // Continue to next check if subscription not found
        }
      }

      // Second check: If user has a Stripe customer ID, check for active subscriptions
      if (user.stripeCustomerId) {
        console.log(`Checking for active subscriptions for customer ${user.stripeCustomerId}`);

        try {
          const subscriptions = await this.findActiveSubscriptions(user.stripeCustomerId);

          if (subscriptions.length > 0) {
            // Use the first active subscription
            const subscription = subscriptions[0];
            console.log(`Found active subscription ${subscription.id} for customer ${user.stripeCustomerId}`);

            // Update user with subscription ID and status
            await authService.updateSubscription(userId, 'active', subscription.id);
            return true;
          }
        } catch (stripeError) {
          console.error(`Error checking subscriptions for customer:`, stripeError);
          // Continue to next check
        }
      }

      // Third check: Try to find customer by email
      if (user.email) {
        console.log(`Checking for Stripe customer by email: ${user.email}`);

        try {
          const customer = await this.findCustomerByEmail(user.email);

          if (customer) {
            // Update user with Stripe customer ID
            await authService.updateStripeCustomerId(userId, customer.id);

            // Check for active subscriptions
            const subscriptions = await this.findActiveSubscriptions(customer.id);

            if (subscriptions.length > 0) {
              // Use the first active subscription
              const subscription = subscriptions[0];
              console.log(`Found active subscription ${subscription.id} for customer ${customer.id}`);

              // Update user with subscription ID and status
              await authService.updateSubscription(userId, 'active', subscription.id);
              return true;
            }
          }
        } catch (stripeError) {
          console.error(`Error checking customer by email:`, stripeError);
          // Fall back to local status
        }
      }

      // If all checks fail, fall back to local status
      console.log(`No active subscription found in Stripe for user ${userId}`);
      console.log(`Falling back to local subscription status: ${user.subscriptionStatus}`);
      return await authService.hasActiveSubscription(userId);
    } catch (error) {
      console.error('Error checking subscription status:', error);
      return false;
    }
  }

  // Get subscription details from Stripe
  async getSubscriptionDetails(subscriptionId) {
    try {
      console.log(`Getting subscription details for subscription ${subscriptionId}`);

      if (!subscriptionId) {
        console.log('No subscription ID provided');
        return null;
      }

      // Retrieve the subscription from Stripe
      const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
        expand: ['items.data.price']
      });

      console.log(`Retrieved subscription details for ${subscriptionId}:`, {
        status: subscription.status,
        priceId: subscription.items.data[0]?.price?.id || 'unknown'
      });

      return subscription;
    } catch (error) {
      console.error(`Error getting subscription details for ${subscriptionId}:`, error);
      return null;
    }
  }
}

module.exports = new SupabaseSubscriptionService();
