#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🍎 macOS MCP Dependencies Test');
console.log('==============================');

const requiredModules = [
    'fastmcp',
    'asyncio',
    'aiohttp',
    'requests',
    'websockets',
    'uvicorn',
    'flask',
    'pydantic',
    'dotenv',
    'json',
    'logging'
];

function testPythonModule(moduleName, pythonCmd = 'python3') {
    return new Promise((resolve) => {
        const testScript = `
import sys
try:
    import ${moduleName}
    print("✅ ${moduleName}: Available")
    if hasattr(${moduleName}, '__version__'):
        print("   Version: " + str(${moduleName}.__version__))
    elif hasattr(${moduleName}, 'version'):
        print("   Version: " + str(${moduleName}.version))
    sys.exit(0)
except ImportError as e:
    print("❌ ${moduleName}: Missing - " + str(e))
    sys.exit(1)
except Exception as e:
    print("⚠️  ${moduleName}: Error - " + str(e))
    sys.exit(2)
`;

        const child = spawn(pythonCmd, ['-c', testScript], {
            stdio: ['pipe', 'pipe', 'pipe']
        });

        let output = '';
        let errorOutput = '';

        child.stdout.on('data', (data) => {
            output += data.toString();
        });

        child.stderr.on('data', (data) => {
            errorOutput += data.toString();
        });

        child.on('close', (code) => {
            if (code === 0) {
                console.log(output.trim());
                resolve({ module: moduleName, status: 'ok', output });
            } else if (code === 1) {
                console.log(output.trim() || errorOutput.trim());
                resolve({ module: moduleName, status: 'missing', error: errorOutput });
            } else {
                console.log(output.trim() || errorOutput.trim());
                resolve({ module: moduleName, status: 'error', error: errorOutput });
            }
        });

        child.on('error', (error) => {
            console.log(`❌ ${moduleName}: System error - ${error.message}`);
            resolve({ module: moduleName, status: 'system_error', error: error.message });
        });
    });
}

async function testMCPServer() {
    console.log('\n🧪 Testing MCP Server Executable...');
    
    const mcpPath = path.join(__dirname, 'src/python-protected/mcp_server_mac');
    
    if (!fs.existsSync(mcpPath)) {
        console.log('❌ MCP server executable not found');
        return { status: 'missing' };
    }

    return new Promise((resolve) => {
        const child = spawn(mcpPath, [], {
            stdio: ['pipe', 'pipe', 'pipe'],
            timeout: 5000
        });

        let output = '';
        let errorOutput = '';

        child.stdout.on('data', (data) => {
            output += data.toString();
        });

        child.stderr.on('data', (data) => {
            errorOutput += data.toString();
        });

        child.on('close', (code) => {
            console.log('📊 MCP Server Test Results:');
            console.log(`   Exit code: ${code}`);
            if (output) console.log(`   Stdout: ${output.trim()}`);
            if (errorOutput) console.log(`   Stderr: ${errorOutput.trim()}`);
            
            // Check both stdout and stderr for subscription validation messages
            const allOutput = (output + ' ' + errorOutput).toLowerCase();
            
            if (allOutput.includes('subscription') || allOutput.includes('invalid') || output.includes('Invalid subscription')) {
                console.log('✅ MCP server starts correctly (subscription validation working)');
                resolve({ status: 'ok' });
            } else if (allOutput.includes('importerror') || allOutput.includes('modulenotfounderror')) {
                console.log('❌ MCP server has import errors');
                resolve({ status: 'import_error', error: errorOutput });
            } else {
                console.log('⚠️  Unexpected MCP server behavior');
                resolve({ status: 'unexpected', error: errorOutput });
            }
        });

        child.on('error', (error) => {
            console.log(`❌ Failed to run MCP server: ${error.message}`);
            resolve({ status: 'execution_error', error: error.message });
        });

        // Kill after timeout
        setTimeout(() => {
            if (!child.killed) {
                child.kill();
            }
        }, 5000);
    });
}

async function generateInstallCommands(missingModules) {
    if (missingModules.length === 0) return;
    
    console.log('\n📦 Installation Commands for Missing Modules:');
    console.log('=============================================');
    
    const pipModules = missingModules.filter(m => !['json', 'logging', 'asyncio'].includes(m));
    
    if (pipModules.length > 0) {
        console.log('Run these commands to install missing modules:');
        console.log(`pip3 install ${pipModules.join(' ')}`);
        console.log('\nOr individually:');
        pipModules.forEach(module => {
            console.log(`pip3 install ${module}`);
        });
    }
}

async function runTests() {
    // First, detect the best Python version (same logic as build script)
    const { execSync } = require('child_process');
    const os = require('os');
    
    const platform = os.platform();
    const isMac = platform === 'darwin';
    const isLinux = platform === 'linux';
    
    let pythonCmd = 'python3';
    
    if (isMac || isLinux) {
        const pythonVersions = [
            'python3.12',
            'python3.11', 
            'python3.10',
            'python3',
            'python'
        ];
        
        console.log('🔍 Detecting best Python version...');
        
        for (const cmd of pythonVersions) {
            try {
                const versionOutput = execSync(`${cmd} --version`, { stdio: 'pipe' }).toString();
                const versionMatch = versionOutput.match(/Python (\d+)\.(\d+)/);
                
                if (versionMatch) {
                    const major = parseInt(versionMatch[1]);
                    const minor = parseInt(versionMatch[2]);
                    
                    console.log(`🐍 Found ${cmd}: Python ${major}.${minor}`);
                    
                    // For macOS, prefer Python 3.10+ for FastMCP compatibility
                    if (isMac && major === 3 && minor >= 10) {
                        pythonCmd = cmd;
                        console.log(`✅ Using ${cmd} for testing (FastMCP compatible)`);
                        break;
                    }
                    // For Linux, Python 3.8+ is usually fine
                    else if (isLinux && major === 3 && minor >= 8) {
                        pythonCmd = cmd;
                        console.log(`✅ Using ${cmd} for testing`);
                        break;
                    }
                    // Fallback for older versions
                    else if (major === 3) {
                        pythonCmd = cmd;
                        console.log(`⚠️  Using ${cmd} (Python ${major}.${minor})`);
                        break;
                    }
                }
            } catch (e) {
                continue;
            }
        }
    }
    
    console.log(`🧪 Testing modules with ${pythonCmd}...\n`);
    
    const results = [];
    const missingModules = [];
    
    for (const module of requiredModules) {
        const result = await testPythonModule(module, pythonCmd);
        results.push(result);
        
        if (result.status === 'missing') {
            // fastmcp is bundled in the executable, so don't count it as missing
            if (module !== 'fastmcp') {
                missingModules.push(module);
            }
        }
    }
    
    // Test MCP server
    const mcpResult = await testMCPServer();
    
    // Generate summary
    console.log('\n📋 Summary:');
    console.log('===========');
    
    const okModules = results.filter(r => r.status === 'ok').length;
    const totalModules = results.length;
    
    const availableCount = okModules + (results.find(r => r.module === 'fastmcp' && r.status === 'missing') ? 1 : 0);
    
    console.log(`✅ Available modules: ${availableCount}/${totalModules}`);
    console.log(`❌ Missing modules: ${missingModules.length}`);
    
    if (missingModules.length > 0) {
        console.log(`   Missing: ${missingModules.join(', ')}`);
        await generateInstallCommands(missingModules);
    }
    
    if (results.find(r => r.module === 'fastmcp' && r.status === 'missing')) {
        console.log('ℹ️  fastmcp is bundled in the MCP executable (not needed as separate module)');
    }
    
    console.log(`🎯 MCP Server: ${mcpResult.status}`);
    
    if (missingModules.length === 0 && mcpResult.status === 'ok') {
        console.log('\n🎉 All dependencies are available! MCP server should work correctly.');
    } else if (mcpResult.status === 'ok' && missingModules.length === 0) {
        console.log('\n🎉 All dependencies are available! MCP server should work correctly.');
    } else {
        console.log('\n⚠️  Some dependencies are missing or MCP server has issues.');
        console.log('   Please install missing modules and rebuild the MCP executable.');
    }
}

runTests().catch(console.error); 