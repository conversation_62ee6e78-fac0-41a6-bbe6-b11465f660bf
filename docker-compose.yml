version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - API_URL=http://backend:5001
      - NEXT_PUBLIC_API_URL=http://localhost:5001
      - NEXT_PUBLIC_GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=${STRIPE_PUBLISHABLE_KEY}
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "5001:5001"
    environment:
      - PORT=5001
      - FRONTEND_URL=http://localhost:3000
      - MCP_SERVER_URL=ws://mcp_server:8765
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - JWT_SECRET=${JWT_SECRET}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - STRIPE_PUBLISHABLE_KEY=${STRIPE_PUBLISHABLE_KEY}
      - STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}
      - STRIPE_PRICE_ID=${STRIPE_PRICE_ID}
      - PS_OPENAIAPIKEY=${PS_OPENAIAPIKEY}
      - PS_DEEPSEEKAPIKEY=${PS_DEEPSEEKAPIKEY}
      - PS_ANTHROPICAPIKEY=${PS_ANTHROPICAPIKEY}
      - PS_GOOGLEAPIKEY=${PS_GOOGLEAPIKEY}
    depends_on:
      - mcp_server
    volumes:
      - ./backend:/app

  mcp_server:
    build:
      context: ./mcp_server
      dockerfile: Dockerfile
    ports:
      - "8765:8765"
      - "9877:9877"
    environment:
      - MCP_PORT=8765
      - MCP_HOST=0.0.0.0
    volumes:
      - ./mcp_server:/app
    extra_hosts:
      - "host.docker.internal:host-gateway"

  mcp_chat:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - PORT=3001
      - MCP_SERVER_URL=ws://mcp_server:9877
      - OPENAI_API_KEY=${PS_OPENAIAPIKEY}
      - ANTHROPIC_API_KEY=${PS_ANTHROPICAPIKEY}
      - GOOGLE_API_KEY=${PS_GOOGLEAPIKEY}
    depends_on:
      - mcp_server
    volumes:
      - ./frontend:/app
      - /app/node_modules