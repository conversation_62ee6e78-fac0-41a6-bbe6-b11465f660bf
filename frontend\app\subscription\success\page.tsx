'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import { useSupabaseAuth } from '../../../contexts/SupabaseAuthContext';

// This function will be used inside the component to get the token
const getToken = (supabaseUser: any) => {
  // For development, use a mock token if USE_MOCK_USER is true
  if (process.env.NEXT_PUBLIC_USE_MOCK_USER === 'true') {
    console.log('Using mock token for development (USE_MOCK_USER=true)');
    return 'mock-token-for-development';
  }

  // If we have a Supabase user, use their access token
  if (supabaseUser && supabaseUser.access_token) {
    console.log('Using Supabase access token');
    return supabaseUser.access_token;
  }

  console.log('No valid token found, using mock token as fallback');
  return 'mock-token-for-development';
};

export default function SubscriptionSuccessPage() {
  const { isAuthenticated, isLoading, updateSubscription } = useAuth();
  const { user: supabaseUser, isLoading: supabaseLoading } = useSupabaseAuth();
  const [countdown, setCountdown] = useState(5);
  const [updated, setUpdated] = useState(false);

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!isLoading && !isAuthenticated) {
      window.location.href = '/login';
      return;
    }

    const updateSubscriptionStatus = async () => {
      try {
        // Update subscription status in the frontend
        updateSubscription(true);
        console.log('Updated subscription status in frontend to active');

        // Get the token using the Supabase user
        const token = getToken(supabaseUser);

        // Call the backend API to update the subscription status
        console.log('Updating subscription status in backend...');
        const apiUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001'}/api/subscription/update`;
        console.log('Using API URL:', apiUrl);
        console.log('Using token:', token ? 'Valid token available' : 'No valid token');

        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ status: true })
        });

        if (!response.ok) {
          console.error('Failed to update subscription status in backend');
          console.error('Response status:', response.status, response.statusText);
          const errorText = await response.text();
          console.error('Error details:', errorText);

          // Even if the backend update fails, we'll still mark as updated in the frontend
          // This is just for demo purposes - in production, you'd want to handle this differently
          setUpdated(true);
        } else {
          console.log('Successfully updated subscription status in backend');
          setUpdated(true);
        }
      } catch (error) {
        console.error('Error updating subscription status:', error);
        // Even if there's an error, we'll still mark as updated in the frontend
        // This is just for demo purposes - in production, you'd want to handle this differently
        setUpdated(true);
      }
    };

    updateSubscriptionStatus();
  }, [isAuthenticated, isLoading, updateSubscription, supabaseUser, supabaseLoading]);

  // Countdown effect
  useEffect(() => {
    if (!updated) return;

    if (countdown <= 0) {
      // Use window.location for a hard redirect instead of router.push
      window.location.href = '/dashboard';
      return;
    }

    const timer = setTimeout(() => {
      setCountdown(countdown - 1);
    }, 1000);

    return () => clearTimeout(timer);
  }, [updated, countdown]);

  // Handle manual redirect
  const handleRedirect = () => {
    window.location.href = '/dashboard';
  };

  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md text-center">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>

        <h1 className="text-2xl font-bold mb-2 text-gray-800">Subscription Successful!</h1>

        <p className="text-gray-600 mb-6">
          Thank you for subscribing to the AI Unreal Engine Assistant. Your subscription has been activated.
        </p>

        {updated ? (
          <p className="text-gray-500 mb-4">
            Redirecting to dashboard in {countdown} seconds...
          </p>
        ) : (
          <p className="text-gray-500 mb-4">
            Updating your subscription status...
          </p>
        )}

        <button
          onClick={handleRedirect}
          className="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Go to Dashboard Now
        </button>
      </div>
    </div>
  );
}
