#!/usr/bin/env python3
"""
Test script for MCP stdio wrapper
"""

import json
import subprocess
import sys

def test_mcp_method(method, params=None, expected_keys=None):
    """Test a specific MCP method"""
    request = {
        "jsonrpc": "2.0",
        "method": method,
        "id": 1
    }
    if params:
        request["params"] = params
    
    # Send request to stdio wrapper
    cmd = ["docker", "exec", "-i", "unreal-mcp-server", "python", "/app/server/mcp_stdio.py"]
    
    try:
        process = subprocess.Popen(cmd, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        stdout, stderr = process.communicate(input=json.dumps(request))
        
        if stderr:
            print(f"❌ {method}: Error - {stderr}")
            return False
        
        # Special case for 'initialized' - it's a notification, no response expected
        if method == "initialized":
            if not stdout.strip():
                print(f"✅ {method}: Success (notification, no response expected)")
                return True
            else:
                print(f"❌ {method}: Unexpected response for notification")
                return False
            
        response = json.loads(stdout.strip())
        
        # Check for errors
        if "error" in response:
            print(f"❌ {method}: {response['error']['message']}")
            return False
            
        # Check expected keys
        if expected_keys:
            result = response.get("result", {})
            for key in expected_keys:
                if key not in result:
                    print(f"❌ {method}: Missing key '{key}' in result")
                    return False
        
        print(f"✅ {method}: Success")
        return True
        
    except Exception as e:
        print(f"❌ {method}: Exception - {str(e)}")
        return False

def main():
    """Run all MCP protocol tests"""
    print("Testing MCP stdio wrapper...")
    print("=" * 40)
    
    tests = [
        ("initialize", {"protocolVersion": "2024-11-05", "capabilities": {}}, ["protocolVersion", "capabilities", "serverInfo"]),
        ("initialized", {}, []),
        ("notifications/initialized", {}, []),
        ("tools/list", {}, ["tools"]),
        ("prompts/list", {}, ["prompts"]),
        ("resources/list", {}, ["resources"]),
        ("ping", {}, [])
    ]
    
    passed = 0
    total = len(tests)
    
    for method, params, expected_keys in tests:
        if test_mcp_method(method, params, expected_keys):
            passed += 1
    
    print("=" * 40)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! MCP stdio wrapper is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Check the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 