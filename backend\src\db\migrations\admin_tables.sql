-- System Settings Table
CREATE TABLE IF NOT EXISTS system_settings (
  id SERIAL PRIMARY KEY,
  key VARCHAR(255) NOT NULL UNIQUE,
  value TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_system_settings_updated_at
BEFORE UPDATE ON system_settings
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Insert default settings
INSERT INTO system_settings (key, value)
VALUES 
  ('api_rate_limit', '100'),
  ('max_token_limit', '100000'),
  ('default_model', 'claude-3-sonnet'),
  ('enable_logging', 'true'),
  ('maintenance_mode', 'false'),
  ('basic_plan_price', '20'),
  ('pro_plan_price', '30'),
  ('token_package_price', '5'),
  ('token_package_amount', '100000')
ON CONFLICT (key) DO NOTHING;

-- Backups Table
CREATE TABLE IF NOT EXISTS backups (
  id VARCHAR(255) PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  status VARCHAR(50) NOT NULL,
  file_name VARCHAR(255) NOT NULL,
  file_size BIGINT NOT NULL,
  last_restored_at TIMESTAMP WITH TIME ZONE,
  notes TEXT
);

-- Add RLS policies for backups
ALTER TABLE backups ENABLE ROW LEVEL SECURITY;

CREATE POLICY backups_select_policy ON backups
  FOR SELECT
  USING (
    auth.uid() IN (
      SELECT id FROM auth.users WHERE email IN ('<EMAIL>', '<EMAIL>') OR is_admin = true
    )
  );

CREATE POLICY backups_insert_policy ON backups
  FOR INSERT
  WITH CHECK (
    auth.uid() IN (
      SELECT id FROM auth.users WHERE email IN ('<EMAIL>', '<EMAIL>') OR is_admin = true
    )
  );

CREATE POLICY backups_update_policy ON backups
  FOR UPDATE
  USING (
    auth.uid() IN (
      SELECT id FROM auth.users WHERE email IN ('<EMAIL>', '<EMAIL>') OR is_admin = true
    )
  );

-- Add is_admin column to users table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'users' AND column_name = 'is_admin'
  ) THEN
    ALTER TABLE users ADD COLUMN is_admin BOOLEAN DEFAULT FALSE;
  END IF;
END $$;

-- Add token_balance table if it doesn't exist
CREATE TABLE IF NOT EXISTS token_balance (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  balance BIGINT DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_token_balance_updated_at
BEFORE UPDATE ON token_balance
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Add RLS policies for token_balance
ALTER TABLE token_balance ENABLE ROW LEVEL SECURITY;

CREATE POLICY token_balance_select_policy ON token_balance
  FOR SELECT
  USING (
    auth.uid() = user_id OR
    auth.uid() IN (
      SELECT id FROM auth.users WHERE email IN ('<EMAIL>', '<EMAIL>') OR is_admin = true
    )
  );

CREATE POLICY token_balance_insert_policy ON token_balance
  FOR INSERT
  WITH CHECK (
    auth.uid() = user_id OR
    auth.uid() IN (
      SELECT id FROM auth.users WHERE email IN ('<EMAIL>', '<EMAIL>') OR is_admin = true
    )
  );

CREATE POLICY token_balance_update_policy ON token_balance
  FOR UPDATE
  USING (
    auth.uid() = user_id OR
    auth.uid() IN (
      SELECT id FROM auth.users WHERE email IN ('<EMAIL>', '<EMAIL>') OR is_admin = true
    )
  );

-- Add last_login column to users table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'users' AND column_name = 'last_login'
  ) THEN
    ALTER TABLE users ADD COLUMN last_login TIMESTAMP WITH TIME ZONE;
  END IF;
END $$;
