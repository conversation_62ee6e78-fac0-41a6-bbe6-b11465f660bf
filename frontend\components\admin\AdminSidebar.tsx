'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  LayoutDashboard, 
  Users, 
  Bar<PERSON>hart, 
  CreditCard, 
  Key, 
  Settings, 
  MessageSquare,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { useState } from 'react';
import { cn } from '@/lib/utils';

interface NavItemProps {
  href: string;
  icon: React.ReactNode;
  title: string;
  isCollapsed: boolean;
}

function NavItem({ href, icon, title, isCollapsed }: NavItemProps) {
  const pathname = usePathname();
  const isActive = pathname === href;
  
  return (
    <Link
      href={href}
      className={cn(
        "flex items-center gap-3 rounded-lg px-3 py-2 transition-all",
        isActive 
          ? "bg-primary text-primary-foreground" 
          : "hover:bg-primary/10 text-gray-700 hover:text-primary",
        isCollapsed && "justify-center px-2"
      )}
    >
      {icon}
      {!isCollapsed && <span>{title}</span>}
    </Link>
  );
}

export default function AdminSidebar() {
  const [isCollapsed, setIsCollapsed] = useState(false);
  
  return (
    <div 
      className={cn(
        "flex flex-col border-r bg-white transition-all duration-300",
        isCollapsed ? "w-16" : "w-64"
      )}
    >
      <div className="flex h-14 items-center border-b px-3">
        {!isCollapsed && (
          <span className="font-semibold text-lg">CreateLex Admin</span>
        )}
        <button 
          onClick={() => setIsCollapsed(!isCollapsed)}
          className={cn(
            "ml-auto rounded-full p-1 hover:bg-gray-100",
            isCollapsed && "mx-auto"
          )}
          aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          {isCollapsed ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
        </button>
      </div>
      <nav className="flex-1 space-y-1 p-2">
        <NavItem 
          href="/admin" 
          icon={<LayoutDashboard size={20} />} 
          title="Dashboard" 
          isCollapsed={isCollapsed} 
        />
        <NavItem 
          href="/admin/users" 
          icon={<Users size={20} />} 
          title="Users" 
          isCollapsed={isCollapsed} 
        />
        <NavItem 
          href="/admin/usage" 
          icon={<BarChart size={20} />} 
          title="Usage" 
          isCollapsed={isCollapsed} 
        />
        <NavItem 
          href="/admin/billing" 
          icon={<CreditCard size={20} />} 
          title="Billing" 
          isCollapsed={isCollapsed} 
        />
        <NavItem 
          href="/admin/api-keys" 
          icon={<Key size={20} />} 
          title="API Keys" 
          isCollapsed={isCollapsed} 
        />
        <NavItem 
          href="/admin/settings" 
          icon={<Settings size={20} />} 
          title="Settings" 
          isCollapsed={isCollapsed} 
        />
      </nav>
      <div className="border-t p-2">
        <NavItem 
          href="/dashboard" 
          icon={<MessageSquare size={20} />} 
          title="Back to App" 
          isCollapsed={isCollapsed} 
        />
      </div>
    </div>
  );
}
