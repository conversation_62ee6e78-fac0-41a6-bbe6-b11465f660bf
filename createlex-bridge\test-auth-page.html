
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CreateLex Bridge - Test Authentication</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
        }
        .container {
            text-align: center;
            max-width: 600px;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            backdrop-filter: blur(10px);
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 16px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
        }
        button:hover {
            background: #45a049;
        }
        .url-display {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            word-break: break-all;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 CreateLex Bridge - Test Authentication</h1>
        <p>This is a temporary test page to simulate the OAuth callback.</p>
        
        <h3>Test Scenarios:</h3>
        
        <button onclick="testSuccessfulAuth()">
            ✅ Test Successful Authentication
        </button>
        
        <button onclick="testActiveSubscription()">
            🎯 Test Active Subscription
        </button>
        
        <button onclick="testInactiveSubscription()">
            ⚠️ Test Inactive Subscription
        </button>
        
        <button onclick="testAuthError()">
            ❌ Test Authentication Error
        </button>
        
        <div id="urlDisplay" class="url-display" style="display: none;"></div>
        
        <p style="margin-top: 30px; font-size: 14px;">
            Click any button above to test the Electron app's protocol handling.
            Make sure the CreateLex Bridge app is running!
        </p>
    </div>

    <script>
        function generateState() {
            return Math.random().toString(36).substring(2, 15);
        }

        function showAndRedirect(url) {
            const display = document.getElementById('urlDisplay');
            display.textContent = url;
            display.style.display = 'block';
            
            setTimeout(() => {
                window.location.href = url;
            }, 2000);
        }

        function testSuccessfulAuth() {
            const url = `createlex-bridge://auth-callback?token=test_jwt_${Date.now()}&userId=test_user_123&subscription=active&state=${generateState()}`;
            showAndRedirect(url);
        }

        function testActiveSubscription() {
            const url = `createlex-bridge://auth-callback?token=premium_jwt_${Date.now()}&userId=premium_user_456&subscription=active&state=${generateState()}`;
            showAndRedirect(url);
        }

        function testInactiveSubscription() {
            const url = `createlex-bridge://auth-callback?token=basic_jwt_${Date.now()}&userId=basic_user_789&subscription=inactive&state=${generateState()}`;
            showAndRedirect(url);
        }

        function testAuthError() {
            const url = `createlex-bridge://auth-callback?error=authentication_failed&error_description=Invalid credentials&state=${generateState()}`;
            showAndRedirect(url);
        }

        // Auto-detect if this page was opened with OAuth parameters
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const state = urlParams.get('state');
        
        if (code) {
            // This would be where we exchange the code for a token
            // For now, just simulate a successful response
            const simulatedUrl = `createlex-bridge://auth-callback?token=oauth_jwt_${Date.now()}&userId=oauth_user_${Math.floor(Math.random() * 1000)}&subscription=active&state=${state || generateState()}`;
            
            document.body.innerHTML = `
                <div class="container">
                    <h1>🔄 Processing OAuth Callback...</h1>
                    <p>Redirecting back to CreateLex Bridge...</p>
                    <div class="url-display">${simulatedUrl}</div>
                </div>
            `;
            
            setTimeout(() => {
                window.location.href = simulatedUrl;
            }, 3000);
        }
    </script>
</body>
</html>
  