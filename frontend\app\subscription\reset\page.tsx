'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../../contexts/AuthContext';
import Link from 'next/link';

// Helper function to get token from session storage
const getToken = () => {
  if (typeof window === 'undefined') return '';

  try {
    const supabaseSession = sessionStorage.getItem('supabase.auth.session');
    if (supabaseSession) {
      const session = JSON.parse(supabaseSession);
      return session.access_token || '';
    }
  } catch (e) {
    console.error('Error getting token:', e);
  }

  return '';
};

export default function ResetSubscriptionPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading, updateSubscription } = useAuth();
  const [isResetting, setIsResetting] = useState(false);
  const [resetComplete, setResetComplete] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, isLoading, router]);

  const handleResetSubscription = async () => {
    setIsResetting(true);
    setError(null);

    try {
      // Update subscription status in the frontend
      updateSubscription(false);

      // Update subscription status in the backend
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/subscription/update`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getToken() || 'mock-token-for-development'}`
        },
        body: JSON.stringify({ status: false })
      });

      if (!response.ok) {
        throw new Error('Failed to reset subscription status');
      }

      setResetComplete(true);
    } catch (err) {
      console.error('Error resetting subscription:', err);
      setError('Failed to reset subscription. Please try again.');
    } finally {
      setIsResetting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-100">
        <div className="text-center">
          <div className="spinner mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md text-center">
        <h1 className="text-2xl font-bold mb-4 text-gray-800">Reset Subscription</h1>

        {resetComplete ? (
          <>
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <p className="text-gray-600 mb-6">
              Your subscription has been reset successfully. You can now test the subscription flow again.
            </p>
            <div className="flex justify-center space-x-4">
              <Link
                href="/subscription"
                className="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Go to Subscription Page
              </Link>
              <Link
                href="/dashboard"
                className="bg-gray-600 text-white py-2 px-4 rounded hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                Go to Dashboard
              </Link>
            </div>
          </>
        ) : (
          <>
            <p className="text-gray-600 mb-6">
              This page is for testing purposes only. It will reset your subscription status so you can test the subscription flow again.
            </p>
            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                {error}
              </div>
            )}
            <div className="flex justify-center">
              <button
                onClick={handleResetSubscription}
                disabled={isResetting}
                className={`${
                  isResetting ? 'bg-gray-400' : 'bg-red-600 hover:bg-red-700'
                } text-white py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-red-500`}
              >
                {isResetting ? 'Resetting...' : 'Reset Subscription'}
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
