import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// Authentication endpoint for Unreal Engine CreateLex plugin
export async function GET(request: NextRequest) {
  try {
    console.log('Unreal Engine requesting authentication status...');

    // Get current session
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Supabase session error:', error);
      return NextResponse.json({
        authenticated: false,
        user_email: '<EMAIL>',
        subscription_active: false,
        user_id: 'unknown',
        source: 'session_error',
        error: error.message
      });
    }

    if (session?.user) {
      // Check subscription status
      let hasSubscription = false;
      
      try {
        const subscriptionResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/subscription/status`, {
          headers: {
            'Authorization': `Bearer ${session.access_token}`,
            'Content-Type': 'application/json',
          },
        });

        if (subscriptionResponse.ok) {
          const subscriptionData = await subscriptionResponse.json();
          hasSubscription = subscriptionData.hasActiveSubscription || false;
        }
      } catch (subError) {
        console.warn('Failed to check subscription:', subError);
        hasSubscription = true; // Default to true for testing
      }

      const authData = {
        authenticated: true,
        user_email: session.user.email || '<EMAIL>',
        subscription_active: hasSubscription,
        user_id: session.user.id,
        source: 'supabase_session',
        last_updated: new Date().toISOString()
      };

      console.log('✅ Returning authentication data to Unreal:', authData);
      
      // Also notify the MCP server
      try {
        await fetch('http://localhost:8080/auth/success', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            user_id: session.user.id,
            email: session.user.email,
            subscription_status: hasSubscription,
            timestamp: Date.now(),
            source: 'unreal_auth_check'
          }),
        });
      } catch (mcpError) {
        console.warn('Failed to notify MCP server:', mcpError);
      }

      // 🆕 NOTIFY HTTP BRIDGE on port 9878
      try {
        await fetch('http://localhost:9878/update-auth', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            user_email: session.user.email,
            is_authenticated: true,
            has_subscription: hasSubscription,
            user_id: session.user.id,
            timestamp: new Date().toISOString(),
            source: 'supabase_frontend_bridge'
          }),
        });
        console.log('✅ Notified HTTP bridge on port 9878');
      } catch (bridgeError) {
        console.warn('Failed to notify HTTP bridge on 9878:', bridgeError);
      }

      return NextResponse.json(authData);
    } else {
      console.log('⚠️ No active session found');
      return NextResponse.json({
        authenticated: false,
        user_email: '<EMAIL>',
        subscription_active: false,
        user_id: 'unknown',
        source: 'no_session'
      });
    }

  } catch (error) {
    console.error('Error in Unreal auth endpoint:', error);
    return NextResponse.json({
      authenticated: false,
      user_email: '<EMAIL>',
      subscription_active: false,
      user_id: 'unknown',
      source: 'endpoint_error',
      error: error.message
    }, { status: 500 });
  }
}

// Handle POST requests for authentication updates
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('Unreal Engine posting authentication update:', body);

    // Forward the update to the HTTP bridge on port 9878
    try {
      await fetch('http://localhost:9878/update-auth', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...body,
          source: 'frontend_relay',
          timestamp: new Date().toISOString()
        }),
      });
      console.log('✅ Forwarded auth update to HTTP bridge on port 9878');
    } catch (bridgeError) {
      console.warn('Failed to forward to HTTP bridge on 9878:', bridgeError);
    }

    return NextResponse.json({
      success: true,
      message: 'Authentication update received and forwarded',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error processing Unreal auth update:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
} 