'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { toast } from '@/components/ui/use-toast';
import { AlertCircle, Database, RefreshCw, Settings } from 'lucide-react';
import { fetchWithAuth, getApiUrl, getAuthToken } from '@/lib/auth-utils';

export default function AdminActions() {
  const [loading, setLoading] = useState(false);

  const addTestData = async () => {
    try {
      setLoading(true);

      toast({
        title: 'Adding test data...',
        description: 'This may take a few seconds.',
      });

      const apiUrl = getApiUrl();
      const requestUrl = `${apiUrl}/api/admin/token-usage/add-test-data`;

      console.log('Making request to:', requestUrl);

      const response = await fetchWithAuth(requestUrl, {
        method: 'POST'
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error(`Failed to add test data: ${response.status} ${errorText}`);
      }

      const result = await response.json();
      console.log('Test data result:', JSON.stringify(result, null, 2));

      toast({
        title: 'Test data added successfully',
        description: `Added ${result.count || 'multiple'} token usage records.`,
      });

      // Refresh the page to show the new data
      window.location.reload();
    } catch (error) {
      console.error('Error adding test data:', error);
      toast({
        title: 'Error adding test data',
        description: error instanceof Error ? error.message : String(error),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const checkAuthToken = () => {
    const token = getAuthToken();

    if (!token) {
      toast({
        title: 'No auth token found',
        description: 'You need to log in first.',
        variant: 'destructive',
      });
      return;
    }

    // Display token info
    try {
      // Manually decode the token
      const parts = token.split('.');
      if (parts.length === 3) {
        // It's a JWT
        const base64Payload = parts[1].replace(/-/g, '+').replace(/_/g, '/');
        const decodedPayload = atob(base64Payload);
        const payload = JSON.parse(decodedPayload);

        toast({
          title: 'Auth token found',
          description: (
            <div className="mt-2 text-xs">
              <p>User ID: {payload.sub || payload.id || 'Unknown'}</p>
              <p>Email: {payload.email || 'Unknown'}</p>
              <p>Is Admin: {payload.is_admin ? 'Yes' : 'No'}</p>
              <p>Expires: {payload.exp ? new Date(payload.exp * 1000).toLocaleString() : 'Unknown'}</p>
            </div>
          ),
        });
      } else {
        toast({
          title: 'Auth token found',
          description: 'Token is not in JWT format.',
        });
      }
    } catch (error) {
      toast({
        title: 'Auth token found',
        description: 'Could not decode token: ' + (error instanceof Error ? error.message : String(error)),
      });
    }
  };

  const refreshData = () => {
    window.location.reload();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline">
          <Settings className="h-4 w-4 mr-2" />
          Admin Actions
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Admin Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={addTestData} disabled={loading}>
          <Database className="h-4 w-4 mr-2" />
          {loading ? 'Adding test data...' : 'Add test token usage data'}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={checkAuthToken}>
          <AlertCircle className="h-4 w-4 mr-2" />
          Check auth token
        </DropdownMenuItem>
        <DropdownMenuItem onClick={refreshData}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh data
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
