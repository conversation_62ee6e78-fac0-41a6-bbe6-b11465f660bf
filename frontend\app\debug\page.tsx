'use client';

import { useEffect, useState } from 'react';
import { getMainAppOrigin } from '@/lib/fetch-auth-token';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { getSupabaseClient } from '@/lib/supabase-singleton';

export default function DebugPage() {
  const [mainAppUrl, setMainAppUrl] = useState('');
  const [sessionData, setSessionData] = useState<any>(null);
  const [cookieData, setCookieData] = useState<any>(null);
  const [localStorageData, setLocalStorageData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Unreal Engine test states
  const [unrealStatus, setUnrealStatus] = useState('');
  const [unrealLoading, setUnrealLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // Get the main app URL
        const origin = getMainAppOrigin();
        setMainAppUrl(origin);

        // Get session data
        const supabase = getSupabaseClient();
        const { data, error } = await supabase.auth.getSession();
        setSessionData({
          session: data.session ? {
            ...data.session,
            access_token: data.session.access_token ? 'present' : 'missing',
            refresh_token: data.session.refresh_token ? 'present' : 'missing',
          } : null,
          error: error ? error.message : null
        });

        // Get cookie data
        const cookies = document.cookie.split(';').reduce((acc, cookie) => {
          const [key, value] = cookie.trim().split('=');
          if (key) {
            acc[key] = value ? 'present' : 'missing';
          }
          return acc;
        }, {} as Record<string, string>);
        setCookieData(cookies);

        // Get localStorage data
        const storage: Record<string, string> = {};
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key) {
            const value = localStorage.getItem(key);
            storage[key] = value ? (
              key.includes('token') ? 'present' : value.substring(0, 20) + '...'
            ) : 'missing';
          }
        }
        setLocalStorageData(storage);
      } catch (error) {
        console.error('Error fetching debug data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const testUnrealUIUpdate = async (testType: string) => {
    setUnrealLoading(true);
    setUnrealStatus(`Testing ${testType}...`);

    try {
      let response;

      if (testType === 'auth-true') {
        // Test authenticated user with subscription
        response = await fetch('http://localhost:9878/update-auth', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            user_email: '<EMAIL>',
            is_authenticated: true,
            has_subscription: true
          }),
        });
      } else if (testType === 'auth-false') {
        // Test unauthenticated user
        response = await fetch('http://localhost:9878/update-auth', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            user_email: '',
            is_authenticated: false,
            has_subscription: false
          }),
        });
      } else if (testType === 'mcp-running') {
        // Test MCP server running
        response = await fetch('http://localhost:9878/update-mcp', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            is_running: true
          }),
        });
      } else if (testType === 'mcp-stopped') {
        // Test MCP server stopped
        response = await fetch('http://localhost:9878/update-mcp', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            is_running: false
          }),
        });
      }

      if (response && response.ok) {
        const result = await response.json();
        setUnrealStatus(`✅ Success: ${result.message || 'UI update sent to Unreal Engine'}`);
      } else {
        setUnrealStatus(`❌ Failed: ${response?.status || 'No response'} - ${response?.statusText || 'Unknown error'}`);
      }
    } catch (error: any) {
      setUnrealStatus(`❌ Error: ${error.message || 'Failed to connect to Unreal Engine'}`);
    } finally {
      setUnrealLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen flex-col p-4">
      <h1 className="text-2xl font-bold mb-4">MCP Chat Debug Page</h1>

      {isLoading ? (
        <div className="flex items-center justify-center h-40">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      ) : (
        <div className="grid gap-4">
          <Card>
            <CardHeader>
              <CardTitle>Main App URL</CardTitle>
              <CardDescription>The URL of the main application</CardDescription>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-2 rounded overflow-auto">{mainAppUrl}</pre>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Session Data</CardTitle>
              <CardDescription>Current Supabase session information</CardDescription>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-2 rounded overflow-auto">{JSON.stringify(sessionData, null, 2)}</pre>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Cookie Data</CardTitle>
              <CardDescription>Current browser cookies</CardDescription>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-2 rounded overflow-auto">{JSON.stringify(cookieData, null, 2)}</pre>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>LocalStorage Data</CardTitle>
              <CardDescription>Current localStorage items</CardDescription>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-2 rounded overflow-auto">{JSON.stringify(localStorageData, null, 2)}</pre>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>🎮 Unreal Engine UI Test</CardTitle>
              <CardDescription>Test real-time UI updates in Unreal Engine plugin</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-700 mb-2">Authentication Tests</h3>
                  <div className="space-y-2">
                    <Button
                      onClick={() => testUnrealUIUpdate('auth-true')}
                      disabled={unrealLoading}
                      className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400"
                    >
                      🟢 Set Authenticated + Subscription
                    </Button>

                    <Button
                      onClick={() => testUnrealUIUpdate('auth-false')}
                      disabled={unrealLoading}
                      className="w-full bg-red-600 hover:bg-red-700 disabled:bg-gray-400"
                    >
                      🔴 Set Not Authenticated
                    </Button>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-700 mb-2">MCP Server Tests</h3>
                  <div className="space-y-2">
                    <Button
                      onClick={() => testUnrealUIUpdate('mcp-running')}
                      disabled={unrealLoading}
                      className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400"
                    >
                      🟢 Set MCP Server Running
                    </Button>

                    <Button
                      onClick={() => testUnrealUIUpdate('mcp-stopped')}
                      disabled={unrealLoading}
                      className="w-full bg-orange-600 hover:bg-orange-700 disabled:bg-gray-400"
                    >
                      🔴 Set MCP Server Stopped
                    </Button>
                  </div>
                </div>

                {unrealStatus && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-md">
                    <h4 className="text-sm font-medium text-gray-700 mb-1">Status:</h4>
                    <p className="text-sm text-gray-600">{unrealStatus}</p>
                  </div>
                )}

                <div className="mt-4 text-xs text-gray-500">
                  <p><strong>Instructions:</strong></p>
                  <ol className="list-decimal list-inside space-y-1">
                    <li>Make sure Unreal Engine is running with the plugin loaded</li>
                    <li>Open the Python Socket Control Panel in Unreal Engine</li>
                    <li>Click the buttons above to test UI updates</li>
                    <li>Watch the emoji circles in Unreal Engine change in real-time</li>
                  </ol>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex space-x-2 mt-4">
            <Button asChild>
              <a href="/chat">Try Chat</a>
            </Button>
            <Button asChild variant="outline">
              <a href={`${mainAppUrl}/dashboard`}>Return to Dashboard</a>
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
