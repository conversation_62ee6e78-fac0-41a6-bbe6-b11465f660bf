-- Create table to track MCP server validations
CREATE TABLE IF NOT EXISTS mcp_validations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    validated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index for faster lookups
CREATE INDEX idx_mcp_validations_user_id ON mcp_validations(user_id);
CREATE INDEX idx_mcp_validations_validated_at ON mcp_validations(validated_at);

-- Add comment
COMMENT ON TABLE mcp_validations IS 'Tracks MCP server validation attempts for security and analytics'; 