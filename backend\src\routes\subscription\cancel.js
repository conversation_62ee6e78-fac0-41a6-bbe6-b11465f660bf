const express = require('express');
const router = express.Router();
const { authenticateJWT } = require('../../middleware/auth');
const subscriptionService = require('../../services/subscriptionService');
const authService = require('../../services/authService');
const emailService = require('../../services/emailService');

// Cancel a user's subscription in Stripe
router.post('/', authenticateJWT, async (req, res) => {
  try {
    const userId = req.user.id;
    console.log(`[Subscription Cancel] Canceling subscription for user: ${userId}`);

    // Reload users from file to ensure we have the latest data
    if (typeof authService.loadUsersFromFile === 'function') {
      authService.loadUsersFromFile();
    }

    // Get the user's subscription details
    const user = await authService.getUserById(userId);

    console.log(`[Subscription Cancel] User details:`, JSON.stringify(user, null, 2));

    if (!user) {
      console.log(`[Subscription Cancel] User not found: ${userId}`);
      return res.status(404).json({ error: 'User not found' });
    }

    if (!user.subscriptionId) {
      console.log(`[Subscription Cancel] No subscription ID found in user record for user: ${userId}`);

      // Check if the user has a Stripe customer ID
      if (user.stripeCustomerId) {
        console.log(`[Subscription Cancel] User has Stripe customer ID: ${user.stripeCustomerId}. Checking for active subscriptions...`);

        try {
          // Check if the user has any active subscriptions in Stripe
          const activeSubscriptions = await subscriptionService.findActiveSubscriptions(user.stripeCustomerId);

          if (activeSubscriptions && activeSubscriptions.length > 0) {
            // User has an active subscription in Stripe but it's not recorded in our database
            const subscription = activeSubscriptions[0];
            console.log(`[Subscription Cancel] Found active subscription in Stripe: ${subscription.id}`);

            // Update the user record with the subscription ID
            await authService.updateSubscription(userId, 'active', subscription.id);

            // Now proceed with cancellation
            console.log(`[Subscription Cancel] Updated user record with subscription ID: ${subscription.id}`);

            // Cancel the subscription in Stripe
            const result = await subscriptionService.cancelSubscription(subscription.id);

            // Update the user's subscription status in our database
            // Keep the subscription ID so we can track it in Stripe
            // Mark as 'active' but will not renew
            const updated = await authService.updateSubscription(userId, 'active', subscription.id);

            // Send cancellation notification email
            try {
              console.log(`[Subscription Cancel] Sending cancellation email notification for user: ${userId}`);
              const emailResult = await emailService.sendSubscriptionCancellationEmail({
                user,
                subscriptionId: subscription.id,
                stripeResult: result
              });

              console.log(`[Subscription Cancel] Email notification result:`, emailResult);
            } catch (emailError) {
              console.error('[Subscription Cancel] Error sending cancellation email:', emailError);
            }

            return res.json({
              success: true,
              message: 'Subscription canceled successfully',
              subscriptionId: subscription.id,
              stripeResult: result
            });
          }
        } catch (error) {
          console.error('[Subscription Cancel] Error checking for active subscriptions:', error);
        }
      }

      // If we get here, the user truly doesn't have an active subscription
      console.log(`[Subscription Cancel] No active subscription found for user: ${userId}`);

      // Send notification email about the attempted cancellation
      try {
        console.log(`[Subscription Cancel] Sending notification email about attempted cancellation for user: ${userId}`);
        const emailResult = await emailService.sendSubscriptionCancellationEmail({
          user,
          subscriptionId: 'none',
          stripeResult: { status: 'none', cancelAtPeriodEnd: false }
        });

        console.log(`[Subscription Cancel] Email notification result for attempted cancellation:`, emailResult);
      } catch (emailError) {
        console.error('[Subscription Cancel] Error sending notification email for attempted cancellation:', emailError);
      }

      return res.status(400).json({
        error: 'No active subscription found',
        message: 'We could not find an active subscription for your account. If you believe this is an error, please contact support.'
      });
    }

    console.log(`[Subscription Cancel] Found subscription ${user.subscriptionId} for user ${userId}`);

    // Cancel the subscription in Stripe
    const result = await subscriptionService.cancelSubscription(user.subscriptionId);

    // Update the user's subscription status in our database
    // Keep the subscription ID so we can track it in Stripe
    // Mark as 'active' but will not renew
    const updated = await authService.updateSubscription(userId, 'active', user.subscriptionId);

    // Send cancellation notification email
    try {
      console.log(`[Subscription Cancel] Sending cancellation email notification for user: ${userId}`);
      const emailResult = await emailService.sendSubscriptionCancellationEmail({
        user,
        subscriptionId: user.subscriptionId,
        stripeResult: result
      });

      console.log(`[Subscription Cancel] Email notification result:`, emailResult);
    } catch (emailError) {
      // Log the error but don't fail the request
      console.error('[Subscription Cancel] Error sending cancellation email:', emailError);
    }

    return res.json({
      success: true,
      message: 'Subscription canceled successfully',
      subscriptionId: user.subscriptionId,
      stripeResult: result
    });
  } catch (error) {
    console.error('[Subscription Cancel] Error canceling subscription:', error);
    return res.status(500).json({
      error: 'Failed to cancel subscription',
      message: error.message
    });
  }
});

module.exports = router;
