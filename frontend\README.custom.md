# MCP Chat for AiWebplatform

This is a standalone MCP Chat application that integrates with the AiWebplatform project. It provides a user-friendly interface for interacting with the Model Context Protocol (MCP) server.

## Setup Instructions

### 1. Install Dependencies

```bash
npm install
```

### 2. Configure Environment Variables

Copy the `.env.example` file to `.env` and update the values:

```bash
cp .env.example .env
```

Make sure to set the following variables:
- `MCP_SERVER_URL`: URL of your MCP server (e.g., `ws://127.0.0.1:9877`)
- `OPENAI_API_KEY`: Your OpenAI API key
- `ANTHROPIC_API_KEY`: Your Anthropic API key
- `GOOGLE_API_KEY`: Your Google API key

### 3. Run the Application

```bash
npm run dev
```

The application will be available at http://localhost:3001

### 4. Docker Deployment

You can also run the application using Docker:

```bash
docker-compose up -d
```

## Integration with AiWebplatform

This MCP Chat application is designed to work alongside the main AiWebplatform project. Users can access it by clicking the "Open MCP Chat" button on the main website.

## Features

- Full integration with Model Context Protocol (MCP) servers
- Support for multiple AI providers (OpenAI, Anthropic, Google, etc.)
- Modern UI with shadcn/ui components
- Database integration for chat history

## License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.
