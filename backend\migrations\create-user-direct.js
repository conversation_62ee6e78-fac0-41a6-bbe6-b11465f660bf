require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: SUPABASE_URL and SUPABASE_SERVICE_KEY must be set in .env file');
  process.exit(1);
}

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key (first 10 chars):', supabaseKey.substring(0, 10) + '...');

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createUser() {
  try {
    // Create a user with the same ID as the one in the token
    const userId = '8075c290-0943-4d3e-94a7-dcdf42bda6c6';
    const email = '<EMAIL>';
    const name = '<PERSON>';
    const picture = 'https://lh3.googleusercontent.com/a/ACg8ocIg6OWCzXa_PzMcgcQuOi9DKRzF6lQEaCJhfHGtSdKJLDvNkuE=s96-c';
    
    console.log(`Creating user with ID: ${userId}`);
    
    // First check if the user already exists
    console.log('Checking if user exists...');
    const { data: existingUser, error: fetchError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (fetchError) {
      console.error('Error fetching user from Supabase:', fetchError);
      
      if (fetchError.code !== 'PGRST116') {
        process.exit(1);
      }
      
      console.log('User not found, creating new user...');
    } else {
      console.log('User already exists:', existingUser);
      process.exit(0);
    }
    
    // Create the user
    console.log('Creating new user...');
    const { data: newUser, error: insertError } = await supabase
      .from('users')
      .insert([{
        id: userId,
        email: email,
        name: name,
        picture: picture,
        subscription_status: 'inactive',
        created_at: new Date().toISOString()
      }]);
    
    if (insertError) {
      console.error('Error creating user in Supabase:', insertError);
      process.exit(1);
    }
    
    console.log('User created successfully');
    
    // Verify the user exists
    const { data: verifyUser, error: verifyError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (verifyError) {
      console.error('Error verifying user in Supabase:', verifyError);
      process.exit(1);
    }
    
    console.log('User verified:', verifyUser);
    
    process.exit(0);
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

createUser();
