import { NextApiRequest, NextApiResponse } from 'next';
import { supabaseAdmin } from '@/lib/supabase';

/**
 * API endpoint to migrate chats from browser-generated IDs to authenticated user IDs
 * This endpoint is called when a user logs in to ensure all their chats are associated with their authenticated user ID
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { userId, oldUserId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    console.log(`API: Migrating chats to authenticated user ID ${userId}`);
    
    // If oldUserId is provided, only migrate chats from that specific ID
    // Otherwise, check localStorage for any browser-generated IDs
    let oldUserIds: string[] = [];
    
    if (oldUserId) {
      oldUserIds = [oldUserId];
      console.log(`API: Migrating chats from specific old user ID: ${oldUserId}`);
    } else {
      // Get all user ID mappings for this device
      const { data: mappings, error: mappingsError } = await supabaseAdmin
        .from('user_id_mappings')
        .select('primary_user_id')
        .order('last_seen_at', { ascending: false });
        
      if (mappingsError) {
        console.error('Error getting user ID mappings:', mappingsError);
      } else if (mappings && mappings.length > 0) {
        // Extract unique user IDs from mappings
        const uniqueIds = new Set<string>();
        mappings.forEach(mapping => {
          if (mapping.primary_user_id && mapping.primary_user_id !== userId) {
            uniqueIds.add(mapping.primary_user_id);
          }
        });
        
        oldUserIds = Array.from(uniqueIds);
        console.log(`API: Found ${oldUserIds.length} old user IDs in mappings`);
      }
    }
    
    // If no old user IDs found, check if there are any chats without a user ID
    if (oldUserIds.length === 0) {
      console.log('API: No old user IDs found, checking for chats without a user ID');
      
      // Get all chats that don't have a user ID
      const { data: noUserChats, error: noUserChatsError } = await supabaseAdmin
        .from('chats')
        .select('id, user_id')
        .is('user_id', null);
        
      if (noUserChatsError) {
        console.error('Error getting chats without user ID:', noUserChatsError);
      } else if (noUserChats && noUserChats.length > 0) {
        console.log(`API: Found ${noUserChats.length} chats without a user ID`);
        
        // Update all chats without a user ID to use the authenticated user ID
        const { error: updateError } = await supabaseAdmin
          .from('chats')
          .update({ user_id: userId })
          .is('user_id', null);
          
        if (updateError) {
          console.error('Error updating chats without user ID:', updateError);
        } else {
          console.log(`API: Successfully migrated ${noUserChats.length} chats without a user ID`);
          
          return res.status(200).json({
            success: true,
            migratedCount: noUserChats.length,
            message: `Successfully migrated ${noUserChats.length} chats without a user ID`
          });
        }
      }
    }
    
    // If we have old user IDs, migrate chats from those IDs
    let totalMigratedCount = 0;
    
    for (const oldId of oldUserIds) {
      // Get all chats for the old user ID
      const { data: oldChats, error: oldChatsError } = await supabaseAdmin
        .from('chats')
        .select('id')
        .eq('user_id', oldId);
        
      if (oldChatsError) {
        console.error(`API: Error getting chats for old user ID ${oldId}:`, oldChatsError);
        continue;
      }
      
      if (!oldChats || oldChats.length === 0) {
        console.log(`API: No chats found for old user ID ${oldId}`);
        continue;
      }
      
      console.log(`API: Found ${oldChats.length} chats for old user ID ${oldId}`);
      
      // Update all chats to use the authenticated user ID
      const { error: updateError } = await supabaseAdmin
        .from('chats')
        .update({ user_id: userId })
        .eq('user_id', oldId);
        
      if (updateError) {
        console.error(`API: Error migrating chats from ${oldId} to ${userId}:`, updateError);
      } else {
        console.log(`API: Successfully migrated ${oldChats.length} chats from ${oldId} to ${userId}`);
        totalMigratedCount += oldChats.length;
      }
    }
    
    // Update all user ID mappings to use the authenticated user ID
    if (oldUserIds.length > 0) {
      const { error: mappingError } = await supabaseAdmin
        .from('user_id_mappings')
        .update({ primary_user_id: userId })
        .in('primary_user_id', oldUserIds);
        
      if (mappingError) {
        console.error('API: Error updating user ID mappings:', mappingError);
      } else {
        console.log('API: Successfully updated all user ID mappings');
      }
    }
    
    return res.status(200).json({
      success: true,
      migratedCount: totalMigratedCount,
      message: `Successfully migrated ${totalMigratedCount} chats to authenticated user ID`
    });
  } catch (error) {
    console.error('Error in migrate-user-chats API:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
