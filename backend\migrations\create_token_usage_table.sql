-- Create token usage tracking table
CREATE TABLE IF NOT EXISTS public.token_usage (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL,
  model_id TEXT NOT NULL,
  prompt_tokens INTEGER NOT NULL,
  completion_tokens INTEGER NOT NULL,
  total_tokens INTEGER NOT NULL,
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  request_type TEXT,
  subscription_plan TEXT
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_token_usage_user_id ON public.token_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_token_usage_timestamp ON public.token_usage(timestamp);
CREATE INDEX IF NOT EXISTS idx_token_usage_user_timestamp ON public.token_usage(user_id, timestamp);

-- Create view for daily usage summaries
CREATE OR REPLACE VIEW public.daily_token_usage AS
SELECT 
  user_id,
  DATE_TRUNC('day', timestamp) AS usage_date,
  SUM(prompt_tokens) AS prompt_tokens,
  SUM(completion_tokens) AS completion_tokens,
  SUM(total_tokens) AS total_tokens,
  subscription_plan
FROM public.token_usage
GROUP BY user_id, DATE_TRUNC('day', timestamp), subscription_plan
ORDER BY DATE_TRUNC('day', timestamp) DESC, SUM(total_tokens) DESC;

-- Create view for monthly usage summaries
CREATE OR REPLACE VIEW public.monthly_token_usage AS
SELECT 
  user_id,
  DATE_TRUNC('month', timestamp) AS usage_month,
  SUM(prompt_tokens) AS prompt_tokens,
  SUM(completion_tokens) AS completion_tokens,
  SUM(total_tokens) AS total_tokens,
  subscription_plan
FROM public.token_usage
GROUP BY user_id, DATE_TRUNC('month', timestamp), subscription_plan
ORDER BY DATE_TRUNC('month', timestamp) DESC, SUM(total_tokens) DESC;
