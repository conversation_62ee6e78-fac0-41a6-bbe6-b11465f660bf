const express = require('express');
const router = express.Router();
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const tokenPurchaseService = require('../../../services/tokenPurchaseService');
const { authenticateJWTWithFallback } = require('../../../middleware/auth');

/**
 * @route POST /api/tokens/purchase/process-checkout
 * @description Process a checkout session manually
 * @access Private
 */
router.post('/', authenticateJWTWithFallback, async (req, res) => {
  // Generate a unique request ID for tracking
  const requestId = `process-checkout-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;

  try {
    console.log(`[${requestId}] Processing checkout session manually`);

    // Get user ID from the request
    const userId = req.headers['x-user-id'] || req.body.userId || req.user?.id;
    const sessionId = req.body.sessionId;

    if (!userId) {
      console.error(`[${requestId}] Missing user ID in request`);
      return res.status(400).json({ 
        error: 'Missing user ID',
        requestId
      });
    }

    if (!sessionId) {
      console.error(`[${requestId}] Missing session ID in request`);
      return res.status(400).json({ 
        error: 'Missing session ID',
        requestId
      });
    }

    console.log(`[${requestId}] Processing checkout for user ${userId}, session ${sessionId}`);

    // Retrieve the checkout session from Stripe
    try {
      const session = await stripe.checkout.sessions.retrieve(sessionId);

      if (!session) {
        console.error(`[${requestId}] No session found with ID: ${sessionId}`);
        return res.status(404).json({ 
          error: 'Checkout session not found',
          requestId
        });
      }

      console.log(`[${requestId}] Retrieved session from Stripe: ${session.id}, status: ${session.status}`);

      // Check if this is a token purchase
      if (session.metadata && session.metadata.type === 'token_purchase') {
        console.log(`[${requestId}] Detected token purchase in session ${session.id}`);

        // Get the token purchase details from metadata
        const sessionUserId = session.metadata.userId;
        const tokens = parseInt(session.metadata.tokens, 10);
        const packageId = session.metadata.packageId;
        const transactionId = session.payment_intent;

        // Verify that the user ID in the session matches the requested user ID
        if (sessionUserId !== userId) {
          console.error(`[${requestId}] User ID mismatch: ${sessionUserId} (session) vs ${userId} (request)`);
          return res.status(403).json({ 
            error: 'User ID mismatch',
            requestId
          });
        }

        if (!tokens) {
          console.error(`[${requestId}] Missing tokens in session metadata`);
          return res.status(400).json({ 
            error: 'Missing tokens in session metadata',
            requestId
          });
        }

        console.log(`[${requestId}] Processing token purchase: ${tokens} tokens (package: ${packageId}) for user ${userId}`);

        try {
          // Add tokens to user's account
          const result = await tokenPurchaseService.addTokensToUser(userId, tokens, transactionId);

          console.log(`[${requestId}] Token purchase processed successfully:`, result);

          // Return success response
          return res.json({
            success: true,
            message: 'Token purchase processed successfully',
            tokens: tokens,
            balance: result.balance,
            transaction: result.transaction,
            requestId
          });
        } catch (tokenError) {
          console.error(`[${requestId}] Error adding tokens to user:`, tokenError);

          // Check if this transaction has already been processed
          if (tokenError.message && tokenError.message.includes('already processed')) {
            console.log(`[${requestId}] Transaction already processed, returning success response`);
            
            // Get the current token balance
            const balance = await tokenPurchaseService.getUserTokenBalance(userId);
            
            return res.json({
              success: true,
              message: 'Token purchase already processed',
              tokens: tokens,
              balance: balance.balance,
              alreadyProcessed: true,
              requestId
            });
          }

          // Return error response
          return res.status(500).json({
            error: 'Failed to add tokens to user',
            details: tokenError.message,
            operationId: tokenError.operationId,
            requestId
          });
        }
      } else {
        console.log(`[${requestId}] Not a token purchase session: ${session.id}`);
        return res.status(400).json({ 
          error: 'Not a token purchase session',
          requestId
        });
      }
    } catch (stripeError) {
      console.error(`[${requestId}] Error retrieving session from Stripe:`, stripeError);

      // If we can't retrieve the session from Stripe, try to use the simulate-webhook endpoint
      console.log(`[${requestId}] Falling back to simulate-webhook`);

      try {
        // Determine the package ID based on the session ID
        // This is a fallback mechanism, so we'll just use 'small' as the default
        const packageId = 'small';

        // Call the simulate-webhook endpoint
        const simulateWebhookRouter = require('./simulate-webhook');
        
        // Create a mock request and response
        const mockReq = {
          headers: {
            'x-user-id': userId
          },
          body: {
            userId: userId,
            packageId: packageId
          },
          user: {
            id: userId
          }
        };
        
        const mockRes = {
          json: (data) => {
            console.log(`[${requestId}] Simulate webhook response:`, data);
            return res.json({
              ...data,
              note: 'Used simulate-webhook as fallback',
              requestId
            });
          },
          status: (code) => {
            return {
              json: (data) => {
                console.log(`[${requestId}] Simulate webhook error response:`, data);
                return res.status(code).json({
                  ...data,
                  note: 'Used simulate-webhook as fallback',
                  requestId
                });
              }
            };
          }
        };
        
        // Call the simulate-webhook endpoint
        await simulateWebhookRouter.handle(mockReq, mockRes);
      } catch (simulateError) {
        console.error(`[${requestId}] Error with simulate-webhook fallback:`, simulateError);
        
        // Return error response
        return res.status(500).json({
          error: 'Failed to process checkout and fallback mechanisms failed',
          details: simulateError.message,
          stripeError: stripeError.message,
          requestId
        });
      }
    }
  } catch (error) {
    console.error(`[${requestId}] Unexpected error in process-checkout:`, error);
    
    // Return error response
    return res.status(500).json({
      error: 'Server error',
      details: error.message,
      requestId
    });
  }
});

module.exports = router;
