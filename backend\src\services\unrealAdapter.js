const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const EventEmitter = require('events');

/**
 * Adapter for communicating with Unreal Engine via Python socket server
 */
class UnrealAdapter extends EventEmitter {
  constructor(config = {}) {
    super();
    this.pythonProcess = null;
    this.pythonPath = config.pythonPath || 'python';
    this.adapterPath = config.adapterPath || path.join(__dirname, '..', '..', '..', 'mcp_server', 'unreal_adapter.py');
    this.env = {
      ...process.env,
      UNREAL_HOST: config.unrealHost || process.env.UNREAL_HOST || 'localhost',
      UNREAL_PORT: config.unrealPort || process.env.UNREAL_PORT || '9877',
      BRIDGE_PORT: config.bridgePort || process.env.BRIDGE_PORT || '9878',
      UNREAL_API_KEY: config.apiKey || process.env.UNREAL_API_KEY || 'your_default_api_key'
    };
    
    this.isConnected = false;
    this.responseHandlers = new Map();
    this.messageId = 0;
    this.buffer = '';
    this.reconnectTimer = null;
    this.reconnectInterval = config.reconnectInterval || 5000;
  }

  /**
   * Start the Python adapter process
   * @returns {Promise<boolean>} True if connection successful
   */
  async connect() {
    if (this.pythonProcess) {
      return this.isConnected;
    }
    
    return new Promise((resolve, reject) => {
      // Check if adapter file exists
      if (!fs.existsSync(this.adapterPath)) {
        reject(new Error(`Unreal adapter script not found at: ${this.adapterPath}`));
        return;
      }
      
      try {
        // Start Python process
        this.pythonProcess = spawn(this.pythonPath, [this.adapterPath], { 
          env: this.env,
          stdio: ['pipe', 'pipe', 'pipe']
        });
        
        // Setup timeout for initial connection
        const connectionTimeout = setTimeout(() => {
          if (!this.isConnected) {
            reject(new Error('Timed out connecting to Unreal Engine'));
          }
        }, 10000);
        
        // Handle process output
        this.pythonProcess.stdout.on('data', (data) => {
          this._handlePythonOutput(data.toString());
        });
        
        // Handle errors
        this.pythonProcess.stderr.on('data', (data) => {
          const output = data.toString().trim();
          if (output.includes('Successfully connected to Unreal Engine')) {
            clearTimeout(connectionTimeout);
            this.isConnected = true;
            resolve(true);
          } else {
            console.error('Unreal adapter error:', output);
            this.emit('error', new Error(output));
          }
        });
        
        // Handle process exit
        this.pythonProcess.on('close', (code) => {
          console.log(`Unreal adapter process exited with code ${code}`);
          this.isConnected = false;
          this.pythonProcess = null;
          this.emit('disconnected');
          
          // Try to reconnect if not explicitly closed
          if (code !== 0) {
            this._scheduleReconnect();
          }
        });
      } catch (err) {
        reject(err);
      }
    });
  }
  
  /**
   * Schedule a reconnection attempt
   * @private
   */
  _scheduleReconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }
    
    this.reconnectTimer = setTimeout(async () => {
      console.log('Attempting to reconnect to Unreal Engine...');
      try {
        await this.connect();
        if (this.isConnected) {
          console.log('Successfully reconnected to Unreal Engine');
          this.emit('reconnected');
        }
      } catch (err) {
        console.error('Failed to reconnect:', err.message);
        this._scheduleReconnect();
      }
    }, this.reconnectInterval);
  }
  
  /**
   * Process output from Python process
   * @param {string} output The output string
   * @private
   */
  _handlePythonOutput(output) {
    this.buffer += output;
    
    let jsonStart = this.buffer.indexOf('{');
    let jsonEnd = this.buffer.indexOf('}', jsonStart);
    
    while (jsonStart !== -1 && jsonEnd !== -1) {
      try {
        const jsonStr = this.buffer.substring(jsonStart, jsonEnd + 1);
        const data = JSON.parse(jsonStr);
        
        // Handle message based on type
        if (data.type === 'event') {
          this.emit(data.event_type, data.data);
        } else if (data.id && this.responseHandlers.has(data.id)) {
          const { resolve, reject } = this.responseHandlers.get(data.id);
          if (data.success) {
            resolve(data);
          } else {
            reject(new Error(data.error || 'Unknown error'));
          }
          this.responseHandlers.delete(data.id);
        } else {
          // Log other messages
          console.log('Unreal adapter message:', data);
        }
      } catch (e) {
        console.error('Error parsing JSON from adapter:', e);
      }
      
      // Move past this JSON object
      this.buffer = this.buffer.substring(jsonEnd + 1);
      jsonStart = this.buffer.indexOf('{');
      jsonEnd = this.buffer.indexOf('}', jsonStart);
    }
  }

  /**
   * Send a command to Unreal Engine
   * @param {Object} command The command object
   * @returns {Promise<Object>} The response
   */
  async sendCommand(command) {
    if (!this.isConnected) {
      try {
        await this.connect();
      } catch (err) {
        throw new Error(`Not connected to Unreal Engine: ${err.message}`);
      }
    }
    
    return new Promise((resolve, reject) => {
      const id = this.messageId++;
      
      // Register handler for response
      this.responseHandlers.set(id, { resolve, reject });
      
      // Add ID to command
      const wrappedCommand = { 
        id, 
        ...command 
      };
      
      // Send to Python process
      try {
        this.pythonProcess.stdin.write(JSON.stringify(wrappedCommand) + '\n');
      } catch (err) {
        this.responseHandlers.delete(id);
        reject(new Error(`Failed to send command: ${err.message}`));
      }
      
      // Set timeout for response
      setTimeout(() => {
        if (this.responseHandlers.has(id)) {
          this.responseHandlers.delete(id);
          reject(new Error('Command timed out'));
        }
      }, 30000);
    });
  }
  
  /**
   * Register for events from Unreal Engine
   * @param {string} eventType The event type to register for
   * @returns {Promise<boolean>} True if registration successful
   */
  async registerEvent(eventType) {
    try {
      const response = await this.sendCommand({
        type: 'register_event',
        event_type: eventType
      });
      return response.success;
    } catch (err) {
      console.error(`Failed to register for event ${eventType}:`, err.message);
      return false;
    }
  }
  
  /**
   * Unregister from events from Unreal Engine
   * @param {string} eventType The event type to unregister from
   * @returns {Promise<boolean>} True if unregistration successful
   */
  async unregisterEvent(eventType) {
    try {
      const response = await this.sendCommand({
        type: 'unregister_event',
        event_type: eventType
      });
      return response.success;
    } catch (err) {
      console.error(`Failed to unregister from event ${eventType}:`, err.message);
      return false;
    }
  }
  
  /**
   * Spawn an object in Unreal Engine
   * @param {Object} options Object properties
   * @returns {Promise<Object>} The response
   */
  async spawnObject(options = {}) {
    const {
      actorClass = 'Cube',
      location = [0, 0, 100],
      rotation = [0, 0, 0],
      scale = [1, 1, 1],
      actorLabel = `Object_${Date.now()}`
    } = options;
    
    return this.sendCommand({
      type: 'spawn',
      actor_class: actorClass,
      location,
      rotation,
      scale,
      actor_label: actorLabel
    });
  }
  
  /**
   * Create a material in Unreal Engine
   * @param {Object} options Material properties
   * @returns {Promise<Object>} The response
   */
  async createMaterial(options = {}) {
    const {
      materialName = `Material_${Date.now()}`,
      color = [1, 1, 1]
    } = options;
    
    return this.sendCommand({
      type: 'create_material',
      material_name: materialName,
      color
    });
  }
  
  /**
   * Set material of an object in Unreal Engine
   * @param {Object} options Material assignment options
   * @returns {Promise<Object>} The response
   */
  async setObjectMaterial(options = {}) {
    const {
      actorName,
      materialPath
    } = options;
    
    if (!actorName || !materialPath) {
      throw new Error('Actor name and material path are required');
    }
    
    return this.sendCommand({
      type: 'modify_object',
      actor_name: actorName,
      property_type: 'material',
      value: materialPath
    });
  }
  
  /**
   * Close the connection to Unreal Engine
   */
  close() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.pythonProcess) {
      this.pythonProcess.kill();
      this.pythonProcess = null;
    }
    
    this.isConnected = false;
  }
}

module.exports = UnrealAdapter;