'use client';

import React, { useEffect, useRef, useState } from 'react';
import Link from 'next/link';
import * as THREE from 'three';
import YouTubeBackground from '../YouTubeBackground';

interface HeroSectionProps {
  onGetStarted: () => void;
  // Add a new prop for the destination URL
  getStartedUrl?: string;
}

const HeroSection: React.FC<HeroSectionProps> = ({ onGetStarted, getStartedUrl = '/signup' }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [showYouTubeBackground, setShowYouTubeBackground] = useState(true);

  // Handle YouTube background fallback
  useEffect(() => {
    // Set a timeout to check if YouTube background loaded properly
    // If there's an issue, we'll fall back to the static image
    const fallbackTimer = setTimeout(() => {
      // Check if YouTube iframe exists and is loaded
      const youtubeIframe = document.querySelector('iframe[src*="youtube.com"]');
      if (!youtubeIframe) {
        console.log('YouTube background failed to load, falling back to static image');
        setShowYouTubeBackground(false);
      }
    }, 5000); // Give it 5 seconds to load

    return () => clearTimeout(fallbackTimer);
  }, []);

  // Initialize Three.js scene
  useEffect(() => {
    if (!canvasRef.current) return;

    // Create scene
    const scene = new THREE.Scene();

    // Create camera
    const camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    camera.position.z = 5;

    // Create renderer
    const renderer = new THREE.WebGLRenderer({
      canvas: canvasRef.current,
      alpha: true,
      antialias: true,
    });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));

    // Create particles
    const particlesGeometry = new THREE.BufferGeometry();
    const particlesCount = 2000;

    const posArray = new Float32Array(particlesCount * 3);
    const colorArray = new Float32Array(particlesCount * 3);

    for (let i = 0; i < particlesCount * 3; i++) {
      // Position
      posArray[i] = (Math.random() - 0.5) * 10;

      // Color - blue/purple gradient
      if (i % 3 === 0) {
        colorArray[i] = 0.1 + Math.random() * 0.2; // R
        colorArray[i + 1] = 0.3 + Math.random() * 0.3; // G
        colorArray[i + 2] = 0.7 + Math.random() * 0.3; // B
      }
    }

    particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));
    particlesGeometry.setAttribute('color', new THREE.BufferAttribute(colorArray, 3));

    // Material
    const particlesMaterial = new THREE.PointsMaterial({
      size: 0.02,
      vertexColors: true,
      transparent: true,
      opacity: 0.8,
    });

    // Mesh
    const particlesMesh = new THREE.Points(particlesGeometry, particlesMaterial);
    scene.add(particlesMesh);

    // Handle resize
    const handleResize = () => {
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
    };

    window.addEventListener('resize', handleResize);

    // Mouse movement effect
    let mouseX = 0;
    let mouseY = 0;

    const handleMouseMove = (event: MouseEvent) => {
      mouseX = (event.clientX / window.innerWidth) * 2 - 1;
      mouseY = -(event.clientY / window.innerHeight) * 2 + 1;
    };

    window.addEventListener('mousemove', handleMouseMove);

    // Animation loop
    const animate = () => {
      requestAnimationFrame(animate);

      // Rotate particles based on mouse position
      particlesMesh.rotation.x += 0.001;
      particlesMesh.rotation.y += 0.001;

      particlesMesh.rotation.x += mouseY * 0.001;
      particlesMesh.rotation.y += mouseX * 0.001;

      renderer.render(scene, camera);
    };

    animate();

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('mousemove', handleMouseMove);

      // Dispose resources
      particlesGeometry.dispose();
      particlesMaterial.dispose();
      renderer.dispose();
    };
  }, []);

  return (
    <section className="relative h-screen flex items-center justify-center overflow-hidden" id="hero">
      {/* Three.js Canvas */}
      <canvas ref={canvasRef} className="absolute top-0 left-0 w-full h-full -z-10" />

      {/* YouTube Background Video */}
      {showYouTubeBackground && (
        <YouTubeBackground
          videoId="qC5KtatMcUw"
          startTime={361} // 6:01 in seconds
          endTime={510}   // 8:30 in seconds
          muted={true}
          loop={true}
          opacity={0.9}
        />
      )}

      {/* Fallback Background Image (shown if YouTube fails to load) */}
      <div
        className={`absolute top-0 left-0 w-full h-full bg-cover bg-center transition-opacity duration-1000 ${showYouTubeBackground ? 'opacity-0' : 'opacity-100'}`}
        style={{
          backgroundImage: 'url(/images/backgrounds/unreal-bg.jpg)',
          backgroundBlendMode: 'overlay',
          filter: 'brightness(0.6)'
        }}
      />

      {/* Light Gradient Overlay - Just enough for text readability */}
      <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-b from-black/30 to-black/40 -z-5" />

      {/* Diagonal Lines - Unreal Style (reduced opacity) */}
      <div className="absolute top-0 left-0 w-full h-full z-0 opacity-10">
        <div className="absolute top-1/4 left-0 w-full h-0.5 bg-blue-400 transform -rotate-12"></div>
        <div className="absolute top-1/3 left-0 w-full h-0.5 bg-blue-500 transform rotate-6"></div>
        <div className="absolute top-2/3 left-0 w-full h-0.5 bg-blue-300 transform -rotate-3"></div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 z-10 text-center" style={{ textShadow: '0 2px 4px rgba(0,0,0,0.5)' }}>
        <div className="inline-block mb-4 px-4 py-1 border border-blue-400 rounded-full bg-blue-900/30">
          <span className="text-blue-300 font-medium">Powered by AI</span>
        </div>

        <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
          <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-300">Unreal Engine</span> Assistant
        </h1>

        <p className="text-xl md:text-2xl text-gray-200 mb-10 max-w-3xl mx-auto">
          Transform your game development workflow with natural language commands.
          Create materials, spawn objects, and generate blueprints with simple text prompts.
        </p>

        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Link
            href={getStartedUrl}
            className="px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-400 hover:from-blue-500 hover:to-blue-300 text-white font-bold rounded-lg text-lg transition-all shadow-lg hover:shadow-blue-500/50 inline-block text-center"
          >
            Get Started
          </Link>

          <Link
            href="/download"
            className="px-8 py-4 bg-gradient-to-r from-green-600 to-green-400 hover:from-green-500 hover:to-green-300 text-white font-bold rounded-lg text-lg transition-all shadow-lg hover:shadow-green-500/50 inline-block text-center"
          >
            Download Plugin
          </Link>

          <a
            href="#demo"
            className="px-8 py-4 bg-transparent border-2 border-blue-400 text-blue-300 font-bold rounded-lg text-lg hover:bg-blue-900/30 transition-colors"
          >
            Watch Demo
          </a>
        </div>
      </div>

      {/* Video toggle button */}
      <button
        onClick={() => setShowYouTubeBackground(!showYouTubeBackground)}
        className="absolute bottom-24 right-6 z-20 bg-gray-800/70 hover:bg-gray-700/70 text-white p-2 rounded-full transition-colors"
        title={showYouTubeBackground ? "Disable video background" : "Enable video background"}
      >
        {showYouTubeBackground ? (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
        ) : (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
          </svg>
        )}
      </button>

      {/* Scroll indicator */}
      <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 animate-bounce">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-10 w-10 text-blue-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 14l-7 7m0 0l-7-7m7 7V3"
          />
        </svg>
      </div>
    </section>
  );
};

export default HeroSection;
