<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>CreateLex Bridge – Login</title>
  <style>
    body { font-family: Arial, sans-serif; background:#202124; color:#e8eaed; display:flex; align-items:center; justify-content:center; height:100vh; margin:0; }
    .card { background:#303134; padding:40px 50px; border-radius:8px; width:320px; box-shadow:0 2px 10px rgba(0,0,0,0.5); }
    h2 { margin-top:0; text-align:center; }
    input { width:100%; padding:10px; margin:10px 0; border:none; border-radius:4px; font-size:14px; }
    button { width:100%; padding:10px; border:none; border-radius:4px; background:#1a73e8; color:#fff; font-size:16px; cursor:pointer; }
    button:hover { background:#1669c1; }
    #status { margin-top:15px; text-align:center; font-size:14px; color:#f28b82; }
  </style>
</head>
<body>
  <div class="card">
    <h2>CreateLex Login</h2>
    <form id="loginForm">
      <input type="email" id="email" placeholder="Email" required />
      <input type="password" id="password" placeholder="Password" required />
      <button type="submit">Log In</button>
    </form>
    <div id="status"></div>
  </div>

  <script>
    const form = document.getElementById('loginForm');
    const statusEl = document.getElementById('status');

    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      statusEl.style.color = '#e8eaed';
      statusEl.textContent = 'Authenticating…';
      const email = document.getElementById('email').value.trim();
      const password = document.getElementById('password').value;
      try {
        const result = await window.bridgeApi.send('authenticate', { email, password });
        if (result.success) {
          statusEl.style.color = '#34a853';
          statusEl.textContent = 'Login successful!';
          // Main process will automatically transition to dashboard
        } else {
          statusEl.style.color = '#f28b82';
          statusEl.textContent = result.error || 'Login failed';
        }
      } catch (err) {
        statusEl.style.color = '#f28b82';
        statusEl.textContent = err.message || 'Unexpected error';
      }
    });
  </script>
</body>
</html> 