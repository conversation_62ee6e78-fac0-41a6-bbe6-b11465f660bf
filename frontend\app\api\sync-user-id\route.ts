import { NextRequest, NextResponse } from 'next/server';

// Completely bypass Supabase during build time
const isBuildTime = process.env.NODE_ENV === 'production' && typeof window === 'undefined';
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY || '';

export async function POST(request: NextRequest) {
  try {
    // Get the request body
    const body = await request.json();
    const { userId, deviceId, source } = body;

    if (!userId || !deviceId) {
      return NextResponse.json({
        success: false,
        error: 'Missing required parameters'
      }, { status: 400 });
    }

    console.log(`API: Using ${source} UUID ${userId} as primary user ID`);

    // During build time, skip Supabase client creation
    if (isBuildTime) {
      console.log('[API] Build-time environment detected, skipping Supabase client creation');
    } else {
      try {
        // Dynamic import to prevent build-time evaluation
        const { createClient } = require('@supabase/supabase-js');
        const supabase = createClient(supabaseUrl, supabaseServiceKey);
        console.log('[Supabase] Client initialized successfully');

        // In a real implementation, we would use the supabase client to store the mapping
      } catch (error) {
        console.error('[Supabase] Error initializing client:', error);
      }
    }

    // In a real implementation, we would store the user ID mapping in the database
    // For now, we'll just return a success response
    return NextResponse.json({
      success: true,
      userId,
      deviceId,
      source
    }, { status: 200 });
  } catch (error) {
    console.error('Error storing user ID mapping:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
