# CreateLex Universal Bridge - Quick Start

Get Unreal Engine tools in your AI coding assistant in **3 simple steps**!

## ⚡ Quick Setup (5 minutes)

### Step 1: Download & Start Bridge App
1. Download the CreateLex Bridge app from [createlex.com/download](https://createlex.com/download)
2. Install and launch the app
3. Login with your CreateLex credentials
4. Click **"Start MCP Server"** in the dashboard

### Step 2: Configure Your AI Assistant

Choose your AI assistant and add the configuration:

#### 🤖 Claude <PERSON>op
Add to `%APPDATA%\Claude\claude_desktop_config.json`:
```json
{
  "mcpServers": {
    "createlex-unreal": {
      "command": "node",
      "args": ["[ProjectName]/Plugins/CreatelexGenAI/Content/Tools/bridge-native.js"]
    }
  }
}
```

> Replace `[ProjectName]` with your actual Unreal project path (e.g., `C:/Dev/MyGame`)

#### 🎯 Cursor
Add to Cursor settings:
```json
{
  "mcp.servers": {
    "createlex-unreal": {
      "command": "node",
      "args": ["[ProjectName]/Plugins/CreatelexGenAI/Content/Tools/bridge-native.js"]
    }
  }
}
```

#### 🌊 Windsurf
Add to Windsurf configuration:
```json
{
  "mcp": {
    "servers": {
      "createlex-unreal": {
        "command": "node",
        "args": ["[ProjectName]/Plugins/CreatelexGenAI/Content/Tools/bridge-native.js"]
      }
    }
  }
}
```

#### 📝 VS Code
If using an MCP extension:
```json
{
  "mcp.servers": [
    {
      "name": "createlex-unreal",
      "command": "node",
      "args": ["[ProjectName]/Plugins/CreatelexGenAI/Content/Tools/bridge-native.js"]
    }
  ]
}
```

### Step 3: Test the Connection

1. **Restart your AI assistant** (Claude Desktop, Cursor, etc.)
2. **Open Unreal Engine** (any project)
3. **Ask your AI assistant**: *"Can you help me create a new material in Unreal Engine?"*

You should see your AI assistant respond with available Unreal Engine tools! 🎉

## 🛠️ What You Can Do

Once connected, ask your AI assistant to help with:

### 📦 **Project Management**
- *"Create a new Unreal Engine project"*
- *"Open my existing project"*
- *"Show me project information"*

### 🎨 **Asset Creation**
- *"Create a red metallic material"*
- *"Import this 3D model into Unreal"*
- *"Create a new Blueprint class"*

### 🏗️ **Level Design**
- *"Place a cube in the scene"*
- *"Create a new level called 'TestLevel'"*
- *"Set up basic lighting"*

### 🎮 **Gameplay**
- *"Create a third-person character controller"*
- *"Set up a game mode"*
- *"Create a UI widget"*

### 🎬 **Animation**
- *"Import this skeletal mesh"*
- *"Create an animation blueprint"*
- *"Set up character movement"*

## 🔧 Troubleshooting

### ❌ "Connection refused" error
- Make sure the CreateLex Bridge app is running
- Click "Start MCP Server" in the dashboard
- Check that port 9877 is not blocked by firewall

### ❌ "Module not found" error
- Make sure Node.js is installed
- Update the path in your config to match your installation
- Use forward slashes `/` in the path, not backslashes `\`

### ❌ AI assistant doesn't see the tools
- Restart your AI assistant after adding the configuration
- Check that the JSON configuration is valid (no syntax errors)
- Look for error messages in your AI assistant's logs

## 🆘 Need Help?

- **Full Setup Guide**: [BRIDGE-SETUP.md](./BRIDGE-SETUP.md)
- **Support Center**: [createlex.com/support](https://createlex.com/support)
- **Documentation**: [createlex.com/docs](https://createlex.com/docs)
- **Contact Us**: [<EMAIL>](mailto:<EMAIL>)

## 🎯 What's Next?

Once you're up and running:
1. **Explore all 21+ tools** - Ask your AI assistant what Unreal Engine tools are available
2. **Try complex workflows** - Create entire game features with natural language
3. **Share feedback** - Let us know how it works for you at [createlex.com/feedback](https://createlex.com/feedback)

---

**Ready to supercharge your Unreal Engine development with AI? Let's go!** 🚀 