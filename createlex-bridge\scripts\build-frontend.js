const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs-extra');

(async () => {
  try {
    const frontendDir = path.resolve(__dirname, '..', '..', 'frontend');
    const outDir = path.join(frontendDir, 'out');
    const destDir = path.resolve(__dirname, '..', 'web');

    console.log('📦 Building Next.js frontend for Electron bridge...');
    
    // Set the Next.js config for bridge mode
    const configFile = path.join(frontendDir, 'next.config.bridge.js');
    
    // Build with bridge-specific configuration
    execSync(`npx next build --config-file=${configFile}`, { 
      cwd: frontendDir, 
      stdio: 'inherit',
      env: {
        ...process.env,
        NODE_ENV: 'production',
        NEXT_PUBLIC_BRIDGE_MODE: 'true'
      }
    });

    console.log('🚚 Copying frontend output to Electron web directory...');
    
    // Ensure destination directory exists and is clean
    await fs.ensureDir(destDir);
    await fs.emptyDir(destDir);
    
    // Copy the static export to the web directory
    if (await fs.pathExists(outDir)) {
      await fs.copy(outDir, destDir);
      console.log('✅ Frontend copied to', destDir);
      
      // Verify critical files exist
      const loginFile = path.join(destDir, 'login', 'index.html');
      const dashboardFile = path.join(destDir, 'dashboard', 'index.html');
      
      if (await fs.pathExists(loginFile)) {
        console.log('✅ Login page found');
      } else {
        console.warn('⚠️ Login page not found at expected location');
      }
      
      if (await fs.pathExists(dashboardFile)) {
        console.log('✅ Dashboard page found');
      } else {
        console.warn('⚠️ Dashboard page not found at expected location');
      }
      
    } else {
      throw new Error(`Output directory not found: ${outDir}`);
    }
    
  } catch (err) {
    console.error('❌ Failed to build frontend', err);
    console.error('\n💡 Make sure you have installed dependencies in the frontend directory:');
    console.error('   cd frontend && npm install');
    process.exit(1);
  }
})(); 