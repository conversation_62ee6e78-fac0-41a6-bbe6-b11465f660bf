# 🐧 Linux Testing Environment

This directory contains scripts to test CreateLex Bridge on Linux (WSL2) as a reference for macOS compatibility.

## 🎯 Purpose

Since macOS and Linux are both Unix-like systems, testing on Linux helps validate:
- ✅ Cross-platform Python detection (`python3`)
- ✅ PyInstaller executable creation
- ✅ File permissions and executable behavior
- ✅ Build system compatibility

## 🚀 Quick Start

### 1. Launch WSL2 Ubuntu
```bash
wsl -d Ubuntu
```

### 2. Run Setup Script
```bash
# Copy and run setup script
cp /mnt/c/Dev/AiWebplatform/createlex-bridge/linux-testing/setup-linux-test.sh ~/
chmod +x ~/setup-linux-test.sh
~/setup-linux-test.sh
```

### 3. Run Tests
```bash
# Copy and run test script
cp /mnt/c/Dev/AiWebplatform/createlex-bridge/linux-testing/test-linux-build.sh ~/
chmod +x ~/test-linux-build.sh
~/test-linux-build.sh
```

## 📋 What Gets Tested

### ✅ Cross-Platform Compatibility
- Platform detection (should show `linux`)
- Python command detection (should use `python3`)
- PyInstaller availability
- Build script validation

### ✅ MCP Executable Build
- Creates `mcp_server_linux` executable
- Bundles all Python dependencies
- Tests executable permissions
- Validates executable can start

### ✅ Full Application Build
- Creates Linux `.AppImage` package
- Tests complete build pipeline
- Validates all components work together

## 🔍 Expected Results

If Linux tests pass, macOS should work similarly:

| Component | Linux | macOS Expected |
|-----------|-------|----------------|
| **Platform** | `linux` | `darwin` |
| **Python Cmd** | `python3` ✅ | `python3` ✅ |
| **Executable** | `mcp_server_linux` ✅ | `mcp_server_mac` ✅ |
| **Package** | `.AppImage` ✅ | `.dmg` ✅ |
| **Dependencies** | Bundled ✅ | Bundled ✅ |

## 🐛 Troubleshooting

### Common Issues:
- **Node.js not found:** Run setup script again
- **PyInstaller fails:** Check Python dependencies
- **Executable won't run:** Check permissions with `chmod +x`
- **Build fails:** Check npm dependencies installed

### Debug Commands:
```bash
# Check versions
node --version
python3 --version
pip3 --version
pyinstaller --version

# Check executable
file src/python-protected/mcp_server_linux
ldd src/python-protected/mcp_server_linux
```

## 🎯 Success Criteria

✅ **Linux tests pass** → macOS should work  
✅ **Executable created** → PyInstaller working  
✅ **Executable runs** → Dependencies bundled correctly  
✅ **Build completes** → Full pipeline working  

This validates the cross-platform improvements work correctly! 🚀 