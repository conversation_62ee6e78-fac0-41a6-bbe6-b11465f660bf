version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - PORT=3000
      - MCP_SERVER_URL=ws://mcp_server:9877
      - API_URL=http://backend:5001
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
    depends_on:
      - mcp_server
    volumes:
      - .:/app
      - /app/node_modules

  mcp_server:
    # This should point to your existing MCP server
    # You can either use the image from your main project or create a new one
    image: mcp_server
    ports:
      - "9877:9877"
    environment:
      - MCP_PORT=9877
      - MCP_HOST=0.0.0.0
    volumes:
      - ../mcp_server:/app
    extra_hosts:
      - "host.docker.internal:host-gateway"
