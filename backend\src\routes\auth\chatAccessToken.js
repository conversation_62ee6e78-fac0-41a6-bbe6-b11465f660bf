const express = require('express');
const router = express.Router();
const crypto = require('crypto');
const { authenticateJWT } = require('../../middleware/auth');
const subscriptionService = require('../../services/subscriptionService');

// Store for temporary access tokens
// In production, this should be stored in Redis or a similar service
const accessTokens = new Map();

// Token expiration time (5 minutes)
const TOKEN_EXPIRATION = 5 * 60 * 1000;

// Clean up expired tokens every 10 minutes
setInterval(() => {
  const now = Date.now();
  for (const [token, data] of accessTokens.entries()) {
    if (now > data.expiresAt) {
      accessTokens.delete(token);
    }
  }
}, 10 * 60 * 1000);

/**
 * @swagger
 * /api/auth/chat-access-token:
 *   post:
 *     summary: Generate a temporary access token for the chat application
 *     tags: [Auth]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Access token generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 token:
 *                   type: string
 *                   description: The temporary access token
 *                 expiresAt:
 *                   type: number
 *                   description: Timestamp when the token expires
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Subscription required
 *       500:
 *         description: Server error
 */
router.post('/chat-access-token', authenticateJWT, async (req, res) => {
  try {
    const userId = req.user.id;

    // Check if the user has a premium subscription
    const hasSubscription = await subscriptionService.checkSubscription(userId);

    if (!hasSubscription) {
      return res.status(403).json({
        error: 'Subscription required',
        message: 'You need an active subscription to access the chat'
      });
    }

    // Generate a random token
    const token = crypto.randomBytes(32).toString('hex');

    // Set expiration time
    const expiresAt = Date.now() + TOKEN_EXPIRATION;

    // Store the token with user information
    accessTokens.set(token, {
      userId,
      expiresAt,
      createdAt: Date.now(),
      userAgent: req.headers['user-agent'] || 'unknown'
    });

    return res.status(200).json({
      token,
      expiresAt
    });
  } catch (error) {
    console.error('Error generating chat access token:', error);
    return res.status(500).json({ error: 'Failed to generate access token' });
  }
});

/**
 * @swagger
 * /api/auth/verify-chat-access:
 *   post:
 *     summary: Verify a chat access token
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               token:
 *                 type: string
 *                 description: The access token to verify
 *     responses:
 *       200:
 *         description: Token is valid
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 valid:
 *                   type: boolean
 *                   description: Whether the token is valid
 *                 userId:
 *                   type: string
 *                   description: The user ID associated with the token
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Invalid or expired token
 *       500:
 *         description: Server error
 */
router.post('/verify-chat-access', async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({ error: 'Token is required' });
    }

    // Check if the token exists and is not expired
    const tokenData = accessTokens.get(token);

    if (!tokenData) {
      return res.status(401).json({
        error: 'Invalid token',
        valid: false
      });
    }

    if (Date.now() > tokenData.expiresAt) {
      // Remove expired token
      accessTokens.delete(token);

      return res.status(401).json({
        error: 'Token expired',
        valid: false
      });
    }

    // Token is valid
    return res.status(200).json({
      valid: true,
      userId: tokenData.userId
    });
  } catch (error) {
    console.error('Error verifying chat access token:', error);
    return res.status(500).json({ error: 'Failed to verify access token' });
  }
});

module.exports = router;
