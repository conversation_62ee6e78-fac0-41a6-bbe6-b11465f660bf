const express = require('express');
const router = express.Router();
const tokenPurchaseService = require('../../services/tokenPurchaseService');
const { authenticateJWT, authenticateJWTWithFallback } = require('../../middleware/auth');

/**
 * @route POST /api/tokens/purchase
 * @description Purchase additional tokens
 * @access Private
 */
router.post('/', authenticateJWTWithFallback, async (req, res) => {
  try {
    // Get user ID from the authenticated user object
    const userId = req.user.id;
    const { packageId, successUrl, cancelUrl, promoCode } = req.body;

    // Validate required fields
    if (!packageId || !successUrl || !cancelUrl) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    console.log(`Creating token purchase checkout session for user ${userId}, package ${packageId}${promoCode ? `, promo code: ${promoCode}` : ''}`);

    // Create a checkout session using the token purchase service
    const session = await tokenPurchaseService.createCheckoutSession(
      userId,
      packageId,
      successUrl,
      cancelUrl,
      promoCode // Pass the promo code to the service
    );

    return res.json({
      success: true,
      sessionId: session.id,
      url: session.url
    });
  } catch (error) {
    console.error('Error creating checkout session:', error);

    // Return a more helpful error message
    return res.status(500).json({
      error: 'Failed to create checkout session',
      details: error.message
    });
  }
});

/**
 * @route POST /api/tokens/purchase/simulate-webhook
 * @description Temporary endpoint to simulate a Stripe webhook event for token purchases
 * @access Private - For testing only
 */
router.post('/simulate-webhook', authenticateJWTWithFallback, async (req, res) => {
  // Generate a unique webhook ID for tracking
  const webhookId = `webhook-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;

  try {
    // Get user ID from the request body or fall back to the authenticated user's ID
    const userId = req.body.userId || req.user.id;
    const { packageId = 'small' } = req.body;

    console.log(`[${webhookId}] SIMULATE WEBHOOK: Starting token purchase simulation for user ${userId}, package ${packageId}`);

    // Get the token amount based on the package ID
    let tokenAmount = 100000; // Default to small package (100,000 tokens)
    if (packageId === 'medium') {
      tokenAmount = 500000;
    } else if (packageId === 'large') {
      tokenAmount = 1000000;
    }

    console.log(`[${webhookId}] SIMULATE WEBHOOK: Selected package ${packageId} with ${tokenAmount} tokens`);

    // Create a transaction ID for this purchase
    const transactionId = `sim-${webhookId}`;

    // Add tokens to the user's account
    console.log(`[${webhookId}] SIMULATE WEBHOOK: Calling addTokensToUser with userId=${userId}, tokens=${tokenAmount}, transactionId=${transactionId}`);

    try {
      // If we're getting a transaction record directly, we need to handle it differently
      let result;
      try {
        result = await tokenPurchaseService.addTokensToUser(userId, tokenAmount, transactionId);

        // Check if we got a valid result with balance property
        if (!result || !result.balance) {
          // If we got a transaction record directly, use it
          if (result && result.id && result.new_balance) {
            console.log(`[${webhookId}] SIMULATE WEBHOOK: Got transaction record directly:`, result);

            // Create a proper result object
            result = {
              balance: result.new_balance,
              transaction: result
            };
          } else {
            console.error(`[${webhookId}] SIMULATE WEBHOOK: Invalid result from addTokensToUser:`, result);
            return res.status(500).json({
              success: false,
              error: 'Failed to add tokens to user - invalid result',
              webhookId
            });
          }
        }
      } catch (tokenError) {
        console.error(`[${webhookId}] SIMULATE WEBHOOK: Error adding tokens:`, tokenError);

        // Return a detailed error response
        return res.status(500).json({
          success: false,
          error: 'Failed to add tokens to user',
          details: tokenError.message,
          operationId: tokenError.operationId,
          webhookId
        });
      }

      console.log(`[${webhookId}] SIMULATE WEBHOOK: Successfully added tokens, new balance=${result.balance}`);
      console.log(`[${webhookId}] SIMULATE WEBHOOK: Transaction details:`, result.transaction);

      return res.json({
        success: true,
        message: `Successfully added ${tokenAmount} tokens to user ${userId}`,
        result,
        webhookId
      });
    } catch (error) {
      console.error(`[${webhookId}] SIMULATE WEBHOOK: Error adding tokens:`, tokenError);

      // Return a detailed error response
      return res.status(500).json({
        success: false,
        error: 'Failed to add tokens to user',
        details: tokenError.message,
        operationId: tokenError.operationId,
        webhookId
      });
    }
  } catch (error) {
    console.error(`[${webhookId}] SIMULATE WEBHOOK: Unexpected error:`, error);

    // Return a more helpful error message
    return res.status(500).json({
      success: false,
      error: 'Failed to simulate webhook',
      details: error.message,
      webhookId
    });
  }
});

module.exports = router;
