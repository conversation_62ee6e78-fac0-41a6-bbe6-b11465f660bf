#!/bin/bash

# This script tests the integration of the frontend application

# Check if the PORT environment variable is set to 3000
if grep -q "PORT=3000" .env 2>/dev/null; then
  echo "✅ PORT is set to 3000 in .env"
else
  echo "❌ PORT is not set to 3000 in .env"
  echo "Please update your .env file to include PORT=3000"
fi

# Check if next.config.js has the correct port
if grep -q "PORT: process.env.PORT || '3000'" next.config.js; then
  echo "✅ next.config.js has the correct port"
else
  echo "❌ next.config.js does not have the correct port"
  echo "Please update next.config.js to use port 3000"
fi

# Check if server.js has the correct port
if grep -q "PORT = process.env.PORT || 3000" server.js; then
  echo "✅ server.js has the correct port"
else
  echo "❌ server.js does not have the correct port"
  echo "Please update server.js to use port 3000"
fi

# Check if Docker<PERSON>le exposes the correct port
if grep -q "EXPOSE 3000" Dockerfile; then
  echo "✅ Dockerfile exposes the correct port"
else
  echo "❌ Dockerfile does not expose the correct port"
  echo "Please update Dockerfile to expose port 3000"
fi

# Check if docker-compose.yml has the correct port
if grep -q '"3000:3000"' docker-compose.yml; then
  echo "✅ docker-compose.yml has the correct port"
else
  echo "❌ docker-compose.yml does not have the correct port"
  echo "Please update docker-compose.yml to use port 3000"
fi

echo ""
echo "Integration test complete. Please fix any issues before running the application."
