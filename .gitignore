# Dependencies
node_modules/
**/node_modules/
**/package-lock.json

# Next.js
**/.next/
**/out/

# Build
**/build/
**/dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.production
.env.docker
*/.env.production

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Docker volumes
docker-volumes/

# IDE
.vscode/
.idea/

# OS
.DS_Store
**/.DS_Store

# Unreal Engine
Binaries/
Intermediate/
Build/
.vs/
*.sln
*.suo
*.user
*.userosscache
*.sln.docstates
*.tmp
*.temp
*.log
*.cache

# Unreal Engine specific
DerivedDataCache/
Saved/
*.uasset
*.umap
!Content/**/*.uasset
!Content/**/*.umap

# Plugin specific
CreateLexServerMCPPlugin/Binaries/
CreateLexServerMCPPlugin/Intermediate/
CreateLexUnrealGenAISupport/Binaries/
CreateLexUnrealGenAISupport/Intermediate/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
develop-eggs/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/

# Database
*.db
*.sqlite
*.sqlite3

# Certificates
*.pem
*.key
*.crt

# Backup files
*.bak
*.backup
*.old

# Test files
test-results/
coverage/
.nyc_output/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# External git repositories (reference only)
CreateLexUnrealGenAISupport/