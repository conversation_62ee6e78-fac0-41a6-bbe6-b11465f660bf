'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { getSupabaseClient } from '../lib/supabase-singleton';
import { useRouter } from 'next/navigation';
import { useSupabaseAuth } from './SupabaseAuthContext';

interface User {
  id: string;
  name?: string;
  email?: string;
  avatar?: string;
}

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  userId: string | null;
  token: string | null;
  hasActiveSubscription: boolean;
  login: (email: string, password?: string, provider?: string) => Promise<void>;
  logout: () => Promise<void>;
  signIn: (provider: string) => Promise<void>;
  updateSubscription: (hasSubscription: boolean) => void;
  refreshSubscriptionStatus: () => Promise<any>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const router = useRouter();
  const { user: supabaseUser, session, isLoading: supabaseLoading } = useSupabaseAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [hasActiveSubscription, setHasActiveSubscription] = useState(false);

  // Use the Supabase auth state to set our auth state
  useEffect(() => {
    const syncWithSupabaseAuth = async () => {
      try {
        // If Supabase is still loading, wait for it
        if (supabaseLoading) {
          return;
        }

        console.log('Syncing with Supabase auth:', { supabaseUser, session });

        if (session && supabaseUser) {
          setIsAuthenticated(true);
          setToken(session.access_token);
          setUserId(supabaseUser.id);
          setUser({
            id: supabaseUser.id,
            name: supabaseUser.user_metadata?.full_name || supabaseUser.email,
            email: supabaseUser.email,
            avatar: supabaseUser.user_metadata?.avatar_url,
          });

          // We'll check subscription status with the backend directly
          console.log('AuthContext: Will check subscription status with backend for user', supabaseUser.id);

          // Check subscription status with the backend
          try {
            // Use environment variable to control subscription bypass
            const bypassSubscription = process.env.NEXT_PUBLIC_BYPASS_SUBSCRIPTION === 'true';

            console.log('AuthContext: Initial subscription bypass set to:', bypassSubscription);

            // Bypass subscription check if configured
            if (bypassSubscription) {
              console.log('AuthContext: Bypassing initial subscription check for user', supabaseUser.id);
              updateSubscription(true);
              return;
            }

            const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
            console.log('AuthContext: Checking subscription status at:', `${apiUrl}/api/subscription/check`);
            console.log('AuthContext: Making API call with token:', session.access_token.substring(0, 10) + '...');

            // Include the user ID in the URL to ensure the backend can identify the user
            const subscriptionResponse = await fetch(`${apiUrl}/api/subscription/check?userId=${supabaseUser.id}`, {
              headers: {
                'Authorization': `Bearer ${session.access_token}`
              }
            });

            console.log('AuthContext: API response status:', subscriptionResponse.status);

            if (subscriptionResponse.ok) {
              const subscriptionData = await subscriptionResponse.json();
              console.log('AuthContext: Subscription check result for user', supabaseUser.id, ':', subscriptionData);
              updateSubscription(subscriptionData.hasActiveSubscription); // Use updateSubscription to ensure proper storage
            } else {
              console.error('AuthContext: Failed to check subscription status for user', supabaseUser.id, ':', subscriptionResponse.status);

              // Check if we should bypass subscription checks
              const bypassSubscription = process.env.NEXT_PUBLIC_BYPASS_SUBSCRIPTION === 'true';

              if (bypassSubscription) {
                console.log('AuthContext: Bypass enabled, setting hasActiveSubscription to true for user', supabaseUser.id);
                updateSubscription(true);
              } else {
                // Respect the actual subscription status (API failed, so no subscription)
                console.log('AuthContext: API failed and bypass disabled, setting hasActiveSubscription to false for user', supabaseUser.id);
                updateSubscription(false);
              }
            }
          } catch (error) {
            console.error('AuthContext: Error checking subscription status for user', supabaseUser.id, ':', error);

            // Check if we should bypass subscription checks
            const bypassSubscription = process.env.NEXT_PUBLIC_BYPASS_SUBSCRIPTION === 'true';

            if (bypassSubscription) {
              console.log('AuthContext: Bypass enabled, setting hasActiveSubscription to true for user', supabaseUser.id);
              updateSubscription(true);
            } else {
              // Respect the actual subscription status (error occurred, so no subscription)
              console.log('AuthContext: Error occurred and bypass disabled, setting hasActiveSubscription to false for user', supabaseUser.id);
              updateSubscription(false);
            }
          }

          // Only redirect from login/signup pages to dashboard if authenticated
          // Never redirect from chat pages, access-denied page, or bridge authentication flows
          if (typeof window !== 'undefined') {
            const path = window.location.pathname;
            const urlParams = new URLSearchParams(window.location.search);
            const isBridgeAuth = urlParams.get('source') === 'bridge';
            const isChatPage = path.startsWith('/chat');
            const isAccessDeniedPage = path === '/access-denied';
            const isBridgeCallbackPage = path === '/bridge-callback';

            // Don't redirect if this is a bridge authentication flow
            if (isBridgeAuth || isBridgeCallbackPage) {
              console.log('AuthContext: Skipping dashboard redirect - bridge authentication detected');
              return;
            }

            // Only redirect from login/signup/home pages
            if ((path === '/login' || path === '/signup' || path === '/') && !isChatPage && !isAccessDeniedPage) {
              console.log('Redirecting to dashboard from AuthContext');
              // Set loading to false before redirecting to prevent loading indicator from persisting
              setIsLoading(false);
              router.push('/dashboard');
              return; // Exit early to avoid setting isLoading again in finally block
            }
          }
        } else {
          // No session found
          setIsAuthenticated(false);
          setToken(null);
          setUserId(null);
          setUser(null);
        }
      } catch (error) {
        console.error('Error syncing with Supabase auth:', error);
      } finally {
        setIsLoading(false);
      }
    };

    syncWithSupabaseAuth();

    // Add a safety timeout to ensure loading state is cleared after a maximum time
    // This prevents the UI from being stuck in a loading state indefinitely
    const safetyTimeout = setTimeout(() => {
      if (isLoading) {
        console.log('Safety timeout triggered: Clearing loading state');
        setIsLoading(false);
      }
    }, 5000); // 5 seconds timeout

    return () => clearTimeout(safetyTimeout);
  }, [supabaseUser, session, supabaseLoading, router, isLoading]);

  // Login with email and password
  const login = async (email: string, password?: string, provider?: string) => {
    try {
      setIsLoading(true);
      const supabase = getSupabaseClient();

      if (provider === 'google' || provider === 'github') {
        // Sign in with OAuth - this will be handled by the SupabaseAuthContext
        // Check if this is bridge authentication to set the correct redirect
        const urlParams = new URLSearchParams(window.location.search);
        const isBridgeAuth = urlParams.get('source') === 'bridge';
        const redirectTo = isBridgeAuth 
          ? window.location.origin + '/bridge-callback'
          : window.location.origin + '/dashboard';

        console.log('OAuth redirect URL:', redirectTo, 'Bridge mode:', isBridgeAuth);

        const { error } = await supabase.auth.signInWithOAuth({
          provider: provider as any,
          options: {
            redirectTo: redirectTo,
          },
        });

        if (error) {
          console.error(`${provider} OAuth error:`, error);
          throw error;
        }

        // This won't actually be reached because the page will redirect
        // But we should set isLoading to false in case the redirect fails
        setIsLoading(false);
        return;
      } else if (email && password) {
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });

        if (error) {
          console.error('Login error:', error);
          throw error;
        }

        if (data.user) {
          // The auth state will be updated by the SupabaseAuthContext
          // We just need to handle the redirect

          // Set loading to false before redirecting
          setIsLoading(false);

          // Redirect to dashboard
          console.log('Redirecting to dashboard after login');
          router.push('/dashboard');
          return; // Exit early to avoid setting isLoading again in finally block
        }
      } else {
        throw new Error('Invalid login parameters');
      }
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Sign in with a provider (Google, GitHub, etc.)
  const signIn = async (provider: string) => {
    try {
      setIsLoading(true);
      const supabase = getSupabaseClient();

      // Check if this is bridge authentication to set the correct redirect
      const urlParams = new URLSearchParams(window.location.search);
      const isBridgeAuth = urlParams.get('source') === 'bridge';
      const redirectTo = isBridgeAuth 
        ? window.location.origin + '/bridge-callback'
        : window.location.origin + '/dashboard';

      console.log('signIn OAuth redirect URL:', redirectTo, 'Bridge mode:', isBridgeAuth);

      const { error } = await supabase.auth.signInWithOAuth({
        provider: provider as any,
        options: {
          redirectTo: redirectTo,
        },
      });

      if (error) {
        console.error(`${provider} OAuth error:`, error);
        throw error;
      }

      // This won't actually be reached because the page will redirect
      // But we should set isLoading to false in case the redirect fails
      setIsLoading(false);
    } catch (error) {
      console.error(`${provider} sign in error:`, error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout - use the Supabase signOut method
  const logout = async () => {
    try {
      setIsLoading(true);

      // Reset subscription status on logout
      console.log('AuthContext: Resetting subscription status for user', userId);

      // Reset subscription status in state
      setHasActiveSubscription(false);

      // Reset auth state immediately to ensure UI updates
      setIsAuthenticated(false);
      setToken(null);
      setUser(null);

      try {
        // Import the signOutAndClearStorage function for a more thorough cleanup
        const { signOutAndClearStorage } = await import('../lib/supabase-singleton');
        await signOutAndClearStorage();
        console.log('AuthContext: Successfully signed out and cleared storage');
      } catch (signOutError) {
        console.warn('AuthContext: Error during signOutAndClearStorage, continuing with redirect:', signOutError);

        // Clear localStorage manually as a fallback
        if (typeof window !== 'undefined') {
          // Clear Supabase session storage
          localStorage.removeItem('sb-ujiakzkncbxisdatygpo-auth-token');

          // Clear our custom storage
          localStorage.removeItem('ai-chat-user-id');
          localStorage.removeItem('ai-chat-user-id-source');
          localStorage.removeItem('ai-chat-auth-token');
          localStorage.removeItem('ai-chat-refresh-token');
        }
      }

      // Set loading to false before redirecting
      setIsLoading(false);

      // Redirect to login page
      console.log('AuthContext: Redirecting to login page after logout');
      router.push('/login');
      return; // Exit early to avoid setting isLoading again in finally block
    } catch (error) {
      console.error('AuthContext: Logout error:', error);

      // Even if there's an error, try to redirect to login
      try {
        setIsLoading(false);
        router.push('/login');
      } catch (redirectError) {
        console.error('AuthContext: Error redirecting after logout:', redirectError);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Update subscription status - simplified approach
  const updateSubscription = (hasSubscription: boolean) => {
    console.log('AuthContext: Updating subscription status for user', userId, ':', hasSubscription);

    // Simply update the state - no storage
    setHasActiveSubscription(hasSubscription);

    // Log the update for debugging
    console.log(`AuthContext: Updated subscription status for user ${userId} to ${hasSubscription}`);
  };

  // Function to refresh subscription status
  const refreshSubscriptionStatus = async () => {
    if (!isAuthenticated || !user) {
      console.log('AuthContext: Cannot refresh subscription status - user not authenticated');
      return null;
    }

    // Log the current user ID for debugging
    console.log('AuthContext: Refreshing subscription status for user ID:', userId);

    // Use environment variable to control subscription bypass
    const bypassSubscription = process.env.NEXT_PUBLIC_BYPASS_SUBSCRIPTION === 'true';

    console.log('AuthContext: Subscription bypass set to:', bypassSubscription);

    // Bypass subscription check if configured
    if (bypassSubscription) {
      console.log('AuthContext: Bypassing subscription check');
      updateSubscription(true);
      return { hasActiveSubscription: true, source: 'bypass_setting', userId };
    }

    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
      console.log('AuthContext: Refreshing subscription status at:', `${apiUrl}/api/subscription/check`);

      const supabase = getSupabaseClient();
      const session = await supabase.auth.getSession();
      if (!session.data.session) {
        console.error('AuthContext: No session available for subscription check');

        // Check if we should bypass subscription checks
        const bypassSubscription = process.env.NEXT_PUBLIC_BYPASS_SUBSCRIPTION === 'true';

        if (bypassSubscription) {
          console.log('AuthContext: Bypass enabled, setting hasActiveSubscription to true');
          setHasActiveSubscription(true);
          return { hasActiveSubscription: true, source: 'bypass_setting' };
        } else {
          // Respect the actual subscription status (no session, so no subscription)
          console.log('AuthContext: No session and bypass disabled, setting hasActiveSubscription to false');
          setHasActiveSubscription(false);
          return { hasActiveSubscription: false, source: 'missing_session' };
        }
      }

      console.log('AuthContext: Making API call with token:', session.data.session.access_token.substring(0, 10) + '...');

      // Include the user ID in the URL to ensure the backend can identify the user
      const subscriptionResponse = await fetch(`${apiUrl}/api/subscription/check?userId=${userId}`, {
        headers: {
          'Authorization': `Bearer ${session.data.session.access_token}`
        }
      });

      console.log('AuthContext: API response status:', subscriptionResponse.status);

      if (subscriptionResponse.ok) {
        const subscriptionData = await subscriptionResponse.json();
        console.log('AuthContext: Subscription refresh result for user', userId, ':', subscriptionData);

        // Use updateSubscription to ensure proper storage with user ID
        updateSubscription(subscriptionData.hasActiveSubscription);

        // Add user ID to the response for debugging
        return { ...subscriptionData, userId };
      } else {
        console.error('AuthContext: Failed to refresh subscription status for user', userId, ':', subscriptionResponse.status);

        // Check if we should bypass subscription checks
        const bypassSubscription = process.env.NEXT_PUBLIC_BYPASS_SUBSCRIPTION === 'true';

        if (bypassSubscription) {
          console.log('AuthContext: Bypass enabled, setting hasActiveSubscription to true for user', userId);
          updateSubscription(true);
          return { hasActiveSubscription: true, source: 'bypass_setting', userId };
        } else {
          // Respect the actual subscription status (API error, so no subscription)
          console.log('AuthContext: API error and bypass disabled, setting hasActiveSubscription to false for user', userId);
          updateSubscription(false);
          return { hasActiveSubscription: false, source: 'api_error', userId };
        }
      }
    } catch (error) {
      console.error('AuthContext: Error refreshing subscription status for user', userId, ':', error);

      // Check if we should bypass subscription checks
      const bypassSubscription = process.env.NEXT_PUBLIC_BYPASS_SUBSCRIPTION === 'true';

      if (bypassSubscription) {
        console.log('AuthContext: Bypass enabled, setting hasActiveSubscription to true for user', userId);
        updateSubscription(true);
        return { hasActiveSubscription: true, source: 'bypass_setting', userId };
      } else {
        // Respect the actual subscription status (exception occurred, so no subscription)
        console.log('AuthContext: Exception occurred and bypass disabled, setting hasActiveSubscription to false for user', userId);
        updateSubscription(false);
        return { hasActiveSubscription: false, source: 'exception', userId };
      }
    }
  };

  const value = {
    isAuthenticated,
    isLoading,
    user,
    userId,
    token,
    hasActiveSubscription,
    login,
    logout,
    signIn,
    updateSubscription,
    refreshSubscriptionStatus,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
