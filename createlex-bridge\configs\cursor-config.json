{"mcp.servers": {"createlex-unreal": {"command": "node", "args": ["[ProjectName]/Plugins/UnrealGenAISupport_with_server/Content/Tools/bridge-native.js"], "env": {}}}, "development": {"workflow": {"rule": "MANDATORY: After ANY CreatelexGenAI plugin code changes, run ./automation-scripts/dev_refresh_bypass.bat and check UE logs", "scripts": {"primary": "./automation-scripts/dev_refresh_bypass.bat", "cleanup": "./automation-scripts/kill_all_mcp.bat", "bridge": "./automation-scripts/start_mcp_bypass_simple.bat"}, "paths": {"project": "C:\\Dev\\YourLife", "plugin_source": "C:\\Dev\\AiWebplatform\\CreatelexGenAI", "bridge": "C:\\Dev\\AiWebplatform\\createlex-bridge", "logs": "C:\\Dev\\YourLife\\Saved\\Logs\\YourLife.log"}, "bypass_mode": {"enabled": true, "auto_start_mcp": true, "no_authentication": true, "port": 9877}}}}