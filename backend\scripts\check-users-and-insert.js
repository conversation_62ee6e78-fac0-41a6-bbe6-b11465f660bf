const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function checkUsersAndInsert() {
  console.log('🔍 Checking existing users and inserting test device...');
  
  // Initialize Supabase client
  const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
  );
  
  try {
    // First, check what users exist
    console.log('📋 Fetching existing users...');
    
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email')
      .limit(5);
    
    if (usersError) {
      console.error('❌ Users fetch error:', usersError);
      return;
    }
    
    console.log('👥 Found users:', users);
    
    if (users.length === 0) {
      console.log('⚠️ No users found. Creating a test user first...');
      
      // Create a test user
      const testUser = {
        id: '077f1533-9f81-429c-b1b1-52d9c83f146c',
        email: '<EMAIL>',
        name: 'Test User',
        created_at: new Date().toISOString()
      };
      
      const { data: newUser, error: userInsertError } = await supabase
        .from('users')
        .insert([testUser])
        .select();
      
      if (userInsertError) {
        console.error('❌ User insert error:', userInsertError);
        return;
      }
      
      console.log('✅ Test user created:', newUser);
      users.push(testUser);
    }
    
    // Use the first available user
    const targetUser = users[0];
    console.log(`\n🎯 Using user: ${targetUser.email} (${targetUser.id})`);
    
    // Test device data
    const testDevice = {
      user_id: targetUser.id,
      device_id: 'test-device-123',
      device_name: 'Test MacBook Pro',
      platform: 'darwin',
      ip_address: '*************',
      is_active: true,
      last_active: new Date().toISOString(),
      created_at: new Date().toISOString()
    };
    
    console.log('📝 Inserting test device:', testDevice);
    
    // Insert device seat
    const { data, error } = await supabase
      .from('device_seats')
      .insert([testDevice])
      .select();
    
    if (error) {
      console.error('❌ Device insert error:', error);
      return;
    }
    
    console.log('✅ Device inserted successfully:', data);
    
    // Test fetching devices
    console.log('\n🔍 Fetching devices for user...');
    
    const { data: devices, error: fetchError } = await supabase
      .from('device_seats')
      .select('*')
      .eq('user_id', targetUser.id);
    
    if (fetchError) {
      console.error('❌ Device fetch error:', fetchError);
      return;
    }
    
    console.log('📱 Found devices:', devices);
    
    // Test the API endpoint
    console.log('\n🌐 Testing API endpoint...');
    
    const response = await fetch('http://localhost:5001/api/device/seats', {
      headers: {
        'x-user-id': targetUser.id,
        'Authorization': 'Bearer mock-token'
      }
    });
    
    if (response.ok) {
      const apiData = await response.json();
      console.log('✅ API response:', apiData);
      console.log('\n🎉 SUCCESS! DeviceSeatsManager should now show data in the dashboard!');
      console.log('📊 Dashboard URL: http://localhost:3000/dashboard');
    } else {
      const errorText = await response.text();
      console.error('❌ API error:', response.status, errorText);
    }
    
  } catch (err) {
    console.error('❌ Test failed:', err);
  }
}

checkUsersAndInsert(); 