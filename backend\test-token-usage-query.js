const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('SUPABASE_URL or SUPABASE_SERVICE_KEY is not set');
  process.exit(1);
}

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key:', supabaseKey.substring(0, 10) + '...');

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testTokenUsageQuery() {
  try {
    console.log('Testing token_usage table query...');

    // Try to query the token_usage table directly
    console.log('Checking if token_usage table exists by querying it...');
    const { data: tokenUsageCheck, error: tokenUsageCheckError } = await supabase
      .from('token_usage')
      .select('*')
      .limit(1);

    if (tokenUsageCheckError) {
      console.error('Error checking if token_usage table exists:', tokenUsageCheckError);

      if (tokenUsageCheckError.code === '42P01') {
        console.log('token_usage table does not exist');
      }

      return;
    }

    console.log('Token usage table exists. Count result:', tokenUsageCheck);

    // Query token_usage table
    console.log('Querying token_usage table...');
    const { data: tokenUsage, error: tokenUsageError } = await supabase
      .from('token_usage')
      .select('*')
      .limit(5);

    if (tokenUsageError) {
      console.error('Error querying token_usage table:', tokenUsageError);
      return;
    }

    console.log('Token usage data (first 5 records):', tokenUsage);

    // Query monthly token usage by summing the results manually
    console.log('Querying monthly token usage...');
    const startDate = new Date();
    startDate.setDate(1);
    startDate.setHours(0, 0, 0, 0);

    const { data: monthlyUsageData, error: monthlyUsageError } = await supabase
      .from('token_usage')
      .select('prompt_tokens, completion_tokens, total_tokens')
      .gte('timestamp', startDate.toISOString());

    // Calculate the sums manually
    let monthlyUsage = null;
    if (!monthlyUsageError && monthlyUsageData) {
      const promptTokensSum = monthlyUsageData.reduce((sum, record) => sum + record.prompt_tokens, 0);
      const completionTokensSum = monthlyUsageData.reduce((sum, record) => sum + record.completion_tokens, 0);
      const totalTokensSum = monthlyUsageData.reduce((sum, record) => sum + record.total_tokens, 0);

      monthlyUsage = [{
        prompt_tokens: promptTokensSum,
        completion_tokens: completionTokensSum,
        total_tokens: totalTokensSum
      }];
    }

    if (monthlyUsageError) {
      console.error('Error querying monthly token usage:', monthlyUsageError);
      return;
    }

    console.log('Monthly token usage data:', monthlyUsage);

    // Check RLS policies by testing permissions
    console.log('Checking RLS policies by testing permissions...');

    // Try to create a token_usage record to test permissions
    console.log('Trying to create a token_usage record to test permissions...');
    const testRecord = {
      user_id: '5af3ff0f-0ccb-41a2-8dba-3e460aa9bd72',
      model_id: 'claude-3.7-sonnet-20240307',
      prompt_tokens: 100,
      completion_tokens: 200,
      total_tokens: 300,
      request_type: 'test',
      timestamp: new Date().toISOString(),
      subscription_plan: 'basic'
    };

    const { data: insertResult, error: insertError } = await supabase
      .from('token_usage')
      .insert(testRecord)
      .select();

    if (insertError) {
      console.error('Error inserting test record:', insertError);
    } else {
      console.log('Successfully inserted test record:', insertResult);
    }

    // Try to query another user's token usage
    console.log('Trying to query another user\'s token usage...');
    const { data: otherUserData, error: otherUserError } = await supabase
      .from('token_usage')
      .select('*')
      .neq('user_id', '5af3ff0f-0ccb-41a2-8dba-3e460aa9bd72')
      .limit(1);

    if (otherUserError) {
      console.error('Error querying another user\'s token usage:', otherUserError);
    } else {
      console.log('Successfully queried another user\'s token usage:', otherUserData);
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

testTokenUsageQuery();
