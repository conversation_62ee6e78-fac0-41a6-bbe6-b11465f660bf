'use client';

import React, { useRef, useEffect, useState } from 'react';
import Image from 'next/image';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

const testimonials = [
  {
    name: '<PERSON>',
    role: 'Game Developer at Indie Studio',
    image: '/testimonials/person1.jpg', // You'll need to add these images
    quote: 'This tool has completely transformed how I work with Unreal Engine. I can create complex materials and blueprints in seconds just by describing what I want.',
  },
  {
    name: '<PERSON>',
    role: 'Technical Artist',
    image: '/testimonials/person2.jpg',
    quote: 'The AI assistant understands exactly what I need. It\'s like having an expert Unreal Engine developer by my side at all times.',
  },
  {
    name: '<PERSON>',
    role: 'Level Designer',
    image: '/testimonials/person3.jpg',
    quote: 'I\'ve been able to iterate on level designs so much faster. What used to take hours now takes minutes with natural language commands.',
  },
  {
    name: '<PERSON>',
    role: 'Indie Game Developer',
    image: '/testimonials/person4.jpg',
    quote: 'As a solo developer, this tool is like having an entire team helping me. It\'s been a game-changer for my productivity.',
  },
];

const TestimonialsSection: React.FC = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const testimonialsRef = useRef<(HTMLDivElement | null)[]>([]);
  const [activeIndex, setActiveIndex] = useState(0);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Animate section elements
    gsap.fromTo(
      '.testimonials-title',
      { opacity: 0, y: 50 },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        scrollTrigger: {
          trigger: sectionRef.current,
          start: 'top 80%',
        },
      }
    );

    // Set up auto-rotation for testimonials
    const interval = setInterval(() => {
      setActiveIndex((prev) => (prev + 1) % testimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  // Update animations when active testimonial changes
  useEffect(() => {
    testimonialsRef.current.forEach((testimonial, index) => {
      if (!testimonial) return;

      gsap.to(testimonial, {
        opacity: index === activeIndex ? 1 : 0,
        x: index === activeIndex ? 0 : (index < activeIndex ? -50 : 50),
        duration: 0.5,
        zIndex: index === activeIndex ? 1 : 0,
      });
    });
  }, [activeIndex]);

  return (
    <section
      ref={sectionRef}
      id="testimonials"
      className="py-20 bg-gray-800 relative overflow-hidden"
    >
      {/* Background elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-full h-full bg-cover bg-center opacity-10"
             style={{ backgroundImage: 'url(/images/backgrounds/unreal-testimonials.jpg)' }} />
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900/95 via-gray-800/90 to-gray-900/95" />
      </div>

      {/* Unreal-style accent elements */}
      <div className="absolute right-0 top-0 w-1/3 h-px bg-blue-500/30" />
      <div className="absolute left-0 bottom-0 w-1/3 h-px bg-blue-500/30" />
      <div className="absolute right-10 top-10 w-20 h-20 border border-blue-500/20 rounded-full" />
      <div className="absolute left-10 bottom-10 w-32 h-32 border border-blue-500/10 rounded-full" />
      <div className="container mx-auto px-4 relative z-10">
        <div className="inline-block mx-auto mb-4 px-4 py-1 border border-blue-400 rounded-full bg-blue-900/30 text-center">
          <span className="text-blue-300 font-medium">Testimonials</span>
        </div>

        <h2 className="testimonials-title text-4xl md:text-5xl font-bold text-center mb-16 text-white">
          What Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-300">Users Say</span>
        </h2>

        <div className="max-w-4xl mx-auto">
          {/* Testimonial Cards */}
          <div className="relative h-[300px]">
            {testimonials.map((testimonial, index) => (
              <div
                key={index}
                ref={(el: HTMLDivElement | null) => {
                  testimonialsRef.current[index] = el;
                }}
                className={`absolute top-0 left-0 w-full bg-gray-800/70 backdrop-blur-sm p-8 rounded-xl border border-gray-700 shadow-lg transition-all duration-500 ${
                  index === activeIndex ? 'opacity-100 z-10' : 'opacity-0 z-0'
                }`}
              >
                <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
                  <div className="w-20 h-20 rounded-full overflow-hidden flex-shrink-0 bg-gray-200 relative">
                    {/* Use initials as fallback */}
                    <div className="w-full h-full bg-blue-500 flex items-center justify-center text-white font-bold text-xl absolute z-0">
                      {testimonial.name.charAt(0)}
                    </div>
                  </div>

                  <div>
                    <div className="mb-4">
                      {[...Array(5)].map((_, i) => (
                        <svg key={i} className="inline-block w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      ))}
                    </div>

                    <blockquote className="text-xl italic text-gray-200 mb-4">
                      "{testimonial.quote}"
                    </blockquote>

                    <div>
                      <p className="font-bold text-white">{testimonial.name}</p>
                      <p className="text-blue-300">{testimonial.role}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Navigation Dots */}
          <div className="flex justify-center mt-8 space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setActiveIndex(index)}
                className={`w-3 h-3 rounded-full transition-colors ${
                  index === activeIndex ? 'bg-blue-400' : 'bg-gray-600'
                }`}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
