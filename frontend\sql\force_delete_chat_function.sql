-- Function to completely delete a chat and all related data
CREATE OR REPLACE FUNCTION force_delete_chat(chat_id TEXT, user_id TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    success BOOLEAN := FALSE;
    rows_deleted INTEGER;
BEGIN
    -- Delete messages first
    BEGIN
        DELETE FROM messages WHERE chat_id = $1;
        GET DIAGNOSTICS rows_deleted = ROW_COUNT;
        RAISE NOTICE 'Deleted % messages for chat %', rows_deleted, $1;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error deleting messages: %', SQLERRM;
    END;

    -- Delete topics if the table exists
    BEGIN
        IF EXISTS (
            SELECT FROM pg_tables
            WHERE schemaname = 'public'
            AND tablename = 'topics'
        ) THEN
            DELETE FROM topics WHERE chat_id = $1;
            GET DIAGNOSTICS rows_deleted = ROW_COUNT;
            RAISE NOTICE 'Deleted % topics for chat %', rows_deleted, $1;
        END IF;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error deleting topics: %', SQLERRM;
    <PERSON><PERSON>;

    -- Delete the chat
    BEGIN
        DELETE FROM chats WHERE id = $1;
        GET DIAGNOSTICS rows_deleted = ROW_COUNT;
        RAISE NOTICE 'Deleted % chats with ID %', rows_deleted, $1;

        IF rows_deleted > 0 THEN
            success := TRUE;
        END IF;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error deleting chat: %', SQLERRM;
    END;

    RETURN success;
END;
$$ LANGUAGE plpgsql;
