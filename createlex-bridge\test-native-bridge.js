#!/usr/bin/env node

const net = require('net');

console.log('🧪 Testing CreateLex Native Bridge Connection...');

const client = net.createConnection(9877, 'localhost');

client.on('connect', () => {
  console.log('✅ Successfully connected to native bridge on localhost:9877');
  
  // Send a simple MCP initialization message
  const initMessage = {
    jsonrpc: "2.0",
    id: 1,
    method: "initialize",
    params: {
      protocolVersion: "2024-11-05",
      capabilities: {},
      clientInfo: {
        name: "test-client",
        version: "1.0.0"
      }
    }
  };
  
  console.log('📤 Sending MCP initialize message...');
  client.write(JSON.stringify(initMessage) + '\n');
});

client.on('data', (data) => {
  console.log('📥 Received response from bridge:');
  console.log(data.toString());
  
  // Close after receiving response
  setTimeout(() => {
    client.end();
  }, 1000);
});

client.on('error', (err) => {
  if (err.code === 'ECONNREFUSED') {
    console.log('❌ Connection refused - Native bridge app is not running');
    console.log('💡 Please:');
    console.log('   1. Start the CreateLex Bridge app: npm start');
    console.log('   2. Click "🌐 Enable Native MCP Button" in the dashboard');
    console.log('   3. Run this test again');
  } else {
    console.error('❌ Connection error:', err.message);
  }
  process.exit(1);
});

client.on('close', () => {
  console.log('🔌 Connection closed');
  console.log('✅ Test completed successfully!');
  process.exit(0);
});

// Timeout after 10 seconds
setTimeout(() => {
  console.log('⏰ Test timeout - no response received');
  client.end();
  process.exit(1);
}, 10000); 