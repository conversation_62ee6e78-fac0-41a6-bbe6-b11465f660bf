import { NextRequest, NextResponse } from 'next/server';
import { adminApiRequest } from '@/lib/admin-api';

export async function GET(req: NextRequest) {
  try {
    // Forward the request to the backend using our helper function
    const response = await adminApiRequest('/api/admin/dashboard-stats', {
      method: 'GET'
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response from backend:', errorText);

      return NextResponse.json(
        { error: `Failed to get dashboard statistics: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in dashboard stats route:', error);

    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}
