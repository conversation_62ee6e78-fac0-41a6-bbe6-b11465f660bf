const express = require('express');
const router = express.Router();
const { supabase } = require('../../services/supabaseService');

/**
 * @route POST /api/tokens/force-update-balance
 * @description Force update token balance for a user
 * @access Private
 */
router.post('/', async (req, res) => {
  try {
    // Get user ID from request user or query parameter
    let userId = req.user?.id;

    // If no user ID in req.user, check for userId in body
    if (!userId && req.body.userId) {
      userId = req.body.userId;
      console.log(`[Force Update Balance API] Using userId from body: ${userId}`);
    }

    // If no user ID in req.user or body, check for x-user-id header
    if (!userId && req.headers['x-user-id']) {
      userId = req.headers['x-user-id'];
      console.log(`[Force Update Balance API] Using userId from x-user-id header: ${userId}`);
    }

    // If still no user ID, return error
    if (!userId) {
      console.error('[Force Update Balance API] No user ID provided');
      return res.status(400).json({ error: 'No user ID provided' });
    }

    // Get balance from request body
    const { balance } = req.body;

    if (balance === undefined || balance === null) {
      console.error('[Force Update Balance API] No balance provided');
      return res.status(400).json({ error: 'No balance provided' });
    }

    console.log(`[Force Update Balance API] Forcing token balance update for user: ${userId} to ${balance}`);

    // Check if Supabase client is initialized
    if (!supabase) {
      console.error('[Force Update Balance API] Supabase client not initialized');
      return res.status(500).json({ error: 'Supabase client not initialized' });
    }

    // Get current token balance
    const { data: existingBalance, error: fetchError } = await supabase
      .from('token_balance')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') {
      console.error('[Force Update Balance API] Error fetching token balance:', fetchError);
      return res.status(500).json({ error: 'Failed to fetch token balance' });
    }

    let result;

    if (existingBalance) {
      // Update existing balance
      console.log(`[Force Update Balance API] Updating existing balance record: ${existingBalance.id}`);
      
      const { data, error } = await supabase
        .from('token_balance')
        .update({
          balance: balance,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingBalance.id)
        .select()
        .single();

      if (error) {
        console.error('[Force Update Balance API] Error updating token balance:', error);
        return res.status(500).json({ error: 'Failed to update token balance' });
      }

      result = data;
    } else {
      // Create new balance record
      console.log(`[Force Update Balance API] Creating new balance record for user: ${userId}`);
      
      const { data, error } = await supabase
        .from('token_balance')
        .insert({
          user_id: userId,
          balance: balance,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        console.error('[Force Update Balance API] Error creating token balance:', error);
        return res.status(500).json({ error: 'Failed to create token balance' });
      }

      result = data;
    }

    console.log(`[Force Update Balance API] Successfully updated token balance: ${JSON.stringify(result)}`);

    // Record the transaction
    const { data: transactionData, error: transactionError } = await supabase
      .from('token_transactions')
      .insert({
        user_id: userId,
        amount: existingBalance ? balance - existingBalance.balance : balance,
        type: 'force_update',
        reference_id: `force-update-${Date.now()}`,
        previous_balance: existingBalance ? existingBalance.balance : 0,
        new_balance: balance
      })
      .select()
      .single();

    if (transactionError) {
      console.error('[Force Update Balance API] Error recording transaction:', transactionError);
      // Continue anyway since the balance was updated successfully
    } else {
      console.log(`[Force Update Balance API] Transaction recorded: ${JSON.stringify(transactionData)}`);
    }

    return res.json(result);
  } catch (error) {
    console.error('[Force Update Balance API] Error:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

module.exports = router;
