const express = require('express');
const router = express.Router();
const supabase = require('../../services/supabaseClient');

/**
 * @route POST /api/tokens/add-test-data
 * @description Add test token usage data to the database
 * @access Public (for testing only)
 */
router.post('/', async (req, res) => {
  // Add CORS headers
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  try {
    const { userId, promptTokens, completionTokens, modelId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Default values
    const promptTokensValue = promptTokens || Math.floor(Math.random() * 1000) + 500;
    const completionTokensValue = completionTokens || Math.floor(Math.random() * 2000) + 1000;
    const modelIdValue = modelId || 'claude-3-sonnet-20240229';
    const totalTokens = promptTokensValue + completionTokensValue;

    console.log(`[Add Test Data] Adding test data for user: ${userId}`);
    console.log(`[Add Test Data] Prompt tokens: ${promptTokensValue}`);
    console.log(`[Add Test Data] Completion tokens: ${completionTokensValue}`);
    console.log(`[Add Test Data] Total tokens: ${totalTokens}`);
    console.log(`[Add Test Data] Model ID: ${modelIdValue}`);

    // Insert test data
    const { data, error } = await supabase
      .from('token_usage')
      .insert({
        user_id: userId,
        model_id: modelIdValue,
        prompt_tokens: promptTokensValue,
        completion_tokens: completionTokensValue,
        total_tokens: totalTokens,
        request_type: 'chat',
        subscription_plan: 'basic',
        timestamp: new Date().toISOString()
      })
      .select();

    if (error) {
      console.error('[Add Test Data] Error adding test data:', error);
      return res.status(500).json({ error: 'Error adding test data' });
    }

    console.log(`[Add Test Data] Successfully added test data for user ${userId}`);
    return res.json({
      success: true,
      message: 'Test data added successfully',
      data
    });
  } catch (error) {
    console.error('[Add Test Data] Error in add test data endpoint:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

module.exports = router;
