name: createlex-frontend-app
region: nyc
services:
  - name: frontend
    github:
      repo: AlexKissiJr/AiWebplatform
      branch: dev
      deploy_on_push: true
    source_dir: frontend
    build_command: npm run build
    run_command: npm start
    http_port: 3000
    instance_size_slug: basic-xs
    instance_count: 1
    routes:
      - path: /
    envs:
      - key: NEXT_PUBLIC_SUPABASE_URL
        scope: RUN_AND_BUILD_TIME
        value: ${SUPABASE_URL}
      - key: NEXT_PUBLIC_SUPABASE_ANON_KEY
        scope: RUN_AND_BUILD_TIME
        value: ${SUPABASE_ANON_KEY}
      - key: NEXT_PUBLIC_API_URL
        scope: RUN_AND_BUILD_TIME
        value: https://createlex-backend-app-yh639.ondigitalocean.app
      - key: NODE_ENV
        scope: RUN_AND_BUILD_TIME
        value: production
