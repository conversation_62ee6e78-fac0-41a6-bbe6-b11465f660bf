#!/bin/bash

# Deployment script for MCP Server on DigitalOcean or other cloud providers
# Usage: ./deploy.sh [production|staging]

set -e

ENVIRONMENT=${1:-production}
echo "🚀 Deploying MCP Server to $ENVIRONMENT environment..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if docker-compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Copy environment file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp env.example .env
    echo "⚠️  Please edit .env file with your actual configuration before running the server!"
fi

# Build and start the services
echo "🔨 Building Docker image..."
docker-compose build

echo "🏃 Starting MCP Server..."
docker-compose up -d

# Wait a moment for the service to start
sleep 5

# Check if the service is running
if docker-compose ps | grep -q "Up"; then
    echo "✅ MCP Server deployed successfully!"
    echo "📊 Service status:"
    docker-compose ps
    echo ""
    echo "📋 To view logs: docker-compose logs -f"
    echo "🛑 To stop: docker-compose down"
    echo "🔄 To restart: docker-compose restart"
    echo ""
    echo "🌐 MCP Server should be accessible at:"
    echo "   - HTTP: http://localhost:8000"
    echo "   - Unreal Communication: localhost:9877"
else
    echo "❌ Deployment failed. Check logs with: docker-compose logs"
    exit 1
fi 