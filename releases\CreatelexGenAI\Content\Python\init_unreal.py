# Content/Python/init_unreal.py - Using UE settings integration
import unreal
import importlib.util
import os
import sys
from utils import logging as log

def find_plugin_python_path():
    """Find the plugin's Python directory"""
    # Try multiple possible plugin names and paths
    possible_names = [
        "UnrealGenAISupport_with_server/Content/Python",
        "GenerativeAISupport/Content/Python", 
        "UnrealGenAISupport/Content/Python"
    ]
    
    for path in sys.path:
        for name in possible_names:
            if name in path:
                log.log_info(f"Found plugin Python path: {path}")
                return path
    
    # If not found in sys.path, try to construct it from current file location
    try:
        current_file = os.path.abspath(__file__)
        plugin_python_path = os.path.dirname(current_file)
        log.log_info(f"Using current file location as plugin path: {plugin_python_path}")
        return plugin_python_path
    except:
        pass
    
    return None

def initialize_socket_server():
    """
    Initialize the Unreal socket server if auto-start is enabled in UE settings.
    This server handles communication with the external Docker MCP server.
    """
    auto_start = False
    
    # Get settings from UE settings system
    try:
        # First get the class reference
        settings_class = unreal.load_class(None, '/Script/GenerativeAISupportEditor.GenerativeAISupportSettings')
        if settings_class:
            # Get the settings object using the class reference
            settings = unreal.get_default_object(settings_class)
            
            # Log available properties for debugging
            log.log_info(f"Settings object properties: {dir(settings)}")
            
            # Check if auto-start is enabled
            if hasattr(settings, 'auto_start_socket_server'):
                auto_start = settings.auto_start_socket_server
                log.log_info(f"Socket server auto-start setting: {auto_start}")
            else:
                log.log_warning("auto_start_socket_server property not found in settings")
                # Try alternative property names that might exist
                for prop in dir(settings):
                    if 'auto' in prop.lower() or 'socket' in prop.lower() or 'server' in prop.lower():
                        log.log_info(f"Found similar property: {prop}")
        else:
            log.log_error("Could not find GenerativeAISupportSettings class")
    except Exception as e:
        log.log_error(f"Error reading UE settings: {e}")
        log.log_info("Falling back to disabled auto-start")

    # Auto-start if configured
    if auto_start:
        log.log_info("Auto-starting Unreal Socket Server...")
        log.log_info("Note: This server communicates with external Docker MCP server on port 8000")

        # Start Unreal Socket Server (this handles communication with external MCP server)
        try:
            # Find our plugin's Python directory
            plugin_python_path = find_plugin_python_path()
    
            if plugin_python_path:
                server_path = os.path.join(plugin_python_path, "unreal_socket_server.py")
    
                if os.path.exists(server_path):
                    # Import and execute the server module
                    spec = importlib.util.spec_from_file_location("unreal_socket_server", server_path)
                    server_module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(server_module)
                    log.log_info("Unreal Socket Server started successfully")
                    log.log_info("Ready to communicate with Docker MCP server (localhost:8000)")
                else:
                    log.log_error(f"Server file not found at: {server_path}")
            else:
                log.log_error("Could not find plugin Python path")
        except Exception as e:
            log.log_error(f"Error starting socket server: {e}")
    else:
        log.log_info("Unreal Socket Server auto-start is disabled")
        log.log_info("To enable: Go to Project Settings > Plugins > Generative AI Support > Auto Start Socket Server")

# Run initialization when this script is loaded
initialize_socket_server()
