'use client';

import React, { useRef, useEffect } from 'react';
import Link from 'next/link';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

interface PricingSectionProps {
  onSubscribe: () => void;
  getStartedUrl?: string;
}

const PricingSection: React.FC<PricingSectionProps> = ({ onSubscribe, getStartedUrl = '/signup' }) => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Animate section elements
    gsap.fromTo(
      '.pricing-title',
      { opacity: 0, y: 50 },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        scrollTrigger: {
          trigger: sectionRef.current,
          start: 'top 80%',
        },
      }
    );

    gsap.fromTo(
      '.pricing-description',
      { opacity: 0, y: 30 },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        delay: 0.2,
        scrollTrigger: {
          trigger: sectionRef.current,
          start: 'top 80%',
        },
      }
    );

    gsap.fromTo(
      cardRef.current,
      { opacity: 0, y: 50 },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        delay: 0.4,
        scrollTrigger: {
          trigger: sectionRef.current,
          start: 'top 70%',
        },
      }
    );
  }, []);

  const basicFeatures = [
            'CreatelexGenAI plugin',
    'Standard AI models',
    'Limited API calls (1000/day)',
    'Object manipulation',
    'Material creation',
    'Blueprint generation',
    'Email support',
    'Regular updates',
  ];

  const proFeatures = [
    'Everything in Basic plan',
    'All AI models (DeepSeek-R1, Claude, GPT-4o)',
    'Unlimited API calls',
    'Advanced blueprint generation',
    'Priority email support',
    'Early access to new features',
  ];

  const enterpriseFeatures = [
    'Everything in Pro plan',
    'Team collaboration features',
    'Custom integration support',
    'Dedicated account manager',
    'SLA and premium support',
    'Custom training and onboarding',
  ];

  return (
    <section
      ref={sectionRef}
      id="pricing"
      className="py-20 bg-gray-900 relative overflow-hidden"
    >
      {/* Background elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 right-0 w-full h-full bg-cover bg-right-top opacity-10"
             style={{ backgroundImage: 'url(/images/backgrounds/unreal-pricing.jpg)' }} />
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900 via-gray-900/95 to-gray-900/90" />
      </div>

      {/* Unreal-style accent lines */}
      <div className="absolute left-0 top-1/4 w-full h-px bg-blue-500/20" />
      <div className="absolute left-0 top-3/4 w-full h-px bg-blue-500/20" />
      <div className="container mx-auto px-4 relative z-10">
        <div className="inline-block mx-auto mb-4 px-4 py-1 border border-blue-400 rounded-full bg-blue-900/30 text-center">
          <span className="text-blue-300 font-medium">Pricing</span>
        </div>

        <h2 className="pricing-title text-4xl md:text-5xl font-bold text-center mb-6 text-white">
          Simple, <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-300">Transparent</span> Pricing
        </h2>

        <p className="pricing-description text-xl text-center mb-16 max-w-3xl mx-auto text-gray-300">
          Choose the plan that fits your needs. From our Basic plan at $20/month to our Pro plan at $30/month,
          or contact us for Enterprise pricing. No hidden fees, cancel anytime.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Basic Plan - $20 */}
          <div
            ref={cardRef}
            className="bg-gray-800/70 backdrop-blur-sm border-2 border-green-500 rounded-2xl shadow-xl overflow-hidden transform transition-all hover:scale-105 hover:shadow-green-500/30 duration-300"
          >
            <div className="bg-gradient-to-r from-green-600 to-green-400 p-6 text-white text-center">
              <h3 className="text-2xl font-bold mb-2">Basic Plan</h3>
              <div className="flex items-center justify-center">
                <span className="text-5xl font-bold">$20</span>
                <span className="ml-2 text-green-100">/month</span>
              </div>
            </div>

            <div className="p-8">
              <ul className="space-y-4 mb-8">
                {basicFeatures.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <svg className="h-6 w-6 text-green-400 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-200">{feature}</span>
                  </li>
                ))}
              </ul>

              <Link
                href="/subscription?plan=basic"
                className="w-full bg-gradient-to-r from-green-600 to-green-400 hover:from-green-500 hover:to-green-300 text-white font-bold py-3 px-4 rounded-lg transition-all shadow-lg hover:shadow-green-500/50 inline-block text-center"
              >
                Get Basic Plan
              </Link>
            </div>
          </div>

          {/* Pro Plan - $30 */}
          <div
            className="bg-gray-800/70 backdrop-blur-sm border-2 border-blue-500 rounded-2xl shadow-xl overflow-hidden transform transition-all hover:scale-105 hover:shadow-blue-500/30 duration-300 relative"
          >
            <div className="absolute top-0 right-0 bg-gradient-to-r from-blue-600 to-blue-400 text-white px-4 py-1 rounded-bl-lg font-medium text-sm">
              MOST POPULAR
            </div>
            <div className="bg-gradient-to-r from-blue-600 to-blue-400 p-6 text-white text-center">
              <h3 className="text-2xl font-bold mb-2">Pro Plan</h3>
              <div className="flex items-center justify-center">
                <span className="text-5xl font-bold">$30</span>
                <span className="ml-2 text-blue-100">/month</span>
              </div>
            </div>

            <div className="p-8">
              <ul className="space-y-4 mb-8">
                {proFeatures.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <svg className="h-6 w-6 text-blue-400 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-200">{feature}</span>
                  </li>
                ))}
              </ul>

              <Link
                href="/subscription?plan=pro"
                className="w-full bg-gradient-to-r from-blue-600 to-blue-400 hover:from-blue-500 hover:to-blue-300 text-white font-bold py-3 px-4 rounded-lg transition-all shadow-lg hover:shadow-blue-500/50 inline-block text-center"
              >
                Get Pro Plan
              </Link>
            </div>
          </div>

          {/* Enterprise Plan */}
          <div
            className="bg-gray-800/70 backdrop-blur-sm border-2 border-purple-500 rounded-2xl shadow-xl overflow-hidden transform transition-all hover:scale-105 hover:shadow-purple-500/30 duration-300"
          >
            <div className="bg-gradient-to-r from-purple-600 to-purple-400 p-6 text-white text-center">
              <h3 className="text-2xl font-bold mb-2">Enterprise</h3>
              <div className="flex items-center justify-center">
                <span className="text-3xl font-bold">Custom Pricing</span>
              </div>
            </div>

            <div className="p-8">
              <ul className="space-y-4 mb-8">
                {enterpriseFeatures.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <svg className="h-6 w-6 text-purple-400 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-200">{feature}</span>
                  </li>
                ))}
              </ul>

              <Link
                href="/contact"
                className="w-full bg-gradient-to-r from-purple-600 to-purple-400 hover:from-purple-500 hover:to-purple-300 text-white font-bold py-3 px-4 rounded-lg transition-all shadow-lg hover:shadow-purple-500/50 inline-block text-center"
              >
                Contact Sales
              </Link>
            </div>
          </div>
        </div>

        <p className="text-center text-gray-400 text-sm mt-8">
          All plans come with a 14-day money-back guarantee. No questions asked.
        </p>
      </div>
    </section>
  );
};

export default PricingSection;
