'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Download, Settings, Code, Zap } from "lucide-react";
import Link from "next/link";
import { useAuth } from '@/contexts/AuthContext';

export default function DocsPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading, hasActiveSubscription, refreshSubscriptionStatus } = useAuth();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const [activeTab, setActiveTab] = useState("setup-guide");

  useEffect(() => {
    const checkAccess = async () => {
      console.log('Docs page: Checking access', {
        isAuthenticated,
        isLoading,
        hasActiveSubscription
      });

      // Redirect to login if not authenticated
      if (!isLoading && !isAuthenticated) {
        console.log('Docs page: User not authenticated, redirecting to login');
        router.push('/login');
        return;
      }

      // Wait for auth to finish loading
      if (isLoading) {
        console.log('Docs page: Still loading auth data, waiting...');
        return;
      }

      // Check if user has an active subscription
      if (!hasActiveSubscription) {
        try {
          console.log('Docs page: Refreshing subscription status');
          const subscriptionData = await refreshSubscriptionStatus();
          console.log('Docs page: Refreshed subscription status:', subscriptionData);

          if (!subscriptionData.hasActiveSubscription) {
            console.log('Docs page: User does not have an active subscription, redirecting to subscription page');
            router.push('/subscription');
            return;
          }
        } catch (error) {
          console.error('Docs page: Error refreshing subscription status:', error);
          console.log('Docs page: Redirecting to subscription page due to error');
          router.push('/subscription');
          return;
        }
      }

      // User is authenticated and has an active subscription
      console.log('Docs page: User is authorized to access documentation');
      setIsAuthorized(true);
      setIsCheckingAuth(false);
    };

    checkAccess();
  }, [isAuthenticated, isLoading, hasActiveSubscription, router, refreshSubscriptionStatus]);

  // Show loading state until fully authorized
  if (isCheckingAuth || !isAuthorized) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
        <p className="text-gray-600">Verifying subscription access...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10 px-4 md:px-6">
      <div className="flex items-center mb-6">
        <Link href="/dashboard">
          <Button variant="ghost" size="sm" className="gap-1">
            <ArrowLeft className="h-4 w-4" />
            Back to Dashboard
          </Button>
        </Link>
        <h1 className="text-3xl font-bold ml-4">CreatelexGenAI Documentation</h1>
      </div>

      <Tabs defaultValue="setup-guide" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="setup-guide">Setup Guide</TabsTrigger>
          <TabsTrigger value="ide-config">IDE Config</TabsTrigger>
          <TabsTrigger value="getting-started">Getting Started</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="subscription">Subscription</TabsTrigger>
          <TabsTrigger value="faq">FAQ</TabsTrigger>
        </TabsList>
        
        <TabsContent value="setup-guide" className="mt-6">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Download className="h-5 w-5" />
                  Complete Setup Guide
                </CardTitle>
                <CardDescription>
                  Step-by-step instructions to download and set up CreatelexGenAI with Unreal Engine
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">📦 Step 1: Download & Install the Unreal Engine Plugin</h3>
                  <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4">
                    <p className="text-blue-700">
                      <strong>Current Version:</strong> v1.0.1 | <strong>Auto-Start Enabled</strong> - No manual server startup required!
                    </p>
                  </div>
                  <ol className="list-decimal pl-5 space-y-2">
                    <li>
                      <strong>Download Options:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1">
                        <li><Link href="/download" className="text-blue-600 hover:underline">Download ZIP</Link> - Direct download from our releases page</li>
                        <li><strong>Epic Marketplace</strong> - Coming soon for easier installation</li>
                      </ul>
                    </li>
                    <li>
                      <strong>Installation:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1">
                        <li>Extract the ZIP file</li>
                        <li>Copy "CreatelexGenAI" folder to your project's "Plugins" directory</li>
                        <li>Path should be: <code className="bg-gray-100 px-1 rounded">YourProject/Plugins/CreatelexGenAI/</code></li>
                        <li>Restart Unreal Engine and enable the plugin in Edit → Plugins</li>
                      </ul>
                    </li>
                  </ol>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-3">🔧 Step 2: Install Node.js (Required)</h3>
                  <div className="bg-amber-50 border-l-4 border-amber-400 p-4 mb-4">
                    <p className="text-amber-700">
                      <strong>Requirement:</strong> Node.js is required to run the integrated bridge functionality
                    </p>
                  </div>
                  <ol className="list-decimal pl-5 space-y-2">
                    <li>
                      <strong>Download Node.js:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1">
                        <li>Visit <a href="https://nodejs.org/" target="_blank" className="text-blue-600 hover:underline">nodejs.org</a></li>
                        <li>Download the LTS version (recommended)</li>
                        <li>Install using the default options</li>
                      </ul>
                    </li>
                    <li>
                      <strong>Verify Installation:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1">
                        <li>Open Command Prompt/Terminal</li>
                        <li>Run: <code className="bg-gray-100 px-1 rounded">node --version</code></li>
                        <li>Should show version number (e.g., v18.x.x or higher)</li>
                      </ul>
                    </li>
                  </ol>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-3">🔗 Step 3: Verify Plugin Installation</h3>
                  <ol className="list-decimal pl-5 space-y-2">
                    <li>Open your Unreal Engine project with the plugin enabled</li>
                    <li>The MCP server will start automatically in the background</li>
                    <li>Verify the bridge file exists at: <code className="bg-gray-100 px-1 rounded">YourProject/Plugins/CreatelexGenAI/Content/Tools/bridge-native.js</code></li>
                    <li>Note your project's full path - you'll need it for IDE configuration</li>
                  </ol>
                </div>

                <div className="bg-amber-50 border-l-4 border-amber-400 p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <Zap className="h-5 w-5 text-amber-400" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-amber-700">
                        <strong>Next Step:</strong> Configure your preferred IDE using the IDE Configuration tab above!
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="ide-config" className="mt-6">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  IDE Configuration Guide
                </CardTitle>
                <CardDescription>
                  Configure Claude Desktop, VSCode, Cursor, and Windsurf to work with CreateLex integrated bridge
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-8">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <Code className="h-5 w-5 text-blue-500" />
                    </div>
                    <div className="ml-3">
                      <h4 className="text-blue-800 font-medium mb-2">Important: Update Your Project Path</h4>
                      <p className="text-sm text-blue-700">
                        In all configurations below, replace <code className="bg-blue-100 px-1 rounded">[YourUnrealProject]</code> with your actual project folder name.
                        <br />Example: If your project is at <code className="bg-blue-100 px-1 rounded">C:\Dev\MyGameProject\</code>, use <code className="bg-blue-100 px-1 rounded">MyGameProject</code>
                      </p>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
                    <Code className="h-5 w-5" />
                    Claude Desktop Configuration
                  </h3>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <p className="text-blue-800 font-medium mb-2">Configuration File Location:</p>
                    <ul className="text-sm text-blue-700 space-y-1">
                      <li><strong>Windows:</strong> <code>%APPDATA%\\Claude\\claude_desktop_config.json</code></li>
                      <li><strong>macOS:</strong> <code>~/Library/Application Support/Claude/claude_desktop_config.json</code></li>
                    </ul>
                  </div>
                  <p className="mb-3">Add this configuration to your Claude Desktop config file:</p>
                  <div className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
                    <pre className="text-sm">
{`{
  "mcpServers": {
    "createlex-unreal": {
      "command": "node",
      "args": ["C:\\\\Dev\\\\[YourUnrealProject]\\\\Plugins\\\\CreatelexGenAI\\\\Content\\\\Tools\\\\bridge-native.js"],
      "env": {}
    }
  }
}`}
                    </pre>
                  </div>
                  <p className="mt-3 text-sm text-gray-600">
                    <strong>Note:</strong> Replace <code>[YourUnrealProject]</code> with your actual project folder name.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4">VSCode Configuration</h3>
                  <ol className="list-decimal pl-5 space-y-2">
                    <li>Install the "MCP Extension" from the VSCode marketplace</li>
                    <li>Open VSCode settings (Ctrl+, or Cmd+,)</li>
                    <li>Search for "MCP" in settings</li>
                    <li>Add CreateLex configuration:</li>
                  </ol>
                  <div className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto mt-3">
                    <pre className="text-sm">
{`{
  "mcp.servers": [
    {
      "name": "CreateLex Unreal",
      "command": "node",
      "args": ["C:\\\\Dev\\\\[YourUnrealProject]\\\\Plugins\\\\CreatelexGenAI\\\\Content\\\\Tools\\\\bridge-native.js"],
      "enabled": true,
      "env": {}
    }
  ]
}`}
                    </pre>
                  </div>
                  <p className="mt-3 text-sm text-gray-600">
                    <strong>Note:</strong> Replace <code>[YourUnrealProject]</code> with your actual project folder name.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4">Cursor Configuration</h3>
                  <ol className="list-decimal pl-5 space-y-2">
                    <li>Open Cursor settings (Ctrl+, or Cmd+,)</li>
                    <li>Navigate to Extensions → MCP Settings</li>
                    <li>Add new MCP server with these details:</li>
                  </ol>
                  <div className="bg-gray-100 border border-gray-300 rounded-lg p-4 mt-3">
                    <ul className="space-y-2">
                      <li><strong>Name:</strong> CreateLex Unreal</li>
                      <li><strong>Command:</strong> <code>node</code></li>
                      <li><strong>Args:</strong> <code>C:\Dev\[YourUnrealProject]\Plugins\CreatelexGenAI\Content\Tools\bridge-native.js</code></li>
                      <li><strong>Working Directory:</strong> <code>C:\Dev\[YourUnrealProject]</code></li>
                      <li><strong>Environment Variables:</strong> <code>{}</code></li>
                    </ul>
                  </div>
                  <p className="mt-3 text-sm text-gray-600">
                    <strong>Note:</strong> Replace <code>[YourUnrealProject]</code> with your actual project folder name.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4">Windsurf Configuration</h3>
                  <ol className="list-decimal pl-5 space-y-2">
                    <li>Open Windsurf preferences</li>
                    <li>Go to Tools → MCP Servers</li>
                    <li>Click "Add Server" and configure:</li>
                  </ol>
                  <div className="bg-gray-100 border border-gray-300 rounded-lg p-4 mt-3">
                    <ul className="space-y-2">
                      <li><strong>Server Name:</strong> CreateLex Unreal Engine</li>
                      <li><strong>Executable Path:</strong> <code>node</code></li>
                      <li><strong>Arguments:</strong> <code>C:\Dev\[YourUnrealProject]\Plugins\CreatelexGenAI\Content\Tools\bridge-native.js</code></li>
                      <li><strong>Working Directory:</strong> <code>C:\Dev\[YourUnrealProject]</code></li>
                      <li><strong>Auto-start:</strong> ✅ Enabled</li>
                    </ul>
                  </div>
                  <p className="mt-3 text-sm text-gray-600">
                    <strong>Note:</strong> Replace <code>[YourUnrealProject]</code> with your actual project folder name.
                  </p>
                </div>

                <div className="bg-green-50 border-l-4 border-green-400 p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <Zap className="h-5 w-5 text-green-400" />
                    </div>
                    <div className="ml-3">
                      <h4 className="text-sm font-medium text-green-800">Testing Your Configuration</h4>
                      <p className="text-sm text-green-700 mt-1">
                        After configuring your IDE, restart it and try asking: "Spawn a cube at location 0,0,100 in my Unreal project"
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-red-50 border-l-4 border-red-400 p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h4 className="text-sm font-medium text-red-800">Troubleshooting</h4>
                      <ul className="text-sm text-red-700 mt-1 space-y-1">
                        <li>• Ensure Node.js is installed and in your PATH</li>
                        <li>• Verify the bridge-native.js file exists in your plugin directory</li>
                        <li>• Make sure your Unreal project is open with the plugin enabled</li>
                        <li>• Check that the file paths use double backslashes (\\) on Windows</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="getting-started" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Getting Started with CreateLex AI</CardTitle>
              <CardDescription>
                Learn how to use CreateLex AI to enhance your legal work
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-2">Welcome to CreateLex AI</h3>
                <p className="text-muted-foreground">
                  CreateLex AI is a powerful tool designed specifically for legal professionals. 
                  Our AI assistant helps you draft documents, research legal topics, and analyze cases 
                  with unprecedented speed and accuracy.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">Quick Start Guide</h3>
                <ol className="list-decimal pl-5 space-y-2">
                  <li>Log in to your account using your credentials</li>
                  <li>Navigate to the Chat page by clicking "Open CreateLex AI" on your dashboard</li>
                  <li>Type your legal question or request in the chat input</li>
                  <li>Receive AI-generated responses tailored to your legal needs</li>
                  <li>Save important conversations for future reference</li>
                </ol>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">Best Practices</h3>
                <ul className="list-disc pl-5 space-y-2">
                  <li>Be specific in your requests for more accurate responses</li>
                  <li>Provide relevant context when asking about legal matters</li>
                  <li>Review AI-generated content before using it in professional work</li>
                  <li>Use the chat history feature to reference previous conversations</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="features" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Features</CardTitle>
              <CardDescription>
                Explore the powerful features of CreateLex AI
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-2">AI-Powered Legal Assistant</h3>
                <p className="text-muted-foreground">
                  Our AI assistant is trained on vast legal datasets and can help with drafting documents,
                  researching case law, analyzing legal arguments, and providing insights on legal topics.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">Document Analysis</h3>
                <p className="text-muted-foreground">
                  Upload legal documents for AI analysis. CreateLex can summarize documents, extract key information,
                  identify potential issues, and suggest improvements.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">Legal Research</h3>
                <p className="text-muted-foreground">
                  Ask questions about specific legal topics, cases, or statutes. The AI can provide relevant information,
                  cite sources, and help you understand complex legal concepts.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">Conversation History</h3>
                <p className="text-muted-foreground">
                  All your conversations are saved and easily accessible. You can reference previous discussions
                  and continue conversations at any time.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="subscription" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Subscription Plans</CardTitle>
              <CardDescription>
                Information about our subscription plans and token usage
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-2">Basic Plan - $20/month</h3>
                <ul className="list-disc pl-5 space-y-1">
                  <li>50,000 tokens daily limit</li>
                  <li>1,000,000 tokens monthly limit</li>
                  <li>Access to all core features</li>
                  <li>Standard response time</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">Pro Plan - $30/month</h3>
                <ul className="list-disc pl-5 space-y-1">
                  <li>100,000 tokens daily limit</li>
                  <li>2,000,000 tokens monthly limit</li>
                  <li>Access to all advanced features</li>
                  <li>Priority response time</li>
                  <li>Enhanced document analysis</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">Additional Tokens</h3>
                <p className="text-muted-foreground">
                  If you need more tokens, you can purchase additional token packages from your dashboard:
                </p>
                <ul className="list-disc pl-5 space-y-1 mt-2">
                  <li>Small Package: 100,000 tokens for $5</li>
                  <li>Medium Package: 500,000 tokens for $20</li>
                  <li>Large Package: 1,000,000 tokens for $35</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">Token Usage</h3>
                <p className="text-muted-foreground">
                  Tokens are used for both your inputs (questions) and the AI's outputs (responses).
                  Longer and more complex conversations will use more tokens. You can monitor your
                  token usage on the dashboard.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="faq" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Frequently Asked Questions</CardTitle>
              <CardDescription>
                Common questions about using CreateLex AI
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-2">What is CreateLex AI?</h3>
                <p className="text-muted-foreground">
                  CreateLex AI is an artificial intelligence assistant specifically designed for legal professionals.
                  It helps with legal research, document drafting, case analysis, and more.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">How accurate is the legal information?</h3>
                <p className="text-muted-foreground">
                  While CreateLex AI is trained on extensive legal data, it should be used as a research and drafting
                  assistant, not as a replacement for professional legal advice. Always review and verify information
                  before using it in professional work.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">What happens if I exceed my token limit?</h3>
                <p className="text-muted-foreground">
                  If you reach your daily or monthly token limit, you can purchase additional token packages
                  from your dashboard to continue using the service without interruption.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">Is my data secure?</h3>
                <p className="text-muted-foreground">
                  Yes, we take data security very seriously. All communications are encrypted, and we do not
                  share your data with third parties. For more information, please review our privacy policy.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">How do I cancel my subscription?</h3>
                <p className="text-muted-foreground">
                  You can cancel your subscription at any time from your account settings page. Your access
                  will continue until the end of your current billing period.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
