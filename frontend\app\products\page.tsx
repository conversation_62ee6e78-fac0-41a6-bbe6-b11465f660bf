'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Navbar from '../../components/landing/Navbar';
import Footer from '../../components/landing/Footer';

export default function ProductsPage() {
  const [isScrolled, setIsScrolled] = React.useState(false);

  // Handle scroll events for navbar styling
  React.useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900 text-white">
      <Navbar isScrolled={isScrolled} isAuthenticated={false} />

      <main className="pt-24 pb-16">
        {/* Hero Section */}
        <section className="container mx-auto px-4 mb-16 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">Our Products</h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Powerful tools to enhance your Unreal Engine development workflow with AI
          </p>
        </section>

        {/* Products Grid */}
        <section className="container mx-auto px-4 mb-20">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Product 1: CreatelexGenAI Plugin */}
            <div className="bg-gray-800/50 border border-gray-700 rounded-xl shadow-lg overflow-hidden group hover:border-blue-500 transition-all duration-300 hover:shadow-blue-500/20 transform hover:-translate-y-1">
              <div className="relative h-64 w-full bg-gradient-to-br from-blue-900 to-blue-700 flex items-center justify-center overflow-hidden">
                {/* Fallback content in case image doesn't load */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-4xl font-bold text-white/20">Unreal Plugin</div>
                </div>

                {/* Product image */}
                <Image
                  src="/images/products/unreal-plugin.jpg"
                  alt="CreatelexGenAI Plugin"
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  className="object-cover transition-transform duration-700 group-hover:scale-110"
                  priority
                />

                {/* Overlay gradient */}
                <div className="absolute inset-0 bg-gradient-to-t from-gray-900 via-transparent to-transparent opacity-70"></div>
              </div>
              <div className="p-8">
                <div className="flex flex-wrap gap-2 mb-4">
                  <span className="text-xs font-medium px-2.5 py-0.5 rounded-full bg-blue-900/50 text-blue-200 border border-blue-700">
                    Unreal Engine
                  </span>
                  <span className="text-xs font-medium px-2.5 py-0.5 rounded-full bg-blue-900/50 text-blue-200 border border-blue-700">
                    Plugin
                  </span>
                  <span className="text-xs font-medium px-2.5 py-0.5 rounded-full bg-blue-900/50 text-blue-200 border border-blue-700">
                    AI
                  </span>
                </div>
                <h2 className="text-2xl font-bold mb-4 text-white">CreatelexGenAI</h2>
                <p className="text-gray-300 mb-6">
                  Integrate AI-powered natural language commands into your Unreal Engine projects. Generate blueprints, materials, and manipulate scenes with simple text prompts.
                </p>

                <h3 className="text-lg font-semibold mb-3 text-gray-200">Key Features</h3>
                <ul className="list-disc pl-5 mb-6 text-gray-300 space-y-1">
                  <li>Natural language blueprint generation</li>
                  <li>Scene manipulation via text commands</li>
                  <li>Material creation and editing</li>
                  <li>Integration with multiple AI models</li>
                  <li>MCP (Model Context Protocol) support</li>
                </ul>

                <div className="flex flex-wrap gap-3">
                  <Link
                    href="/download"
                    className="px-5 py-2.5 bg-gradient-to-r from-blue-600 to-blue-500 text-white rounded-md hover:from-blue-700 hover:to-blue-600 transition-colors"
                  >
                    Download
                  </Link>
                  <Link
                    href="/docs/unreal-plugin"
                    className="px-5 py-2.5 bg-gray-700 text-gray-200 rounded-md hover:bg-gray-600 transition-colors border border-gray-600"
                  >
                    Documentation
                  </Link>
                </div>
              </div>
            </div>

            {/* Product 2: AI Webplatform */}
            <div className="bg-gray-800/50 border border-gray-700 rounded-xl shadow-lg overflow-hidden group hover:border-purple-500 transition-all duration-300 hover:shadow-purple-500/20 transform hover:-translate-y-1">
              <div className="relative h-64 w-full bg-gradient-to-br from-purple-900 to-purple-700 flex items-center justify-center overflow-hidden">
                {/* Fallback content in case image doesn't load */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-4xl font-bold text-white/20">Webplatform</div>
                </div>

                {/* Product image */}
                <Image
                  src="/images/products/ai-webplatform.jpg"
                  alt="AI Webplatform"
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  className="object-cover transition-transform duration-700 group-hover:scale-110"
                  priority
                />

                {/* Overlay gradient */}
                <div className="absolute inset-0 bg-gradient-to-t from-gray-900 via-transparent to-transparent opacity-70"></div>
              </div>
              <div className="p-8">
                <div className="flex flex-wrap gap-2 mb-4">
                  <span className="text-xs font-medium px-2.5 py-0.5 rounded-full bg-purple-900/50 text-purple-200 border border-purple-700">
                    SaaS
                  </span>
                  <span className="text-xs font-medium px-2.5 py-0.5 rounded-full bg-purple-900/50 text-purple-200 border border-purple-700">
                    Cloud
                  </span>
                  <span className="text-xs font-medium px-2.5 py-0.5 rounded-full bg-purple-900/50 text-purple-200 border border-purple-700">
                    Collaboration
                  </span>
                </div>
                <h2 className="text-2xl font-bold mb-4 text-white">AI Webplatform</h2>
                <p className="text-gray-300 mb-6">
                  Our cloud-based platform for AI-powered Unreal Engine development. Connect to your projects remotely and collaborate with team members in real-time.
                </p>

                <h3 className="text-lg font-semibold mb-3 text-gray-200">Key Features</h3>
                <ul className="list-disc pl-5 mb-6 text-gray-300 space-y-1">
                  <li>Remote connection to Unreal Engine projects</li>
                  <li>Real-time collaboration with team members</li>
                  <li>Cloud-based AI processing</li>
                  <li>Project management tools</li>
                  <li>Version control integration</li>
                </ul>

                <div className="flex flex-wrap gap-3">
                  <Link
                    href="/dashboard"
                    className="px-5 py-2.5 bg-gradient-to-r from-purple-600 to-purple-500 text-white rounded-md hover:from-purple-700 hover:to-purple-600 transition-colors"
                  >
                    Get Started
                  </Link>
                  <Link
                    href="/docs/webplatform"
                    className="px-5 py-2.5 bg-gray-700 text-gray-200 rounded-md hover:bg-gray-600 transition-colors border border-gray-600"
                  >
                    Documentation
                  </Link>
                </div>
              </div>
            </div>

            {/* Product 3: MCP Chat */}
            <div className="bg-gray-800/50 border border-gray-700 rounded-xl shadow-lg overflow-hidden group hover:border-green-500 transition-all duration-300 hover:shadow-green-500/20 transform hover:-translate-y-1">
              <div className="relative h-64 w-full bg-gradient-to-br from-green-900 to-green-700 flex items-center justify-center overflow-hidden">
                {/* Fallback content in case image doesn't load */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-4xl font-bold text-white/20">MCP Chat</div>
                </div>

                {/* Product image */}
                <Image
                  src="/images/products/chat.jpg"
                  alt="MCP Chat"
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  className="object-cover transition-transform duration-700 group-hover:scale-110"
                  priority
                />

                {/* Overlay gradient */}
                <div className="absolute inset-0 bg-gradient-to-t from-gray-900 via-transparent to-transparent opacity-70"></div>
              </div>
              <div className="p-8">
                <div className="flex flex-wrap gap-2 mb-4">
                  <span className="text-xs font-medium px-2.5 py-0.5 rounded-full bg-green-900/50 text-green-200 border border-green-700">
                    Chat
                  </span>
                  <span className="text-xs font-medium px-2.5 py-0.5 rounded-full bg-green-900/50 text-green-200 border border-green-700">
                    MCP
                  </span>
                  <span className="text-xs font-medium px-2.5 py-0.5 rounded-full bg-green-900/50 text-green-200 border border-green-700">
                    AI
                  </span>
                </div>
                <h2 className="text-2xl font-bold mb-4 text-white">MCP Chat</h2>
                <p className="text-gray-300 mb-6">
                  Standalone chat application for Model Context Protocol (MCP) interactions. Connect to any MCP-compatible service and leverage the power of AI in your workflow.
                </p>

                <h3 className="text-lg font-semibold mb-3 text-gray-200">Key Features</h3>
                <ul className="list-disc pl-5 mb-6 text-gray-300 space-y-1">
                  <li>Connect to any MCP-compatible service</li>
                  <li>Multiple AI model support</li>
                  <li>Chat history and session management</li>
                  <li>File upload and sharing</li>
                  <li>Custom tool integration</li>
                </ul>

                <div className="flex flex-wrap gap-3">
                  <a
                    href={process.env.NEXT_PUBLIC_API_URL ? `${process.env.NEXT_PUBLIC_API_URL}/login` : "/login"}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="px-5 py-2.5 bg-gradient-to-r from-green-600 to-green-500 text-white rounded-md hover:from-green-700 hover:to-green-600 transition-colors"
                  >
                    Open App
                  </a>
                  <Link
                    href="/docs/chat"
                    className="px-5 py-2.5 bg-gray-700 text-gray-200 rounded-md hover:bg-gray-600 transition-colors border border-gray-600"
                  >
                    Documentation
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="container mx-auto px-4 mb-20">
          <h2 className="text-3xl font-bold mb-4 text-center">Pricing Plans</h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto text-center mb-12">
            Choose the plan that fits your needs and budget
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Basic Plan - $20 */}
            <div className="bg-gradient-to-b from-green-900/50 to-gray-800/80 border border-green-700/50 rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl transform hover:-translate-y-1">
              <div className="p-8">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-2xl font-bold text-white">Basic</h3>
                    <p className="text-gray-300 mt-1">Essential features for starters</p>
                  </div>
                  <span className="bg-green-900/50 text-green-300 px-3 py-1 rounded-full text-sm font-medium">
                    Starter
                  </span>
                </div>

                <div className="mt-6 mb-8">
                  <span className="text-4xl font-bold text-white">$20</span>
                  <span className="text-gray-300 ml-2">/month</span>
                </div>

                <ul className="space-y-4 mb-8">
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-400 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-200">CreatelexGenAI plugin</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-400 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-200">Standard API calls (1000/day)</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-400 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-200">Standard AI models</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-400 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-200">Email support</span>
                  </li>
                  <li className="flex items-start opacity-50">
                    <svg className="h-6 w-6 text-gray-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    <span className="text-gray-400">No team collaboration</span>
                  </li>
                </ul>

                <Link
                  href="/subscription?plan=basic"
                  className="block w-full py-3 px-4 bg-gradient-to-r from-green-600 to-green-400 hover:from-green-500 hover:to-green-300 text-white text-center rounded-lg transition-colors shadow-lg"
                >
                  Get Basic Plan
                </Link>
              </div>
            </div>

            {/* Pro Plan - $30 */}
            <div className="bg-gradient-to-b from-blue-900/50 to-gray-800/80 border border-blue-700/50 rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl transform hover:-translate-y-1 relative">
              <div className="absolute top-0 right-0 bg-gradient-to-r from-blue-600 to-blue-400 text-white px-4 py-1 rounded-bl-lg font-medium text-sm">
                MOST POPULAR
              </div>
              <div className="p-8">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-2xl font-bold text-white">Pro</h3>
                    <p className="text-gray-300 mt-1">Perfect for individual developers</p>
                  </div>
                  <span className="bg-blue-900/50 text-blue-300 px-3 py-1 rounded-full text-sm font-medium">
                    Full Access
                  </span>
                </div>

                <div className="mt-6 mb-8">
                  <span className="text-4xl font-bold text-white">$30</span>
                  <span className="text-gray-300 ml-2">/month</span>
                </div>

                <ul className="space-y-4 mb-8">
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-400 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-200">Everything in Basic plan</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-400 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-200">Unlimited API calls</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-400 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-200">Access to all AI models</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-400 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-200">Priority email support</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-400 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-200">Regular updates</span>
                  </li>
                </ul>

                <Link
                  href="/subscription?plan=pro"
                  className="block w-full py-3 px-4 bg-gradient-to-r from-blue-600 to-blue-400 hover:from-blue-500 hover:to-blue-300 text-white text-center rounded-lg transition-colors shadow-lg"
                >
                  Get Pro Plan
                </Link>
              </div>
            </div>

            {/* Enterprise Plan */}
            <div className="bg-gradient-to-b from-purple-900/50 to-gray-800/80 border border-purple-700/50 rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl transform hover:-translate-y-1">
              <div className="p-8">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-2xl font-bold text-white">Enterprise</h3>
                    <p className="text-gray-300 mt-1">For teams and organizations</p>
                  </div>
                  <span className="bg-purple-900/50 text-purple-300 px-3 py-1 rounded-full text-sm font-medium">
                    Custom
                  </span>
                </div>

                <div className="mt-6 mb-8">
                  <span className="text-4xl font-bold text-white">Custom</span>
                  <span className="text-gray-300 ml-2">pricing</span>
                </div>

                <ul className="space-y-4 mb-8">
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-400 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-200">Everything in Pro plan</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-400 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-200">Team collaboration features</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-400 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-200">Custom integration support</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-400 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-200">Dedicated account manager</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="h-6 w-6 text-green-400 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-200">SLA and premium support</span>
                  </li>
                </ul>

                <Link
                  href="/contact"
                  className="block w-full py-3 px-4 bg-gradient-to-r from-purple-600 to-purple-400 hover:from-purple-500 hover:to-purple-300 text-white text-center rounded-lg transition-colors shadow-lg"
                >
                  Contact Sales
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* Comparison Table */}
        <section className="container mx-auto px-4 mb-20">
          <h2 className="text-3xl font-bold mb-8 text-center">Product Comparison</h2>

          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-gray-800">
                  <th className="p-4 text-left border-b border-gray-700">Feature</th>
                  <th className="p-4 text-center border-b border-gray-700">CreatelexGenAI</th>
                  <th className="p-4 text-center border-b border-gray-700">AI Webplatform</th>
                  <th className="p-4 text-center border-b border-gray-700">MCP Chat</th>
                </tr>
              </thead>
              <tbody>
                <tr className="bg-gray-900/50">
                  <td className="p-4 border-b border-gray-800">Unreal Engine Integration</td>
                  <td className="p-4 text-center border-b border-gray-800">✅</td>
                  <td className="p-4 text-center border-b border-gray-800">✅</td>
                  <td className="p-4 text-center border-b border-gray-800">⚠️ Via MCP</td>
                </tr>
                <tr className="bg-gray-800/50">
                  <td className="p-4 border-b border-gray-800">Blueprint Generation</td>
                  <td className="p-4 text-center border-b border-gray-800">✅</td>
                  <td className="p-4 text-center border-b border-gray-800">✅</td>
                  <td className="p-4 text-center border-b border-gray-800">❌</td>
                </tr>
                <tr className="bg-gray-900/50">
                  <td className="p-4 border-b border-gray-800">Cloud-Based</td>
                  <td className="p-4 text-center border-b border-gray-800">❌</td>
                  <td className="p-4 text-center border-b border-gray-800">✅</td>
                  <td className="p-4 text-center border-b border-gray-800">✅</td>
                </tr>
                <tr className="bg-gray-800/50">
                  <td className="p-4 border-b border-gray-800">Team Collaboration</td>
                  <td className="p-4 text-center border-b border-gray-800">❌</td>
                  <td className="p-4 text-center border-b border-gray-800">✅</td>
                  <td className="p-4 text-center border-b border-gray-800">⚠️ Limited</td>
                </tr>
                <tr className="bg-gray-900/50">
                  <td className="p-4 border-b border-gray-800">Multiple AI Models</td>
                  <td className="p-4 text-center border-b border-gray-800">✅</td>
                  <td className="p-4 text-center border-b border-gray-800">✅</td>
                  <td className="p-4 text-center border-b border-gray-800">✅</td>
                </tr>
                <tr className="bg-gray-800/50">
                  <td className="p-4 border-b border-gray-800">Standalone Application</td>
                  <td className="p-4 text-center border-b border-gray-800">✅</td>
                  <td className="p-4 text-center border-b border-gray-800">❌</td>
                  <td className="p-4 text-center border-b border-gray-800">✅</td>
                </tr>
                <tr className="bg-gray-900/50">
                  <td className="p-4 border-b border-gray-800">MCP Support</td>
                  <td className="p-4 text-center border-b border-gray-800">✅</td>
                  <td className="p-4 text-center border-b border-gray-800">✅</td>
                  <td className="p-4 text-center border-b border-gray-800">✅</td>
                </tr>
              </tbody>
            </table>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="container mx-auto px-4 mb-20">
          <h2 className="text-3xl font-bold mb-8 text-center">Frequently Asked Questions</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-6">
              <h3 className="text-xl font-bold mb-3">Which product should I choose?</h3>
              <p className="text-gray-300">
                If you're working directly in Unreal Engine, the CreatelexGenAI plugin is ideal. For team collaboration and cloud features, choose the AI Webplatform. For a standalone chat experience, MCP Chat is your best option.
              </p>
            </div>

            <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-6">
              <h3 className="text-xl font-bold mb-3">Can I use multiple products together?</h3>
              <p className="text-gray-300">
                Yes! Our products are designed to work together. You can use the CreatelexGenAI plugin with the AI Webplatform for a complete solution, or use MCP Chat as a standalone interface.
              </p>
            </div>

            <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-6">
              <h3 className="text-xl font-bold mb-3">What Unreal Engine versions are supported?</h3>
              <p className="text-gray-300">
                Our products support Unreal Engine 5.1 and higher. For older versions, please contact our support team for compatibility options.
              </p>
            </div>

            <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-6">
              <h3 className="text-xl font-bold mb-3">Do I need an API key?</h3>
              <p className="text-gray-300">
                Yes, you'll need API keys for the AI services you want to use. Our products support OpenAI, Anthropic, DeepSeek, and other providers. You can set these up in the configuration settings.
              </p>
            </div>

            <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-6">
              <h3 className="text-xl font-bold mb-3">What's the difference between the $20 and $30 plans?</h3>
              <p className="text-gray-300">
                The Basic plan ($20/month) includes the CreatelexGenAI plugin with standard features, limited API calls, and standard AI models. The Pro plan ($30/month) adds unlimited API calls, access to all AI models, priority support, and more advanced features.
              </p>
            </div>

            <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-6">
              <h3 className="text-xl font-bold mb-3">How does Enterprise pricing work?</h3>
              <p className="text-gray-300">
                Enterprise pricing is customized based on your organization's needs, team size, and specific requirements. Contact our sales team for a personalized quote and to discuss custom integration options and dedicated support.
              </p>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="container mx-auto px-4">
          <div className="bg-gradient-to-r from-blue-900 to-purple-900 rounded-xl p-8 md:p-12 text-center">
            <h2 className="text-3xl font-bold mb-4">Ready to transform your Unreal Engine workflow?</h2>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Choose the plan that fits your needs and start creating with AI today.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link
                href="/subscription?plan=basic"
                className="px-6 py-3 bg-gradient-to-r from-green-600 to-green-400 hover:from-green-500 hover:to-green-300 text-white font-bold rounded-lg text-lg transition-all shadow-lg hover:shadow-green-500/50"
              >
                Basic Plan ($20/mo)
              </Link>
              <Link
                href="/subscription?plan=pro"
                className="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-400 hover:from-blue-500 hover:to-blue-300 text-white font-bold rounded-lg text-lg transition-all shadow-lg hover:shadow-blue-500/50"
              >
                Pro Plan ($30/mo)
              </Link>
              <Link
                href="/contact"
                className="px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-400 hover:from-purple-500 hover:to-purple-300 text-white font-bold rounded-lg text-lg transition-all shadow-lg hover:shadow-purple-500/50"
              >
                Enterprise Quote
              </Link>
            </div>
            <p className="mt-6 text-gray-300 text-sm">
              All plans come with a 14-day money-back guarantee. No questions asked.
            </p>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
