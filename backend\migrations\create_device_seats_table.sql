-- Create device seats table to track active installations
CREATE TABLE IF NOT EXISTS device_seats (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    device_id VARCHAR(255) NOT NULL,
    device_name VARCHAR(255),
    platform VARCHAR(50),
    last_active TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    is_active BOOLEAN DEFAULT true,
    
    -- Unique constraint to prevent duplicate device registrations
    UNIQUE(user_id, device_id)
);

-- Create index for faster queries
CREATE INDEX idx_device_seats_user_id ON device_seats(user_id);
CREATE INDEX idx_device_seats_last_active ON device_seats(last_active);
CREATE INDEX idx_device_seats_is_active ON device_seats(is_active);

-- Create function to check seat availability
CREATE OR REPLACE FUNCTION check_seat_availability(p_user_id TEXT, p_device_id VARCHAR)
RETURNS BOOLEAN AS $$
DECLARE
    active_seats INTEGER;
    seat_limit INTEGER := 2; -- Default seat limit
    device_exists BOOLEAN;
BEGIN
    -- Check if this device is already registered
    SELECT EXISTS(
        SELECT 1 FROM device_seats 
        WHERE user_id = p_user_id AND device_id = p_device_id
    ) INTO device_exists;
    
    IF device_exists THEN
        -- Device already registered, update last_active
        UPDATE device_seats 
        SET last_active = CURRENT_TIMESTAMP, is_active = true
        WHERE user_id = p_user_id AND device_id = p_device_id;
        RETURN true;
    END IF;
    
    -- Count active seats for this user
    SELECT COUNT(*) INTO active_seats
    FROM device_seats
    WHERE user_id = p_user_id 
    AND is_active = true
    AND last_active > CURRENT_TIMESTAMP - INTERVAL '30 days';
    
    -- Check if user has available seats
    IF active_seats < seat_limit THEN
        RETURN true;
    ELSE
        RETURN false;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create function to register a device
CREATE OR REPLACE FUNCTION register_device_seat(
    p_user_id TEXT,
    p_device_id VARCHAR,
    p_device_name VARCHAR,
    p_platform VARCHAR,
    p_ip_address INET
)
RETURNS TABLE(success BOOLEAN, message TEXT, seat_count INTEGER) AS $$
DECLARE
    can_register BOOLEAN;
    current_seats INTEGER;
BEGIN
    -- Check seat availability
    SELECT check_seat_availability(p_user_id, p_device_id) INTO can_register;
    
    IF NOT can_register THEN
        -- Get current active seat count
        SELECT COUNT(*) INTO current_seats
        FROM device_seats
        WHERE user_id = p_user_id 
        AND is_active = true
        AND last_active > CURRENT_TIMESTAMP - INTERVAL '30 days';
        
        RETURN QUERY SELECT false, 'Seat limit exceeded. You have reached the maximum of 2 active devices.', current_seats;
        RETURN;
    END IF;
    
    -- Register or update the device
    INSERT INTO device_seats (user_id, device_id, device_name, platform, ip_address)
    VALUES (p_user_id, p_device_id, p_device_name, p_platform, p_ip_address)
    ON CONFLICT (user_id, device_id) 
    DO UPDATE SET 
        last_active = CURRENT_TIMESTAMP,
        is_active = true,
        device_name = EXCLUDED.device_name,
        platform = EXCLUDED.platform,
        ip_address = EXCLUDED.ip_address;
    
    -- Get updated seat count
    SELECT COUNT(*) INTO current_seats
    FROM device_seats
    WHERE user_id = p_user_id 
    AND is_active = true
    AND last_active > CURRENT_TIMESTAMP - INTERVAL '30 days';
    
    RETURN QUERY SELECT true, 'Device registered successfully', current_seats;
END;
$$ LANGUAGE plpgsql;

-- Create function to deactivate old devices (run periodically)
CREATE OR REPLACE FUNCTION deactivate_inactive_devices()
RETURNS void AS $$
BEGIN
    UPDATE device_seats
    SET is_active = false
    WHERE last_active < CURRENT_TIMESTAMP - INTERVAL '30 days'
    AND is_active = true;
END;
$$ LANGUAGE plpgsql; 