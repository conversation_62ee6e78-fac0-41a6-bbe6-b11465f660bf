'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../contexts/AuthContext';
import { useSupabaseAuth } from '../../contexts/SupabaseAuthContext';
import Link from 'next/link';

// Subscription plans
const subscriptionPlans = [
  {
    id: 'basic',
    name: 'Basic',
    price: '$20.00',
    period: 'month',
    features: [
      'Access to AI Chat Assistant',
      'Unreal Engine integration',
      'Email support',
      'Unlimited projects',
      'Custom material generation',
      'Blueprint generation',
      'Gemini Flash AI model',
    ],
    recommended: false,
  },
  {
    id: 'pro',
    name: 'Pro',
    price: '$30.00',
    period: 'month',
    features: [
      'Everything in Basic plan',
      'AI model selection',
      'Priority support',
      'Advanced Blueprint generation',
      'Early access to new features',
    ],
    recommended: true,
  },
];

export default function SubscriptionPage() {
  const { isAuthenticated, isLoading, user, hasActiveSubscription, updateSubscription, token } = useAuth();
  const { user: supabaseUser, isLoading: supabaseLoading } = useSupabaseAuth();
  const router = useRouter();
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [couponCode, setCouponCode] = useState<string>('');

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!isLoading && !supabaseLoading) {
      if (!isAuthenticated || !supabaseUser) {
        router.push('/login');
      }
    }
  }, [isAuthenticated, isLoading, supabaseUser, supabaseLoading, router]);

  const handleSelectPlan = (planId: string) => {
    setSelectedPlan(planId);
  };

  const handleSubscribe = async () => {
    if (!selectedPlan) {
      setError('Please select a subscription plan');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // Call the backend to create a Stripe checkout session
      console.log('Creating Stripe checkout session...');

      // Use the API URL from environment variables or fallback to localhost
      // Make sure to use the correct port (5001) for the backend
      const apiUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001'}/api/subscription/create-checkout`;
      // Log the full URL for debugging
      console.log('Full API URL:', apiUrl);
      console.log('Using API URL:', apiUrl);

      // Get the token for authentication
      const authToken = token || '';
      console.log('Using token:', authToken ? 'Valid token available' : 'No valid token');

      if (!authToken) {
        throw new Error('Authentication token not available. Please log in again.');
      }

      // Create the request body with success and cancel URLs and plan type
      // Use dashboard as success URL with a query parameter to indicate success
      // This avoids the 404 error when Stripe redirects to a non-existent route
      const requestBody = {
        successUrl: `${window.location.origin}/dashboard?subscription=success`,
        cancelUrl: `${window.location.origin}/subscription/cancel`,
        planType: selectedPlan, // Pass the selected plan type to the backend
        couponCode: couponCode // Pass the coupon code to the backend if provided
      };
      console.log('Request body:', requestBody);

      // Make the API call to create a checkout session
      let response;
      try {
        response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`,
            'Accept': 'application/json'
          },
          body: JSON.stringify(requestBody),
          credentials: 'include'
        });

        console.log('Response status:', response.status, response.statusText);

        // Handle error responses
        if (!response.ok) {
          const errorText = await response.text();
          console.error('Checkout error response text:', errorText);

          try {
            const errorData = JSON.parse(errorText);
            console.error('Checkout error response parsed:', errorData);
            throw new Error(errorData.error || `Failed to create checkout session: ${response.status} ${response.statusText}`);
          } catch (e) {
            console.error('Could not parse error response as JSON');
            throw new Error(`Failed to create checkout session: ${response.status} ${response.statusText}`);
          }
        }
      } catch (fetchError) {
        console.error('Fetch error:', fetchError);
        // Check if fetchError is an Error object before accessing its properties
        if (fetchError instanceof Error) {
          throw new Error(`Network error: ${fetchError.message}. Please check your connection and try again.`);
        } else {
          throw new Error(`Network error: Please check your connection and try again.`);
        }
      }

      // Parse the response data
      const data = await response.json();
      console.log('Checkout session created:', data);

      // Check if the user already has an active subscription
      if (data.alreadySubscribed) {
        console.log('User already has an active subscription');
        // Update the local subscription status
        updateSubscription(true);
        // Show success message and redirect to dashboard
        router.push('/dashboard');
        return;
      }

      // Redirect to the Stripe checkout URL
      if (data.url) {
        console.log('Redirecting to Stripe Checkout URL:', data.url);
        window.location.href = data.url;
      } else {
        console.error('No URL in response data:', data);
        throw new Error('No checkout URL returned from server');
      }
    } catch (err) {
      console.error('Subscription error:', err);
      // Check if err is an Error object before accessing its properties
      if (err instanceof Error) {
        setError(`Failed to process subscription: ${err.message}`);
      } else {
        setError('Failed to process subscription. Please try again later.');
      }
      setIsProcessing(false);
    }
  };

  if (isLoading || supabaseLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-[#f7f7f7]">
        <div className="text-center">
          <div className="spinner mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-[#f7f7f7]">
      {/* Header */}
      <header className="py-4 px-6 border-b border-gray-200 bg-white">
        <div className="max-w-screen-xl mx-auto flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full"></div>
            <span className="text-xl font-bold">CreateLex</span>
          </div>
          <div className="flex items-center space-x-4">
            <Link href="/dashboard" className="text-gray-600 hover:text-gray-900">
              Dashboard
            </Link>
            {supabaseUser && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">{supabaseUser.email}</span>
                {supabaseUser.user_metadata?.avatar_url ? (
                  <img
                    src={supabaseUser.user_metadata.avatar_url}
                    alt={supabaseUser.user_metadata.full_name || 'User'}
                    className="h-8 w-8 rounded-full"
                  />
                ) : (
                  <button
                    onClick={() => router.push('/profile')}
                    className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 hover:bg-gray-300 transition-colors"
                  >
                    {supabaseUser.email?.charAt(0).toUpperCase() || 'U'}
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="flex-1 py-12 px-6">
        <div className="max-w-screen-xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-3xl font-bold text-gray-800 mb-4">Choose Your Subscription Plan</h1>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Get access to our AI-powered Unreal Engine assistant and unlock your game development potential.
            </p>
          </div>

          {hasActiveSubscription && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-8 max-w-2xl mx-auto">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span>You already have an active subscription. You can manage your subscription from your account settings.</span>
              </div>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-8 max-w-2xl mx-auto">
              {error}
            </div>
          )}

          {/* Subscription Plans */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            {subscriptionPlans.map((plan) => (
              <div
                key={plan.id}
                className={`bg-white rounded-lg shadow-sm border ${
                  selectedPlan === plan.id
                    ? 'border-blue-500 ring-2 ring-blue-200'
                    : plan.recommended
                      ? 'border-blue-200'
                      : 'border-gray-200'
                } overflow-hidden transition-all`}
              >
                {plan.recommended && (
                  <div className="bg-blue-500 text-white text-center py-1 text-sm font-medium">
                    Recommended
                  </div>
                )}

                <div className="p-6">
                  <h3 className="text-xl font-bold mb-2">{plan.name}</h3>
                  <div className="mb-4">
                    <span className="text-3xl font-bold">{plan.price}</span>
                    <span className="text-gray-600">/{plan.period}</span>
                  </div>
                  <ul className="space-y-3 mb-6">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        <span className="text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <button
                    onClick={() => handleSelectPlan(plan.id)}
                    disabled={hasActiveSubscription || isProcessing}
                    className={`w-full py-2 rounded-md transition-colors ${
                      selectedPlan === plan.id
                        ? 'bg-blue-600 text-white hover:bg-blue-700'
                        : plan.recommended
                          ? 'bg-blue-500 text-white hover:bg-blue-600'
                          : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                    } ${(hasActiveSubscription || isProcessing) ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    {selectedPlan === plan.id ? 'Selected' : 'Select Plan'}
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* Subscribe Button */}
          <div className="text-center">
            <button
              onClick={handleSubscribe}
              disabled={!selectedPlan || hasActiveSubscription || isProcessing}
              className={`px-8 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors ${
                (!selectedPlan || hasActiveSubscription || isProcessing) ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {isProcessing ? (
                <span className="flex items-center justify-center">
                  <div className="spinner mr-2" style={{ borderLeftColor: 'white', width: '20px', height: '20px' }}></div>
                  Processing...
                </span>
              ) : hasActiveSubscription ? (
                'Already Subscribed'
              ) : (
                'Subscribe Now'
              )}
            </button>
            {!hasActiveSubscription && (
              <p className="mt-4 text-sm text-gray-600">
                You can cancel your subscription at any time from your account settings.
              </p>
            )}
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="py-6 px-8 border-t border-gray-200 bg-white mt-auto">
        <div className="max-w-screen-xl mx-auto flex flex-col md:flex-row justify-between items-center text-sm text-gray-500">
          <div className="mb-4 md:mb-0">
            © {new Date().getFullYear()} CreateLex. All rights reserved.
          </div>
          <div className="flex space-x-6">
            <Link href="/terms" className="hover:text-gray-700">Terms</Link>
            <Link href="/privacy" className="hover:text-gray-700">Privacy</Link>
            <Link href="/contact" className="hover:text-gray-700">Contact</Link>
          </div>
        </div>
      </footer>
    </div>
  );
}
