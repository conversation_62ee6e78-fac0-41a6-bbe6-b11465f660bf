#!/usr/bin/env node

/**
 * Test script to verify MCP server automatic shutdown functionality
 * This script tests the logic without requiring the full Electron app
 */

const { MCPBridgeServer } = require('./src/mcp/bridge-server');

async function testMCPShutdown() {
  console.log('🧪 Testing MCP Server Automatic Shutdown Functionality\n');
  
  // Test 1: Basic start/stop functionality
  console.log('Test 1: Basic Start/Stop');
  const mcpServer = new MCPBridgeServer();
  
  try {
    console.log('  ⏳ Starting MCP server...');
    await mcpServer.start();
    console.log('  ✅ MCP server started successfully');
    
    console.log('  ⏳ Stopping MCP server...');
    mcpServer.stop('Manual test stop');
    console.log('  ✅ MCP server stopped successfully');
  } catch (error) {
    console.log('  ❌ Error:', error.message);
  }
  
  // Test 2: Event handling
  console.log('\nTest 2: Event Handling');
  const mcpServer2 = new MCPBridgeServer();
  
  mcpServer2.on('stopped', (reason) => {
    console.log(`  ✅ MCP server stopped event received. Reason: ${reason}`);
  });
  
  try {
    console.log('  ⏳ Starting MCP server...');
    await mcpServer2.start();
    console.log('  ✅ MCP server started successfully');
    
    console.log('  ⏳ Simulating logout scenario...');
    mcpServer2.stop('User logout');
    
    console.log('  ⏳ Simulating app quit scenario...');
    setTimeout(() => {
      const mcpServer3 = new MCPBridgeServer();
      mcpServer3.start().then(() => {
        console.log('  ✅ New MCP server started for quit test');
        mcpServer3.stop('App quit');
        console.log('  ✅ MCP server stopped for app quit');
      });
    }, 1000);
    
  } catch (error) {
    console.log('  ❌ Error:', error.message);
  }
  
  console.log('\n🎉 All tests completed!');
  console.log('\n📋 Summary of automatic shutdown scenarios implemented:');
  console.log('  • User logout - MCP server stops automatically');
  console.log('  • App window closed - MCP server stops automatically');
  console.log('  • App quit - MCP server stops automatically');
  console.log('  • Tray quit - MCP server stops automatically');
  console.log('  • Process signals (SIGINT/SIGTERM) - MCP server stops automatically');
  console.log('  • macOS all windows closed - MCP server stops but app stays in tray');
  
  process.exit(0);
}

// Run the test
testMCPShutdown().catch(console.error); 