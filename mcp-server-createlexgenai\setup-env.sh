#!/bin/bash

# UnrealGenAI Docker Integration Environment Setup Script

set -e

echo "🚀 UnrealGenAI Docker Integration Setup"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}ℹ${NC} $1"
}

print_success() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "env.integrated.example" ]; then
    print_error "env.integrated.example not found. Please run this script from the UnrealGenAISupport_with_server/server directory."
    exit 1
fi

# Create .env file from template
print_status "Creating .env file from template..."
cp env.integrated.example .env
print_success ".env file created"

# Check for existing environment files to extract values
print_status "Looking for existing environment configurations..."

# Look for root .env file
if [ -f "../../.env" ]; then
    print_success "Found root .env file"
    ROOT_ENV_FILE="../../.env"
elif [ -f "../../../.env" ]; then
    print_success "Found parent .env file"
    ROOT_ENV_FILE="../../../.env"
else
    print_warning "No root .env file found"
    ROOT_ENV_FILE=""
fi

# Look for backend .env file
if [ -f "../../backend/.env" ]; then
    print_success "Found backend .env file"
    BACKEND_ENV_FILE="../../backend/.env"
elif [ -f "../../backend/.env.production" ]; then
    print_success "Found backend .env.production file"
    BACKEND_ENV_FILE="../../backend/.env.production"
else
    print_warning "No backend .env file found"
    BACKEND_ENV_FILE=""
fi

# Look for frontend .env file
if [ -f "../../frontend/.env.local" ]; then
    print_success "Found frontend .env.local file"
    FRONTEND_ENV_FILE="../../frontend/.env.local"
elif [ -f "../../frontend/.env" ]; then
    print_success "Found frontend .env file"
    FRONTEND_ENV_FILE="../../frontend/.env"
else
    print_warning "No frontend .env file found"
    FRONTEND_ENV_FILE=""
fi

# Function to extract value from env file
extract_env_value() {
    local file=$1
    local key=$2
    if [ -f "$file" ]; then
        grep "^$key=" "$file" | cut -d'=' -f2- | sed 's/^"//' | sed 's/"$//'
    fi
}

# Function to update env value in .env file
update_env_value() {
    local key=$1
    local value=$2
    if [ -n "$value" ]; then
        # Escape special characters for sed
        escaped_value=$(echo "$value" | sed 's/[[\.*^$()+?{|]/\\&/g')
        sed -i.bak "s|^$key=.*|$key=$escaped_value|" .env
        print_success "Updated $key"
    fi
}

print_status "Extracting environment variables from existing files..."

# Array of environment variables to extract
declare -a env_vars=(
    "JWT_SECRET"
    "GOOGLE_CLIENT_ID"
    "STRIPE_SECRET_KEY"
    "STRIPE_PUBLISHABLE_KEY"
    "STRIPE_WEBHOOK_SECRET"
    "STRIPE_PRICE_ID"
    "STRIPE_PRICE_ID_BASIC"
    "STRIPE_PRICE_ID_PRO"
    "SUPABASE_URL"
    "SUPABASE_SERVICE_KEY"
    "PS_OPENAIAPIKEY"
    "PS_DEEPSEEKAPIKEY"
    "PS_ANTHROPICAPIKEY"
    "PS_GOOGLEAPIKEY"
    "EMAIL_HOST"
    "EMAIL_USER"
    "EMAIL_PASSWORD"
)

# Extract and update environment variables
for var in "${env_vars[@]}"; do
    value=""
    
    # Try to find the variable in different env files
    if [ -n "$ROOT_ENV_FILE" ]; then
        value=$(extract_env_value "$ROOT_ENV_FILE" "$var")
    fi
    
    if [ -z "$value" ] && [ -n "$BACKEND_ENV_FILE" ]; then
        value=$(extract_env_value "$BACKEND_ENV_FILE" "$var")
    fi
    
    if [ -z "$value" ] && [ -n "$FRONTEND_ENV_FILE" ]; then
        value=$(extract_env_value "$FRONTEND_ENV_FILE" "$var")
        
        # Try frontend variants
        if [ -z "$value" ]; then
            value=$(extract_env_value "$FRONTEND_ENV_FILE" "NEXT_PUBLIC_$var")
        fi
    fi
    
    # Update the .env file if we found a value
    if [ -n "$value" ]; then
        update_env_value "$var" "$value"
        
        # Also update NEXT_PUBLIC variants for frontend variables
        case $var in
            "GOOGLE_CLIENT_ID"|"STRIPE_PUBLISHABLE_KEY"|"SUPABASE_URL")
                update_env_value "NEXT_PUBLIC_$var" "$value"
                ;;
        esac
    else
        print_warning "No value found for $var"
    fi
done

# Special handling for SUPABASE_ANON_KEY
supabase_anon_key=""
if [ -n "$FRONTEND_ENV_FILE" ]; then
    supabase_anon_key=$(extract_env_value "$FRONTEND_ENV_FILE" "NEXT_PUBLIC_SUPABASE_ANON_KEY")
fi
if [ -n "$supabase_anon_key" ]; then
    update_env_value "NEXT_PUBLIC_SUPABASE_ANON_KEY" "$supabase_anon_key"
fi

# Set development defaults
print_status "Setting development defaults..."
update_env_value "ALWAYS_SUBSCRIBED" "true"
update_env_value "LOG_LEVEL" "INFO"
update_env_value "NODE_ENV" "development"

print_success "Environment extraction complete!"

echo ""
echo "📋 Next Steps:"
echo "=============="
echo "1. Review and edit the .env file:"
echo "   nano .env"
echo ""
echo "2. Make sure to update these important values:"
echo "   - JWT_SECRET (should be a secure random string)"
echo "   - All Stripe keys (secret, publishable, webhook secret)"
echo "   - Google OAuth client ID"
echo "   - Supabase URL and keys"
echo "   - AI API keys (OpenAI, Anthropic, etc.)"
echo ""
echo "3. For development, you can leave ALWAYS_SUBSCRIBED=true"
echo "   For production, set ALWAYS_SUBSCRIBED=false"
echo ""
echo "4. Test the configuration:"
echo "   docker-compose -f docker-compose.integrated.yml up -d"
echo ""
echo "5. Check health status:"
echo "   curl http://localhost:8001/health"

# Clean up backup file
if [ -f ".env.bak" ]; then
    rm .env.bak
fi

print_success "🎉 Setup complete! Review the .env file and start your Docker services." 