const { app, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, ipc<PERSON>ain } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');

// Set up logging to file for production debugging
const logDir = path.join(app.getPath('userData'), 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

const logFile = path.join(logDir, `bridge-${new Date().toISOString().split('T')[0]}.log`);
const logStream = fs.createWriteStream(logFile, { flags: 'a' });

// Override console methods to also write to file
const originalLog = console.log;
const originalError = console.error;

console.log = function(...args) {
  const timestamp = new Date().toISOString();
  const message = `[${timestamp}] [LOG] ${args.join(' ')}`;
  logStream.write(message + '\n');
  originalLog.apply(console, args);
};

console.error = function(...args) {
  const timestamp = new Date().toISOString();
  const message = `[${timestamp}] [ERROR] ${args.join(' ')}`;
  logStream.write(message + '\n');
  originalError.apply(console, args);
};

console.log('=== CreateLex Bridge Starting ===');
console.log('Log file:', logFile);
console.log('App version:', app.getVersion());
console.log('Electron version:', process.versions.electron);
console.log('Node version:', process.versions.node);
console.log('Platform:', process.platform);
console.log('Environment variables:');
console.log('  NODE_ENV:', process.env.NODE_ENV);
console.log('  DEV_MODE:', process.env.DEV_MODE);
console.log('  DEBUG_MODE:', process.env.DEBUG_MODE);
console.log('  BYPASS_SUBSCRIPTION:', process.env.BYPASS_SUBSCRIPTION);

const { AuthHandler } = require('./src/auth/auth-handler');
const { MCPBridgeServer } = require('./src/mcp/bridge-server');
const { MCPUpdater } = require('./src/updater/mcp-updater');

// Set default environment variables for production builds
if (!process.env.NODE_ENV) {
  process.env.NODE_ENV = 'production';
}

// Always bypass subscription checks
process.env.BYPASS_SUBSCRIPTION = 'true';

let mainWindow;
let tray;
let authHandler;
let mcpServer;
let mcpUpdater;

function createMainWindow() {
  const { nativeImage } = require('electron');
  let iconPath = path.join(__dirname, 'assets', 'icons', 'logo.png');
  let windowIcon = nativeImage.createFromPath(iconPath);
  
  // Resize icon for window use (64x64 is good for window icons)
  if (!windowIcon.isEmpty()) {
    windowIcon = windowIcon.resize({ width: 64, height: 64 });
    console.log('Loaded and resized logo for main window icon');
  }

  mainWindow = new BrowserWindow({
    width: 400,
    height: 600,
    resizable: false,
    icon: windowIcon.isEmpty() ? undefined : windowIcon,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true,
      allowRunningInsecureContent: false,
      preload: path.join(__dirname, 'preload.js'),
    },
  });

  // Always load local files when available, regardless of dev/prod
  const loginFilePath = path.join(__dirname, 'web', 'login', 'index.html');
  console.log('Loading login page from:', loginFilePath);
  
  // Check if file exists before loading
  const fs = require('fs');
  if (fs.existsSync(loginFilePath)) {
    console.log('✅ Loading LOCAL login page');
    mainWindow.loadFile(loginFilePath);
  } else {
    console.log('⚠️ Local login file not found, using web version');
    mainWindow.loadURL('https://createlex.com/login');
  }

  mainWindow.on('closed', async () => {
    console.log('Main window closed - stopping MCP server and logging out...');
    if (mcpServer) {
      mcpServer.stop('Main window closed');
    }
    if (authHandler) {
      await authHandler.logout();
    }
    mainWindow = null;
  });

  // Add error handling for failed loads
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.error('Failed to load:', validatedURL, 'Error:', errorDescription);
    
    // If local file fails to load, try the web version
    if (!isDev && validatedURL.includes('index.html')) {
      console.log('Falling back to web version...');
      mainWindow.loadURL('https://createlex.com/login');
    }
  });
}

function createTray() {
  let iconPath = path.join(__dirname, 'assets', 'icons', 'logo.png');
  try {
    const { nativeImage } = require('electron');
    // Load the image and resize it for tray use
    let image = nativeImage.createFromPath(iconPath);
    if (image.isEmpty()) {
      console.log('Logo image not found, using empty icon');
      image = nativeImage.createEmpty();
    } else {
      // Resize to appropriate tray icon size (32x32 for better quality)
      image = image.resize({ width: 32, height: 32 });
      console.log('Loaded and resized logo for tray icon');
    }
    tray = new Tray(image);
  } catch (err) {
    console.error('Error loading tray icon:', err);
    // Fallback to empty tray icon if asset missing
    const { nativeImage } = require('electron');
    tray = new Tray(nativeImage.createEmpty());
  }
  const contextMenu = Menu.buildFromTemplate([
    { label: 'Open Dashboard', click: () => mainWindow?.show() },
    { type: 'separator' },
    { 
      label: 'MCP Server Status', 
      click: () => {
        const status = mcpServer.getStatus();
        console.log('MCP Status:', status);
      }
    },
    { 
      label: 'Stop MCP Server', 
      click: () => {
        console.log('Stopping MCP server...');
        mcpServer.stop();
        tray?.setToolTip('CreateLex Bridge – MCP Server Stopped');
      }
    },
    { 
      label: 'Restart MCP Server', 
      click: async () => {
        console.log('Restarting MCP server...');
        mcpServer.stop();
        await mcpServer.start().catch(console.error);
        tray?.setToolTip('CreateLex Bridge – MCP Server Running');
      }
    },
    { type: 'separator' },
    { 
      label: 'Open Logs Folder', 
      click: () => {
        const { shell } = require('electron');
        const logsPath = path.join(app.getPath('userData'), 'logs');
        
        // Ensure the directory exists
        if (!fs.existsSync(logsPath)) {
          fs.mkdirSync(logsPath, { recursive: true });
        }
        
        // Open the folder in file explorer
        shell.openPath(logsPath);
        console.log('Opened logs folder:', logsPath);
      }
    },
    { 
      label: 'Clear All Logs', 
      click: async () => {
        try {
          const { dialog } = require('electron');
          const result = await dialog.showMessageBox({
            type: 'question',
            buttons: ['Yes', 'No'],
            defaultId: 1,
            title: 'Clear Logs',
            message: 'Are you sure you want to clear all log files?',
            detail: 'This will delete all Bridge and MCP log files. This action cannot be undone.'
          });
          
          if (result.response === 0) { // User clicked "Yes"
            // Clear Electron logs
            const electronLogDir = path.join(app.getPath('userData'), 'logs');
            if (fs.existsSync(electronLogDir)) {
              const files = fs.readdirSync(electronLogDir);
              for (const file of files) {
                if (file.endsWith('.log')) {
                  fs.unlinkSync(path.join(electronLogDir, file));
                }
              }
              console.log('Cleared Electron logs');
            }
            
            // Clear Python/MCP logs
            const pythonLogDir = path.join(os.homedir(), '.createlex-bridge', 'logs');
            if (fs.existsSync(pythonLogDir)) {
              const files = fs.readdirSync(pythonLogDir);
              for (const file of files) {
                if (file.endsWith('.log')) {
                  fs.unlinkSync(path.join(pythonLogDir, file));
                }
              }
              console.log('Cleared Python/MCP logs');
            }
            
            // Clear current session log stream
            if (logStream) {
              logStream.end();
              logStream = fs.createWriteStream(logFile, { flags: 'w' }); // Recreate with write mode to clear
            }
            
            console.log('All logs cleared successfully');
            
            dialog.showMessageBox({
              type: 'info',
              title: 'Logs Cleared',
              message: 'All log files have been cleared successfully.',
              buttons: ['OK']
            });
          }
        } catch (error) {
          console.error('Error clearing logs:', error);
          const { dialog } = require('electron');
          dialog.showErrorBox('Error', `Failed to clear logs: ${error.message}`);
        }
      }
    },
    { type: 'separator' },
    { label: 'Quit', click: async () => {
        console.log('Quit selected from tray - stopping MCP server and logging out...');
        if (mcpServer) {
          mcpServer.stop('Quit from tray');
        }
        if (authHandler) {
          await authHandler.logout();
        }
        app.quit();
      }
    },
  ]);
  tray.setToolTip('CreateLex Bridge');
  tray.setContextMenu(contextMenu);
}

function showDashboard() {
  // If window exists, reuse it instead of creating new one
  if (mainWindow && !mainWindow.isDestroyed()) {
    // Resize window for dashboard
    mainWindow.setSize(800, 600);
    mainWindow.setResizable(true);
    
    // Load dashboard
    const dashboardFilePath = path.join(__dirname, 'web', 'dashboard', 'index.html');
    console.log('Loading dashboard page from:', dashboardFilePath);
    
    const fs = require('fs');
    if (fs.existsSync(dashboardFilePath)) {
      console.log('✅ Loading LOCAL dashboard page');
      mainWindow.loadFile(dashboardFilePath);
    } else {
      console.log('⚠️ Local dashboard file not found, using web version');
      mainWindow.loadURL('https://createlex.com/dashboard');
    }
    
    // Show and focus the window
    mainWindow.show();
    mainWindow.focus();
  } else {
    // Create new window only if none exists
    const { nativeImage } = require('electron');
    let iconPath = path.join(__dirname, 'assets', 'icons', 'logo.png');
    let windowIcon = nativeImage.createFromPath(iconPath);
    
    // Resize icon for window use (64x64 is good for window icons)
    if (!windowIcon.isEmpty()) {
      windowIcon = windowIcon.resize({ width: 64, height: 64 });
      console.log('Loaded and resized logo for dashboard window icon');
    }

    mainWindow = new BrowserWindow({
      width: 800,
      height: 600,
      icon: windowIcon.isEmpty() ? undefined : windowIcon,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        webSecurity: true,
        allowRunningInsecureContent: false,
        preload: path.join(__dirname, 'preload.js'),
      },
    });
    
    // Always load local files when available, regardless of dev/prod
    const dashboardFilePath = path.join(__dirname, 'web', 'dashboard', 'index.html');
    console.log('Loading dashboard page from:', dashboardFilePath);
    
    // Check if file exists before loading
    const fs = require('fs');
    if (fs.existsSync(dashboardFilePath)) {
      console.log('✅ Loading LOCAL dashboard page');
      mainWindow.loadFile(dashboardFilePath);
    } else {
      console.log('⚠️ Local dashboard file not found, using web version');
      mainWindow.loadURL('https://createlex.com/dashboard');
    }

    // Add error handling for failed loads
    mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
      console.error('Failed to load dashboard:', validatedURL, 'Error:', errorDescription);
      
      // If local file fails to load, try the web version
      if (!isDev && validatedURL.includes('index.html')) {
        console.log('Falling back to web dashboard...');
        mainWindow.loadURL('https://createlex.com/dashboard');
      }
    });
    
    mainWindow.on('closed', async () => {
      console.log('Dashboard window closed - stopping MCP server and logging out...');
      if (mcpServer) {
        mcpServer.stop('Dashboard window closed');
      }
      if (authHandler) {
        await authHandler.logout();
      }
      mainWindow = null;
    });
  }
}

app.whenReady().then(async () => {
  authHandler = new AuthHandler();
  mcpServer = new MCPBridgeServer();
  mcpUpdater = new MCPUpdater();

  // Check for bypass mode
  const bypassLogin = process.env.BYPASS_LOGIN === 'true' || process.env.SKIP_AUTH === 'true';
  const autoStartMCP = process.env.AUTO_START_MCP === 'true';
  
  if (bypassLogin) {
    console.log('🔧 BYPASS MODE: Skipping authentication and auto-starting MCP server');
    console.log('🔧 Environment flags detected:');
    console.log('  BYPASS_LOGIN:', process.env.BYPASS_LOGIN);
    console.log('  SKIP_AUTH:', process.env.SKIP_AUTH);  
    console.log('  AUTO_START_MCP:', process.env.AUTO_START_MCP);
    console.log('  BYPASS_SUBSCRIPTION:', process.env.BYPASS_SUBSCRIPTION);
    
    // Create window and tray without login
    createMainWindow();
    createTray();
    
    // Skip to dashboard directly
    setTimeout(() => {
      console.log('🔧 BYPASS: Loading dashboard directly...');
      showDashboard();
      
      // Auto-start MCP server if requested
      if (autoStartMCP) {
        console.log('🔧 BYPASS: Auto-starting MCP server...');
        setTimeout(async () => {
          try {
            await mcpServer.start();
            console.log('✅ BYPASS: MCP server started automatically!');
            tray?.setToolTip('CreateLex Bridge – MCP Server Running (Bypass Mode)');
          } catch (error) {
            console.error('❌ BYPASS: Failed to auto-start MCP server:', error);
          }
        }, 2000); // Give dashboard time to load
      }
    }, 1000);
    
  } else {
    // Normal startup with login screen for security
    // This ensures proper device/seat validation on each session
    console.log('App startup - forcing login for security and device validation');
    
    // Clear any existing authentication to ensure fresh login
    await authHandler.logout();
    
    createMainWindow();
    createTray();
  }

  // Handle subscription events
  mcpServer.on('subscription-invalid', (subscriptionStatus) => {
    console.log('🚨 Subscription invalid - MCP server stopped automatically');
    tray?.setToolTip('CreateLex Bridge – Subscription Expired');
    
    // Notify the frontend if window is open
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('subscription-invalid', {
        message: 'Your subscription has expired. The MCP server has been stopped.',
        error: subscriptionStatus.error
      });
    }
  });

  mcpServer.on('subscription-check-error', (error) => {
    console.log('⚠️ Subscription check error (server continues running):', error.message);
    
    // Notify the frontend of the warning
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('subscription-warning', {
        message: 'Unable to verify subscription status. Please check your internet connection.',
        error: error.message
      });
    }
  });

  // Set up MCP updater event listeners
  mcpUpdater.on('update-start', () => {
    console.log('🔄 MCP server update started');
    tray?.setToolTip('CreateLex Bridge – Updating MCP Server');
    
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('mcp-update-status', {
        status: 'updating',
        message: 'MCP server update in progress...'
      });
    }
  });

  mcpUpdater.on('download-progress', ({ percent }) => {
    console.log(`📥 Download progress: ${percent}%`);
    
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('mcp-update-progress', { percent });
    }
  });

  mcpUpdater.on('update-complete', ({ version, message }) => {
    console.log(`✅ MCP server updated to v${version}`);
    tray?.setToolTip(`CreateLex Bridge – MCP Server Updated (v${version})`);
    
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('mcp-update-status', {
        status: 'complete',
        version,
        message
      });
    }
  });

  mcpUpdater.on('update-error', ({ error, restored, message }) => {
    console.error('❌ MCP server update failed:', error);
    tray?.setToolTip('CreateLex Bridge – Update Failed');
    
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('mcp-update-status', {
        status: 'error',
        error,
        restored,
        message
      });
    }
  });

  app.on('activate', async () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      // Always show login for security (no persistent authentication)
      console.log('App activated - showing login for security');
      await authHandler.logout(); // Clear any stored auth
      createMainWindow();
    }
  });
});

app.on('window-all-closed', async () => {
  // On macOS, keep the app running in the tray but stop the MCP server and logout for security
  if (process.platform === 'darwin') {
    console.log('All windows closed (macOS) - stopping MCP server and logging out...');
    if (mcpServer) {
      mcpServer.stop('All windows closed (macOS)');
    }
    if (authHandler) {
      await authHandler.logout();
    }
  } else {
    // On Windows/Linux, stop MCP server, logout, and quit the app
    console.log('All windows closed - stopping MCP server, logging out, and quitting app...');
    if (mcpServer) {
      mcpServer.stop('All windows closed');
    }
    if (authHandler) {
      await authHandler.logout();
    }
    app.quit();
  }
});

// Add proper app quit handling
app.on('before-quit', async () => {
  console.log('App is about to quit - stopping MCP server and logging out...');
  if (mcpServer) {
    mcpServer.stop('App quit');
  }
  if (authHandler) {
    await authHandler.logout();
  }
});

// Handle process termination signals for proper cleanup
process.on('SIGINT', async () => {
  console.log('Received SIGINT - stopping MCP server, logging out, and exiting...');
  if (mcpServer) {
    mcpServer.stop('SIGINT received');
  }
  if (authHandler) {
    await authHandler.logout();
  }
  app.quit();
});

process.on('SIGTERM', async () => {
  console.log('Received SIGTERM - stopping MCP server, logging out, and exiting...');
  if (mcpServer) {
    mcpServer.stop('SIGTERM received');
  }
  if (authHandler) {
    await authHandler.logout();
  }
  app.quit();
});

// IPC handlers
ipcMain.handle('authenticate', async (event, credentials) => {
  // Legacy authentication for backward compatibility
  const result = await authHandler.authenticate(credentials);
  if (result.success) {
    process.env.AUTH_TOKEN = result.token;
    tray?.setToolTip('CreateLex Bridge – Authenticated');
    showDashboard();
  }
  return result;
});

// New OAuth authentication handler
ipcMain.handle('authenticate-oauth', async (event) => {
  try {
    console.log('IPC: authenticate-oauth called');
    const result = await authHandler.authenticateOAuth();
    
    if (result.success) {
      process.env.AUTH_TOKEN = result.token;
      tray?.setToolTip('CreateLex Bridge – Authenticated (OAuth)');
      console.log('OAuth authentication successful, showing dashboard');
      
      // Log the validated subscription status
      const hasSubscription = result.userData?.subscription?.hasActiveSubscription || false;
      console.log(`User subscription status: ${hasSubscription ? 'Active' : 'Inactive'}`);
      
      showDashboard();
    }
    
    return result;
  } catch (error) {
    console.error('IPC: OAuth authentication failed:', error);
    return { success: false, error: error.message };
  }
});

// Cancel OAuth authentication
ipcMain.handle('cancel-oauth', async (event) => {
  try {
    authHandler.cancelOAuth();
    return { success: true };
  } catch (error) {
    console.error('IPC: Error canceling OAuth:', error);
    return { success: false, error: error.message };
  }
});

// Check authentication status
ipcMain.handle('check-auth-status', async (event) => {
  try {
    const status = await authHandler.isAuthenticated();
    console.log('IPC: Authentication status check:', status);
    return status;
  } catch (error) {
    console.error('IPC: Error checking auth status:', error);
    return { authenticated: false, reason: 'check_failed', error: error.message };
  }
});

// Show dashboard handler
ipcMain.handle('show-dashboard', async (event) => {
  try {
    console.log('IPC: show-dashboard called');
    showDashboard();
    return { success: true };
  } catch (error) {
    console.error('IPC: Error showing dashboard:', error);
    return { success: false, error: error.message };
  }
});

// Logout handler
ipcMain.handle('logout', async (event) => {
  try {
    // Stop MCP server when user logs out
    if (mcpServer) {
      console.log('User logging out - stopping MCP server...');
      mcpServer.stop('User logout');
    }
    
    const result = await authHandler.logout();
    if (result.success) {
      process.env.AUTH_TOKEN = null;
      tray?.setToolTip('CreateLex Bridge – Not Authenticated');
      
      // Navigate existing window to login page instead of creating new one
      if (mainWindow && !mainWindow.isDestroyed()) {
        // Resize window for login page
        mainWindow.setSize(400, 600);
        mainWindow.setResizable(false);
        
        // Load login page
        const loginFilePath = path.join(__dirname, 'web', 'login', 'index.html');
        const fs = require('fs');
        
        if (fs.existsSync(loginFilePath)) {
          console.log('✅ Loading LOCAL login page after logout');
          mainWindow.loadFile(loginFilePath);
        } else {
          console.log('⚠️ Local login file not found, using web version');
          mainWindow.loadURL('https://createlex.com/login');
        }
      } else {
        // Only create new window if none exists
        createMainWindow();
      }
    }
    return result;
  } catch (error) {
    console.error('IPC: Error during logout:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('start-mcp', async (event, authData) => {
  try {
    console.log('IPC: start-mcp called');
    
    // If authentication data is provided, store it for the MCP server to use
    if (authData && authData.token) {
      console.log('IPC: Setting auth token for MCP server');
      await authHandler.tokenManager.storeToken(authData.token);
      process.env.AUTH_TOKEN = authData.token;
    }
    
    await mcpServer.start();
    console.log('IPC: MCP bridge server started successfully');
    tray?.setToolTip('CreateLex Bridge – MCP Server Running (Port 9877)');
    return { success: true, status: mcpServer.getStatus() };
  } catch (err) {
    console.error('IPC: MCP bridge server start failed:', err);
    tray?.setToolTip('CreateLex Bridge – MCP Server Failed');
    return { success: false, error: err.message };
  }
});

ipcMain.handle('stop-mcp', async () => {
  try {
    console.log('IPC: stop-mcp called');
    mcpServer.stop();
    console.log('IPC: MCP server stopped successfully');
    tray?.setToolTip('CreateLex Bridge – MCP Server Stopped');
    return { success: true, status: mcpServer.getStatus() };
  } catch (err) {
    console.error('IPC: MCP server stop failed:', err);
    return { success: false, error: err.message };
  }
});

ipcMain.handle('get-mcp-status', () => {
  return mcpServer.getStatus();
});

ipcMain.handle('check-subscription', async () => {
  try {
    const isValid = await mcpServer.getSubscriptionStatus();
    return { success: true, valid: isValid };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// Add new IPC handlers for MCP updates
ipcMain.handle('check-mcp-updates', async () => {
  try {
    const updateCheck = await mcpUpdater.checkForUpdates();
    return { success: true, ...updateCheck };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('update-mcp-server', async () => {
  try {
    // Stop MCP server before updating
    if (mcpServer.getStatus().running) {
      console.log('Stopping MCP server for update...');
      mcpServer.stop();
    }

    const result = await mcpUpdater.performUpdate();
    
    // Restart MCP server after successful update
    if (result.success) {
      console.log('Restarting MCP server after update...');
      await mcpServer.start();
      tray?.setToolTip('CreateLex Bridge – MCP Server Running (Updated)');
    }
    
    return result;
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-mcp-version', async () => {
  try {
    const version = await mcpUpdater.getCurrentVersion();
    return { success: true, version };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('cleanup-mcp-updates', async () => {
  try {
    await mcpUpdater.cleanup();
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
}); 