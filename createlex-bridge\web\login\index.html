<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CreateLex Bridge - Login</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #333;
    }

    .login-container {
      background: white;
      border-radius: 12px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      padding: 40px;
      width: 100%;
      max-width: 420px;
      text-align: center;
    }

    .logo {
      width: 64px;
      height: 64px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 16px;
      margin: 0 auto 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-weight: bold;
      color: white;
    }

    h1 {
      font-size: 28px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #1a202c;
    }

    .subtitle {
      color: #718096;
      margin-bottom: 32px;
      font-size: 16px;
    }

    .oauth-button {
      width: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 16px;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    .oauth-button:hover {
      transform: translateY(-1px);
      box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    }

    .oauth-button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    .status {
      background: #f0fdf4;
      border: 1px solid #bbf7d0;
      color: #16a34a;
      padding: 12px;
      border-radius: 8px;
      margin: 20px 0;
      font-size: 14px;
      display: none;
    }

    .error {
      background: #fef2f2;
      border: 1px solid #fecaca;
      color: #dc2626;
    }

    .loading {
      display: none;
      margin-top: 16px;
      color: #667eea;
    }

    .spinner {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 8px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="login-container">
    <div class="logo">CL</div>
    <h1>Welcome to CreateLex</h1>
    <p class="subtitle">Sign in to access your MCP bridge</p>

    <button id="oauthBtn" class="oauth-button">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
      </svg>
      Sign in with CreateLex
    </button>

    <!-- Loading State -->
    <div id="loading" class="loading">
      <span class="spinner"></span>
      <span>Opening browser for authentication...</span>
    </div>

    <!-- Messages -->
    <div id="message" class="status"></div>
  </div>

  <script>
    // Check if we're in Electron
    const isElectron = typeof window !== 'undefined' && window.electronAPI;

    const oauthBtn = document.getElementById('oauthBtn');
    const loading = document.getElementById('loading');
    const message = document.getElementById('message');

    function showMessage(text, isError = false) {
      message.textContent = text;
      message.className = isError ? 'status error' : 'status';
      message.style.display = 'block';
    }

    function showLoading() {
      loading.style.display = 'block';
      oauthBtn.disabled = true;
    }

    function hideLoading() {
      loading.style.display = 'none';
      oauthBtn.disabled = false;
    }

    // OAuth authentication
    oauthBtn.addEventListener('click', async () => {
      if (!isElectron) {
        showMessage('OAuth authentication is only available in the desktop app', true);
        return;
      }

      showLoading();

      try {
        const result = await window.electronAPI.authenticateOAuth();
        
        if (result.success) {
          showMessage('Authentication successful! Opening local dashboard...');
          // The main process will handle showing the local dashboard
        } else {
          hideLoading();
          showMessage(result.error || 'Authentication failed', true);
        }
      } catch (err) {
        hideLoading();
        showMessage('Authentication error: ' + err.message, true);
      }
    });

    // Auto-start OAuth if in Electron
    if (isElectron) {
      // Check if already authenticated
      window.electronAPI.checkAuthStatus().then(status => {
        if (status.authenticated) {
          showMessage('Already authenticated. You can close this window.');
        }
      }).catch(() => {
        // Ignore errors
      });
    } else {
      showMessage('This is the CreateLex Bridge login page. Please use the desktop application.', true);
    }
  </script>
</body>
</html>