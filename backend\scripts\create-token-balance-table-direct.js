require('dotenv').config();
const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');

async function createTokenBalanceTable() {
  // Create a connection pool
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL || process.env.SUPABASE_DB_URL,
    ssl: { rejectUnauthorized: false }
  });

  try {
    console.log('Connected to PostgreSQL database');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../migrations/create_token_balance_table.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Split the SQL into individual statements
    const statements = sql.split(';').filter(stmt => stmt.trim() !== '');
    
    // Execute each statement
    for (const statement of statements) {
      if (statement.trim()) {
        console.log(`Executing: ${statement.trim().substring(0, 50)}...`);
        await pool.query(statement);
      }
    }
    
    console.log('Token balance table created successfully!');
  } catch (error) {
    console.error('Error creating token_balance table:', error);
  } finally {
    // Close the pool
    await pool.end();
  }
}

createTokenBalanceTable();
