<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Set Supabase User ID</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .card {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background-color: #4a6cf7;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #3a5ce5;
        }
        input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100%;
            font-size: 16px;
            margin-bottom: 10px;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Set Supabase User ID for MCP-Chat</h1>
    
    <div class="card">
        <h2>Current User ID</h2>
        <p>Current User ID: <span id="currentUserId">Loading...</span></p>
        <p>Source: <span id="currentUserIdSource">Loading...</span></p>
    </div>
    
    <div class="card">
        <h2>Set Supabase User ID</h2>
        <p>Enter your Supabase User ID:</p>
        <input type="text" id="supabaseUserId" placeholder="e.g., 077f1533-9f81-429c-b1b1-52d9c83f146c" value="077f1533-9f81-429c-b1b1-52d9c83f146c">
        <button id="setUserIdBtn">Set User ID</button>
        <p id="result"></p>
    </div>
    
    <div class="card">
        <h2>Instructions</h2>
        <ol>
            <li>Enter your Supabase User ID in the field above</li>
            <li>Click "Set User ID"</li>
            <li>Return to the MCP-Chat application</li>
            <li>Refresh the page to see your chats</li>
        </ol>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Display current user ID
            const currentUserId = localStorage.getItem('ai-chat-user-id') || 'Not set';
            const currentUserIdSource = localStorage.getItem('ai-chat-user-id-source') || 'Not set';
            
            document.getElementById('currentUserId').textContent = currentUserId;
            document.getElementById('currentUserIdSource').textContent = currentUserIdSource;
            
            // Set up button click handler
            document.getElementById('setUserIdBtn').addEventListener('click', function() {
                const supabaseUserId = document.getElementById('supabaseUserId').value.trim();
                
                if (!supabaseUserId) {
                    document.getElementById('result').textContent = 'Please enter a valid Supabase User ID';
                    document.getElementById('result').className = 'error';
                    return;
                }
                
                try {
                    // Set the user ID in localStorage
                    localStorage.setItem('ai-chat-user-id', supabaseUserId);
                    localStorage.setItem('ai-chat-user-id-source', 'supabase');
                    
                    // Update the display
                    document.getElementById('currentUserId').textContent = supabaseUserId;
                    document.getElementById('currentUserIdSource').textContent = 'supabase';
                    
                    // Show success message
                    document.getElementById('result').textContent = 'User ID set successfully! Please refresh the MCP-Chat application.';
                    document.getElementById('result').className = 'success';
                } catch (error) {
                    document.getElementById('result').textContent = 'Error setting user ID: ' + error.message;
                    document.getElementById('result').className = 'error';
                }
            });
        });
    </script>
</body>
</html>
