const express = require('express');
const router = express.Router();
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const authService = require('../../services/authService');
const emailService = require('../../services/emailService');
const supabase = require('../../services/supabaseClient');

// Generate a unique ID for this webhook request
const generateWebhookId = () => `webhook-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;

// Stripe webhook handler
router.post('/', express.raw({ type: 'application/json' }), async (req, res) => {
  // Generate a unique webhook ID for tracking
  const webhookId = generateWebhookId();

  try {
    console.log(`[${webhookId}] Received Stripe webhook request`);
    console.log(`[${webhookId}] Request headers:`, req.headers);

    // Log the first 100 characters of the body for debugging
    const bodyPreview = req.body ? req.body.toString().substring(0, 100) + '...' : 'Empty body';
    console.log(`[${webhookId}] Request body preview: ${bodyPreview}`);

    const sig = req.headers['stripe-signature'];
    const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;
    const isDevelopment = process.env.NODE_ENV === 'development' || process.env.BYPASS_WEBHOOK_SIGNATURE === 'true';

    let event;

    // In development mode, we can bypass signature verification if needed
    if (isDevelopment && (!endpointSecret || process.env.BYPASS_WEBHOOK_SIGNATURE === 'true')) {
      console.log(`[${webhookId}] Running in development mode, bypassing signature verification`);
      try {
        // Parse the raw body as JSON
        if (typeof req.body === 'string') {
          event = JSON.parse(req.body);
        } else if (Buffer.isBuffer(req.body)) {
          event = JSON.parse(req.body.toString('utf8'));
        } else if (typeof req.body === 'object') {
          event = req.body;
        } else {
          throw new Error(`Unexpected body type: ${typeof req.body}`);
        }

        console.log(`[${webhookId}] Successfully parsed webhook payload without verification`);
        console.log(`[${webhookId}] Event type: ${event.type}, ID: ${event.id}`);
      } catch (parseError) {
        console.error(`[${webhookId}] Failed to parse webhook payload:`, parseError.message);
        return res.status(400).send(`Webhook Error: Invalid JSON payload - ${parseError.message}`);
      }
    } else {
      // Verify the webhook signature in production or if secret is available
      try {
        if (!sig) {
          console.error(`[${webhookId}] Missing Stripe signature header`);
          return res.status(400).send('Webhook Error: Missing stripe-signature header');
        }

        if (!endpointSecret) {
          console.error(`[${webhookId}] Missing webhook secret in environment variables`);
          return res.status(400).send('Webhook Error: Missing webhook secret configuration');
        }

        console.log(`[${webhookId}] Verifying webhook signature with secret: ${endpointSecret.substring(0, 4)}...`);

        // Ensure req.body is a buffer
        const payload = Buffer.isBuffer(req.body) ? req.body : Buffer.from(req.body, 'utf8');

        event = stripe.webhooks.constructEvent(payload, sig, endpointSecret);
        console.log(`[${webhookId}] Webhook signature verified successfully`);
      } catch (err) {
        console.error(`[${webhookId}] Webhook signature verification failed:`, err.message);

        // Try to parse the payload anyway in development mode as a fallback
        if (isDevelopment) {
          console.log(`[${webhookId}] Attempting to parse payload as fallback in development mode`);
          try {
            if (Buffer.isBuffer(req.body)) {
              event = JSON.parse(req.body.toString('utf8'));
            } else if (typeof req.body === 'string') {
              event = JSON.parse(req.body);
            } else if (typeof req.body === 'object') {
              event = req.body;
            } else {
              throw new Error(`Unexpected body type: ${typeof req.body}`);
            }

            console.log(`[${webhookId}] Successfully parsed webhook payload as fallback`);
            console.log(`[${webhookId}] Event type: ${event.type}, ID: ${event.id}`);
          } catch (parseError) {
            console.error(`[${webhookId}] Fallback parsing failed:`, parseError.message);
            return res.status(400).send(`Webhook Error: ${err.message} and fallback parsing failed`);
          }
        } else {
          return res.status(400).send(`Webhook Error: ${err.message}`);
        }
      }
    }

    console.log(`[${webhookId}] Received event: ${event.type}, ID: ${event.data.object.id}`);

    // Handle the event based on its type
    switch (event.type) {
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
        await handleSubscriptionChange(event.data.object, webhookId);
        break;

      case 'customer.subscription.deleted':
        await handleSubscriptionCanceled(event.data.object, webhookId);
        break;

      case 'checkout.session.completed':
        await handleCheckoutCompleted(event.data.object, webhookId);
        break;

      default:
        console.log(`[${webhookId}] Unhandled event type: ${event.type}`);
    }

    // Return a 200 response to acknowledge receipt of the event
    console.log(`[${webhookId}] Successfully processed webhook event: ${event.type}`);
    res.json({ received: true, webhookId });
  } catch (error) {
    console.error(`[${webhookId}] Error processing webhook:`, error);
    res.status(500).json({ error: 'Failed to process webhook', webhookId });
  }
});

// Handle subscription changes (created or updated)
async function handleSubscriptionChange(subscription, webhookId) {
  try {
    console.log(`[${webhookId}] Processing subscription change for subscription ${subscription.id}`);

    // Get the customer ID from the subscription
    const customerId = subscription.customer;

    // Find the user with this Stripe customer ID
    const user = await findUserByStripeCustomerId(customerId);

    if (!user) {
      console.log(`[${webhookId}] No user found for Stripe customer ID: ${customerId}`);
      return;
    }

    console.log(`[${webhookId}] Found user ${user.id} for customer ${customerId}`);

    // Update the user's subscription status
    const status = subscription.status === 'active' ? 'active' : 'inactive';

    await authService.updateSubscription(user.id, status, subscription.id);

    console.log(`[${webhookId}] Updated subscription for user ${user.id} to status: ${status}`);
  } catch (error) {
    console.error(`[${webhookId}] Error handling subscription change:`, error);
    throw error; // Re-throw to be caught by the main handler
  }
}

// Handle subscription cancellation
async function handleSubscriptionCanceled(subscription, webhookId) {
  try {
    console.log(`[${webhookId}] Processing subscription cancellation for subscription ${subscription.id}`);

    // Get the customer ID from the subscription
    const customerId = subscription.customer;

    // Find the user with this Stripe customer ID
    const user = await findUserByStripeCustomerId(customerId);

    if (!user) {
      console.log(`[${webhookId}] No user found for Stripe customer ID: ${customerId}`);
      return;
    }

    console.log(`[${webhookId}] Found user ${user.id} for customer ${customerId}`);

    // Update the user's subscription status
    // Keep the subscription ID so we can track it in Stripe
    await authService.updateSubscription(user.id, 'active', subscription.id);

    console.log(`[${webhookId}] Updated subscription for user ${user.id} to status: active (will not renew)`);

    // Send cancellation notification email
    try {
      console.log(`[${webhookId}] Sending cancellation email notification for user: ${user.id}`);
      const emailResult = await emailService.sendSubscriptionCancellationEmail({
        user,
        subscriptionId: subscription.id,
        stripeResult: {
          status: subscription.status,
          cancelAtPeriodEnd: subscription.cancel_at_period_end,
          currentPeriodEnd: new Date(subscription.current_period_end * 1000).toISOString()
        }
      });

      console.log(`[${webhookId}] Email notification result:`, emailResult);
    } catch (emailError) {
      console.error(`[${webhookId}] Error sending cancellation email:`, emailError);
      // Don't throw this error, as we've already updated the subscription status
    }
  } catch (error) {
    console.error(`[${webhookId}] Error handling subscription cancellation:`, error);
    throw error; // Re-throw to be caught by the main handler
  }
}

// Handle checkout session completion
async function handleCheckoutCompleted(session, webhookId) {
  try {
    console.log(`[${webhookId}] Processing checkout completion for session ${session.id}`);

    // Check if this is a token purchase
    if (session.metadata && session.metadata.type === 'token_purchase') {
      console.log(`[${webhookId}] Detected token purchase in session ${session.id}`);

      // Get the token purchase details from metadata
      const userId = session.metadata.userId;
      const tokens = parseInt(session.metadata.tokens, 10);
      const packageId = session.metadata.packageId;
      const transactionId = session.payment_intent;

      if (!userId || !tokens) {
        console.error(`[${webhookId}] Missing required metadata for token purchase: userId=${userId}, tokens=${tokens}`);
        return;
      }

      console.log(`[${webhookId}] Processing token purchase: ${tokens} tokens (package: ${packageId}) for user ${userId}`);

      // Import the token purchase service
      const tokenPurchaseService = require('../../services/tokenPurchaseService');

      try {
        // Add tokens to user's account
        const result = await tokenPurchaseService.addTokensToUser(userId, tokens, transactionId);

        console.log(`[${webhookId}] Token purchase processed successfully:`, result);

        // Update the token balance for the user
        const newBalance = result.balance;
        console.log(`[${webhookId}] Updated token balance for user: ${userId}, new balance: ${newBalance}`);
      } catch (tokenError) {
        console.error(`[${webhookId}] Error adding tokens to user:`, tokenError);

        // Check if this transaction has already been processed
        if (tokenError.message && tokenError.message.includes('already processed')) {
          console.log(`[${webhookId}] Transaction already processed, ignoring duplicate webhook`);
        } else {
          // Re-throw the error to be caught by the outer try/catch
          throw tokenError;
        }
      }

      return;
    }

    // Only process subscription checkouts
    if (session.mode !== 'subscription') {
      console.log(`[${webhookId}] Ignoring non-subscription checkout: ${session.mode}`);
      return;
    }

    // Get the customer and subscription from the session
    const customerId = session.customer;
    const subscriptionId = session.subscription;

    // Get the user email from the session
    const userEmail = session.customer_details?.email;

    if (!userEmail) {
      console.log(`[${webhookId}] No email found in checkout session: ${session.id}`);
      return;
    }

    console.log(`[${webhookId}] Looking for user with email: ${userEmail}`);

    // Find the user with this email
    let user = await findUserByEmail(userEmail);

    if (!user) {
      console.log(`[${webhookId}] No user found for email: ${userEmail}`);
      return;
    }

    console.log(`[${webhookId}] Found user ${user.id} for email ${userEmail}`);

    // Update the user's Stripe customer ID if it's not set
    if (!user.stripeCustomerId) {
      user = await updateUserStripeCustomerId(user.id, customerId);
      console.log(`[${webhookId}] Updated Stripe customer ID for user ${user.id}: ${customerId}`);
    }

    // Get the subscription details
    try {
      const subscription = await stripe.subscriptions.retrieve(subscriptionId);

      // Update the user's subscription status
      const status = subscription.status === 'active' ? 'active' : 'inactive';

      await authService.updateSubscription(user.id, status, subscriptionId);

      console.log(`[${webhookId}] Updated subscription for user ${user.id} to status: ${status}`);

      // Update the last checkout session ID
      await updateLastCheckoutSessionId(user.id, session.id);

      console.log(`[${webhookId}] Updated last checkout session ID for user ${user.id}: ${session.id}`);
    } catch (stripeError) {
      console.error(`[${webhookId}] Error retrieving subscription from Stripe:`, stripeError);
      throw stripeError;
    }
  } catch (error) {
    console.error(`[${webhookId}] Error handling checkout completion:`, error);
    throw error; // Re-throw to be caught by the main handler
  }
}

// Helper function to find a user by Stripe customer ID
async function findUserByStripeCustomerId(customerId) {
  try {
    // Try to find the user in Supabase
    const { data: users, error } = await supabase
      .from('users')
      .select('*')
      .eq('stripe_customer_id', customerId);

    if (error) {
      console.error('[Stripe Webhook] Error finding user by Stripe customer ID:', error);
      return null;
    }

    if (users && users.length > 0) {
      const user = users[0];
      return {
        id: user.id,
        email: user.email,
        name: user.name,
        picture: user.picture,
        subscriptionStatus: user.subscription_status,
        subscriptionId: user.subscription_id,
        stripeCustomerId: user.stripe_customer_id
      };
    }

    return null;
  } catch (error) {
    console.error('[Stripe Webhook] Error in findUserByStripeCustomerId:', error);
    return null;
  }
}

// Helper function to find a user by email
async function findUserByEmail(email) {
  try {
    // Try to find the user in Supabase
    const { data: users, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', email);

    if (error) {
      console.error('[Stripe Webhook] Error finding user by email:', error);
      return null;
    }

    if (users && users.length > 0) {
      const user = users[0];
      return {
        id: user.id,
        email: user.email,
        name: user.name,
        picture: user.picture,
        subscriptionStatus: user.subscription_status,
        subscriptionId: user.subscription_id,
        stripeCustomerId: user.stripe_customer_id
      };
    }

    return null;
  } catch (error) {
    console.error('[Stripe Webhook] Error in findUserByEmail:', error);
    return null;
  }
}

// Helper function to update a user's Stripe customer ID
async function updateUserStripeCustomerId(userId, customerId) {
  try {
    // Update the user in Supabase
    const { data: user, error } = await supabase
      .from('users')
      .update({ stripe_customer_id: customerId })
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      console.error('[Stripe Webhook] Error updating user Stripe customer ID:', error);
      return null;
    }

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      picture: user.picture,
      subscriptionStatus: user.subscription_status,
      subscriptionId: user.subscription_id,
      stripeCustomerId: user.stripe_customer_id
    };
  } catch (error) {
    console.error('[Stripe Webhook] Error in updateUserStripeCustomerId:', error);
    return null;
  }
}

// Helper function to update a user's last checkout session ID
async function updateLastCheckoutSessionId(userId, sessionId) {
  try {
    // Update the user in Supabase
    const { error } = await supabase
      .from('users')
      .update({ last_checkout_session_id: sessionId })
      .eq('id', userId);

    if (error) {
      console.error('[Stripe Webhook] Error updating last checkout session ID:', error);
    }
  } catch (error) {
    console.error('[Stripe Webhook] Error in updateLastCheckoutSessionId:', error);
  }
}

module.exports = router;
