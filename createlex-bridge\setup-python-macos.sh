#!/bin/bash

echo "🍎 macOS Python Setup for CreateLex Bridge"
echo "=========================================="

# Check if Homebrew is installed
if ! command -v brew &> /dev/null; then
    echo "📦 Installing Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
else
    echo "✅ Homebrew is already installed"
fi

# Check current Python versions
echo ""
echo "🐍 Checking Python versions..."

python_versions=("python3.12" "python3.11" "python3.10" "python3" "python")
best_python=""
best_version=""

for cmd in "${python_versions[@]}"; do
    if command -v "$cmd" &> /dev/null; then
        version_output=$($cmd --version 2>&1)
        if [[ $version_output =~ Python\ ([0-9]+)\.([0-9]+) ]]; then
            major=${BASH_REMATCH[1]}
            minor=${BASH_REMATCH[2]}
            echo "   Found $cmd: Python $major.$minor"
            
            # Check if this is suitable for FastMCP (3.10+)
            if [ "$major" -eq 3 ] && [ "$minor" -ge 10 ]; then
                if [ -z "$best_python" ]; then
                    best_python="$cmd"
                    best_version="$major.$minor"
                fi
            fi
        fi
    fi
done

if [ -n "$best_python" ]; then
    echo ""
    echo "✅ Found suitable Python: $best_python (Python $best_version)"
    echo "   This version supports FastMCP (requires Python 3.10+)"
else
    echo ""
    echo "⚠️  No suitable Python version found for FastMCP"
    echo "   FastMCP requires Python 3.10 or higher"
    echo ""
    echo "📦 Installing Python 3.11..."
    
    brew install python@3.11
    
    # Add to PATH if needed
    echo ""
    echo "🔧 Setting up PATH..."
    if ! echo $PATH | grep -q "/opt/homebrew/bin"; then
        echo 'export PATH="/opt/homebrew/bin:$PATH"' >> ~/.zshrc
        export PATH="/opt/homebrew/bin:$PATH"
    fi
    
    if command -v python3.11 &> /dev/null; then
        echo "✅ Python 3.11 installed successfully!"
        best_python="python3.11"
    else
        echo "❌ Failed to install Python 3.11"
        exit 1
    fi
fi

echo ""
echo "🔧 Installing FastMCP and dependencies with $best_python..."

# Install FastMCP and other dependencies
$best_python -m pip install --user fastmcp aiohttp requests websockets uvicorn flask pydantic python-dotenv

echo ""
echo "✅ Setup complete!"
echo ""
echo "🧪 Testing installation..."
echo "Using Python: $best_python"

# Test FastMCP installation
if $best_python -c "import fastmcp; print('FastMCP version:', fastmcp.__version__)" 2>/dev/null; then
    echo "✅ FastMCP is working!"
else
    echo "❌ FastMCP installation failed"
fi

echo ""
echo "🚀 Now you can run:"
echo "   npm run build:mcp-exe    # Will automatically use $best_python"
echo "   npm run test-macos-deps  # Test all dependencies" 