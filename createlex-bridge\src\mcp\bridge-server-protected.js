const { spawn } = require('child_process');
const net = require('net');
const path = require('path');
const { EventEmitter } = require('events');
const fs = require('fs');
const { AuthHandler } = require('../auth/auth-handler');

class MCPBridgeServerProtected extends EventEmitter {
  constructor(options = {}) {
    super();
    this.port = options.port || 9877;
    this.mcpProcess = null;
    this.tcpServer = null;
    this.clients = new Set();
    this.isReady = false;
    this.authHandler = new AuthHandler();
  }

  async start() {
    if (this.mcpProcess) {
      console.log('MCP bridge server already running');
      return;
    }

    // Start the MCP executable
    await this.startMCPExecutable();
    
    // Start TCP server for other IDEs
    await this.startTCPServer();
    
    console.log(`MCP Bridge Server (Protected) running on port ${this.port}`);
  }

  async startMCPExecutable() {
    let execPath;
    const isDev = process.env.NODE_ENV !== 'production' || process.env.DEV_MODE === 'true';
    
    if (isDev) {
      // In development, still use Python files
      const scriptPath = path.join(__dirname, '..', 'python', 'mcp_server_protected.py');
      console.log('Development mode - using Python script:', scriptPath);
      
      return this.startPythonServer(scriptPath);
    } else {
      // In production, use the compiled executable
      const { app } = require('electron');
      const resourcesPath = process.resourcesPath || app.getAppPath();
      
      // Determine executable name based on platform
      const os = require('os');
      const platform = os.platform();
      let executableName = 'mcp_server';
      
      if (platform === 'win32') {
        executableName = 'mcp_server.exe';
      } else if (platform === 'darwin') {
        executableName = 'mcp_server_mac';
      } else if (platform === 'linux') {
        executableName = 'mcp_server_linux';
      }
      
      execPath = path.join(resourcesPath, 'mcp', executableName);
      
      if (!fs.existsSync(execPath)) {
        throw new Error(`MCP executable not found at: ${execPath} (platform: ${platform})`);
      }
      
      console.log(`Starting protected MCP executable for ${platform}:`, execPath);
    }

    // Get auth token for the executable
    const authToken = process.env.AUTH_TOKEN || '';

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('MCP executable start timeout'));
      }, 30000);

      // Spawn the executable directly (not through Python)
      this.mcpProcess = spawn(execPath, [], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          AUTH_TOKEN: authToken,
          API_BASE_URL: process.env.API_BASE_URL || 'https://api.createlex.com/api',
          PYTHONIOENCODING: 'utf-8',
          // The executable doesn't need Python-specific env vars
        }
      });

      // Handle stdout (MCP protocol data)
      this.mcpProcess.stdout.on('data', (data) => {
        const output = data.toString();
        
        // Check for ready state
        if (output.includes('MCP_READY') || output.includes('Server started') || output.includes('"method"')) {
          if (!this.isReady) {
            clearTimeout(timeout);
            this.isReady = true;
            this.emit('ready');
            resolve();
          }
        }
        
        // Broadcast to all connected clients
        this.clients.forEach(client => {
          if (!client.destroyed) {
            client.write(data);
          }
        });
      });

      this.mcpProcess.stderr.on('data', (data) => {
        const output = data.toString();
        console.error('[MCP Protected Error]', output);
        
        // Check for ready messages in stderr too
        if (output.includes('MCP Server started') || output.includes('Starting MCP server')) {
          if (!this.isReady) {
            clearTimeout(timeout);
            this.isReady = true;
            this.emit('ready');
            resolve();
          }
        }
      });

      this.mcpProcess.on('exit', (code) => {
        clearTimeout(timeout);
        console.log(`MCP executable exited with code: ${code}`);
        this.isReady = false;
        this.mcpProcess = null;
        this.emit('exit', code);
        
        if (code !== 0) {
          reject(new Error(`MCP executable exited with code ${code}`));
        }
      });

      this.mcpProcess.on('error', (err) => {
        clearTimeout(timeout);
        console.error('MCP executable error:', err);
        reject(err);
      });
    });
  }

  // Fallback method for development
  async startPythonServer(scriptPath) {
    // Similar to original implementation but simplified
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Python MCP server start timeout'));
      }, 30000);

      // Determine Python command based on platform
      const os = require('os');
      const platform = os.platform();
      let pythonCmd = 'python';
      
      if (platform === 'darwin' || platform === 'linux') {
        // Try python3 first on Unix systems
        try {
          require('child_process').execSync('python3 --version', { stdio: 'pipe' });
          pythonCmd = 'python3';
        } catch (e) {
          // Fall back to python
          pythonCmd = 'python';
        }
      }

      console.log(`Using Python command: ${pythonCmd} (platform: ${platform})`);

      this.mcpProcess = spawn(pythonCmd, [scriptPath], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          PYTHONIOENCODING: 'utf-8',
          DEV_MODE: 'true',
          BYPASS_SUBSCRIPTION: 'true'
        }
      });

      // ... rest of the Python handling code ...
      this.mcpProcess.stdout.on('data', (data) => {
        if (!this.isReady) {
          clearTimeout(timeout);
          this.isReady = true;
          this.emit('ready');
          resolve();
        }
        
        this.clients.forEach(client => {
          if (!client.destroyed) {
            client.write(data);
          }
        });
      });

      this.mcpProcess.on('exit', (code) => {
        clearTimeout(timeout);
        this.isReady = false;
        this.mcpProcess = null;
        this.emit('exit', code);
        
        if (code !== 0) {
          reject(new Error(`Python server exited with code ${code}`));
        }
      });
    });
  }

  async startTCPServer() {
    // Same as original implementation
    return new Promise((resolve, reject) => {
      this.tcpServer = net.createServer((socket) => {
        console.log('New client connected to MCP bridge');
        this.clients.add(socket);

        socket.on('data', (data) => {
          if (this.mcpProcess && !this.mcpProcess.killed) {
            this.mcpProcess.stdin.write(data);
          }
        });

        socket.on('close', () => {
          console.log('Client disconnected from MCP bridge');
          this.clients.delete(socket);
        });

        socket.on('error', (err) => {
          console.error('Client socket error:', err);
          this.clients.delete(socket);
        });
      });

      this.tcpServer.listen(this.port, 'localhost', () => {
        console.log(`TCP bridge server listening on localhost:${this.port}`);
        resolve();
      });

      this.tcpServer.on('error', (err) => {
        console.error('TCP server error:', err);
        reject(err);
      });
    });
  }

  stop(reason = 'Manual stop') {
    console.log(`Stopping MCP bridge server... (${reason})`);
    
    this.clients.forEach(client => {
      if (!client.destroyed) {
        client.end();
      }
    });
    this.clients.clear();

    if (this.tcpServer) {
      this.tcpServer.close();
      this.tcpServer = null;
    }

    if (this.mcpProcess) {
      this.mcpProcess.kill('SIGTERM');
      this.mcpProcess = null;
    }

    this.isReady = false;
    this.emit('stopped', reason);
  }

  getStatus() {
    return {
      isRunning: !!this.mcpProcess && this.isReady,
      port: this.port,
      clientCount: this.clients.size,
      processId: this.mcpProcess?.pid || null
    };
  }
}

module.exports = { MCPBridgeServerProtected }; 