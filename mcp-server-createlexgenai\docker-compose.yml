version: '3.8'

services:
  unreal-mcp-server:
    build: .
    container_name: unreal-mcp-server
    ports:
      - "8000:8000"  # MCP FastMCP HTTP server
      - "9877:9877"  # Communication with Unreal Engine
    environment:
      - UNREAL_HOST=${UNREAL_HOST:-host.docker.internal}
      - UNREAL_PORT=${UNREAL_PORT:-9877}
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs  # Optional: for log persistence
      - .:/app/server     # Mount server directory for stdio access 