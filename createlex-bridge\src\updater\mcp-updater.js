const fs = require('fs-extra');
const path = require('path');
const axios = require('axios');
const crypto = require('crypto');
const { EventEmitter } = require('events');
const os = require('os');

class MCPUpdater extends EventEmitter {
  constructor() {
    super();
    // Use local backend for testing, production URL for production
    const isDev = process.env.NODE_ENV !== 'production';
    this.updateServerUrl = process.env.MCP_UPDATE_SERVER || (isDev ? 'http://localhost:5001/api/mcp-updates' : 'https://createlex.com/api/mcp-updates');
    
    // Platform-aware path construction
    this.localMCPPath = path.resolve(__dirname, '..', 'python');
    this.backupPath = path.resolve(__dirname, '..', 'python-backup');
    this.versionFile = path.join(this.localMCPPath, 'version.json');
    this.tempDir = path.join(os.tmpdir(), 'createlex-mcp-update');
    this.isUpdating = false;
    
    // Platform detection
    this.platform = process.platform;
    this.isWindows = this.platform === 'win32';
    this.isMacOS = this.platform === 'darwin';
    this.isLinux = this.platform === 'linux';
    
    console.log(`[MCP Updater] Initialized for platform: ${this.platform}`);
  }

  /**
   * Get platform-specific executable permissions
   */
  getExecutablePermissions() {
    if (this.isWindows) {
      return null; // Windows doesn't use Unix permissions
    }
    return 0o755; // rwxr-xr-x for Unix-like systems
  }

  /**
   * Set executable permissions on Python files (Unix-like systems only)
   */
  async setExecutablePermissions(filePath) {
    if (this.isWindows) {
      return; // No need to set permissions on Windows
    }

    try {
      const { execSync } = require('child_process');
      const quotedPath = `"${filePath}"`;
      execSync(`chmod +x ${quotedPath}`, { stdio: 'pipe' });
      console.log(`✅ Set executable permissions for: ${filePath}`);
    } catch (error) {
      console.warn(`⚠️ Failed to set executable permissions for ${filePath}:`, error.message);
      // Don't throw error as this is not critical for functionality
    }
  }

  /**
   * Get current local MCP server version
   */
  async getCurrentVersion() {
    try {
      if (await fs.pathExists(this.versionFile)) {
        const versionData = await fs.readJson(this.versionFile);
        return versionData.version || '1.0.0';
      }
      return '1.0.0'; // Default version if no version file exists
    } catch (error) {
      console.warn('Failed to read version file:', error.message);
      return '1.0.0';
    }
  }

  /**
   * Check for available updates from the server
   */
  async checkForUpdates() {
    try {
      const currentVersion = await this.getCurrentVersion();
      console.log(`Current MCP version: ${currentVersion}`);

      const response = await axios.get(`${this.updateServerUrl}/check`, {
        params: { current_version: currentVersion },
        timeout: 10000,
        headers: {
          'User-Agent': `CreateLex-Bridge/${currentVersion} (${this.platform})`
        }
      });

      const { hasUpdate, latestVersion, updateInfo } = response.data;
      
      if (hasUpdate) {
        console.log(`Update available: ${currentVersion} → ${latestVersion}`);
        return {
          hasUpdate: true,
          currentVersion,
          latestVersion,
          updateInfo: updateInfo || {}
        };
      }

      console.log('MCP server is up to date');
      return { hasUpdate: false, currentVersion };
    } catch (error) {
      console.error('Failed to check for updates:', error.message);
      throw new Error(`Update check failed: ${error.message}`);
    }
  }

  /**
   * Download and verify MCP server update package
   */
  async downloadUpdate(version) {
    try {
      console.log(`Downloading MCP server update v${version}...`);
      this.emit('download-start', { version });

      const response = await axios.get(`${this.updateServerUrl}/download/${version}`, {
        responseType: 'arraybuffer',
        timeout: 60000,
        headers: {
          'User-Agent': `CreateLex-Bridge/${version} (${this.platform})`
        },
        onDownloadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          this.emit('download-progress', { percent: percentCompleted });
        }
      });

      // Verify checksum if provided
      const expectedChecksum = response.headers['x-checksum'];
      if (expectedChecksum) {
        const actualChecksum = crypto.createHash('sha256').update(response.data).digest('hex');
        if (actualChecksum !== expectedChecksum) {
          throw new Error('Update package checksum verification failed');
        }
        console.log('✅ Update package checksum verified');
      }

      this.emit('download-complete', { version });
      return response.data;
    } catch (error) {
      this.emit('download-error', { error: error.message });
      throw new Error(`Download failed: ${error.message}`);
    }
  }

  /**
   * Create backup of current MCP server files
   */
  async createBackup() {
    try {
      console.log('Creating backup of current MCP server...');
      
      // Remove old backup if exists
      if (await fs.pathExists(this.backupPath)) {
        await fs.remove(this.backupPath);
      }

      // Ensure backup directory exists
      await fs.ensureDir(path.dirname(this.backupPath));

      // Copy current files to backup with platform-aware options
      await fs.copy(this.localMCPPath, this.backupPath, {
        preserveTimestamps: true,
        dereference: false // Don't follow symlinks
      });
      
      console.log('✅ Backup created successfully');
      return true;
    } catch (error) {
      console.error('Failed to create backup:', error.message);
      throw new Error(`Backup failed: ${error.message}`);
    }
  }

  /**
   * Restore from backup in case of update failure
   */
  async restoreFromBackup() {
    try {
      console.log('Restoring from backup...');
      
      if (!(await fs.pathExists(this.backupPath))) {
        throw new Error('No backup found to restore from');
      }

      // Remove current (potentially corrupted) files
      if (await fs.pathExists(this.localMCPPath)) {
        await fs.remove(this.localMCPPath);
      }
      
      // Ensure parent directory exists
      await fs.ensureDir(path.dirname(this.localMCPPath));
      
      // Restore from backup with platform-aware options
      await fs.copy(this.backupPath, this.localMCPPath, {
        preserveTimestamps: true,
        dereference: false
      });
      
      // Restore executable permissions on Unix-like systems
      if (!this.isWindows) {
        const pythonFiles = ['mcp_server_protected.py', 'mcp_server_stdio.py'];
        for (const file of pythonFiles) {
          const filePath = path.join(this.localMCPPath, file);
          if (await fs.pathExists(filePath)) {
            await this.setExecutablePermissions(filePath);
          }
        }
      }
      
      console.log('✅ Successfully restored from backup');
      return true;
    } catch (error) {
      console.error('Failed to restore from backup:', error.message);
      throw new Error(`Restore failed: ${error.message}`);
    }
  }

  /**
   * Extract and install update package
   */
  async installUpdate(updateData, version) {
    try {
      console.log(`Installing MCP server update v${version}...`);
      this.emit('install-start', { version });

      // Create platform-aware temporary directory
      const tempExtractDir = path.join(this.tempDir, `extract-${Date.now()}`);
      await fs.ensureDir(tempExtractDir);

      try {
        // Extract ZIP file using adm-zip (cross-platform)
        const AdmZip = require('adm-zip');
        const zip = new AdmZip(updateData);
        
        // Extract with platform-aware options
        zip.extractAllTo(tempExtractDir, true);
        console.log(`✅ Update extracted to: ${tempExtractDir}`);

        // Verify required files exist in the update
        const requiredFiles = ['mcp_server_protected.py', 'mcp_server_stdio.py', 'version.json'];
        for (const file of requiredFiles) {
          const filePath = path.join(tempExtractDir, file);
          if (!(await fs.pathExists(filePath))) {
            throw new Error(`Required file missing in update: ${file}`);
          }
        }

        // Remove current MCP files (backup already created)
        if (await fs.pathExists(this.localMCPPath)) {
          await fs.remove(this.localMCPPath);
        }

        // Ensure parent directory exists
        await fs.ensureDir(path.dirname(this.localMCPPath));

        // Move new files to MCP directory with platform-aware copy
        await fs.copy(tempExtractDir, this.localMCPPath, {
          preserveTimestamps: true,
          dereference: false
        });

        // Set executable permissions on Unix-like systems
        if (!this.isWindows) {
          const pythonFiles = ['mcp_server_protected.py', 'mcp_server_stdio.py'];
          for (const file of pythonFiles) {
            const filePath = path.join(this.localMCPPath, file);
            if (await fs.pathExists(filePath)) {
              await this.setExecutablePermissions(filePath);
            }
          }
        }

        console.log('✅ MCP server update installed successfully');
        this.emit('install-complete', { version });
        
        return true;
      } finally {
        // Clean up temp directory
        if (await fs.pathExists(tempExtractDir)) {
          await fs.remove(tempExtractDir);
        }
      }
    } catch (error) {
      console.error('Failed to install update:', error.message);
      this.emit('install-error', { error: error.message });
      throw new Error(`Installation failed: ${error.message}`);
    }
  }

  /**
   * Perform complete update process
   */
  async performUpdate() {
    if (this.isUpdating) {
      throw new Error('Update already in progress');
    }

    this.isUpdating = true;
    
    try {
      this.emit('update-start');

      // 1. Check for updates
      const updateCheck = await this.checkForUpdates();
      if (!updateCheck.hasUpdate) {
        this.emit('update-complete', { message: 'No updates available' });
        return { success: true, message: 'No updates available' };
      }

      const { latestVersion } = updateCheck;

      // 2. Create backup
      await this.createBackup();

      // 3. Download update
      const updateData = await this.downloadUpdate(latestVersion);

      // 4. Install update
      await this.installUpdate(updateData, latestVersion);

      // 5. Verify installation
      const newVersion = await this.getCurrentVersion();
      if (newVersion !== latestVersion) {
        throw new Error('Version verification failed after update');
      }

      this.emit('update-complete', { 
        version: latestVersion,
        message: `Successfully updated to v${latestVersion}` 
      });

      return { 
        success: true, 
        version: latestVersion,
        message: `Successfully updated to v${latestVersion}` 
      };

    } catch (error) {
      console.error('Update failed:', error.message);
      
      // Attempt to restore from backup
      try {
        await this.restoreFromBackup();
        this.emit('update-error', { 
          error: error.message, 
          restored: true,
          message: 'Update failed but successfully restored from backup' 
        });
      } catch (restoreError) {
        console.error('Failed to restore from backup:', restoreError.message);
        this.emit('update-error', { 
          error: error.message, 
          restored: false,
          message: 'Update failed and backup restore also failed' 
        });
      }

      throw error;
    } finally {
      this.isUpdating = false;
    }
  }

  /**
   * Clean up old backups and temp files
   */
  async cleanup() {
    try {
      const cleanupPaths = [
        this.backupPath, 
        this.tempDir,
        path.join(__dirname, '..', 'temp-update') // Legacy temp directory
      ];
      
      for (const cleanupPath of cleanupPaths) {
        if (await fs.pathExists(cleanupPath)) {
          await fs.remove(cleanupPath);
          console.log(`✅ Cleaned up: ${cleanupPath}`);
        }
      }
      
      console.log('✅ Cleanup completed');
    } catch (error) {
      console.warn('Cleanup warning:', error.message);
    }
  }

  /**
   * Get platform information for debugging
   */
  getPlatformInfo() {
    return {
      platform: this.platform,
      isWindows: this.isWindows,
      isMacOS: this.isMacOS,
      isLinux: this.isLinux,
      arch: process.arch,
      nodeVersion: process.version,
      paths: {
        localMCP: this.localMCPPath,
        backup: this.backupPath,
        temp: this.tempDir,
        version: this.versionFile
      }
    };
  }
}

module.exports = { MCPUpdater }; 