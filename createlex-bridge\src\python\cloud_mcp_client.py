#!/usr/bin/env python3
"""
Cloud MCP Client
This acts as a bridge between <PERSON> (stdio) and the cloud MCP server (HTTP)
"""

import sys
import json
import asyncio
import aiohttp
import logging
from typing import Any, Dict

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Cloud MCP server URL - update this to your deployed server
CLOUD_MCP_URL = "https://unreal-mcp-cloud-server-bg2zd.ondigitalocean.app"

class CloudMCPClient:
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.session = None
        
    async def start(self):
        """Start the HTTP session"""
        self.session = aiohttp.ClientSession()
        
    async def stop(self):
        """Stop the HTTP session"""
        if self.session:
            await self.session.close()
            
    async def send_request(self, method: str, data: dict) -> dict:
        """Send request to cloud MCP server"""
        if not self.session:
            raise RuntimeError("Client not started")
            
        url = f"{self.base_url}/mcp/{method}"
        
        try:
            async with self.session.post(url, json=data) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    return {
                        "jsonrpc": "2.0",
                        "id": data.get("id"),
                        "error": {
                            "code": response.status,
                            "message": f"HTTP {response.status}: {error_text}"
                        }
                    }
        except Exception as e:
            return {
                "jsonrpc": "2.0",
                "id": data.get("id"),
                "error": {
                    "code": -1,
                    "message": str(e)
                }
            }

async def handle_stdin():
    """Handle stdin messages from Claude Desktop"""
    client = CloudMCPClient(CLOUD_MCP_URL)
    await client.start()
    
    try:
        while True:
            try:
                # Read line from stdin
                line = await asyncio.get_event_loop().run_in_executor(None, sys.stdin.readline)
                if not line:
                    break
                    
                line = line.strip()
                if not line:
                    continue
                    
                # Parse JSON-RPC request
                try:
                    request = json.loads(line)
                except json.JSONDecodeError as e:
                    logger.error(f"Invalid JSON: {e}")
                    continue
                
                # Extract method from request
                method = request.get("method", "")
                
                # Handle different MCP methods
                if method == "initialize":
                    response = await client.send_request("initialize", request)
                elif method == "tools/list":
                    response = await client.send_request("tools/list", request)
                elif method == "tools/call":
                    response = await client.send_request("tools/call", request)
                elif method == "resources/list":
                    response = await client.send_request("resources/list", request)
                elif method == "prompts/list":
                    response = await client.send_request("prompts/list", request)
                else:
                    # Unknown method
                    response = {
                        "jsonrpc": "2.0",
                        "id": request.get("id"),
                        "error": {
                            "code": -32601,
                            "message": f"Method not found: {method}"
                        }
                    }
                
                # Send response to stdout
                print(json.dumps(response), flush=True)
                
            except Exception as e:
                logger.error(f"Error handling request: {e}")
                error_response = {
                    "jsonrpc": "2.0",
                    "id": None,
                    "error": {
                        "code": -1,
                        "message": str(e)
                    }
                }
                print(json.dumps(error_response), flush=True)
                
    finally:
        await client.stop()

if __name__ == "__main__":
    # Print startup message to stderr (won't interfere with stdio protocol)
    print("🌐 Starting Cloud MCP Client...", file=sys.stderr)
    print(f"📡 Connecting to: {CLOUD_MCP_URL}", file=sys.stderr)
    
    # Run the async handler
    try:
        asyncio.run(handle_stdin())
    except KeyboardInterrupt:
        print("🔌 Cloud MCP Client stopped", file=sys.stderr)
    except Exception as e:
        print(f"❌ Error: {e}", file=sys.stderr)
        sys.exit(1) 