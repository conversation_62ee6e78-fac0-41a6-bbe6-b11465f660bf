import unreal
import json
from utils import logging as log

class AssetRegistryBridge:
    """
    Python bridge to the C++ AssetRegistryBlueprintAnalyzer
    Provides lightweight and efficient Blueprint analysis using Unreal's Asset Registry API
    """
    
    @staticmethod
    def get_current_level_script_blueprint():
        """
        Get the current Level Script Blueprint using the C++ Asset Registry API
        Much more efficient than the complex detection methods in blueprint_context_handler.py
        """
        try:
            log.log_info("[Asset Registry] Getting current Level Script Blueprint via C++ API...")
            
            # Call the C++ function directly
            analyzer_class = unreal.AssetRegistryBlueprintAnalyzer
            
            if not analyzer_class:
                log.log_error("[Asset Registry] AssetRegistryBlueprintAnalyzer class not found")
                return {
                    "success": False,
                    "error": "C++ AssetRegistryBlueprintAnalyzer not available",
                    "fallback": True
                }
            
            # Call the C++ method
            result_json = analyzer_class.get_current_level_script_blueprint()
            
            if not result_json:
                log.log_error("[Asset Registry] C++ function returned empty result")
                return {
                    "success": False,
                    "error": "C++ function returned empty result",
                    "fallback": True
                }
            
            # Parse the JSON result
            try:
                result = json.loads(result_json)
                blueprint_name = result.get('blueprintName', 'Unknown')
                log.log_info(f"[Asset Registry] ✅ Successfully retrieved Level Script Blueprint: {blueprint_name}")
                
                # Log Blueprint details
                blueprint_info = result.get('blueprintInfo', {})
                if blueprint_info:
                    log.log_info(f"[Asset Registry] 📊 Blueprint Details:")
                    log.log_info(f"[Asset Registry]   Type: {blueprint_info.get('blueprintType', 'Unknown')}")
                    log.log_info(f"[Asset Registry]   Parent Class: {blueprint_info.get('parentClass', 'None')}")
                
                # Log variables
                variables = result.get('variables', [])
                if variables:
                    log.log_info(f"[Asset Registry] 📝 Variables ({len(variables)}):")
                    for var in variables[:5]:  # Show first 5 variables
                        var_name = var.get('name', 'Unknown')
                        var_type = var.get('type', 'Unknown')
                        log.log_info(f"[Asset Registry]   • {var_name} ({var_type})")
                    if len(variables) > 5:
                        log.log_info(f"[Asset Registry]   ... and {len(variables) - 5} more variables")
                
                # Log functions
                functions = result.get('functions', [])
                if functions:
                    log.log_info(f"[Asset Registry] ⚙️ Functions ({len(functions)}):")
                    for func in functions[:5]:  # Show first 5 functions
                        func_name = func.get('name', 'Unknown')
                        node_count = func.get('nodeCount', 0)
                        log.log_info(f"[Asset Registry]   • {func_name} ({node_count} nodes)")
                    if len(functions) > 5:
                        log.log_info(f"[Asset Registry]   ... and {len(functions) - 5} more functions")
                
                # Log statistics
                stats = result.get('statistics', {})
                if stats:
                    log.log_info(f"[Asset Registry] 📈 Statistics:")
                    log.log_info(f"[Asset Registry]   Total Nodes: {stats.get('totalNodes', 0)}")
                    log.log_info(f"[Asset Registry]   Variables: {stats.get('totalVariables', 0)}")
                    log.log_info(f"[Asset Registry]   Functions: {stats.get('totalFunctions', 0)}")
                    log.log_info(f"[Asset Registry]   Event Graphs: {stats.get('totalEventGraphs', 0)}")
                
                # Add additional context for compatibility
                result["detection_method"] = "cpp_asset_registry"
                result["lightweight"] = True
                result["api_version"] = "ue_native"
                
                return result
                
            except json.JSONDecodeError as e:
                log.log_error(f"[Asset Registry] Failed to parse JSON result: {e}")
                return {
                    "success": False,
                    "error": f"JSON parsing failed: {e}",
                    "raw_result": result_json
                }
                
        except Exception as e:
            log.log_error(f"[Asset Registry] Error calling C++ Asset Registry API: {e}")
            return {
                "success": False,
                "error": f"C++ API call failed: {e}",
                "fallback": True
            }
    
    @staticmethod
    def get_blueprint_asset_info(blueprint_path):
        """
        Get Blueprint information using Asset Registry
        @param blueprint_path: Object path to the Blueprint asset
        """
        try:
            log.log_info(f"[Asset Registry] Getting Blueprint asset info for: {blueprint_path}")
            
            analyzer_class = unreal.AssetRegistryBlueprintAnalyzer
            result_json = analyzer_class.get_blueprint_asset_info(blueprint_path)
            
            if result_json:
                result = json.loads(result_json)
                result["detection_method"] = "cpp_asset_registry"
                return result
            else:
                return {"success": False, "error": "No result from C++ function"}
                
        except Exception as e:
            log.log_error(f"[Asset Registry] Error getting Blueprint asset info: {e}")
            return {"success": False, "error": str(e)}
    
    @staticmethod
    def get_all_level_script_blueprints():
        """
        Get all Level Script Blueprints in the project using Asset Registry
        """
        try:
            log.log_info("[Asset Registry] Getting all Level Script Blueprints...")
            
            analyzer_class = unreal.AssetRegistryBlueprintAnalyzer
            result_json = analyzer_class.get_all_level_script_blueprints()
            
            if result_json:
                result = json.loads(result_json)
                log.log_info(f"[Asset Registry] Found {result.get('count', 0)} Level Script Blueprints")
                return result
            else:
                return {"success": False, "error": "No result from C++ function"}
                
        except Exception as e:
            log.log_error(f"[Asset Registry] Error getting all Level Script Blueprints: {e}")
            return {"success": False, "error": str(e)}
    
    @staticmethod
    def get_blueprint_event_graph(blueprint_path):
        """
        Get detailed event graph information from a Blueprint
        @param blueprint_path: Object path to the Blueprint asset
        """
        try:
            log.log_info(f"[Asset Registry] Getting event graph for: {blueprint_path}")
            
            analyzer_class = unreal.AssetRegistryBlueprintAnalyzer
            result_json = analyzer_class.get_blueprint_event_graph(blueprint_path)
            
            if result_json:
                result = json.loads(result_json)
                node_count = result.get('eventGraphNodes', 0)
                log.log_info(f"[Asset Registry] Found {node_count} event graph nodes")
                
                # Log detailed node information
                nodes = result.get('nodes', [])
                if nodes:
                    log.log_info(f"[Asset Registry] 📋 Detailed Node Analysis:")
                    for i, node in enumerate(nodes):
                        node_name = node.get('name', 'Unknown')
                        node_type = node.get('type', 'unknown')
                        node_title = node.get('title', 'No Title')
                        
                        # Log specific details based on node type
                        if node_type == 'event':
                            event_name = node.get('eventName', 'Unknown Event')
                            log.log_info(f"[Asset Registry]   Node {i+1}: EVENT - {event_name} ({node_title})")
                        elif node_type == 'function_call':
                            func_name = node.get('functionName', 'Unknown Function')
                            func_class = node.get('functionClass', 'Unknown Class')
                            log.log_info(f"[Asset Registry]   Node {i+1}: FUNCTION - {func_name} from {func_class}")
                        elif node_type in ['variable_get', 'variable_set']:
                            var_name = node.get('variableName', 'Unknown Variable')
                            log.log_info(f"[Asset Registry]   Node {i+1}: VARIABLE {node_type.upper()} - {var_name}")
                        else:
                            log.log_info(f"[Asset Registry]   Node {i+1}: {node_type.upper()} - {node_title}")
                        
                        # Log pin information
                        pins = node.get('pins', [])
                        if pins:
                            input_pins = [p for p in pins if p.get('direction') == 'input']
                            output_pins = [p for p in pins if p.get('direction') == 'output']
                            log.log_info(f"[Asset Registry]     📌 Pins: {len(input_pins)} inputs, {len(output_pins)} outputs")
                
                result["detection_method"] = "cpp_asset_registry"
                return result
            else:
                return {"success": False, "error": "No result from C++ function"}
                
        except Exception as e:
            log.log_error(f"[Asset Registry] Error getting event graph: {e}")
            return {"success": False, "error": str(e)}
    
    @staticmethod
    def test_cpp_api_availability():
        """
        Test if the C++ Asset Registry API is available and working
        """
        try:
            log.log_info("[Asset Registry] Testing C++ API availability...")
            
            # Check if the class is available
            analyzer_class = unreal.AssetRegistryBlueprintAnalyzer
            
            if analyzer_class:
                log.log_info("[Asset Registry] ✅ C++ AssetRegistryBlueprintAnalyzer class is available")
                
                # Test a simple function call
                try:
                    result_json = analyzer_class.get_all_level_script_blueprints()
                    if result_json:
                        result = json.loads(result_json)
                        log.log_info(f"[Asset Registry] ✅ C++ API test successful - found {result.get('count', 0)} Level Script Blueprints")
                        return {
                            "available": True,
                            "working": True,
                            "test_result": result
                        }
                    else:
                        log.log_warning("[Asset Registry] ⚠️ C++ API available but returned empty result")
                        return {
                            "available": True,
                            "working": False,
                            "error": "Empty result from C++ function"
                        }
                except Exception as e:
                    log.log_error(f"[Asset Registry] ❌ C++ API available but not working: {e}")
                    return {
                        "available": True,
                        "working": False,
                        "error": str(e)
                    }
            else:
                log.log_error("[Asset Registry] ❌ C++ AssetRegistryBlueprintAnalyzer class not found")
                return {
                    "available": False,
                    "working": False,
                    "error": "C++ class not found"
                }
                
        except Exception as e:
            log.log_error(f"[Asset Registry] ❌ Error testing C++ API: {e}")
            return {
                "available": False,
                "working": False,
                "error": str(e)
            }

def get_blueprint_context_via_asset_registry():
    """
    Main function to get Blueprint context using the efficient Asset Registry API
    This replaces the complex detection logic in blueprint_context_handler.py
    """
    log.log_info("[Asset Registry] 🚀 Getting Blueprint context via Asset Registry API...")
    
    # First test if the C++ API is available
    api_test = AssetRegistryBridge.test_cpp_api_availability()
    
    if not api_test.get("available", False):
        log.log_warning("[Asset Registry] C++ API not available, falling back to Python detection")
        return {
            "success": False,
            "error": "C++ Asset Registry API not available",
            "fallback_required": True,
            "api_test": api_test
        }
    
    # Get the current Level Script Blueprint
    level_blueprint = AssetRegistryBridge.get_current_level_script_blueprint()
    
    if level_blueprint.get("success", False):
        log.log_info("[Asset Registry] ✅ Successfully retrieved Level Script Blueprint via Asset Registry")
        
        # Enhance with additional context
        enhanced_result = {
            "success": True,
            "method": "asset_registry_cpp",
            "lightweight": True,
            "performance": "high",
            "level_blueprint": level_blueprint,
            "api_test": api_test
        }
        
        # If we have a blueprint path, get detailed event graph info
        blueprint_path = level_blueprint.get("blueprintPath")
        if blueprint_path:
            event_graph = AssetRegistryBridge.get_blueprint_event_graph(blueprint_path)
            if event_graph.get("success", False):
                enhanced_result["event_graph"] = event_graph
                log.log_info(f"[Asset Registry] ✅ Retrieved event graph with {event_graph.get('eventGraphNodes', 0)} nodes")
        
        return enhanced_result
    else:
        log.log_warning("[Asset Registry] Failed to get Level Script Blueprint, may need fallback")
        return {
            "success": False,
            "error": level_blueprint.get("error", "Unknown error"),
            "fallback_required": True,
            "level_blueprint_result": level_blueprint,
            "api_test": api_test
        } 