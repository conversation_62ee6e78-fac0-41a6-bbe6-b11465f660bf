# Token Usage Tracking System

This document describes the token usage tracking system implemented for the CreateLex AI platform.

## Overview

The token usage tracking system monitors and manages API token consumption for all users of the platform. It provides:

1. **Usage Tracking**: Records token usage per user, model, and request type
2. **Usage Limits**: Enforces daily and monthly token limits based on subscription tier
3. **Progressive Throttling**: Applies increasing delays to requests as users approach their limits
4. **Admin Dashboard**: Provides visibility into token usage, costs, and profitability
5. **User Management**: Allows admins to apply restrictions to high-usage users

## Components

### 1. Token Usage Service (`tokenUsageService.js`)

The core service that handles:
- Tracking token usage in the database
- Checking usage limits
- Estimating token counts
- Optimizing prompts to reduce token usage
- Generating usage statistics for the admin dashboard

### 2. Token Usage Middleware (`tokenUsageMiddleware.js`)

Middleware functions that:
- Check if a user has exceeded their usage limits
- Apply progressive throttling based on usage patterns
- Optimize prompts to reduce token consumption

### 3. Admin API Routes (`routes/admin/tokenUsage.js`)

API endpoints for the admin dashboard:
- Get token usage statistics
- Get high-usage users
- Get detailed usage for a specific user
- Apply usage restrictions to users

### 4. Admin Dashboard UI

Frontend components for the admin dashboard:
- Token usage overview
- Profit analysis
- High-usage users table
- User detail page with restriction controls

## Usage Limits

The system enforces the following limits based on subscription tier:

| Tier | Daily Limit | Monthly Limit | Max Request Length |
|------|-------------|---------------|-------------------|
| Basic | 100,000 tokens | 2,000,000 tokens | 4,000 tokens |
| Pro | 200,000 tokens | 4,000,000 tokens | 8,000 tokens |

## Progressive Throttling

As users approach their monthly limits, the system applies progressive throttling:

| Usage Percentage | Throttling Delay | Message |
|------------------|------------------|---------|
| > 50% | 1 second | None |
| > 75% | 2 seconds | "You're using your subscription at a high rate. Slight delays may occur." |
| > 90% | 5 seconds | "You're approaching your monthly limit. Responses may be delayed." |

## Usage Restrictions

Admins can apply the following restrictions to users:

1. **None**: No restrictions
2. **Throttled**: Applies a delay to all requests
3. **Limited**: Reduces the user's token limits
4. **Blocked**: Prevents the user from using the API

## Database Schema

The token usage tracking system uses the following database tables:

### `token_usage` Table

Stores individual token usage records:

```sql
CREATE TABLE IF NOT EXISTS token_usage (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  model_id TEXT NOT NULL,
  prompt_tokens INTEGER NOT NULL,
  completion_tokens INTEGER NOT NULL,
  total_tokens INTEGER NOT NULL,
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  request_type TEXT,
  subscription_plan TEXT
);
```

### Views

The system also creates the following views for easier reporting:

- `daily_token_usage`: Summarizes token usage by user and day
- `monthly_token_usage`: Summarizes token usage by user and month

## Setup Instructions

1. Run the database migrations:
   ```
   node scripts/run-migrations.js
   ```

2. Restart the backend server:
   ```
   npm run start
   ```

3. Access the admin dashboard at:
   ```
   http://localhost:3000/admin
   ```

## Admin Access

Admin access is restricted to users with the following email addresses:
- <EMAIL>
- <EMAIL>

## Cost Calculation

The system uses the following cost rates for calculating API costs:

- Input tokens: $0.008 per 1,000 tokens
- Output tokens: $0.024 per 1,000 tokens

These rates are based on Claude 3 Opus pricing as of 2023 and can be updated in the `tokenUsageService.js` file.
