'use client';

import { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Tabs,
  Ta<PERSON>Content,
  Ta<PERSON>List,
  TabsTrigger
} from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  ArrowLeft,
  Calendar,
  CreditCard,
  Edit,
  Mail,
  RefreshCw,
  Shield,
  Trash,
  User
} from 'lucide-react';
import { fetchWithAuth, getApiUrl } from '@/lib/auth-utils';
import { format } from 'date-fns';
import UserTokenUsageChart from '@/components/admin/UserTokenUsageChart';
import UserSubscriptionCard from '@/components/admin/UserSubscriptionCard';
import UserTokenBalanceCard from '@/components/admin/UserTokenBalanceCard';
import UserInvoicesTable from '@/components/admin/UserInvoicesTable';
import { toast } from 'sonner';

interface UserDetails {
  id: string;
  email: string;
  name: string;
  created_at: string;
  subscription_status: string;
  subscription_id: string | null;
  stripe_customer_id: string | null;
  is_admin: boolean;
  last_login: string | null;
  token_balance: number;
  daily_token_usage: number;
  monthly_token_usage: number;
  daily_token_limit: number;
  monthly_token_limit: number;
  subscription_plan: string;
}

export default function UserDetailPage() {
  const params = useParams();
  const router = useRouter();
  const userId = params?.userId as string || '';

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [user, setUser] = useState<UserDetails | null>(null);
  const [editMode, setEditMode] = useState(false);
  const [editedUser, setEditedUser] = useState<Partial<UserDetails>>({});
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [confirmSubscriptionChange, setConfirmSubscriptionChange] = useState(false);
  const [newSubscriptionStatus, setNewSubscriptionStatus] = useState<string>('');
  const [savingChanges, setSavingChanges] = useState(false);

  useEffect(() => {
    fetchUserDetails();
  }, [userId]);

  const fetchUserDetails = async () => {
    try {
      setLoading(true);

      const apiUrl = getApiUrl();
      const response = await fetchWithAuth(`${apiUrl}/api/admin/users/${userId}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch user details: ${response.status}`);
      }

      const data = await response.json();
      setUser(data);
      setEditedUser({});
    } catch (error) {
      console.error('Error fetching user details:', error);
      setError('Failed to load user details');
    } finally {
      setLoading(false);
    }
  };

  const handleEditToggle = () => {
    if (editMode) {
      // Cancel edit mode
      setEditedUser({});
    }
    setEditMode(!editMode);
  };

  const handleInputChange = (field: string, value: any) => {
    setEditedUser({
      ...editedUser,
      [field]: value
    });
  };

  const handleSaveChanges = async () => {
    try {
      setSavingChanges(true);

      const apiUrl = getApiUrl();
      const response = await fetchWithAuth(`${apiUrl}/api/admin/users/${userId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(editedUser)
      });

      if (!response.ok) {
        throw new Error(`Failed to update user: ${response.status}`);
      }

      const updatedUser = await response.json();
      setUser(updatedUser);
      setEditMode(false);
      setEditedUser({});

      toast.success('User updated successfully');
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error('Failed to update user');
    } finally {
      setSavingChanges(false);
    }
  };

  const handleDeleteUser = async () => {
    try {
      const apiUrl = getApiUrl();
      const response = await fetchWithAuth(`${apiUrl}/api/admin/users/${userId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error(`Failed to delete user: ${response.status}`);
      }

      toast.success('User deleted successfully');
      router.push('/admin/users');
    } catch (error) {
      console.error('Error deleting user:', error);
      toast.error('Failed to delete user');
    } finally {
      setConfirmDelete(false);
    }
  };

  const handleSubscriptionChange = async () => {
    try {
      const apiUrl = getApiUrl();
      const response = await fetchWithAuth(`${apiUrl}/api/admin/users/${userId}/subscription`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status: newSubscriptionStatus
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to update subscription: ${response.status}`);
      }

      const updatedUser = await response.json();
      setUser(updatedUser);

      toast.success(`Subscription status updated to ${newSubscriptionStatus}`);
    } catch (error) {
      console.error('Error updating subscription:', error);
      toast.error('Failed to update subscription');
    } finally {
      setConfirmSubscriptionChange(false);
    }
  };

  const handleResetTokenUsage = async () => {
    try {
      const apiUrl = getApiUrl();
      const response = await fetchWithAuth(`${apiUrl}/api/admin/users/${userId}/reset-token-usage`, {
        method: 'POST'
      });

      if (!response.ok) {
        throw new Error(`Failed to reset token usage: ${response.status}`);
      }

      toast.success('Token usage reset successfully');
      fetchUserDetails();
    } catch (error) {
      console.error('Error resetting token usage:', error);
      toast.error('Failed to reset token usage');
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" onClick={() => router.push('/admin/users')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <Skeleton className="h-8 w-64" />
        </div>

        <div className="grid gap-4 md:grid-cols-3">
          <Skeleton className="h-40" />
          <Skeleton className="h-40" />
          <Skeleton className="h-40" />
        </div>

        <Skeleton className="h-96" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4">
        <Button variant="outline" size="icon" onClick={() => router.push('/admin/users')}>
          <ArrowLeft className="h-4 w-4" />
        </Button>

        <Card>
          <CardContent className="p-6">
            <div className="text-red-500">{error}</div>
            <Button className="mt-4" onClick={fetchUserDetails}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" onClick={() => router.push('/admin/users')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">{user?.name || 'User Details'}</h1>
          {user?.is_admin && (
            <Badge className="ml-2">Admin</Badge>
          )}
        </div>

        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleEditToggle}>
            <Edit className="h-4 w-4 mr-2" />
            {editMode ? 'Cancel' : 'Edit'}
          </Button>

          <Dialog open={confirmDelete} onOpenChange={setConfirmDelete}>
            <DialogTrigger asChild>
              <Button variant="destructive">
                <Trash className="h-4 w-4 mr-2" />
                Delete
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Delete User</DialogTitle>
                <DialogDescription>
                  Are you sure you want to delete this user? This action cannot be undone.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button variant="outline" onClick={() => setConfirmDelete(false)}>
                  Cancel
                </Button>
                <Button variant="destructive" onClick={handleDeleteUser}>
                  Delete
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-3">
        <UserSubscriptionCard
          user={user}
          onSubscriptionChange={(status) => {
            setNewSubscriptionStatus(status);
            setConfirmSubscriptionChange(true);
          }}
        />

        <UserTokenBalanceCard
          user={user}
          onResetTokenUsage={handleResetTokenUsage}
        />

        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">User Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center">
                <Mail className="h-4 w-4 mr-2 text-gray-500" />
                <span className="text-sm">{user?.email}</span>
              </div>
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                <span className="text-sm">Joined {user?.created_at ? format(new Date(user.created_at), 'MMM dd, yyyy') : 'Unknown'}</span>
              </div>
              <div className="flex items-center">
                <User className="h-4 w-4 mr-2 text-gray-500" />
                <span className="text-sm">Last login {user?.last_login ? format(new Date(user.last_login), 'MMM dd, yyyy HH:mm') : 'Never'}</span>
              </div>
              {user?.stripe_customer_id && (
                <div className="flex items-center">
                  <CreditCard className="h-4 w-4 mr-2 text-gray-500" />
                  <span className="text-sm">Stripe ID: {user.stripe_customer_id}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {editMode ? (
        <Card>
          <CardHeader>
            <CardTitle>Edit User</CardTitle>
            <CardDescription>
              Update user information and permissions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    defaultValue={user?.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    defaultValue={user?.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                  />
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="is_admin">Admin Status</Label>
                  <Select
                    defaultValue={user?.is_admin ? 'true' : 'false'}
                    onValueChange={(value) => handleInputChange('is_admin', value === 'true')}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select admin status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="true">Admin</SelectItem>
                      <SelectItem value="false">Regular User</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="token_balance">Token Balance</Label>
                  <Input
                    id="token_balance"
                    type="number"
                    defaultValue={user?.token_balance}
                    onChange={(e) => handleInputChange('token_balance', parseInt(e.target.value))}
                  />
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-end space-x-2">
            <Button variant="outline" onClick={handleEditToggle}>
              Cancel
            </Button>
            <Button onClick={handleSaveChanges} disabled={savingChanges}>
              {savingChanges ? 'Saving...' : 'Save Changes'}
            </Button>
          </CardFooter>
        </Card>
      ) : (
        <Tabs defaultValue="usage">
          <TabsList>
            <TabsTrigger value="usage">Token Usage</TabsTrigger>
            <TabsTrigger value="invoices">Invoices</TabsTrigger>
            <TabsTrigger value="activity">Activity Log</TabsTrigger>
          </TabsList>

          <TabsContent value="usage" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Token Usage</CardTitle>
                <CardDescription>
                  Token usage over time for this user
                </CardDescription>
              </CardHeader>
              <CardContent>
                <UserTokenUsageChart userId={userId} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="invoices" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Invoices</CardTitle>
                <CardDescription>
                  Billing history for this user
                </CardDescription>
              </CardHeader>
              <CardContent>
                <UserInvoicesTable userId={userId} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="activity" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Activity Log</CardTitle>
                <CardDescription>
                  Recent activity for this user
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-center py-8 text-gray-500">
                  Activity log will be available in a future update.
                </p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}

      <Dialog open={confirmSubscriptionChange} onOpenChange={setConfirmSubscriptionChange}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Change Subscription Status</DialogTitle>
            <DialogDescription>
              Are you sure you want to change this user's subscription status to {newSubscriptionStatus}?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setConfirmSubscriptionChange(false)}>
              Cancel
            </Button>
            <Button onClick={handleSubscriptionChange}>
              Confirm
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
