/** @type {import('next').NextConfig} */
const nextConfig = {
  // Basic configuration
  reactStrictMode: true,
  swcMinify: true,

  // Configure image domains
  images: {
    domains: ['localhost', 'lh3.googleusercontent.com', 'avatars.githubusercontent.com', 'via.placeholder.com'],
    unoptimized: process.env.NODE_ENV === 'development',
  },

  // Experimental features
  experimental: {
    serverComponentsExternalPackages: ['sharp'],
    // Disable worker threads to prevent static generation
    workerThreads: false,
    cpus: 1
  },

  // Output standalone build for easier deployment
  output: 'standalone',

  // Disable static exports completely
  distDir: process.env.BUILD_DIR || '.next',

  // Disable static generation
  generateEtags: false,

  // Skip type checking to speed up build
  typescript: {
    ignoreBuildErrors: true,
  },

  // Disable eslint during build
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Skip static generation during build
  env: {
    NEXT_DISABLE_STATIC_GENERATION: 'true',
    NEXT_SKIP_STATIC_EXPORT: 'true',
    NEXT_DISABLE_STATIC_EXPORT: 'true',
  },

  // Configure API routes to be fully dynamic
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Cache-Control', value: 'no-store, must-revalidate' },
          { key: 'Pragma', value: 'no-cache' },
          { key: 'Expires', value: '0' }
        ],
      },
    ];
  },
};

module.exports = nextConfig;
