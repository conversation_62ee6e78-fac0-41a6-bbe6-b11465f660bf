// Next.js API route support: https://nextjs.org/docs/api-routes/introduction

export default async function handler(req, res) {
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }
    
    // Get the user ID from the request body
    const { userId, promptTokens, completionTokens, modelId } = req.body;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    
    console.log('Adding test data for user:', userId);
    
    // Forward the request to the backend
    const response = await fetch('http://localhost:5001/api/tokens/add-test-data', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        promptTokens,
        completionTokens,
        modelId
      }),
    });

    if (!response.ok) {
      throw new Error(`Backend API error: ${response.status}`);
    }

    const data = await response.json();
    console.log('Test data added:', data);

    // Return the data to the frontend
    res.status(200).json(data);
  } catch (error) {
    console.error('Error in add-test-data API route:', error);
    res.status(500).json({ error: 'Failed to add test data' });
  }
}
