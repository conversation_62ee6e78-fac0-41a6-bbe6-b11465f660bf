const { spawn } = require('child_process');
const net = require('net');
const path = require('path');
const { EventEmitter } = require('events');
const fs = require('fs');
const { AuthHandler } = require('../auth/auth-handler');

class MCPBridgeServer extends EventEmitter {
  constructor(options = {}) {
    super();
    this.port = options.port || 9877;
    this.pythonProcess = null;
    this.tcpServer = null;
    this.clients = new Set();
    this.isReady = false;
    this.authHandler = new AuthHandler();
    this.subscriptionCheckInterval = null;
    this.subscriptionCheckFrequency = process.env.NODE_ENV === 'development' ? 30 * 1000 : 5 * 60 * 1000; // 30 seconds in dev, 5 minutes in prod
    
    // Device heartbeat configuration
    this.deviceHeartbeatInterval = null;
    this.deviceHeartbeatFrequency = 10 * 60 * 1000; // Send heartbeat every 10 minutes
    this.deviceId = null;
  }

  async start() {
    if (this.pythonProcess) {
      console.log('MCP bridge server already running');
      return;
    }

    // Start the Python MCP server
    await this.startPythonServer();
    
    // Start TCP server for other IDEs
    await this.startTCPServer();
    
    // Start periodic subscription validation
    this.startSubscriptionMonitoring();
    
    // Start device heartbeat
    this.startDeviceHeartbeat();
    
    console.log(`MCP Bridge Server running on port ${this.port}`);
  }

  async startPythonServer() {
    const isDev = process.env.NODE_ENV !== 'production' || process.env.DEV_MODE === 'true';
    const isDebugMode = process.env.DEBUG_MODE === 'true';
    
    // Check if we should use the compiled executable (production mode)
    if (!isDev) {
      const { app } = require('electron');
      const resourcesPath = process.resourcesPath || app.getAppPath();
      
      // Determine executable name based on platform
      const os = require('os');
      const platform = os.platform();
      let executableName = 'mcp_server';
      
      if (platform === 'win32') {
        executableName = 'mcp_server.exe';
      } else if (platform === 'darwin') {
        executableName = 'mcp_server_mac';
      } else if (platform === 'linux') {
        executableName = 'mcp_server_linux';
      }
      
      const execPath = path.join(resourcesPath, 'mcp', executableName);
      
      if (fs.existsSync(execPath)) {
        console.log(`Starting protected MCP executable for ${platform}:`, execPath);
        return this.startMCPExecutable(execPath);
      }
    }
    
    // Development mode or fallback to Python
    let scriptPath;
    
    // NEW: Try API-driven configuration for development mode
    if (isDev) {
      try {
        console.log('🌐 Attempting to get MCP server configuration from API...');
        const mcpConfig = await this.getMCPServerConfig();
        
        if (mcpConfig && mcpConfig.serverType === 'url') {
          console.log('📥 Downloading MCP server from API:', mcpConfig.serverUrl);
          scriptPath = await this.downloadMCPServer(mcpConfig);
          console.log('✅ API-driven MCP server ready');
          return this.startPythonProcess(scriptPath, isDev, isDebugMode, mcpConfig);
        }
      } catch (error) {
        console.log('⚠️ API-driven configuration failed, falling back to local files:', error.message);
      }
      
      // Development mode - use dev script that bypasses subscription
      const devScriptPath = path.join(__dirname, '..', 'python', 'mcp_server_protected_dev.py');
      if (fs.existsSync(devScriptPath)) {
        scriptPath = devScriptPath;
        console.log('Using development MCP server script (subscription bypassed)');
      } else {
        // Fallback to new or legacy source directory for dev
        const newSourcePath = path.resolve(__dirname, '..', '..', '..', 'mcp-server-createlexgenai', 'mcp_server_protected.py');
        const legacySourcePath = path.resolve(__dirname, '..', '..', '..', 'CreatelexGenAI_with_server', 'server', 'mcp_server_protected.py');
        if (fs.existsSync(newSourcePath)) {
          scriptPath = newSourcePath;
        } else {
          scriptPath = legacySourcePath;
        }
        console.log('Dev script not found, using new or legacy source directory for dev');
      }
    } else {
      // Production mode fallback to Python
      const { app } = require('electron');
      const resourcesPath = process.resourcesPath || app.getAppPath();
      scriptPath = path.join(resourcesPath, 'python', 'mcp_server_protected.py');
      
      // Fallback to bundled path if extraResources not found
      if (!fs.existsSync(scriptPath)) {
        const bundledPath = path.join(__dirname, '..', 'python', 'mcp_server_protected.py');
        if (fs.existsSync(bundledPath)) {
          scriptPath = bundledPath;
        } else {
          // Fallback to original source directory for dev
          const newSourcePath = path.resolve(__dirname, '..', '..', '..', 'mcp-server-createlexgenai', 'mcp_server_protected.py');
          const legacySourcePath = path.resolve(__dirname, '..', '..', '..', 'CreatelexGenAI_with_server', 'server', 'mcp_server_protected.py');
          if (fs.existsSync(newSourcePath)) {
            scriptPath = newSourcePath;
          } else {
            scriptPath = legacySourcePath;
          }
        }
      }
    }

    console.log('Starting Python MCP server:', scriptPath);
    return this.startPythonProcess(scriptPath, isDev, isDebugMode);
  }

  async startMCPExecutable(execPath) {
    // Check if subscription is already validated by the bridge
    let hasValidSubscription = false;
    try {
      const subscriptionStatus = await this.authHandler.getSubscriptionStatus();
      hasValidSubscription = subscriptionStatus?.hasActiveSubscription === true || 
                           subscriptionStatus?.subscriptionStatus === 'active' ||
                           subscriptionStatus?.hasSubscription === true;
    } catch (error) {
      console.log('Could not check subscription status, proceeding with default settings');
    }

    // Get auth token for the executable
    const authToken = process.env.AUTH_TOKEN || '';

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('MCP executable start timeout'));
      }, 30000);

      // Spawn the executable directly
      this.pythonProcess = spawn(execPath, [], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          AUTH_TOKEN: authToken,
          API_BASE_URL: process.env.API_BASE_URL || 'https://api.createlex.com/api',
          BRIDGE_SUBSCRIPTION_VALIDATED: hasValidSubscription ? 'true' : 'false',
        }
      });

      // Handle stdout (MCP protocol data)
      this.pythonProcess.stdout.on('data', (data) => {
        const output = data.toString();
        
        // Check for ready state
        if (output.includes('MCP_READY') || output.includes('Server started') || output.includes('"method"')) {
          if (!this.isReady) {
            clearTimeout(timeout);
            this.isReady = true;
            this.emit('ready');
            resolve();
          }
        }
        
        // Broadcast to all connected clients
        this.clients.forEach(client => {
          if (!client.destroyed) {
            client.write(data);
          }
        });
      });

      this.pythonProcess.stderr.on('data', (data) => {
        const output = data.toString();
        console.error('[MCP Protected Error]', output);
        
        // Check for ready messages in stderr too
        if (output.includes('MCP Server started') || output.includes('Starting MCP server')) {
          if (!this.isReady) {
            clearTimeout(timeout);
            this.isReady = true;
            this.emit('ready');
            resolve();
          }
        }
      });

      this.pythonProcess.on('exit', (code) => {
        clearTimeout(timeout);
        console.log(`MCP executable exited with code: ${code}`);
        this.isReady = false;
        this.pythonProcess = null;
        this.emit('exit', code);
        
        if (code !== 0) {
          reject(new Error(`MCP executable exited with code ${code}`));
        }
      });

      this.pythonProcess.on('error', (err) => {
        clearTimeout(timeout);
        console.error('MCP executable error:', err);
        reject(err);
      });
    });
  }

  async startPythonProcess(scriptPath, isDev, isDebugMode) {
    // Check if subscription is already validated by the bridge
    let hasValidSubscription = false;
    try {
      const subscriptionStatus = await this.authHandler.getSubscriptionStatus();
      hasValidSubscription = subscriptionStatus?.hasActiveSubscription === true || 
                           subscriptionStatus?.subscriptionStatus === 'active' ||
                           subscriptionStatus?.hasSubscription === true;
    } catch (error) {
      console.log('Could not check subscription status, proceeding with default settings');
    }

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Python MCP server start timeout'));
      }, 30000);

      const envVars = {
        ...process.env,
        AUTH_TOKEN: process.env.AUTH_TOKEN || '',
        PYTHONIOENCODING: 'utf-8',
        DEV_MODE: isDev ? 'true' : 'false',
        NODE_ENV: isDev ? 'development' : 'production',
        BRIDGE_SUBSCRIPTION_VALIDATED: hasValidSubscription ? 'true' : 'false',
        DEBUG_MODE: isDebugMode ? 'true' : 'false',
        BYPASS_SUBSCRIPTION: 'true',
      };

      if (isDev || isDebugMode) {
        console.log('[DEBUG] Bridge Server Environment Variables:');
        console.log(`[DEBUG]   isDev: ${isDev}`);
        console.log(`[DEBUG]   isDebugMode: ${isDebugMode}`);
        console.log(`[DEBUG]   hasValidSubscription: ${hasValidSubscription}`);
        console.log(`[DEBUG]   DEV_MODE: ${envVars.DEV_MODE}`);
        console.log(`[DEBUG]   NODE_ENV: ${envVars.NODE_ENV}`);
        console.log(`[DEBUG]   BRIDGE_SUBSCRIPTION_VALIDATED: ${envVars.BRIDGE_SUBSCRIPTION_VALIDATED}`);
        console.log(`[DEBUG]   DEBUG_MODE: ${envVars.DEBUG_MODE}`);
        console.log(`[DEBUG]   Script path: ${scriptPath}`);
        console.log(`[DEBUG]   CREATELEX_BASE_URL: ${process.env.CREATELEX_BASE_URL || 'default'}`);
        console.log(`[DEBUG]   API_BASE_URL: ${process.env.API_BASE_URL || 'default'}`);
      }

      // Determine Python command based on platform
      const os = require('os');
      const platform = os.platform();
      let pythonCmd = 'python';
      
      if (platform === 'darwin' || platform === 'linux') {
        // Try python3.11 first (has fastmcp), then python3, then python
        try {
          require('child_process').execSync('/opt/homebrew/bin/python3.11 --version', { stdio: 'pipe' });
          pythonCmd = '/opt/homebrew/bin/python3.11';
        } catch (e) {
          try {
            require('child_process').execSync('python3 --version', { stdio: 'pipe' });
            pythonCmd = 'python3';
          } catch (e2) {
            // Fall back to python
            pythonCmd = 'python';
          }
        }
      }

      console.log(`Using Python command: ${pythonCmd} (platform: ${platform})`);

      this.pythonProcess = spawn(pythonCmd, [scriptPath], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: envVars
      });

             // Handle Python server ready state
       this.pythonProcess.stdout.on('data', (data) => {
         const output = data.toString();
         
         // Check for ready state (but don't log MCP protocol messages)
         if (output.includes('MCP_READY') || output.includes('Server started') || output.includes('"method"')) {
           if (!this.isReady) {
             clearTimeout(timeout);
             this.isReady = true;
             this.emit('ready');
             resolve();
           }
         }
         
         // Broadcast to all connected clients (this is the actual MCP protocol data)
         this.clients.forEach(client => {
           if (!client.destroyed) {
             client.write(data);
           }
         });
       });

      this.pythonProcess.stderr.on('data', (data) => {
        const output = data.toString();
        console.error('[Python MCP Error]', output);
        
        // Check for ready state in stderr as well (MCP server outputs ready message to stderr)
        if (output.includes('MCP Server started with PID file at:') || output.includes('Starting MCP server')) {
          if (!this.isReady) {
            clearTimeout(timeout);
            this.isReady = true;
            this.emit('ready');
            resolve();
          }
        }
      });

      this.pythonProcess.on('exit', (code) => {
        clearTimeout(timeout);
        console.log(`Python MCP server exited with code: ${code}`);
        this.isReady = false;
        this.pythonProcess = null;
        this.emit('exit', code);
        
        if (code !== 0) {
          reject(new Error(`Python server exited with code ${code}`));
        }
      });

      this.pythonProcess.on('error', (err) => {
        clearTimeout(timeout);
        console.error('Python MCP server error:', err);
        reject(err);
      });
    });
  }

  async startTCPServer() {
    return new Promise((resolve, reject) => {
      this.tcpServer = net.createServer((socket) => {
        console.log('New client connected to MCP bridge');
        this.clients.add(socket);

        // Forward client data to Python server
        socket.on('data', (data) => {
          if (this.pythonProcess && !this.pythonProcess.killed) {
            this.pythonProcess.stdin.write(data);
          }
        });

        socket.on('close', () => {
          console.log('Client disconnected from MCP bridge');
          this.clients.delete(socket);
        });

        socket.on('error', (err) => {
          console.error('Client socket error:', err);
          this.clients.delete(socket);
        });
      });

      this.tcpServer.listen(this.port, 'localhost', () => {
        console.log(`TCP bridge server listening on localhost:${this.port}`);
        resolve();
      });

      this.tcpServer.on('error', (err) => {
        console.error('TCP server error:', err);
        reject(err);
      });
    });
  }

  // Method for Claude Desktop stdio bridge
  createStdioBridge() {
    if (!this.pythonProcess) {
      throw new Error('Python MCP server not running');
    }

    // Pipe stdin to Python server
    process.stdin.pipe(this.pythonProcess.stdin);
    
    // Pipe Python server output to stdout
    this.pythonProcess.stdout.pipe(process.stdout);
    this.pythonProcess.stderr.pipe(process.stderr);

    // Handle process termination
    this.pythonProcess.on('exit', (code) => {
      process.exit(code);
    });

    process.on('SIGINT', () => {
      this.stop();
    });

    process.on('SIGTERM', () => {
      this.stop();
    });
  }

  startSubscriptionMonitoring() {
    console.log(`Starting subscription monitoring... (checking every ${this.subscriptionCheckFrequency / 1000} seconds)`);
    
    // Initial subscription check
    this.performSubscriptionCheck();
    
    // Set up periodic checks
    this.subscriptionCheckInterval = setInterval(() => {
      console.log('⏰ Periodic subscription check...');
      this.performSubscriptionCheck();
    }, this.subscriptionCheckFrequency);
  }

  async performSubscriptionCheck() {
    const result = await this.checkSubscriptionStatus();
    
    if (!result.hasSubscription) {
      console.error('❌ Subscription validation failed:', result.error || 'No active subscription');
      console.log('🛑 Automatically stopping MCP server due to invalid subscription');
      
      // Emit subscription invalid event
      this.emit('subscription-invalid', result);
      
      // Stop the server (unless in development mode)
      if (result.mode !== 'development' && result.mode !== 'development_fallback') {
        this.stop('Subscription expired');
      }
    }
  }

  async checkSubscriptionStatus() {
    const isDev = process.env.NODE_ENV !== 'production' || process.env.DEV_MODE === 'true';
    const bypassSubscription = process.env.BYPASS_SUBSCRIPTION === 'true';
    
    try {
      if (isDev && bypassSubscription) {
        console.log('🔧 Development mode with BYPASS_SUBSCRIPTION=true - bypassing subscription validation');
        return { hasSubscription: true, mode: 'development' };
      }

      console.log('Checking subscription status...');
      const status = await this.authHandler.getSubscriptionStatus();
      
      console.log('MCP Bridge: Received subscription status:', status);
      
      // Check if subscription is valid
      const hasValidSubscription = status.hasActiveSubscription === true || 
                                  status.subscriptionStatus === 'active' ||
                                  status.hasSubscription === true;
      
      if (hasValidSubscription) {
        console.log('✅ Subscription validated successfully');
        return { hasSubscription: true };
      } else {
        console.log('❌ Subscription validation failed:', status.error || 'No active subscription');
        return { hasSubscription: false, error: status.error || 'No active subscription' };
      }
    } catch (error) {
      console.error('❌ Error checking subscription:', error.message);
      
      // In development with bypass, allow fallback
      if (isDev && bypassSubscription) {
        console.log('🔧 Development fallback with BYPASS_SUBSCRIPTION=true - allowing access');
        return { hasSubscription: true, mode: 'development_fallback' };
      }
      
      return { hasSubscription: false, error: error.message };
    }
  }

  stop(reason = 'Manual stop') {
    console.log(`Stopping MCP bridge server... (${reason})`);
    
    // Clear subscription monitoring
    if (this.subscriptionCheckInterval) {
      clearInterval(this.subscriptionCheckInterval);
      this.subscriptionCheckInterval = null;
    }
    
    // Clear device heartbeat
    if (this.deviceHeartbeatInterval) {
      clearInterval(this.deviceHeartbeatInterval);
      this.deviceHeartbeatInterval = null;
    }
    
    // Close all client connections
    this.clients.forEach(client => {
      if (!client.destroyed) {
        client.end();
      }
    });
    this.clients.clear();

    // Close TCP server
    if (this.tcpServer) {
      this.tcpServer.close();
      this.tcpServer = null;
    }

    // Terminate Python process
    if (this.pythonProcess) {
      this.pythonProcess.kill('SIGTERM');
      this.pythonProcess = null;
    }

    this.isReady = false;
    this.emit('stopped', reason);
  }

  async getSubscriptionStatus() {
    const isDev = process.env.NODE_ENV !== 'production' || process.env.DEV_MODE === 'true';
    const bypassSubscription = process.env.BYPASS_SUBSCRIPTION === 'true';
    
    if (isDev && bypassSubscription) {
      console.log('🔧 Development mode with BYPASS_SUBSCRIPTION=true - returning valid subscription for IPC call');
      return true;
    }
    
    try {
      console.log('Getting subscription status for IPC...');
      const result = await this.checkSubscriptionStatus();
      
      if (result.hasSubscription) {
        console.log('✅ Subscription is valid for IPC call');
        return true;
      } else {
        console.log('❌ Subscription is invalid for IPC call:', result.error || 'No active subscription');
        return false;
      }
    } catch (error) {
      console.error('❌ Error getting subscription status for IPC:', error.message);
      return false;
    }
  }

  startDeviceHeartbeat() {
    console.log(`Starting device heartbeat... (sending every ${this.deviceHeartbeatFrequency / 1000} seconds)`);
    
    // Get device ID from auth handler
    this.deviceId = this.authHandler.deviceId;
    
    // Send initial heartbeat
    this.sendDeviceHeartbeat();
    
    // Set up periodic heartbeats
    this.deviceHeartbeatInterval = setInterval(() => {
      console.log('💓 Sending device heartbeat...');
      this.sendDeviceHeartbeat();
    }, this.deviceHeartbeatFrequency);
  }

  async sendDeviceHeartbeat() {
    try {
      const token = await this.authHandler.tokenManager.getToken();
      if (!token || !this.deviceId) {
        console.log('Skipping device heartbeat - no token or device ID');
        return;
      }
      
      console.log(`Sending heartbeat for device: ${this.deviceId}`);
      
      // Send heartbeat to backend
      const response = await this.authHandler.makeApiRequest('/device/heartbeat', {
        method: 'POST',
        headers: { 
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        data: {
          deviceId: this.deviceId
        }
      });
      
      console.log('Device heartbeat sent successfully');
    } catch (error) {
      console.error('Error sending device heartbeat:', error.message);
      // Don't stop the bridge if heartbeat fails
    }
  }

  getStatus() {
    return {
      isRunning: !!this.pythonProcess && this.isReady,
      port: this.port,
      clientCount: this.clients.size,
      pythonProcessId: this.pythonProcess?.pid || null
    };
  }

  // Get MCP server configuration from API
  async getMCPServerConfig() {
    const os = require('os');
    const { app } = require('electron');
    
    const deviceId = this.authHandler.deviceId;
    const platform = os.platform();
    const appVersion = app ? app.getVersion() : '1.0.0';
    
    // Get authentication token
    const token = await this.authHandler.tokenManager.getToken();
    if (!token) {
      throw new Error('No authentication token available for MCP config request');
    }
    
    const response = await this.authHandler.makeApiRequest('/mcp/config', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      params: {
        device_id: deviceId,
        platform,
        app_version: appVersion
      }
    });
    
    if (!response || !response.data || !response.data.success) {
      throw new Error('Failed to get MCP configuration from API');
    }
    
    return response.data.config;
  }

  // Download MCP server from URL with caching and fallbacks
  async downloadMCPServer(mcpConfig) {
    const axios = require('axios');
    const crypto = require('crypto');
    const path = require('path');
    const fs = require('fs');
    const os = require('os');
    
    try {
      // Create cache directory
      const cacheDir = path.join(os.tmpdir(), 'createlex-mcp-cache');
      if (!fs.existsSync(cacheDir)) {
        fs.mkdirSync(cacheDir, { recursive: true });
      }
      
      // Generate cache key based on server URL and version
      const cacheKey = crypto.createHash('md5')
        .update(mcpConfig.serverUrl + mcpConfig.version)
        .digest('hex');
      const cacheFile = path.join(cacheDir, `mcp_server_${cacheKey}.py`);
      
      // Check if we have a cached version that's still valid
      if (mcpConfig.cacheEnabled && fs.existsSync(cacheFile)) {
        const stats = fs.statSync(cacheFile);
        const age = Date.now() - stats.mtime.getTime();
        
        if (age < mcpConfig.updateFrequency) {
          console.log('📋 Using cached MCP server (cache hit)');
          return cacheFile;
        }
      }
      
      // Download from backend API
      console.log(`📥 Downloading MCP server: ${mcpConfig.serverUrl}`);
      
      const token = await this.authHandler.tokenManager.getToken();
      const response = await axios.get(mcpConfig.serverUrl, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'User-Agent': 'CreateLex-Bridge/1.0'
        },
        timeout: 30000
      });
      
      // Save to cache
      fs.writeFileSync(cacheFile, response.data);
      console.log('✅ MCP server downloaded and cached successfully');
      
      return cacheFile;
      
    } catch (error) {
      console.error('❌ Primary download failed:', error.message);
      
      // Try fallback URLs
      if (mcpConfig.fallbackUrls && mcpConfig.fallbackUrls.length > 0) {
        for (const fallbackUrl of mcpConfig.fallbackUrls) {
          try {
            console.log(`🔄 Trying fallback URL: ${fallbackUrl}`);
            
            // All fallback URLs are backend API endpoints, use user auth token
            const apiToken = await this.authHandler.tokenManager.getToken();
            const response = await axios.get(fallbackUrl, {
              headers: {
                'Authorization': `Bearer ${apiToken}`,
                'User-Agent': 'CreateLex-Bridge/1.0'
              },
              timeout: 15000
            });
            
            const fallbackFile = path.join(os.tmpdir(), 'createlex-mcp-fallback.py');
            fs.writeFileSync(fallbackFile, response.data);
            console.log('✅ Fallback download successful');
            return fallbackFile;
            
          } catch (fallbackError) {
            console.error(`❌ Fallback failed: ${fallbackError.message}`);
          }
        }
      }
      
      throw new Error('All download attempts failed');
    }
  }
}

module.exports = { MCPBridgeServer }; 