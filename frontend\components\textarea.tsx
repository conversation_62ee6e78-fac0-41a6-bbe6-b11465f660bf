import { modelID } from "@/ai/providers";
import { Textarea as ShadcnTextarea } from "@/components/ui/textarea";
import { ArrowUp, Loader2, AlertTriangle } from "lucide-react";
import { ModelPicker } from "./model-picker";
import { useSocket } from "@/contexts/SocketContext";

interface InputProps {
  input: string;
  handleInputChange: (event: React.ChangeEvent<HTMLTextAreaElement>) => void;
  isLoading: boolean;
  status: string;
  stop: () => void;
  selectedModel: modelID;
  setSelectedModel: (model: modelID) => void;
  tokenLimitExceeded?: boolean; // Add direct token limit check
}

export const Textarea = ({
  input,
  handleInputChange,
  isLoading,
  status,
  stop,
  selectedModel,
  setSelectedModel,
  tokenLimitExceeded = false, // Default to false
}: InputProps) => {
  const isStreaming = status === "streaming" || status === "submitted";
  const { hasTokenLimitError } = useSocket();

  // Use either the direct token limit check or the socket token limit check
  const isLimitExceeded = tokenLimitExceeded || hasTokenLimitError;

  return (
    <div className="relative w-full max-w-full">
      <ShadcnTextarea
        className={`resize-none bg-background/50 dark:bg-muted/50 backdrop-blur-sm w-full rounded-2xl pr-12 pt-4 pb-16 border-input focus-visible:ring-ring placeholder:text-muted-foreground min-h-[80px] max-h-[200px] ${isLimitExceeded ? 'opacity-50 cursor-not-allowed' : ''}`}
        value={input}
        autoFocus
        disabled={isLimitExceeded}
        placeholder={isLimitExceeded ? "Token limit exceeded. Cannot send messages." : "Send a message..."}
        onChange={handleInputChange}
        onKeyDown={(e) => {
          if (e.key === "Enter" && !e.shiftKey && !isLoading && input.trim() && !isLimitExceeded) {
            e.preventDefault();
            e.currentTarget.form?.requestSubmit();
          }
        }}
      />
      <ModelPicker
        setSelectedModel={setSelectedModel}
        selectedModel={selectedModel}
      />

      <button
        type={isStreaming ? "button" : "submit"}
        onClick={isStreaming ? stop : undefined}
        disabled={(!isStreaming && !input.trim()) || (isStreaming && status === "submitted") || isLimitExceeded}
        className="absolute right-2 bottom-2 rounded-full p-2 bg-primary hover:bg-primary/90 disabled:bg-muted disabled:cursor-not-allowed transition-all duration-200"
      >
        {isStreaming ? (
          <Loader2 className="h-4 w-4 text-primary-foreground animate-spin" />
        ) : isLimitExceeded ? (
          <AlertTriangle className="h-4 w-4 text-primary-foreground" />
        ) : (
          <ArrowUp className="h-4 w-4 text-primary-foreground" />
        )}
      </button>
    </div>
  );
};
