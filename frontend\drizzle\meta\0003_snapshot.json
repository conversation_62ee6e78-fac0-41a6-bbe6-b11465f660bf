{"id": "4d2bf069-17f7-4848-a16e-ce008e47d268", "prevId": "9ea87331-4108-40dd-8ac1-32fb1d2f1149", "version": "7", "dialect": "postgresql", "tables": {"public.chats": {"name": "chats", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "default": "'New Chat'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.messages": {"name": "messages", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "chat_id": {"name": "chat_id", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "reasoning": {"name": "reasoning", "type": "text", "primaryKey": false, "notNull": false}, "tool_calls": {"name": "tool_calls", "type": "jsonb", "primaryKey": false, "notNull": false}, "tool_results": {"name": "tool_results", "type": "jsonb", "primaryKey": false, "notNull": false}, "step_type": {"name": "step_type", "type": "text", "primaryKey": false, "notNull": false}, "finish_reason": {"name": "finish_reason", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"messages_chat_id_chats_id_fk": {"name": "messages_chat_id_chats_id_fk", "tableFrom": "messages", "tableTo": "chats", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}