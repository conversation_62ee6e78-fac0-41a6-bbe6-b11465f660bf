#!/bin/bash

echo "🧪 Testing CreateLex Bridge Linux Build"
echo "========================================"

cd ~/createlex-bridge-linux

echo ""
echo "1️⃣ Testing Cross-Platform Compatibility..."
npm run test-cross-platform

echo ""
echo "2️⃣ Testing MCP Executable Build..."
npm run build:mcp-exe

echo ""
echo "3️⃣ Checking if executable was created..."
if [ -f "src/python-protected/mcp_server_linux" ]; then
    echo "✅ mcp_server_linux executable found!"
    ls -la src/python-protected/mcp_server_linux
    file src/python-protected/mcp_server_linux
    
    echo ""
    echo "4️⃣ Testing executable permissions..."
    chmod +x src/python-protected/mcp_server_linux
    
    echo ""
    echo "5️⃣ Testing executable (5 second timeout)..."
    timeout 5s ./src/python-protected/mcp_server_linux || echo "✅ Executable started (timed out as expected)"
    
else
    echo "❌ mcp_server_linux executable not found!"
    echo "📁 Contents of src/python-protected/:"
    ls -la src/python-protected/
fi

echo ""
echo "6️⃣ Testing Full Linux Build..."
npm run build-linux-protected

echo ""
echo "🎯 Linux Build Test Complete!"
echo ""
echo "📊 Results Summary:"
echo "   - Cross-platform test: Check output above"
echo "   - MCP executable: Check if mcp_server_linux exists"
echo "   - Full build: Check if .AppImage was created in dist/"
echo ""
echo "📁 Build outputs:"
ls -la dist/ 2>/dev/null || echo "No dist/ directory found" 