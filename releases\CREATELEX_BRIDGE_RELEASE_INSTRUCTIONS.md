# CreateLex Bridge Release Instructions

## Create GitHub Release for CreateLex Bridge v1.0.0

### Step 1: Navigate to GitHub Releases
1. Go to: https://github.com/AlexKissiJr/AiWebplatform/releases
2. Click **"Create a new release"**

### Step 2: Release Configuration
- **Choose a tag:** `v1.0.0-bridge` (already created and pushed)
- **Release title:** `CreateLex Bridge v1.0.0`
- **Target:** `windows-working` branch

### Step 3: Release Description
Copy and paste this description:

```markdown
# CreateLex Bridge v1.0.0

Desktop application for seamless AI integration across your development tools.

## 🚀 What's New
- **Cross-Platform Support**: Windows and macOS versions (Intel & Apple Silicon)
- **MCP Integration**: Model Control Protocol server management
- **Subscription Validation**: Secure access control
- **Development Workflow**: Direct AI tool integration

## 📦 Downloads

### Windows
- **File:** `CreateLex Bridge Setup 1.0.0.exe`
- **Size:** 435 MB
- **Requirements:** Windows 10/11 (64-bit), .NET Framework 4.8+

### macOS (Apple Silicon)
- **File:** `CreateLex Bridge-1.0.0-arm64.dmg` 
- **Size:** 122 MB
- **Requirements:** macOS 12.0+ (Monterey), Apple Silicon (M1/M2/M3)

### macOS (Intel)
- **File:** `CreateLex Bridge-1.0.0.dmg` 
- **Size:** 124 MB
- **Requirements:** macOS 10.15+ (Catalina), Intel x64 processor

## 🔧 Features
- MCP server management and monitoring
- Cross-platform AI tool integration
- Secure subscription validation
- Real-time connection management
- Development workflow automation

## 📋 Installation
1. Download the appropriate file for your platform
2. Run the installer (Windows) or mount the DMG (macOS)
3. Follow the installation wizard
4. Launch CreateLex Bridge from your applications

## 🎯 Access Requirements
- Active CreateLex subscription required
- Available in dashboard for subscribed users

---

**Note:** This release requires an active CreateLex subscription to access downloads.
```

### Step 4: Upload Binary Files
**IMPORTANT:** You must upload these files as release assets:

1. **Windows File:**
   - File: `releases/CreateLex Bridge Setup 1.0.0.exe`
   - Size: ~435 MB
   - Name exactly: `CreateLex Bridge Setup 1.0.0.exe`

2. **macOS (Apple Silicon) File:**
   - File: `releases/CreateLex Bridge-1.0.0-arm64.dmg`
   - Size: ~122 MB  
   - Name exactly: `CreateLex Bridge-1.0.0-arm64.dmg`

3. **macOS (Intel) File:**
   - File: `releases/CreateLex Bridge-1.0.0.dmg`
   - Size: ~124 MB  
   - Name exactly: `CreateLex Bridge-1.0.0.dmg`

### Step 5: Publish Release
1. Set as **latest release**: ❌ (leave unchecked - this is a separate app release)
2. Set as **pre-release**: ❌ (this is a stable release)
3. Click **"Publish release"**

## ✅ Verification

After publishing, verify these URLs work:
- Windows: https://github.com/AlexKissiJr/AiWebplatform/releases/download/v1.0.0-bridge/CreateLex%20Bridge%20Setup%201.0.0.exe
- macOS (Apple Silicon): https://github.com/AlexKissiJr/AiWebplatform/releases/download/v1.0.0-bridge/CreateLex%20Bridge-1.0.0-arm64.dmg
- macOS (Intel): https://github.com/AlexKissiJr/AiWebplatform/releases/download/v1.0.0-bridge/CreateLex%20Bridge-1.0.0.dmg

## 🔄 Component Updates

The CreateLexBridgeDownload component has been updated to use the `v1.0.0-bridge` release URLs. Once the GitHub release is created with the binary uploads, the download links will work correctly.

## 📁 File Locations

The binary files are available in:
- `releases/CreateLex Bridge Setup 1.0.0.exe`
- `releases/CreateLex Bridge-1.0.0-arm64.dmg`
- `releases/CreateLex Bridge-1.0.0.dmg`

These files are tracked in Git LFS and have been pushed to the repository. 