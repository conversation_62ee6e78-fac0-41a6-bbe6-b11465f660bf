#!/usr/bin/env python3
"""
Simple stdio-to-websocket bridge for MCP server - Windows compatible
"""

import json
import sys
import websockets
import asyncio

# WebSocket server endpoint
WEBSOCKET_URL = "ws://localhost:8000"

async def send_mcp_request_websocket(message):
    """Send a request to the WebSocket MCP server"""
    try:
        print(f"DEBUG: Connecting to WebSocket server at {WEBSOCKET_URL}", file=sys.stderr)
        
        async with websockets.connect(WEBSOCKET_URL) as websocket:
            print(f"DEBUG: Sending message: {json.dumps(message)}", file=sys.stderr)
            await websocket.send(json.dumps(message))
            
            print("DEBUG: Waiting for response...", file=sys.stderr)
            response = await websocket.recv()
            print(f"DEBUG: Received response: {response}", file=sys.stderr)
            
            return json.loads(response)
            
    except Exception as e:
        print(f"DEBUG: WebSocket error: {e}", file=sys.stderr)
        return {
            "jsonrpc": "2.0",
            "id": message.get("id", 1),
            "error": {
                "code": -32603,
                "message": f"WebSocket error: {str(e)}"
            }
        }

def main():
    """Main entry point"""
    print("DEBUG: Starting stdio-to-websocket bridge...", file=sys.stderr)
    
    for line in sys.stdin:
        try:
            line = line.strip()
            if not line:
                continue
                
            print(f"DEBUG: Received stdin: {line}", file=sys.stderr)
            
            # Parse JSON request
            request = json.loads(line)
            method = request.get("method")
            request_id = request.get("id", 1)
            
            print(f"DEBUG: Processing request: {method}", file=sys.stderr)
            
            # Handle notifications (no response expected)
            if method in ["initialized", "notifications/initialized"]:
                # Send to WebSocket server but don't send response to Claude
                asyncio.run(send_mcp_request_websocket(request))
                continue
            
            # Send request to WebSocket server and get response
            response = asyncio.run(send_mcp_request_websocket(request))
            
            # Send response back to Claude Desktop
            print(json.dumps(response), flush=True)
            
        except json.JSONDecodeError as e:
            print(f"DEBUG: JSON decode error: {e}", file=sys.stderr)
            error_response = {
                "jsonrpc": "2.0",
                "id": 1,
                "error": {
                    "code": -32700,
                    "message": f"Parse error: {str(e)}"
                }
            }
            print(json.dumps(error_response), flush=True)
        except Exception as e:
            print(f"DEBUG: General error: {e}", file=sys.stderr)
            error_response = {
                "jsonrpc": "2.0",
                "id": 1,
                "error": {
                    "code": -32603,
                    "message": f"Internal error: {str(e)}"
                }
            }
            print(json.dumps(error_response), flush=True)

if __name__ == "__main__":
    main() 