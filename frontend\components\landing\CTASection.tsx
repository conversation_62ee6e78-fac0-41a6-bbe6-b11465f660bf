'use client';

import React, { useRef, useEffect } from 'react';
import Link from 'next/link';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

interface CTASectionProps {
  onGetStarted: () => void;
  getStartedUrl?: string;
}

const CTASection: React.FC<CTASectionProps> = ({ onGetStarted, getStartedUrl = '/signup' }) => {
  const sectionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Animate section elements
    gsap.fromTo(
      '.cta-content',
      { opacity: 0, y: 50 },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        scrollTrigger: {
          trigger: sectionRef.current,
          start: 'top 80%',
        },
      }
    );
  }, []);

  return (
    <section
      ref={sectionRef}
      className="py-20 bg-gray-900 text-white relative overflow-hidden"
    >
      {/* Background elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-full h-full bg-cover bg-center opacity-20"
             style={{ backgroundImage: 'url(/images/backgrounds/unreal-cta.jpg)' }} />
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/80 via-gray-900/90 to-purple-900/80" />
      </div>

      {/* Unreal-style accent elements */}
      <div className="absolute left-0 top-0 w-full h-1 bg-gradient-to-r from-blue-500/0 via-blue-500/50 to-blue-500/0" />
      <div className="absolute right-0 bottom-0 w-full h-1 bg-gradient-to-r from-blue-500/0 via-blue-500/50 to-blue-500/0" />
      <div className="container mx-auto px-4 relative z-10">
        <div className="cta-content max-w-4xl mx-auto text-center">
          <div className="inline-block mx-auto mb-4 px-4 py-1 border border-blue-400 rounded-full bg-blue-900/30">
            <span className="text-blue-300 font-medium">Get Started</span>
          </div>

          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Ready to <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-300">Transform</span> Your Unreal Engine Workflow?
          </h2>

          <p className="text-xl mb-10 text-blue-100">
            Join thousands of developers who are creating amazing games faster and easier with AI assistance.
          </p>

          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link
              href={getStartedUrl}
              className="px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-400 hover:from-blue-500 hover:to-blue-300 text-white font-bold rounded-lg text-lg transition-all shadow-lg hover:shadow-blue-500/50 inline-block text-center"
            >
              Get Started Now
            </Link>

            <Link
              href="/download"
              className="px-8 py-4 bg-gradient-to-r from-green-600 to-green-400 hover:from-green-500 hover:to-green-300 text-white font-bold rounded-lg text-lg transition-all shadow-lg hover:shadow-green-500/50 inline-block text-center"
            >
              Download Plugin
            </Link>

            <Link
              href="/products"
              className="px-8 py-4 bg-gradient-to-r from-purple-600 to-purple-400 hover:from-purple-500 hover:to-purple-300 text-white font-bold rounded-lg text-lg transition-all shadow-lg hover:shadow-purple-500/50 inline-block text-center"
            >
              View Products
            </Link>

            <a
              href="#demo"
              className="px-8 py-4 bg-transparent border-2 border-blue-400 text-blue-300 font-bold rounded-lg text-lg hover:bg-blue-900/30 transition-colors"
            >
              See How It Works
            </a>
          </div>

          <p className="mt-8 text-blue-100">
            No credit card required to try. Cancel anytime.
          </p>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
