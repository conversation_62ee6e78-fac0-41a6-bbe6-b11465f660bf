const express = require('express');
const router = express.Router();
const { authenticateJWT } = require('../../middleware/auth');
const crypto = require('crypto');
const supabase = require('../../services/supabaseClient');

// Middleware to check if user is an admin
const isAdmin = (req, res, next) => {
  const adminEmails = ['<EMAIL>', '<EMAIL>'];

  // Check if user has is_admin flag set
  if (req.user && req.user.is_admin === true) {
    console.log('[Admin] User is admin by flag:', req.user.email);
    return next();
  }

  // Check if user email is in admin list
  if (req.user && req.user.email && adminEmails.includes(req.user.email.toLowerCase())) {
    console.log('[Admin] User is admin by email:', req.user.email);
    return next();
  }

  // Check environment variable for bypass (works in both dev and production)
  if (process.env.BYPASS_ADMIN_CHECK === 'true') {
    console.log('[Admin] Bypassing admin check via environment variable');
    return next();
  }

  console.log('[Admin] Access denied for user:', req.user?.email || 'unknown');
  return res.status(403).json({ error: 'Admin access required' });
};

/**
 * Get all API keys
 * GET /api/admin/api-keys
 */
router.get('/', authenticateJWT, isAdmin, async (req, res) => {
  try {
    console.log('[Admin] Getting all API keys');

    // Query Supabase for API keys
    const { data, error } = await supabase
      .from('api_keys')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('[Admin] Error getting API keys:', error);
      return res.status(500).json({ error: 'Failed to get API keys' });
    }

    // Mask the API keys for security
    const maskedKeys = data.map(key => ({
      ...key,
      api_key: maskApiKey(key.api_key)
    }));

    // Get user details for each key
    const keysWithUserDetails = await Promise.all(maskedKeys.map(async (key) => {
      try {
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('email')
          .eq('id', key.user_id)
          .single();

        if (userError || !userData) {
          return key;
        }

        return {
          ...key,
          user_email: userData.email
        };
      } catch (error) {
        console.error(`[Admin] Error getting user details for key ${key.id}:`, error);
        return key;
      }
    }));

    res.json({ keys: keysWithUserDetails });
  } catch (error) {
    console.error('[Admin] Error getting API keys:', error);
    res.status(500).json({ error: 'Failed to get API keys' });
  }
});

/**
 * Create a new API key
 * POST /api/admin/api-keys
 * Body: { keyName, rateLimit, userId }
 */
router.post('/', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { keyName, rateLimit, userId } = req.body;

    // Validate required fields
    if (!keyName) {
      return res.status(400).json({ error: 'Key name is required' });
    }

    // Use the authenticated user's ID if no userId is provided
    const effectiveUserId = userId || req.user.id;

    // Generate a random API key
    const apiKey = generateApiKey();
    const prefix = apiKey.substring(0, 8);

    console.log(`[Admin] Creating API key "${keyName}" for user ${effectiveUserId}`);

    // Insert the new API key into Supabase
    const { data, error } = await supabase
      .from('api_keys')
      .insert({
        user_id: effectiveUserId,
        key_name: keyName,
        api_key: apiKey,
        rate_limit: rateLimit || 100,
        is_active: true,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('[Admin] Error creating API key:', error);
      return res.status(500).json({ error: 'Failed to create API key' });
    }

    // Return the new API key (only once, for security)
    res.json({
      id: data.id,
      keyName: data.key_name,
      apiKey: apiKey,
      prefix: prefix,
      rateLimit: data.rate_limit,
      createdAt: data.created_at
    });
  } catch (error) {
    console.error('[Admin] Error creating API key:', error);
    res.status(500).json({ error: 'Failed to create API key' });
  }
});

/**
 * Revoke an API key
 * DELETE /api/admin/api-keys/:keyId
 */
router.delete('/:keyId', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { keyId } = req.params;

    console.log(`[Admin] Revoking API key ${keyId}`);

    // Update the API key to set is_active to false
    const { data, error } = await supabase
      .from('api_keys')
      .update({ is_active: false })
      .eq('id', keyId)
      .select()
      .single();

    if (error) {
      console.error('[Admin] Error revoking API key:', error);
      return res.status(500).json({ error: 'Failed to revoke API key' });
    }

    if (!data) {
      return res.status(404).json({ error: 'API key not found' });
    }

    res.json({
      success: true,
      message: 'API key revoked successfully',
      keyId: keyId
    });
  } catch (error) {
    console.error('[Admin] Error revoking API key:', error);
    res.status(500).json({ error: 'Failed to revoke API key' });
  }
});

/**
 * Get API key usage statistics
 * GET /api/admin/api-keys/:keyId/usage
 */
router.get('/:keyId/usage', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { keyId } = req.params;

    console.log(`[Admin] Getting usage statistics for API key ${keyId}`);

    // Get the API key
    const { data: keyData, error: keyError } = await supabase
      .from('api_keys')
      .select('*')
      .eq('id', keyId)
      .single();

    if (keyError || !keyData) {
      console.error('[Admin] Error getting API key:', keyError);
      return res.status(404).json({ error: 'API key not found' });
    }

    // TODO: Implement API key usage tracking and statistics

    // For now, return mock data
    res.json({
      keyId: keyId,
      keyName: keyData.key_name,
      totalRequests: 1248,
      averageResponseTime: 320,
      errorRate: 0.8,
      requestsByEndpoint: {
        '/api/chat': 842,
        '/api/completion': 356,
        '/api/embedding': 50
      },
      requestsByDay: [
        { date: '2023-06-01', count: 120 },
        { date: '2023-06-02', count: 145 },
        { date: '2023-06-03', count: 132 },
        { date: '2023-06-04', count: 156 },
        { date: '2023-06-05', count: 178 },
        { date: '2023-06-06', count: 201 },
        { date: '2023-06-07', count: 316 }
      ]
    });
  } catch (error) {
    console.error('[Admin] Error getting API key usage:', error);
    res.status(500).json({ error: 'Failed to get API key usage' });
  }
});

// Helper function to generate a random API key
function generateApiKey() {
  const prefix = 'sk_';
  const randomBytes = crypto.randomBytes(24).toString('hex');
  return `${prefix}${randomBytes}`;
}

// Helper function to mask an API key for display
function maskApiKey(apiKey) {
  if (!apiKey) return '';

  const prefix = apiKey.substring(0, 8);
  return `${prefix}${'•'.repeat(16)}`;
}

module.exports = router;
