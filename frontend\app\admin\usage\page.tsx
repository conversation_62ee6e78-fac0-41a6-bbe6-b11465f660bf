import { Suspense } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import CreditUsageChart from '@/components/admin/CreditUsageChart';
import CreditUsageTable from '@/components/admin/CreditUsageTable';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

export default function UsagePage() {
  return (
    <div className="space-y-4">
      <h1 className="text-2xl font-bold">Credit Usage</h1>

      <Tabs defaultValue="overview">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="logs">Usage Logs</TabsTrigger>
          <TabsTrigger value="models">By Model</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Suspense fallback={<Card className="h-80"><CardContent className="flex items-center justify-center h-full"><LoadingSpinner /></CardContent></Card>}>
            <CreditUsageChart />
          </Suspense>

          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Credits</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2.4M</div>
                <p className="text-xs text-muted-foreground">
                  +20.1% from last month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Estimated Cost</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$42.50</div>
                <p className="text-xs text-muted-foreground">
                  +12.3% from last month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Active Users</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">128</div>
                <p className="text-xs text-muted-foreground">
                  +5.2% from last month
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Usage Logs</CardTitle>
              <CardDescription>
                Detailed logs of credit usage by request
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<LoadingSpinner />}>
                <CreditUsageTable />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="models" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Usage by Model</CardTitle>
              <CardDescription>
                Credit usage broken down by AI model
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Claude 3.7 Sonnet</h3>
                  <div className="flex items-center">
                    <div className="w-full bg-gray-200 rounded-full h-4">
                      <div className="bg-blue-600 h-4 rounded-full" style={{ width: '65%' }}></div>
                    </div>
                    <span className="ml-2 text-sm font-medium">65%</span>
                  </div>
                  <div className="text-sm text-gray-500">1.56M credits (Prompt: 520K, Completion: 1.04M)</div>
                </div>

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Claude 3 Opus</h3>
                  <div className="flex items-center">
                    <div className="w-full bg-gray-200 rounded-full h-4">
                      <div className="bg-green-600 h-4 rounded-full" style={{ width: '25%' }}></div>
                    </div>
                    <span className="ml-2 text-sm font-medium">25%</span>
                  </div>
                  <div className="text-sm text-gray-500">600K credits (Prompt: 200K, Completion: 400K)</div>
                </div>

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Claude 3 Haiku</h3>
                  <div className="flex items-center">
                    <div className="w-full bg-gray-200 rounded-full h-4">
                      <div className="bg-purple-600 h-4 rounded-full" style={{ width: '10%' }}></div>
                    </div>
                    <span className="ml-2 text-sm font-medium">10%</span>
                  </div>
                  <div className="text-sm text-gray-500">240K credits (Prompt: 80K, Completion: 160K)</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Usage Trends</CardTitle>
              <CardDescription>
                Credit usage trends over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center justify-center p-8">
                <p className="text-gray-500 mb-4">
                  Trend analysis will be available in a future update.
                </p>
                <div className="w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                  <p className="text-gray-400">Coming soon</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
