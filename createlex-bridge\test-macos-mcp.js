#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

console.log('🍎 Testing macOS MCP Server Executable');
console.log('=====================================');

const mcpPath = path.join(__dirname, 'src/python-protected/mcp_server_mac');

// Check if file exists
if (!fs.existsSync(mcpPath)) {
    console.log('❌ MCP server executable not found at:', mcpPath);
    process.exit(1);
}

// Check file stats
const stats = fs.statSync(mcpPath);
console.log('✅ File found:', mcpPath);
console.log('📦 Size:', Math.round(stats.size / 1024 / 1024) + 'MB');
console.log('🔒 Permissions:', (stats.mode & parseInt('777', 8)).toString(8));

// Test executable
console.log('\n🧪 Testing executable startup...');
const child = spawn(mcpPath, [], {
    stdio: ['pipe', 'pipe', 'pipe'],
    timeout: 3000
});

let output = '';
let errorOutput = '';

child.stdout.on('data', (data) => {
    output += data.toString();
});

child.stderr.on('data', (data) => {
    errorOutput += data.toString();
});

child.on('close', (code) => {
    console.log('📊 Exit code:', code);
    if (output) {
        console.log('📤 Stdout:', output.trim());
    }
    if (errorOutput) {
        console.log('📤 Stderr:', errorOutput.trim());
    }
    
    if (errorOutput.includes('Invalid or expired subscription') || 
        errorOutput.includes('subscription')) {
        console.log('✅ MCP server starts correctly (subscription check working)');
        console.log('🎯 Ready for Claude Desktop integration!');
    } else {
        console.log('⚠️  Unexpected output, but executable runs');
    }
    
    console.log('\n🍎 macOS MCP Server Test Complete!');
});

child.on('error', (error) => {
    console.log('❌ Error running executable:', error.message);
    process.exit(1);
});

// Kill after timeout
setTimeout(() => {
    if (!child.killed) {
        child.kill();
        console.log('⏰ Test completed (timeout)');
    }
}, 3000); 