import { NextApiRequest, NextApiResponse } from 'next';
import { getSupabaseClient } from '@/lib/supabase-singleton';

/**
 * API endpoint to get all known user IDs
 * This endpoint returns all user IDs that have been used in the system
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get all chats from Supabase to find all user IDs
    const supabase = getSupabaseClient();
    const { data: chats, error: chatsError } = await supabase
      .from('chats')
      .select('user_id')
      .order('created_at', { ascending: false });

    if (chatsError) {
      console.error('Error getting chats:', chatsError);
      return res.status(500).json({ error: 'Error getting chats' });
    }

    // Get all user ID mappings
    const { data: mappings, error: mappingsError } = await supabase
      .from('user_id_mappings')
      .select('primary_user_id')
      .order('last_seen_at', { ascending: false });

    if (mappingsError) {
      console.error('Error getting user ID mappings:', mappingsError);
      return res.status(500).json({ error: 'Error getting user ID mappings' });
    }

    // Extract unique user IDs from chats and mappings
    const userIds = new Set<string>();

    // Add user IDs from chats
    chats?.forEach(chat => {
      if (chat.user_id) {
        userIds.add(chat.user_id);
      }
    });

    // Add user IDs from mappings
    mappings?.forEach(mapping => {
      if (mapping.primary_user_id) {
        userIds.add(mapping.primary_user_id);
      }
    });

    // Convert to array and sort by whether they look like Supabase UUIDs
    const userIdArray = Array.from(userIds);
    const sortedUserIds = userIdArray.sort((a, b) => {
      const aIsUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(a);
      const bIsUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(b);

      if (aIsUUID && !bIsUUID) return -1;
      if (!aIsUUID && bIsUUID) return 1;
      return 0;
    });

    return res.status(200).json({
      userIds: sortedUserIds,
      supabaseUuids: sortedUserIds.filter(id =>
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id)
      )
    });
  } catch (error) {
    console.error('Error in known-user-ids API:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
