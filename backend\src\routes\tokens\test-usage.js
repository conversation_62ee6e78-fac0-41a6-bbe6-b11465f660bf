const express = require('express');
const router = express.Router();
const { supabase } = require('../../services/supabaseService');
const tokenUsageService = require('../../services/tokenUsageService');

/**
 * @route GET /api/tokens/test-usage
 * @description Test endpoint to get token usage data directly from Supabase
 * @access Public (for testing only)
 */
router.get('/', async (req, res) => {
  // Add CORS headers
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  try {
    const userId = req.query.userId || '077f1533-9f81-429c-b1b1-52d9c83f146c'; // Default to a known user ID

    console.log(`[Test Usage] Getting token usage for user: ${userId}`);

    // Use the tokenUsageService to get the usage data
    const usageStatus = await tokenUsageService.checkUsageLimits(userId);
    console.log(`[Test Usage] Token usage status:`, usageStatus);

    // Check subscription status first
    const subscriptionService = require('../../services/subscriptionService');
    let hasActiveSubscription = false;

    try {
      hasActiveSubscription = await subscriptionService.checkSubscription(userId);
      console.log(`[Test Usage] Subscription status for user ${userId}: ${hasActiveSubscription}`);
    } catch (subError) {
      console.error(`[Test Usage] Error checking subscription for user ${userId}:`, subError);
      hasActiveSubscription = false;
    }

    // If we have valid usage data from the service, return it
    if (usageStatus && usageStatus.dailyUsage && usageStatus.monthlyUsage) {
      return res.json({
        userId,
        hasActiveSubscription,
        source: 'token_usage_service',
        dailyUsage: usageStatus.dailyUsage,
        monthlyUsage: usageStatus.monthlyUsage,
        plan: usageStatus.plan || 'basic',
        maxRequestLength: usageStatus.maxRequestLength || 4000
      });
    }

    // If the tokenUsageService didn't return valid data, try to use Supabase directly
    if (supabase) {
      // Check daily usage
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Get all daily usage records and sum manually
      const { data: dailyUsageRecords, error: dailyError } = await supabase
        .from('token_usage')
        .select('total_tokens')
        .eq('user_id', userId)
        .gte('timestamp', today.toISOString());

      if (dailyError) {
        console.error('[Test Usage] Error checking daily usage:', dailyError);
        return res.status(500).json({ error: 'Error checking daily usage' });
      }

      // Calculate sum manually
      const dailyTotal = dailyUsageRecords?.reduce((sum, record) => sum + record.total_tokens, 0) || 0;
      console.log(`[Test Usage] Daily usage for user ${userId}: ${dailyTotal} tokens`);

      // Check monthly usage
      const firstDayOfMonth = new Date();
      firstDayOfMonth.setDate(1);
      firstDayOfMonth.setHours(0, 0, 0, 0);

      // Get all monthly usage records and sum manually
      const { data: monthlyUsageRecords, error: monthlyError } = await supabase
        .from('token_usage')
        .select('total_tokens')
        .eq('user_id', userId)
        .gte('timestamp', firstDayOfMonth.toISOString());

      if (monthlyError) {
        console.error('[Test Usage] Error checking monthly usage:', monthlyError);
        return res.status(500).json({ error: 'Error checking monthly usage' });
      }

      // Calculate sum manually
      const monthlyTotal = monthlyUsageRecords?.reduce((sum, record) => sum + record.total_tokens, 0) || 0;
      console.log(`[Test Usage] Monthly usage for user ${userId}: ${monthlyTotal} tokens`);

      // Get all token usage records for this user
      const { data: allRecords, error: allError } = await supabase
        .from('token_usage')
        .select('*')
        .eq('user_id', userId)
        .order('timestamp', { ascending: false })
        .limit(10);

      if (allError) {
        console.error('[Test Usage] Error getting all records:', allError);
      } else {
        console.log(`[Test Usage] Found ${allRecords.length} records for user ${userId}`);
        console.log('[Test Usage] Sample records:', allRecords.slice(0, 3));
      }

      // Return the usage data
      return res.json({
        userId,
        hasActiveSubscription,
        source: 'supabase_direct',
        dailyUsage: {
          used: dailyTotal,
          limit: 50000,
          percentage: (dailyTotal / 50000) * 100,
          exceeded: dailyTotal > 50000
        },
        monthlyUsage: {
          used: monthlyTotal,
          limit: 1000000,
          percentage: (monthlyTotal / 1000000) * 100,
          exceeded: monthlyTotal > 1000000
        },
        plan: 'basic',
        maxRequestLength: 4000,
        records: allRecords || []
      });
    }

    // If neither method worked, return empty data
    console.log('[Test Usage] No data available for user', userId);
    return res.json({
      userId,
      hasActiveSubscription,
      source: 'empty_data',
      dailyUsage: {
        used: 0,
        limit: 50000,
        percentage: 0,
        exceeded: false
      },
      monthlyUsage: {
        used: 0,
        limit: 1000000,
        percentage: 0,
        exceeded: false
      },
      plan: 'basic',
      maxRequestLength: 4000
    });
  } catch (error) {
    console.error('[Test Usage] Error in test usage endpoint:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

module.exports = router;
