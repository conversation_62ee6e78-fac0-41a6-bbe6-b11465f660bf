const os = require('os');
const fs = require('fs-extra');
const path = require('path');

async function testCrossPlatformSupport() {
  console.log('🧪 Testing Cross-Platform Support for CreateLex Bridge');
  console.log('=' .repeat(60));
  
  const platform = os.platform();
  const arch = os.arch();
  
  console.log(`📱 Platform: ${platform}`);
  console.log(`🔧 Architecture: ${arch}`);
  console.log(`🏠 Node.js Version: ${process.version}`);
  
  // Test 1: Check if platform is supported
  console.log('\n1️⃣ Platform Support Check:');
  const supportedPlatforms = ['win32', 'darwin', 'linux'];
  if (supportedPlatforms.includes(platform)) {
    console.log(`✅ Platform ${platform} is supported`);
  } else {
    console.log(`❌ Platform ${platform} is not supported`);
    console.log(`   Supported platforms: ${supportedPlatforms.join(', ')}`);
  }
  
  // Test 2: Check executable naming
  console.log('\n2️⃣ Executable Naming Check:');
  let expectedExecutable = 'mcp_server';
  if (platform === 'win32') {
    expectedExecutable = 'mcp_server.exe';
  } else if (platform === 'darwin') {
    expectedExecutable = 'mcp_server_mac';
  } else if (platform === 'linux') {
    expectedExecutable = 'mcp_server_linux';
  }
  console.log(`✅ Expected executable name: ${expectedExecutable}`);
  
  // Test 3: Check Python availability
  console.log('\n3️⃣ Python Availability Check:');
  const { execSync } = require('child_process');
  
  let pythonCmd = 'python';
  let pythonVersion = null;
  
  // Try different Python commands
  const pythonCommands = ['python3', 'python', 'py'];
  
  for (const cmd of pythonCommands) {
    try {
      const version = execSync(`${cmd} --version`, { stdio: 'pipe' }).toString().trim();
      console.log(`✅ Found ${cmd}: ${version}`);
      if (!pythonVersion) {
        pythonCmd = cmd;
        pythonVersion = version;
      }
    } catch (e) {
      console.log(`❌ ${cmd} not found`);
    }
  }
  
  if (pythonVersion) {
    console.log(`✅ Using Python command: ${pythonCmd}`);
  } else {
    console.log(`❌ No Python installation found`);
  }
  
  // Test 4: Check PyInstaller availability
  console.log('\n4️⃣ PyInstaller Availability Check:');
  if (pythonCmd) {
    try {
      const pyinstallerVersion = execSync(`${pythonCmd} -m PyInstaller --version`, { stdio: 'pipe' }).toString().trim();
      console.log(`✅ PyInstaller available: ${pyinstallerVersion}`);
    } catch (e) {
      console.log(`❌ PyInstaller not found via ${pythonCmd} -m PyInstaller`);
      
      // Try direct command
      try {
        const directVersion = execSync('pyinstaller --version', { stdio: 'pipe' }).toString().trim();
        console.log(`✅ PyInstaller available (direct): ${directVersion}`);
      } catch (e2) {
        console.log(`❌ PyInstaller not found. Install with: ${pythonCmd} -m pip install pyinstaller`);
      }
    }
  }
  
  // Test 5: Check build scripts
  console.log('\n5️⃣ Build Scripts Check:');
  const packageJsonPath = path.join(__dirname, 'package.json');
  if (await fs.pathExists(packageJsonPath)) {
    const packageJson = await fs.readJson(packageJsonPath);
    const scripts = packageJson.scripts || {};
    
    const buildScripts = [
      'build-win-protected',
      'build-mac-protected', 
      'build-linux-protected'
    ];
    
    buildScripts.forEach(script => {
      if (scripts[script]) {
        console.log(`✅ ${script}: ${scripts[script]}`);
      } else {
        console.log(`❌ ${script}: Not found`);
      }
    });
  }
  
  // Test 6: Check source files
  console.log('\n6️⃣ Source Files Check:');
  const criticalFiles = [
    'src/mcp/bridge-server-protected.js',
    'src/python/mcp_server_protected.py',
    'scripts/build-mcp-exe.js'
  ];
  
  for (const file of criticalFiles) {
    const filePath = path.join(__dirname, file);
    if (await fs.pathExists(filePath)) {
      console.log(`✅ ${file}: Found`);
    } else {
      console.log(`❌ ${file}: Missing`);
    }
  }
  
  // Test 7: Platform-specific recommendations
  console.log('\n7️⃣ Platform-Specific Recommendations:');
  if (platform === 'win32') {
    console.log('🪟 Windows:');
    console.log('   • Use npm run build-win-protected');
    console.log('   • Executable will be mcp_server.exe');
    console.log('   • May need to run as administrator for some operations');
  } else if (platform === 'darwin') {
    console.log('🍎 macOS:');
    console.log('   • Use npm run build-mac-protected');
    console.log('   • Executable will be mcp_server_mac');
    console.log('   • May need to allow executable in System Preferences > Security');
    console.log('   • Use python3 command if available');
  } else if (platform === 'linux') {
    console.log('🐧 Linux:');
    console.log('   • Use npm run build-linux-protected');
    console.log('   • Executable will be mcp_server_linux');
    console.log('   • May need to chmod +x the executable');
    console.log('   • Use python3 command if available');
  }
  
  console.log('\n🎯 Summary:');
  console.log(`Platform: ${platform} (${supportedPlatforms.includes(platform) ? 'Supported' : 'Not Supported'})`);
  console.log(`Python: ${pythonVersion || 'Not Found'}`);
  console.log(`Expected Executable: ${expectedExecutable}`);
  
  if (supportedPlatforms.includes(platform) && pythonVersion) {
    console.log('✅ System appears ready for cross-platform build!');
  } else {
    console.log('❌ System may need additional setup for cross-platform build');
  }
}

// Run the test
testCrossPlatformSupport().catch(console.error); 