require('dotenv').config();
const supabase = require('./src/services/supabaseClient');

async function checkAdminUsers() {
  console.log('Checking admin users in Supabase...');
  
  try {
    // First check if the is_admin column exists
    console.log('Checking if is_admin column exists...');
    
    try {
      // Try to query users with is_admin field
      const { data: adminUsers, error: adminError } = await supabase
        .from('users')
        .select('id, email, name, is_admin')
        .eq('is_admin', true);
        
      if (adminError && adminError.message && adminError.message.includes('column "is_admin" does not exist')) {
        console.log('is_admin column does not exist yet. You need to run the admin-tables.sql script.');
        return;
      } else if (adminError) {
        console.error('Error querying admin users:', adminError);
        return;
      }
      
      console.log('is_admin column exists');
      console.log('Admin users:', adminUsers);
      
      // Check for specific admin emails
      const { data: specificUsers, error: specificError } = await supabase
        .from('users')
        .select('id, email, name, is_admin')
        .in('email', ['<EMAIL>', '<EMAIL>']);
        
      if (specificError) {
        console.error('Error querying specific users:', specificError);
        return;
      }
      
      console.log('Users with admin emails:', specificUsers);
      
      // Check if they have admin privileges
      const needsUpdate = specificUsers.filter(user => !user.is_admin);
      
      if (needsUpdate.length > 0) {
        console.log('The following users need admin privileges set:');
        needsUpdate.forEach(user => {
          console.log(`- ${user.name} (${user.email})`);
        });
        
        // Update these users
        console.log('Setting admin privileges...');
        const { error: updateError } = await supabase
          .from('users')
          .update({ is_admin: true })
          .in('id', needsUpdate.map(user => user.id));
          
        if (updateError) {
          console.error('Error updating admin privileges:', updateError);
        } else {
          console.log('Successfully set admin privileges');
        }
      } else {
        console.log('All users with admin emails already have admin privileges');
      }
    } catch (error) {
      console.error('Error checking admin users:', error);
    }
  } catch (error) {
    console.error('Exception in check:', error);
  }
}

// Run the check
checkAdminUsers()
  .then(() => {
    console.log('Check completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
