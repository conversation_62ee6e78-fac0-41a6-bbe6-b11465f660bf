/**
 * Test script to verify connection to the MCP server
 * 
 * This script tests the connection to the MCP server using WebSockets.
 * 
 * Usage:
 * node test-mcp-connection.js
 */

// Load environment variables
require('dotenv').config();

// Import WebSocket
const WebSocket = require('ws');

console.log('Starting MCP server connection test...');
const mcpUrl = process.env.MCP_SERVER_URL || 'ws://127.0.0.1:9877';
console.log(`MCP URL: ${mcpUrl}`);

// Create a new WebSocket connection
console.log('Connecting to MCP server...');
const ws = new WebSocket(mcpUrl);

// Set a connection timeout
const connectionTimeout = setTimeout(() => {
  if (ws.readyState !== WebSocket.OPEN) {
    console.error('❌ WebSocket connection timeout');
    ws.terminate();
    console.log('Make sure the MCP server is running.');
    console.log('To start the MCP server:');
    console.log('1. Navigate to the mcp_server directory');
    console.log('2. Run: python mcp_server.py');
    process.exit(1);
  }
}, 5000); // 5 second timeout

ws.on('open', () => {
  console.log('✅ Connected to MCP server');
  clearTimeout(connectionTimeout);

  // Send a handshake message
  const handshakeMessage = {
    type: 'handshake',
    message: 'Testing connection from CreateLex AI Assistant',
    id: Date.now().toString()
  };
  console.log('Sending handshake message:', JSON.stringify(handshakeMessage));
  ws.send(JSON.stringify(handshakeMessage));
});

ws.on('message', (data) => {
  try {
    const response = JSON.parse(data.toString());
    console.log('Received response:', JSON.stringify(response, null, 2));
    
    // Close the connection after receiving a response
    ws.close();
    console.log('Test completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error parsing response:', error.message);
    ws.close();
    process.exit(1);
  }
});

ws.on('error', (error) => {
  console.error('❌ WebSocket error:', error.message);
  clearTimeout(connectionTimeout);
  console.log('Make sure the MCP server is running.');
  console.log('To start the MCP server:');
  console.log('1. Navigate to the mcp_server directory');
  console.log('2. Run: python mcp_server.py');
  process.exit(1);
});

ws.on('close', () => {
  console.log('WebSocket connection closed');
  clearTimeout(connectionTimeout);
  process.exit(0);
});
