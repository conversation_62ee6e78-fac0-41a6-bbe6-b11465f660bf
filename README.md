# CreateLex AI Platform for Unreal Engine

A comprehensive AI-powered platform with Unreal Engine integration, featuring protected MCP (Model Control Protocol) servers, native bridge applications, and professional development tools.

## 🚀 Current Architecture - LIVE & OPERATIONAL

We have successfully implemented a **protected, subscription-based MCP solution** that safeguards intellectual property while providing scalable, real-time AI-powered Unreal Engine integration.

### ✅ Core Components:
- **🌉 CreateLex Bridge**: Native application with protected MCP server and subscription validation
- **🎨 Frontend Platform**: Next.js web application with AI chat interface and user management
- **⚙️ Backend Services**: Node.js API server with authentication, billing, and token tracking
- **🎯 Unreal Plugin**: Native C++ plugin with Python handlers for Unreal Engine integration
- **📦 VS Code Extension**: Professional development tools for CreateLex workflow

### 🛡️ Security & Protection Features:
- **🔒 IP Protection**: All business logic runs in protected, validated MCP servers
- **💰 SaaS Model**: Subscription-based access with comprehensive token usage tracking
- **🔐 Subscription Validation**: Real-time verification and auto-shutdown on invalid subscriptions
- **🌉 Bridge Architecture**: Secure cloud-to-local Unreal Engine connectivity
- **🚫 No Exposed Handlers**: Users cannot access or modify tool implementations

## 🌉 Universal Bridge for AI Coding Assistants

**NEW**: Our CreateLex Bridge application works with **Claude Desktop, Cursor, Windsurf, VS Code**, and any MCP-compatible AI coding assistant!

### ✅ **Supported AI Assistants**
- **Claude Desktop** ✅ Fully tested and working
- **Cursor** ✅ Fully tested and working  
- **Windsurf** ✅ Fully tested and working
- **VS Code** ✅ Compatible with MCP extensions
- **Any MCP Client** ✅ Universal compatibility

### 🚀 **Quick Setup**

1. **Download & Install CreateLex Bridge**
   - Download from the releases section
   - Install and login with your CreateLex credentials
   - Click "Start MCP Server" in the dashboard

2. **Configure Your AI Assistant**

**Claude Desktop** - Add to `claude_desktop_config.json`:
```json
{
  "mcpServers": {
    "createlex-unreal": {
      "command": "node",
      "args": ["path/to/createlex-bridge/bridge-native.js"]
    }
  }
}
```

**Cursor** - Add to settings:
```json
{
  "mcp.servers": {
    "createlex-unreal": {
      "command": "node", 
      "args": ["path/to/createlex-bridge/bridge-native.js"]
    }
  }
}
```

**Windsurf** - Add to configuration:
```json
{
  "mcp": {
    "servers": {
      "createlex-unreal": {
        "command": "node",
        "args": ["path/to/createlex-bridge/bridge-native.js"]
      }
    }
  }
}
```

### 🛠️ **Available Tools**
Once configured, your AI assistant gets access to **31+ Unreal Engine tools**:
- **Project Management**: Create, open, configure Unreal projects
- **Asset Management**: Import models, textures, create materials
- **Level Design**: Place actors, setup lighting, create levels
- **Animation & Rigging**: Skeletal meshes, animation blueprints
- **Gameplay Programming**: Game modes, player controllers, UI systems
- **Blueprint Creation**: Automatic blueprint generation and modification
- **Material Systems**: Advanced material creation and assignment
- **And much more...**

### 📚 **Complete Setup Guide**
For detailed configuration instructions for all AI assistants, see:
**[Bridge Setup Guide](./createlex-bridge/BRIDGE-SETUP.md)**

---

## 🏗️ Architecture Overview

### Current Implementation Flow:
```
AI Assistant → CreateLex Bridge → Subscription Validation → Unreal Engine
                      ↓                    ↓
               MCP Protocol         Token Tracking
                      ↓                    ↓
              Protected Handlers    Usage Analytics
```

### 🎯 Key Architecture Benefits:
- **🔒 Ultimate IP Protection**: Zero business logic exposed on user machines
- **✅ Subscription Control**: Real-time validation with auto-shutdown
- **📊 Usage Tracking**: Comprehensive token-based metering
- **🌐 Universal Compatibility**: Works with any MCP-compatible AI assistant
- **⚡ High Performance**: Optimized for real-time Unreal Engine communication

## Getting Started

### Prerequisites

- Node.js 18+
- Python 3.11+
- Unreal Engine 5.1+ (for plugin integration)
- CreateLex subscription (for bridge access)

### Development Setup

1. **Clone the repository**

```bash
git clone https://github.com/yourusername/AiWebplatform.git
cd AiWebplatform
```

2. **Setup the backend**

```bash
cd backend
npm install
cp .env.example .env
# Edit .env file with your configuration
```

3. **Setup the frontend**

```bash
cd frontend
npm install
cp .env.example .env
# Edit .env file with your configuration
```

4. **Setup the CreateLex Bridge**

```bash
cd createlex-bridge
npm install
# Follow the bridge setup guide for configuration
```

5. **Run the platform in development mode**

```bash
# Terminal 1 - Backend services
cd backend
npm run dev

# Terminal 2 - Frontend application
cd frontend
npm run dev

# Terminal 3 - Build bridge (if developing bridge features)
cd createlex-bridge
npm run build-win-protected  # or appropriate build command
```

### Using Docker Compose

You can use Docker Compose to run the web platform services:

```bash
docker-compose up
```

## Integrating with Unreal Engine

1. **Install the UnrealGenAISupport Plugin**
   - Copy the `UnrealGenAISupport/` folder to your project's `Plugins/` directory
   - Enable the plugin in your Unreal project settings
   - Ensure Python is enabled in your Unreal Engine installation

2. **Configure the Bridge Connection**
   - Start the CreateLex Bridge application
   - Login with your CreateLex credentials
   - Verify the MCP server is running (green status)

3. **Start Using AI Commands**
   - Open your preferred AI assistant (Claude Desktop, Cursor, etc.)
   - Begin typing natural language commands for Unreal Engine
   - The bridge will automatically handle validation and execution

For detailed instructions, see the [Unreal Engine Integration Guide](docs/UNREAL_INTEGRATION_GUIDE.md).

## 🔧 Platform Features

### 🤖 AI Integration
- **Multi-Provider Support**: OpenAI, Anthropic, Google, DeepSeek integration
- **Natural Language Processing**: Convert plain English to Unreal Engine commands
- **Real-time Communication**: Instant feedback and streaming responses
- **Context Awareness**: AI understands current project state and history

### 🛡️ Security & Access Control
- **Subscription Management**: Stripe-powered billing and user management
- **Token-based Usage**: Metered billing with comprehensive usage tracking
- **IP Protection**: All business logic runs on protected servers
- **Session Management**: Secure authentication and authorization

### 🎮 Unreal Engine Integration
- **Complete Tool Suite**: 31+ tools covering all major Unreal Engine workflows
- **Real-time Updates**: Immediate reflection of changes in Unreal Engine
- **Asset Management**: Import, organize, and manage project assets
- **Blueprint Generation**: Automatic creation of gameplay blueprints
- **Material Systems**: Advanced material creation and application

## API Endpoints

The backend provides these key endpoints for platform interaction:

- `GET /api/auth/profile` - User profile and subscription status
- `POST /api/tokens/purchase` - Purchase additional usage tokens
- `GET /api/usage/history` - Token usage history and analytics
- `POST /api/admin/users` - User management (admin only)
- `GET /api/health` - Platform health and status

See the platform documentation for complete API reference.

## Token Purchase System

The platform includes a comprehensive token-based usage system:

- **Subscription Tokens**: Base allocation included with subscription
- **Additional Purchases**: Buy extra tokens through the dashboard
- **Usage Tracking**: Real-time monitoring of token consumption
- **Billing Integration**: Seamless Stripe integration for payments
- **Analytics**: Detailed usage reports and cost optimization

For detailed documentation:
- [Token Purchase Developer Guide](docs/token-purchase-developer-guide.md)
- [Token Purchase Admin Guide](docs/token-purchase-admin-guide.md)

## 📚 Documentation

### 🛠️ Setup & Integration
- **[Bridge Setup Guide](./createlex-bridge/BRIDGE-SETUP.md)** - Configure with any AI assistant
- **[Build Guide](./createlex-bridge/BUILD_GUIDE.md)** - Build bridge applications for different platforms
- **[Unreal Plugin Guide](./UnrealGenAISupport/README.md)** - Plugin installation and configuration

### 💰 Business & Administration
- [Token Purchase System](./docs/token-purchase-system.md) - Billing and usage tracking overview
- [Seat Management Guide](./docs/SEAT_MANAGEMENT_GUIDE.md) - Multi-user seat allocation
- [Admin Access Guide](./docs/admin-access-guide.md) - Administrative functionality

### 🔧 Development & Architecture
- [UE 5.6 Compatibility Fixes](./UE_5.6_COMPATIBILITY_FIXES.md) - Latest compatibility updates
- [Backend Documentation](./backend/README.md) - API server setup and configuration
- [Frontend Documentation](./frontend/README.md) - Web application development

## 🗺️ Development Roadmap

### ✅ Current Status (Stable)
- [x] **Protected MCP Architecture** - Secure, subscription-validated bridge system
- [x] **Universal AI Assistant Support** - Claude Desktop, Cursor, Windsurf, VS Code compatibility
- [x] **Complete Tool Suite** - 31+ Unreal Engine tools with full functionality
- [x] **User Management** - Authentication, subscriptions, and billing integration
- [x] **Production Ready** - Stable releases with cross-platform support

### 🚧 Active Development
- [ ] **Enhanced Analytics** - Advanced usage analytics and optimization tools
- [ ] **Team Collaboration** - Multi-user project collaboration features
- [ ] **Advanced Blueprints** - Visual blueprint editing and advanced generation
- [ ] **Performance Optimization** - Faster tool execution and response times
- [ ] **Mobile Integration** - Remote Unreal Engine control from mobile devices

### 🔮 Future Features
- [ ] **Enterprise Integrations** - Custom enterprise workflow integrations
- [ ] **Advanced AI Models** - Integration with latest AI model capabilities
- [ ] **Cloud Rendering** - Remote Unreal Engine rendering capabilities
- [ ] **VR/AR Support** - Extended reality development tools
- [ ] **Marketplace Integration** - Direct integration with Unreal Engine marketplace

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes and commit: `git commit -am 'Add feature'`
4. Push to the branch: `git push origin feature-name`
5. Submit a pull request

Please ensure your contributions follow our coding standards and include appropriate tests.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

For support and questions:
- **Issues**: [Create an issue on GitHub](https://github.com/AlexKissiJr/AiWebplatform/issues)
- **Email**: <EMAIL>
- **Documentation**: [Project Documentation](./docs/)
- **Community**: [Discord Server](https://discord.gg/createlex)

## 🙏 Acknowledgments

- Unreal Engine team for the excellent development platform
- Model Control Protocol (MCP) specification for standardized AI integration
- Open source MCP community for inspiration and collaboration
- All contributors and beta testers who helped shape this platform

---

**Status**: ✅ **Production Ready** - Stable protected MCP architecture with universal AI assistant support and comprehensive Unreal Engine integration. Ready for professional game development workflows.