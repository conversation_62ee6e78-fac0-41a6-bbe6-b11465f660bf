name: Build and Release

on:
  push:
    branches: [ main, windows-working ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        include:
          - os: ubuntu-latest
            platform: linux
            command: build-linux-protected
            artifact: '*.AppImage'
          - os: windows-latest
            platform: windows
            command: build-win-protected
            artifact: '*.exe'
          - os: macos-latest
            platform: macos
            command: build-mac-protected
            artifact: '*.dmg'

    runs-on: ${{ matrix.os }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: 'createlex-bridge/package-lock.json'

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pyinstaller

    - name: Install dependencies
      working-directory: ./createlex-bridge
      run: npm ci

    - name: Run cross-platform tests
      working-directory: ./createlex-bridge
      run: npm run test-cross-platform

    - name: Build MCP executable
      working-directory: ./createlex-bridge
      run: npm run build:mcp-exe

    - name: Build application
      working-directory: ./createlex-bridge
      run: npm run ${{ matrix.command }}
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: createlex-bridge-${{ matrix.platform }}
        path: |
          createlex-bridge/dist/${{ matrix.artifact }}
          createlex-bridge/dist/latest*.yml
        retention-days: 30

  release:
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')

    steps:
    - name: Download all artifacts
      uses: actions/download-artifact@v4

    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        files: |
          createlex-bridge-linux/*.AppImage
          createlex-bridge-windows/*.exe
          createlex-bridge-macos/*.dmg
          createlex-bridge-*/latest*.yml
        draft: false
        prerelease: false
        generate_release_notes: true
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }} 