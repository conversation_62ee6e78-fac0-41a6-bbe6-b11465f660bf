'use client';

import { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { updateUserId } from "@/lib/user-id";
import { useRouter } from "next/navigation";

export default function DirectSetUserIdPage() {
  const [targetUserId, setTargetUserId] = useState('077f1533-9f81-429c-b1b1-52d9c83f146c'); // Pre-filled with the Supabase UUID
  const [currentUserId, setCurrentUserId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  // Get the current user ID from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedUserId = localStorage.getItem('ai-chat-user-id');
      setCurrentUserId(storedUserId || '');
    }
  }, []);

  const handleSetUserId = async () => {
    if (!targetUserId.trim()) {
      toast.error("Target user ID cannot be empty");
      return;
    }

    setIsLoading(true);

    try {
      // Call the direct-set-user-id API
      const response = await fetch('/api/direct-set-user-id', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ targetUserId: targetUserId.trim() }),
      });

      if (!response.ok) {
        toast.error(`Failed to set user ID: ${response.statusText}`);
        setIsLoading(false);
        return;
      }

      const data = await response.json();

      if (data.success) {
        // Update the user ID in localStorage
        updateUserId(targetUserId.trim(), 'supabase');
        
        toast.success("User ID set successfully for all devices");
        
        // Redirect to the chat page after a short delay
        setTimeout(() => {
          router.push('/chat');
          // Reload the page to ensure all components use the new user ID
          window.location.reload();
        }, 1500);
      } else {
        toast.error("Failed to set user ID");
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Error setting user ID:', error);
      toast.error("An error occurred while setting the user ID");
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Direct Set User ID</CardTitle>
          <CardDescription>
            Set the user ID for all devices to ensure consistent chat history
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="currentUserId">Current User ID</Label>
            <Input
              id="currentUserId"
              value={currentUserId}
              readOnly
              className="font-mono text-xs"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="targetUserId">Target User ID</Label>
            <Input
              id="targetUserId"
              value={targetUserId}
              onChange={(e) => setTargetUserId(e.target.value)}
              placeholder="Enter the target user ID"
              className="font-mono text-xs"
            />
            <p className="text-xs text-muted-foreground">
              This will set the user ID for all devices and migrate all chats
            </p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => router.push('/chat')} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleSetUserId} disabled={isLoading}>
            {isLoading ? "Setting..." : "Set User ID for All Devices"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
