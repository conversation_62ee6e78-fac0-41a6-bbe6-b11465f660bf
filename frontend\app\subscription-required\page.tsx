'use client';

import { useEffect, useState } from 'react';
import { getMainAppOrigin } from '@/lib/fetch-auth-token';

export default function SubscriptionRequiredPage() {
  const [mainAppUrl, setMainAppUrl] = useState('');

  useEffect(() => {
    // Get the main app URL
    const origin = getMainAppOrigin();
    setMainAppUrl(origin);

    // Remove any existing elements from the body
    // This ensures no sidebars or other components are shown
    if (typeof document !== 'undefined') {
      // Apply styles to hide any potential sidebars
      const style = document.createElement('style');
      style.innerHTML = `
        /* Hide all elements except our container */
        body > *:not(#subscription-required-container) {
          display: none !important;
        }

        /* Reset body styles */
        body {
          overflow: auto !important;
          background-color: white !important;
          margin: 0 !important;
          padding: 0 !important;
        }

        /* Hide specific elements that might be causing issues */
        .sidebar,
        nav,
        header,
        aside,
        [data-sidebar],
        [role="navigation"] {
          display: none !important;
        }
      `;
      document.head.appendChild(style);

      // More aggressive approach: remove all elements except our container
      setTimeout(() => {
        const container = document.getElementById('subscription-required-container');
        if (container) {
          // Move container to be a direct child of body
          document.body.appendChild(container);

          // Remove all other children of body
          Array.from(document.body.children).forEach(child => {
            if (child.id !== 'subscription-required-container') {
              child.remove();
            }
          });
        }
      }, 0);
    }
  }, []);

  return (
    <div id="subscription-required-container" style={{
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      backgroundColor: 'white',
      zIndex: 9999,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{
        maxWidth: '500px',
        width: '100%',
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08)',
        overflow: 'hidden',
        textAlign: 'center',
        padding: '30px'
      }}>
        <div style={{ color: '#3b82f6', marginBottom: '20px' }}>
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="12"></line>
            <line x1="12" y1="16" x2="12.01" y2="16"></line>
          </svg>
        </div>

        <h1 style={{
          fontSize: '24px',
          fontWeight: 'bold',
          marginBottom: '8px',
          color: '#111827'
        }}>
          Subscription Required
        </h1>

        <p style={{
          fontSize: '16px',
          color: '#6b7280',
          marginBottom: '24px'
        }}>
          You need an active subscription to access this feature
        </p>

        <p style={{
          fontSize: '14px',
          color: '#6b7280',
          marginBottom: '24px',
          lineHeight: '1.5'
        }}>
          To use the CreateLex AI Chat, you need an active subscription.
          Please visit the subscription page to choose a plan that suits your needs.
        </p>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          <a
            href={`${mainAppUrl}/subscription`}
            style={{
              display: 'block',
              width: '100%',
              padding: '10px',
              backgroundColor: '#3b82f6',
              color: 'white',
              borderRadius: '6px',
              textDecoration: 'none',
              fontWeight: 'bold',
              textAlign: 'center'
            }}
          >
            View Subscription Plans
          </a>

          <a
            href={`${mainAppUrl}/dashboard`}
            style={{
              display: 'block',
              width: '100%',
              padding: '10px',
              backgroundColor: 'white',
              color: '#3b82f6',
              border: '1px solid #e5e7eb',
              borderRadius: '6px',
              textDecoration: 'none',
              fontWeight: 'bold',
              textAlign: 'center'
            }}
          >
            Return to Dashboard
          </a>
        </div>
      </div>
    </div>
  );
}
