
# Upload Instructions for MCP Server v1.1.0

## Files to upload to createlex.com/api/mcp-updates/:

1. **Update Package**: mcp-server-v1.1.0.zip
   - Upload to: /api/mcp-updates/download/1.1.0
   - Set header: x-checksum: a60ddcec0cff79a7544ea8ce3d60955390728106dd01fdf880faf1f75b7a4fa6

2. **Metadata**: {
  "version": "1.1.0",
  "filename": "mcp-server-v1.1.0.zip",
  "size": 50990,
  "checksum": "a60ddcec0cff79a7544ea8ce3d60955390728106dd01fdf880faf1f75b7a4fa6",
  "created": "2025-06-13T20:12:25.553Z",
  "files_count": 26
}
   - Add to version database
   - Update /api/mcp-updates/check endpoint

## API Endpoints to update:

### GET /api/mcp-updates/check?current_version=X.X.X
Response:
```json
{
  "hasUpdate": true,
  "latestVersion": "1.1.0",
  "updateInfo": {
    "size": 50990,
    "description": "CreateLex MCP Server v1.1.0",
    "releaseNotes": "Add your release notes here"
  }
}
```

### GET /api/mcp-updates/download/1.1.0
- Return the ZIP file with x-checksum header
- Content-Type: application/zip
- x-checksum: a60ddcec0cff79a7544ea8ce3d60955390728106dd01fdf880faf1f75b7a4fa6
