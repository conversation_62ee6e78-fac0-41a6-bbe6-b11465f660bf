'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { fetchWithAuth, getApiUrl } from '@/lib/auth-utils';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePickerWithRange } from '@/components/ui/date-range-picker';
import { addDays, format, subDays, subMonths } from 'date-fns';
import { DateRange } from 'react-day-picker';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';

interface TokenUsageData {
  date: string;
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
}

export default function TokenUsageChart() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [usageData, setUsageData] = useState<TokenUsageData[]>([]);
  const [timeRange, setTimeRange] = useState<string>('7d');
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: subDays(new Date(), 7),
    to: new Date(),
  });

  useEffect(() => {
    fetchUsageData();
  }, [timeRange, dateRange]);

  const fetchUsageData = async () => {
    try {
      setLoading(true);

      // Calculate date range based on selected time range
      let fromDate: Date;
      let toDate = new Date();

      if (timeRange === 'custom' && dateRange?.from) {
        fromDate = dateRange.from;
        toDate = dateRange.to || new Date();
      } else {
        switch (timeRange) {
          case '7d':
            fromDate = subDays(new Date(), 7);
            break;
          case '30d':
            fromDate = subDays(new Date(), 30);
            break;
          case '90d':
            fromDate = subDays(new Date(), 90);
            break;
          default:
            fromDate = subDays(new Date(), 7);
        }
      }

      const apiUrl = getApiUrl();
      const requestUrl = `${apiUrl}/api/admin/token-usage/daily?from=${format(fromDate, 'yyyy-MM-dd')}&to=${format(toDate, 'yyyy-MM-dd')}`;

      const response = await fetchWithAuth(requestUrl);

      if (!response.ok) {
        throw new Error(`Failed to fetch token usage data: ${response.status}`);
      }

      const data = await response.json();

      // Transform data for chart
      const chartData = data.dailyUsage.map((day: any) => ({
        date: format(new Date(day.date), 'MMM dd'),
        promptTokens: day.promptTokens,
        completionTokens: day.completionTokens,
        totalTokens: day.totalTokens
      }));

      setUsageData(chartData);
    } catch (error) {
      console.error('Error fetching token usage data:', error);
      setError('Failed to load token usage data');

      // Use mock data for development
      const mockData = generateMockData(timeRange);
      setUsageData(mockData);
    } finally {
      setLoading(false);
    }
  };

  const generateMockData = (range: string): TokenUsageData[] => {
    const data: TokenUsageData[] = [];
    let days = 7;

    switch (range) {
      case '30d':
        days = 30;
        break;
      case '90d':
        days = 90;
        break;
      default:
        days = 7;
    }

    for (let i = days; i >= 0; i--) {
      const date = subDays(new Date(), i);
      const promptTokens = Math.floor(Math.random() * 50000) + 10000;
      const completionTokens = Math.floor(Math.random() * 30000) + 5000;

      data.push({
        date: format(date, 'MMM dd'),
        promptTokens,
        completionTokens,
        totalTokens: promptTokens + completionTokens
      });
    }

    return data;
  };

  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value);

    // Update date range based on selected time range
    if (value !== 'custom') {
      let fromDate: Date;

      switch (value) {
        case '7d':
          fromDate = subDays(new Date(), 7);
          break;
        case '30d':
          fromDate = subDays(new Date(), 30);
          break;
        case '90d':
          fromDate = subDays(new Date(), 90);
          break;
        default:
          fromDate = subDays(new Date(), 7);
      }

      setDateRange({
        from: fromDate,
        to: new Date()
      });
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Token Usage Over Time</CardTitle>
          <CardDescription>Daily token consumption</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <Skeleton className="w-full h-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <CardTitle>Token Usage Over Time</CardTitle>
            <CardDescription>Daily token consumption</CardDescription>
          </div>
          <div className="flex flex-col md:flex-row gap-2 mt-2 md:mt-0">
            <Select value={timeRange} onValueChange={handleTimeRangeChange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select time range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
                <SelectItem value="custom">Custom range</SelectItem>
              </SelectContent>
            </Select>

            {timeRange === 'custom' && (
              <DatePickerWithRange
                date={dateRange}
                setDate={setDateRange}
              />
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-80">
          {usageData.length === 0 ? (
            <div className="w-full h-full flex items-center justify-center">
              <p className="text-gray-500">No data available for the selected period</p>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={usageData}
                margin={{
                  top: 20,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="promptTokens" name="Prompt Tokens" fill="#8884d8" />
                <Bar dataKey="completionTokens" name="Completion Tokens" fill="#82ca9d" />
              </BarChart>
            </ResponsiveContainer>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
