require('dotenv').config();
const supabase = require('./src/services/supabaseClient');

async function setupAdminTables() {
  console.log('Setting up admin dashboard tables...');
  
  try {
    // First, check if users have is_admin column
    console.log('Checking users table structure...');
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .limit(1);
      
    if (userError) {
      console.error('Error checking users table:', userError);
      return;
    }
    
    console.log('User table sample:', userData);
    
    // Set admin privileges for specified users using update
    console.log('Setting admin privileges for specified users...');
    
    // First, check if the users exist
    const { data: adminUsers, error: findError } = await supabase
      .from('users')
      .select('id, email, name')
      .or('<EMAIL>,<EMAIL>');
      
    if (findError) {
      console.error('Error finding admin users:', findError);
      return;
    }
    
    console.log('Found potential admin users:', adminUsers);
    
    // Now try to update them with is_admin field
    if (adminUsers && adminUsers.length > 0) {
      for (const user of adminUsers) {
        console.log(`Attempting to set admin privileges for ${user.email}...`);
        
        try {
          // Try to update with is_admin field
          const { error: updateError } = await supabase
            .from('users')
            .update({ is_admin: true })
            .eq('id', user.id);
            
          if (updateError) {
            if (updateError.message && updateError.message.includes('column "is_admin" does not exist')) {
              console.log('Need to add is_admin column first');
              
              // Create SQL function to add column if not exists
              const { error: functionError } = await supabase.rpc('create_admin_column');
              
              if (functionError) {
                console.error('Error creating SQL function:', functionError);
                console.log('Will try direct approach with REST API instead');
                
                // For now, let's just note which users should be admins
                console.log(`User ${user.email} (${user.id}) should be an admin`);
              } else {
                console.log('Successfully added is_admin column');
                
                // Try update again
                const { error: retryError } = await supabase
                  .from('users')
                  .update({ is_admin: true })
                  .eq('id', user.id);
                  
                if (retryError) {
                  console.error('Error setting admin privileges after adding column:', retryError);
                } else {
                  console.log(`Successfully set admin privileges for ${user.email}`);
                }
              }
            } else {
              console.error('Error setting admin privileges:', updateError);
            }
          } else {
            console.log(`Successfully set admin privileges for ${user.email}`);
          }
        } catch (err) {
          console.error(`Exception updating user ${user.email}:`, err);
        }
      }
    } else {
      console.log('No admin users found with the specified emails');
    }
    
  } catch (error) {
    console.error('Exception in setup:', error);
  }
}

// Run the setup
setupAdminTables()
  .then(() => {
    console.log('Setup completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
