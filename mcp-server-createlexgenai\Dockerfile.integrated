FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install additional dependencies for JWT and requests
RUN pip install --no-cache-dir \
    pyjwt \
    requests \
    cryptography

# Copy the integrated subscription manager
COPY subscription_manager_integrated.py subscription_manager.py
COPY auth_validator.py .

# Copy compiled Python modules (protects source code)
COPY protected/ ./protected/

# Copy configuration files
COPY config/ ./config/

# Create non-root user for security
RUN useradd -m -u 1000 mcpuser && chown -R mcpuser:mcpuser /app
USER mcpuser

# Expose the MCP server port and health check port
EXPOSE 8000 8001

# Health check that uses your backend API integration
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# Run the integrated subscription-protected MCP server
CMD ["python", "subscription_manager.py"] 