// Load environment variables based on NODE_ENV
try {
  const dotenv = require('dotenv');

  // Check if we're in development or production mode
  const isDevelopment = process.env.NODE_ENV === 'development';

  if (isDevelopment) {
    // Load .env for development (npm run dev)
    console.log('[Environment] Loading .env for development mode');
    dotenv.config();
  } else {
    // Load .env.production for production (npm start)
    console.log('[Environment] Loading .env.production for production mode');
    dotenv.config({ path: '.env.production' });
  }

  console.log(`[Environment] Running in ${isDevelopment ? 'development' : 'production'} mode`);
} catch (err) {
  console.log('dotenv not available, skipping...', err);
}

const express = require('express');
const http = require('http');
const { Server } = require('socket.io');
const cors = require('cors');
const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
// For HTTP requests to Unreal Engine (Node.js 18+ has built-in fetch)
const fetch = globalThis.fetch || require('node-fetch');
const McpBridge = require('./services/mcpBridge');
const TcpBridge = require('./services/tcpBridge');
const aiService = require('./services/aiService');
// Import the global event emitter
const eventEmitter = require('./services/eventEmitter');
// Make the event emitter globally available
global.eventEmitter = eventEmitter;
// Import services
// Use Supabase services if SUPABASE_URL is set, otherwise use file-based services
let authService, subscriptionService;

// Force using Supabase for user data storage
if (process.env.SUPABASE_URL && process.env.SUPABASE_SERVICE_KEY) {
  console.log('Using Supabase for user data storage');
  authService = require('./services/supabaseAuthService');
  subscriptionService = require('./services/supabaseSubscriptionService');
} else {
  console.log('Using file-based storage for user data (fallback)');
  authService = require('./services/authService');
  subscriptionService = require('./services/subscriptionService');
}
const bodyParser = require('body-parser');

// 🚀 Unreal Engine UI Update Function - Uses C++ Lambda Callbacks
async function updateUnrealEngineUI(userEmail, isAuthenticated, hasSubscription) {
  try {
    // Instead of HTTP to localhost, trigger C++ lambda callback through Python
    // This calls the HandleAuthenticationUpdate function directly
    console.log(`🎯 Triggering Unreal Engine UI update via backend: ${userEmail}, auth: ${isAuthenticated}, sub: ${hasSubscription}`);

    // For now, we'll use a simple approach - the Unreal Engine plugin
    // should poll this endpoint or we can implement WebSocket/SSE later

    // Store the latest auth state that Unreal Engine can query
    global.latestAuthState = {
      user_email: userEmail,
      is_authenticated: isAuthenticated,
      has_subscription: hasSubscription,
      timestamp: Date.now()
    };

    console.log('✅ Unreal Engine UI state updated in backend');
    return true;
  } catch (error) {
    console.warn('⚠️ Failed to update Unreal Engine UI state:', error.message);
    return false;
  }
}

// App Setup
const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000', // Use specific origin instead of wildcard
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'X-User-ID', 'x-user-id', 'Access-Control-Allow-Origin', 'Cache-Control', 'Pragma', 'Expires'],
    credentials: true
  }
});

// Direct connection to MCP server for test_unreal_connection
function sendToMcpServer(command) {
  return new Promise((resolve, reject) => {
    console.log(`[Backend] Connecting to MCP server to send: ${JSON.stringify(command)}`);

    try {
      // Use the McpBridge service to send the command
      // This will use TCP instead of WebSockets
      const mcpBridge = McpBridge.getInstance();

      // Format the command for the MCP server
      const formattedCommand = {
        command: 'handshake',
        params: {
          message: 'Testing connection from CreateLex AI Assistant'
        }
      };

      console.log(`[Backend] Sending formatted command via McpBridge: ${JSON.stringify(formattedCommand)}`);

      // Send the command using the McpBridge service
      mcpBridge.sendCommand(formattedCommand, (err, response) => {
        if (err) {
          console.error('[Backend] Error sending command to MCP server:', err);
          reject(new Error(`Failed to connect to Unreal Engine: ${err.message}`));
          return;
        }

        console.log(`[Backend] Received MCP response: ${JSON.stringify(response)}`);

        // Format the response for the client
        const formattedResponse = {
          result: {
            status: response.success ? 'success' : 'error',
            result: response.message || 'Connection successful',
            error: response.error || null
          }
        };

        console.log(`[Backend] Formatted response for client: ${JSON.stringify(formattedResponse)}`);
        resolve(formattedResponse);
      });
    } catch (error) {
      console.error('[Backend] Error sending command to MCP server:', error);
      reject(new Error(`Failed to connect to Unreal Engine: ${error.message}`));
    }
  });
}

// Middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000', // Use specific origin instead of wildcard
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'X-User-ID', 'x-user-id', 'Access-Control-Allow-Origin', 'Cache-Control', 'Pragma', 'Expires'],
  exposedHeaders: ['Content-Type', 'Authorization', 'Accept', 'X-User-ID', 'x-user-id'],
  credentials: true
}));

// Add OPTIONS handling for preflight requests
app.options('*', cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}));

// Add a specific handler for preflight requests
app.use((req, res, next) => {
  if (req.method === 'OPTIONS') {
    res.header('Access-Control-Allow-Origin', process.env.FRONTEND_URL || 'http://localhost:3000');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept, X-User-ID, x-user-id, Access-Control-Allow-Origin, Cache-Control, Pragma, Expires');
    res.header('Access-Control-Allow-Credentials', 'true');
    res.header('Access-Control-Max-Age', '86400'); // 24 hours
    return res.status(204).send();
  }
  next();
});

// --- STRIPE WEBHOOK ROUTE FIRST ---
const stripeWebhookRoute = require('./routes/webhook/stripe');
app.use('/api/webhook/stripe', stripeWebhookRoute);

// --- THEN body parsers ---
app.use(express.json());
app.use(bodyParser.raw({ type: 'application/json' })); // For Stripe webhooks

// Import token usage middleware
const tokenUsageMiddleware = require('./middleware/tokenUsageMiddleware');

// Import rate limiting middleware
const rateLimitMiddleware = require('./middleware/rateLimitMiddleware');

// Import and use the usage tracker middleware
const usageTracker = require('./middleware/usageTracker');
app.use(usageTracker());

// Apply global rate limiting to all requests
app.use(rateLimitMiddleware.globalLimiter);

// Authentication middleware
const authenticateJWT = async (req, res, next) => {
  const authHeader = req.headers.authorization;

  if (authHeader) {
    const token = authHeader.split(' ')[1];

    try {
      // For development purposes, allow any token to be used
      // This is useful for testing with new users
      let user;

      try {
        user = await authService.verifyToken(token);
      } catch (verifyError) {
        console.error('Error verifying token:', verifyError);

        // For development, create a temporary user
        if (process.env.NODE_ENV !== 'production') {
          console.log('Creating temporary user for development');

          // Extract user ID from token if possible
          const decoded = jwt.decode(token);
          const userId = decoded?.sub || decoded?.id || `temp-${Date.now()}`;

          user = {
            id: userId,
            email: decoded?.email || '<EMAIL>',
            name: decoded?.name || 'Temporary User',
            picture: '',
            subscriptionStatus: 'inactive'
          };
        } else {
          // In production, we should enforce proper token verification
          throw verifyError;
        }
      }

      // Check if user exists
      if (!user) {
        console.error('User not found in database');
        return res.status(404).json({ error: 'User not found', code: 'USER_NOT_FOUND' });
      }

      req.user = user;
      next();
    } catch (error) {
      console.error('Authentication error:', error.message);

      // Check if the error is because the user was not found
      if (error.message && (error.message.includes('User not found') || error.message.includes('no rows returned'))) {
        return res.status(404).json({ error: 'User not found', code: 'USER_NOT_FOUND' });
      }

      return res.status(403).json({ error: 'Invalid token' });
    }
  } else {
    return res.status(401).json({ error: 'Authentication required' });
  }
};

// Subscription check middleware
const requireSubscription = async (req, res, next) => {
  try {
    const hasSubscription = await subscriptionService.checkSubscription(req.user.id);

    if (hasSubscription) {
      next();
    } else {
      return res.status(403).json({ error: 'Subscription required' });
    }
  } catch (error) {
    console.error('Error checking subscription:', error);
    return res.status(500).json({ error: 'Failed to check subscription' });
  }
};

// Routes
app.get('/', (req, res) => {
  res.send('AI Webplatform API is running');
});

// Import and use the models routes
const modelsRoutes = require('./routes/models');
app.use('/api', modelsRoutes);

// Import chat access token routes
const chatAccessTokenRoutes = require('./routes/auth/chatAccessToken');

// Authentication routes
app.post('/api/auth/google', rateLimitMiddleware.authLimiter, async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({ error: 'Token is required' });
    }

    // Verify Google token
    const user = await authService.verifyGoogleToken(token);

    // Generate JWT token
    const jwtToken = authService.generateToken(user);

    // 🚀 NEW: Update Unreal Engine UI after successful authentication
    try {
      const hasSubscription = user.subscriptionStatus === 'active';
      await updateUnrealEngineUI(user.email, true, hasSubscription);
      console.log(`✅ Unreal Engine UI updated for user: ${user.email}, subscription: ${hasSubscription}`);
    } catch (uiError) {
      console.warn('⚠️ Failed to update Unreal Engine UI (non-critical):', uiError.message);
      // Don't fail the authentication if UI update fails
    }

    res.json({
      token: jwtToken,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        picture: user.picture,
        subscriptionStatus: user.subscriptionStatus
      }
    });
  } catch (error) {
    console.error('Error authenticating with Google:', error);
    res.status(401).json({ error: 'Authentication failed' });
  }
});

// Sign out route
app.post('/api/auth/signout', authenticateJWT, async (req, res) => {
  try {
    // 🚀 NEW: Update Unreal Engine UI after sign out
    try {
      await updateUnrealEngineUI('', false, false);
      console.log('✅ Unreal Engine UI updated for sign out');
    } catch (uiError) {
      console.warn('⚠️ Failed to update Unreal Engine UI on sign out (non-critical):', uiError.message);
    }

    res.json({ success: true, message: 'Signed out successfully' });
  } catch (error) {
    console.error('Error during sign out:', error);
    res.status(500).json({ error: 'Sign out failed' });
  }
});

// 🎯 Unreal Engine UI State Endpoint
app.get('/api/unreal/auth-state', (req, res) => {
  try {
    const authState = global.latestAuthState || {
      user_email: '',
      is_authenticated: false,
      has_subscription: false,
      timestamp: 0
    };

    console.log('🎯 Unreal Engine queried auth state:', authState);
    res.json(authState);
  } catch (error) {
    console.error('Error getting Unreal Engine auth state:', error);
    res.status(500).json({ error: 'Failed to get auth state' });
  }
});

// Use chat access token routes
app.use('/api/auth', chatAccessTokenRoutes);

// User routes
app.get('/api/user', authenticateJWT, async (req, res) => {
  try {
    console.log(`Getting user by ID: ${req.user.id}`);
    const user = await authService.getUserById(req.user.id);

    if (user) {
      console.log(`User found: ${user.email}`);
      res.json({
        id: user.id,
        email: user.email,
        name: user.name,
        picture: user.picture,
        subscriptionStatus: user.subscriptionStatus
      });
    } else {
      console.log(`User not found with ID: ${req.user.id}`);
      res.status(404).json({ error: 'User not found' });
    }
  } catch (error) {
    console.error(`Error getting user: ${error.message}`);
    res.status(500).json({ error: 'Failed to get user information' });
  }
});

// Subscription routes
app.post('/api/subscription/create-checkout', authenticateJWT, async (req, res) => {
  try {
    console.log('Received checkout request:', req.body);
    console.log('User:', req.user);

    const { successUrl, cancelUrl, planType, couponCode } = req.body;

    if (!successUrl || !cancelUrl) {
      console.log('Missing success or cancel URL');
      return res.status(400).json({ error: 'Success and cancel URLs are required' });
    }

    // Default to 'pro' plan if not specified
    const selectedPlan = planType || 'pro';

    console.log('Creating checkout session with:', {
      userId: req.user.id,
      successUrl,
      cancelUrl,
      planType: selectedPlan,
      couponCode: couponCode || 'none'
    });

    // Use the backend success URL to handle the webhook
    // Make sure we don't have duplicate 'api' in the URL
    const baseUrl = process.env.BACKEND_URL || 'http://localhost:5001';
    const backendSuccessUrl = `${baseUrl}/subscription/success?session_id={CHECKOUT_SESSION_ID}`;

    console.log(`Using backend success URL: ${backendSuccessUrl}`);

    // Use the subscription service to create a checkout session
    const session = await subscriptionService.createCheckoutSession(
      req.user.id,
      backendSuccessUrl,
      cancelUrl,
      selectedPlan,
      couponCode
    );

    console.log('Checkout session created:', {
      sessionId: session.id,
      url: session.url,
      planType: selectedPlan,
      couponApplied: !!couponCode
    });

    res.json({
      sessionId: session.id,
      url: session.url,
      planType: selectedPlan,
      couponApplied: !!couponCode
    });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    res.status(500).json({ error: `Failed to create checkout session: ${error.message}` });
  }
});

app.get('/api/subscription/status', authenticateJWT, async (req, res) => {
  try {
    const hasSubscription = await subscriptionService.checkSubscription(req.user.id);

    // For backward compatibility
    return res.status(200).json({
      hasActiveSubscription: hasSubscription,
      subscriptionTier: hasSubscription ? 'pro' : 'free',
      subscriptionStatus: hasSubscription ? 'active' : 'inactive'
    });
  } catch (error) {
    console.error('Error checking subscription status:', error);
    return res.status(500).json({ error: 'Failed to check subscription status' });
  }
});

// Import and use the subscription check route
const subscriptionCheckRoute = require('./routes/subscription/check');
app.use('/api/subscription/check', subscriptionCheckRoute);

// Import and use the subscription cancel route
const subscriptionCancelRoute = require('./routes/subscription/cancel');
app.use('/api/subscription/cancel', subscriptionCancelRoute);

// Import and use the subscription success route
const subscriptionSuccessRoute = require('./routes/subscription/success');
// Mount the route at both paths to handle potential duplicate 'api' in the URL
app.use('/api/subscription/success', subscriptionSuccessRoute);
app.use('/subscription/success', subscriptionSuccessRoute); // Add this route to handle the URL without duplicate 'api'

// Import and use the subscription plan route
const subscriptionPlanRoute = require('./routes/subscription/plan');
app.use('/api/subscription/plan', subscriptionPlanRoute);

// Import and use the test email route
const testEmailRoute = require('./routes/test/email');
app.use('/api/test/email', testEmailRoute);

// Import and use the admin routes
const adminRoutes = require('./routes/admin');
app.use('/api/admin', rateLimitMiddleware.adminLimiter, adminRoutes);

// Import and use the token routes
const tokenRoutes = require('./routes/tokens');
app.use('/api/tokens', tokenRoutes);

// Import and use the device seat management routes
const deviceRoutes = require('./routes/device');
app.use('/api/device', deviceRoutes);

// Import and use the MCP updates routes
const mcpUpdatesRoutes = require('./routes/mcp-updates');
app.use('/api/mcp-updates', mcpUpdatesRoutes);

// Import and use the MCP validation routes
const mcpValidationRoutes = require('./routes/mcp/validation');
app.use('/api/mcp', mcpValidationRoutes);
app.use('/api/mcp', require('./routes/mcp/mcp-config'));
app.use('/api/mcp', require('./routes/mcp/server-files')); // Re-enabled as fallback for private GitHub repos

// Check if user has premium subscription
app.get('/api/subscription/is-premium', authenticateJWT, async (req, res) => {
  try {
    // Get the user's subscription details
    const user = await authService.getUserById(req.user.id);

    if (!user || !user.subscriptionId) {
      return res.json({ isPremium: false });
    }

    // Check if the user has an active subscription
    if (user.subscriptionStatus !== 'active') {
      return res.json({ isPremium: false });
    }

    // Get the subscription details from Stripe
    const subscriptionDetails = await subscriptionService.getSubscriptionDetails(user.subscriptionId);

    // Check if the subscription is for the Pro plan ($30/month)
    // The Pro plan has the price ID: price_1RGQdDJpp8exsOhC0spC2NGd
    const isPremium = subscriptionDetails &&
                     subscriptionDetails.items &&
                     subscriptionDetails.items.data &&
                     subscriptionDetails.items.data.length > 0 &&
                     subscriptionDetails.items.data[0].price &&
                     subscriptionDetails.items.data[0].price.id === process.env.STRIPE_PRICE_ID_PRO;

    return res.json({
      isPremium,
      plan: isPremium ? 'pro' : 'basic',
      subscriptionId: user.subscriptionId
    });
  } catch (error) {
    console.error('Error checking premium status:', error);
    return res.status(500).json({ error: 'Failed to check premium status' });
  }
});

// Update subscription status (for development and testing)
app.post('/api/subscription/update', authenticateJWT, async (req, res) => {
  try {
    const { status, customerId, subscriptionId } = req.body;

    if (status === undefined) {
      return res.status(400).json({ error: 'Subscription status is required' });
    }

    console.log(`Updating subscription for user ${req.user.id} to ${status ? 'active' : 'inactive'}`);

    // Update the user's subscription status
    const updated = await authService.updateSubscription(
      req.user.id,
      status ? 'active' : 'inactive',
      status ? (subscriptionId || 'test-subscription-id') : null
    );

    // If customerId is provided, update the user's Stripe customer ID
    if (customerId && updated) {
      console.log(`Updating Stripe customer ID for user ${req.user.id} to ${customerId}`);
      await authService.updateStripeCustomerId(req.user.id, customerId);
    }

    if (updated) {
      console.log(`Updated subscription status for user ${req.user.id} to ${status ? 'active' : 'inactive'}`);
      res.json({
        success: true,
        hasActiveSubscription: status,
        customerId: customerId || null,
        subscriptionId: status ? (subscriptionId || 'test-subscription-id') : null
      });
    } else {
      console.log(`Failed to update subscription for user ${req.user.id} - user not found`);
      res.status(404).json({ error: 'User not found' });
    }
  } catch (error) {
    console.error('Error updating subscription status:', error);
    res.status(500).json({ error: 'Failed to update subscription status' });
  }
});

// Find Stripe customer by email
app.get('/api/subscription/find-customer', authenticateJWT, async (req, res) => {
  try {
    console.log(`Finding Stripe customer for user ${req.user.id}`);
    const user = await authService.getUserById(req.user.id);

    if (!user || !user.email) {
      console.log(`User email not found for ID: ${req.user.id}`);
      return res.status(400).json({ error: 'User email not found' });
    }

    console.log(`Looking up Stripe customer for email: ${user.email}`);
    // Find customer by email
    const customer = await subscriptionService.findCustomerByEmail(user.email);

    if (customer) {
      console.log(`Found Stripe customer: ${customer.id}`);
      // Update user with Stripe customer ID
      await authService.updateStripeCustomerId(req.user.id, customer.id);

      // Find active subscriptions
      console.log(`Looking up active subscriptions for customer: ${customer.id}`);
      const subscriptions = await subscriptionService.findActiveSubscriptions(customer.id);

      if (subscriptions.length > 0) {
        // Update user with subscription ID and status
        const subscription = subscriptions[0];
        console.log(`Found active subscription: ${subscription.id}`);
        await authService.updateSubscription(req.user.id, 'active', subscription.id);

        res.json({
          success: true,
          customer: {
            id: customer.id,
            email: customer.email,
            name: customer.name
          },
          hasActiveSubscription: true,
          subscription: {
            id: subscription.id,
            status: subscription.status,
            currentPeriodEnd: new Date(subscription.current_period_end * 1000).toISOString()
          }
        });
      } else {
        console.log(`No active subscriptions found for customer: ${customer.id}`);
        res.json({
          success: true,
          customer: {
            id: customer.id,
            email: customer.email,
            name: customer.name
          },
          hasActiveSubscription: false,
          subscription: null
        });
      }
    } else {
      console.log(`No Stripe customer found for email: ${user.email}`);
      res.json({
        success: false,
        message: 'No Stripe customer found for this email'
      });
    }
  } catch (error) {
    console.error('Error finding Stripe customer:', error);
    res.status(500).json({ error: 'Failed to find Stripe customer' });
  }
});

// We're now using the McpBridge service for all communication with Unreal Engine
// The McpBridge service uses WebSockets to communicate with the MCP server
// This is more reliable than the TCP socket connection we were using before

// Initialize the McpBridge service
const mcpBridge = McpBridge.getInstance();

// Function to check Unreal Engine connection status
async function checkUnrealEngineConnection() {
  try {
    console.log('[Backend] Checking Unreal Engine connection status via McpBridge');

    // Use the McpBridge service to check the connection
    const result = await mcpBridge.sendCommand({
      command: 'test_unreal_connection'
    });

    console.log(`[Backend] Unreal Engine connection check result: ${JSON.stringify(result)}`);
    return { connected: result && result.success === true };
  } catch (error) {
    console.error('[Backend] Error checking Unreal Engine connection:', error);
    return { connected: false, error: error.message };
  }
}

// No need for these helper functions anymore since we're using WebSockets
// The McpBridge service handles all the connection management, heartbeats, etc.

// Make the McpBridge instance globally available
global.mcpBridge = mcpBridge;

// Function to send a command to Unreal Engine
// Make it available globally
global.sendToUnrealEngine = function sendToUnrealEngine(command) {
  return new Promise((resolve, reject) => {
    console.log(`[Backend] Sending command to Unreal Engine via McpBridge: ${JSON.stringify(command)}`);

    // Format the command for the McpBridge service
    let formattedCommand;

    if (command.type === 'handshake') {
      // Special case for handshake
      formattedCommand = {
        command: 'test_unreal_connection'
      };
    } else {
      // For other commands, convert from the old format to the new format
      formattedCommand = {
        command: command.type,
        params: { ...command }
      };

      // Remove the type from params to avoid duplication
      if (formattedCommand.params.type) {
        delete formattedCommand.params.type;
      }
    }

    console.log(`[Backend] Formatted command for McpBridge: ${JSON.stringify(formattedCommand)}`);

    // Use the McpBridge service to send the command
    mcpBridge.sendCommand(formattedCommand, (error, response) => {
      if (error) {
        console.error(`[Backend] Error executing command via McpBridge: ${error.message}`);
        reject(error);
        return;
      }

      console.log(`[Backend] Command executed successfully via McpBridge: ${JSON.stringify(response)}`);

      // Format the response to match the expected format
      const formattedResponse = {
        success: true,
        ...response
      };

      resolve(formattedResponse);
    });
  });
}

// Chat message endpoint
app.post('/api/chat/message', authenticateJWT, rateLimitMiddleware.chatLimiter, tokenUsageMiddleware.checkUsageLimits, tokenUsageMiddleware.applyProgressiveThrottling, tokenUsageMiddleware.optimizePrompts, async (req, res) => {
  // Check if we should use purchased tokens
  const usePurchasedTokens = req.useTokenBalance === true;
  try {
    console.log(`[Backend] Received chat message: ${JSON.stringify(req.body)}`);

    const { message, modelId } = req.body;

    if (!message) {
      return res.status(400).json({ error: 'Message is required' });
    }

    console.log(`[Backend] Processing message: "${message}", modelId: ${modelId || 'rule-based'}`);

    // Special handling for test_unreal_connection
    if (message && message.trim().toLowerCase() === 'test_unreal_connection') {
      console.log('[Backend] DETECTED test_unreal_connection command');

      try {
        // Send to Unreal Engine directly
        const response = await sendToUnrealEngine({
          type: 'handshake',
          message: 'Testing connection from CreateLex AI Assistant'
        });

        console.log(`[Backend] Got Unreal Engine response: ${JSON.stringify(response)}`);

        // Format nice message for client
        let responseMessage = '';

        if (response.success) {
          responseMessage = `Connection test SUCCESS: Successfully connected to Unreal Engine`;
        } else {
          responseMessage = `Connection test FAILED: ${response.error || 'Unknown error'}`;
        }

        // Emit connection status to all clients
        io.emit('unrealConnectionStatus', { connected: response.success === true });

        return res.json({ message: responseMessage });
      } catch (error) {
        console.error('[Backend] Error handling test connection:', error);
        return res.status(500).json({ error: `Error testing connection: ${error.message}` });
      }
    }

    // Special handling for create a cube
    if (message && message.trim().toLowerCase() === 'create a cube') {
      console.log('[Backend] DETECTED create a cube command');

      try {
        // Generate a unique ID for the cube
        const cubeId = `Cube_${Date.now()}`;
        const apiKey = process.env.UNREAL_API_KEY || 'web_auth';

        // Send to Unreal Engine directly
        const response = await sendToUnrealEngine({
          type: 'spawn',
          actor_class: 'Cube',
          location: [0, 0, 100],
          rotation: [0, 0, 0],
          scale: [1, 1, 1],
          actor_label: cubeId
          // API key will be added by sendToUnrealEngine
        });

        console.log(`[Backend] Got Unreal Engine response: ${JSON.stringify(response)}`);

        // Format nice message for client
        let responseMessage = '';

        if (response.success) {
          responseMessage = `Successfully created a cube with ID: ${cubeId}`;
        } else {
          responseMessage = `Failed to create a cube: ${response.error || 'Unknown error'}`;
        }

        return res.json({ message: responseMessage });
      } catch (error) {
        console.error('[Backend] Error handling cube creation:', error);
        return res.status(500).json({ error: `Error creating cube: ${error.message}` });
      }
    }

    // Special handling for create a sphere
    if (message && message.trim().toLowerCase() === 'create a sphere') {
      console.log('[Backend] DETECTED create a sphere command');

      try {
        // Generate a unique ID for the sphere
        const sphereId = `Sphere_${Date.now()}`;
        const apiKey = process.env.UNREAL_API_KEY || 'web_auth';

        // Send to Unreal Engine directly
        const response = await sendToUnrealEngine({
          type: 'spawn',
          actor_class: 'Sphere',
          location: [0, 0, 100],
          rotation: [0, 0, 0],
          scale: [1, 1, 1],
          actor_label: sphereId
          // API key will be added by sendToUnrealEngine
        });

        console.log(`[Backend] Got Unreal Engine response: ${JSON.stringify(response)}`);

        // Format nice message for client
        let responseMessage = '';

        if (response.success) {
          responseMessage = `Successfully created a sphere with ID: ${sphereId}`;
        } else {
          responseMessage = `Failed to create a sphere: ${response.error || 'Unknown error'}`;
        }

        return res.json({ message: responseMessage });
      } catch (error) {
        console.error('[Backend] Error handling sphere creation:', error);
        return res.status(500).json({ error: `Error creating sphere: ${error.message}` });
      }
    }

    // For other commands, use the AI service and TcpBridge
    try {
      // Create a new McpBridge instance to handle the message
      const mcpBridgeInstance = McpBridge.getInstance();

      console.log(`[Backend] Calling mcpBridge.sendMessage with: "${message}", model: ${modelId || 'rule-based'}`);

      // Process the message using McpBridge which handles both AI and Unreal Engine communication
      const response = await mcpBridgeInstance.sendMessage(message, modelId || 'rule-based');

      console.log(`[Backend] McpBridge response: ${response}`);

      // Send the response back to the client
      res.json({ message: response });
    } catch (error) {
      console.error('[Backend] Error in mcpBridge processing:', error);
      res.status(500).json({ error: `Error: ${error.message}` });
    }

  } catch (error) {
    console.error('[Backend] Error processing message:', error);
    res.status(500).json({ error: `Error: ${error.message}` });
  }
});

// WebSocket for frontend communication
io.on('connection', (socket) => {
  console.log(`Client connected: ${socket.id}`);

  // Authenticate socket connection
  socket.on('authenticate', async (data) => {
    try {
      const { token } = data;

      if (!token) {
        socket.emit('error', { message: 'Authentication token is required' });
        return;
      }

      // Verify token
      console.log('Authenticating socket with token');
      const user = await authService.verifyToken(token);
      console.log(`Socket authenticated for user: ${user.id}`);

      // Check subscription status
      console.log(`Checking subscription for user: ${user.id}`);
      const hasSubscription = await subscriptionService.checkSubscription(user.id);
      console.log(`Subscription status: ${hasSubscription ? 'active' : 'inactive'}`);

      if (!hasSubscription) {
        socket.emit('error', { message: 'Subscription required' });
        return;
      }

      // Store user ID in socket
      socket.userId = user.id;
      socket.emit('authenticated', { success: true });
      console.log(`Socket ${socket.id} authenticated for user ${user.id}`);

      // Set up listener for token balance updates
      const tokenBalanceUpdatedListener = (data) => {
        // Only send updates to the correct user
        if (data.userId === socket.userId) {
          console.log(`Sending token balance update to user ${socket.userId}: ${data.balance}`);
          socket.emit('tokenBalanceUpdated', {
            balance: data.balance,
            timestamp: new Date().toISOString()
          });
        }
      };

      // Add the listener to the global event emitter
      eventEmitter.on('tokenBalanceUpdated', tokenBalanceUpdatedListener);

      // Remove the listener when the socket disconnects
      socket.on('disconnect', () => {
        eventEmitter.removeListener('tokenBalanceUpdated', tokenBalanceUpdatedListener);
      });

      // Check Unreal Engine connection status and send to client
      try {
        const status = await checkUnrealEngineConnection();
        socket.emit('unrealConnectionStatus', status);
      } catch (error) {
        console.error('[Backend] Error checking Unreal Engine connection after auth:', error);
      }
    } catch (error) {
      console.error('Socket authentication error:', error);
      socket.emit('error', { message: 'Authentication failed' });
    }
  });

  // Handle message from client
  socket.on('sendMessage', async (data) => {
    console.log(`[Backend] Received sendMessage event with data: ${JSON.stringify(data)}`);

    // Check if socket is authenticated
    if (!socket.userId) {
      console.log(`[Backend] Authentication required for sendMessage. Socket ID: ${socket.id}, Has userId: ${!!socket.userId}`);
      socket.emit('error', { message: 'Authentication required' });
      return;
    }

    // Apply rate limiting
    const rateLimitResult = rateLimitMiddleware.socketRateLimiter.checkLimit(socket.userId, socket.id);
    if (rateLimitResult.limited) {
      console.log(`[Backend] Rate limit exceeded for user ${socket.userId}. Remaining time: ${Math.ceil(rateLimitResult.remainingMs / 1000)}s`);
      socket.emit('error', {
        message: rateLimitResult.message,
        retryAfter: Math.ceil(rateLimitResult.remainingMs / 1000)
      });
      return;
    }

    try {
      console.log(`[Backend] Processing message from client: ${JSON.stringify(data)}`);

      const { message, modelId } = data;

      // Check token usage limits
      const tokenUsageService = require('./services/tokenUsageService');
      const usageStatus = await tokenUsageService.checkUsageLimits(socket.userId);

      if (usageStatus.hasExceededLimits) {
        console.log(`[Backend] User ${socket.userId} has exceeded token usage limits`);

        if (usageStatus.dailyUsage.exceeded) {
          socket.emit('error', {
            message: `You've reached your daily limit of ${usageStatus.dailyUsage.limit.toLocaleString()} tokens. This will reset tomorrow.`,
            usageStatus: {
              daily: {
                used: usageStatus.dailyUsage.used,
                limit: usageStatus.dailyUsage.limit,
                percentage: Math.round(usageStatus.dailyUsage.percentage)
              },
              monthly: {
                used: usageStatus.monthlyUsage.used,
                limit: usageStatus.monthlyUsage.limit,
                percentage: Math.round(usageStatus.monthlyUsage.percentage)
              },
              plan: usageStatus.plan
            },
            upgradeOptions: usageStatus.plan === 'basic' ? {
              suggestUpgrade: true,
              currentPlan: 'basic',
              upgradePlan: 'pro',
              benefits: 'Upgrade to Pro for double the daily token limit.'
            } : null
          });
          return;
        }

        if (usageStatus.monthlyUsage.exceeded) {
          socket.emit('error', {
            message: `You've reached your monthly limit of ${usageStatus.monthlyUsage.limit.toLocaleString()} tokens. This will reset on the 1st of next month.`,
            usageStatus: {
              daily: {
                used: usageStatus.dailyUsage.used,
                limit: usageStatus.dailyUsage.limit,
                percentage: Math.round(usageStatus.dailyUsage.percentage)
              },
              monthly: {
                used: usageStatus.monthlyUsage.used,
                limit: usageStatus.monthlyUsage.limit,
                percentage: Math.round(usageStatus.monthlyUsage.percentage)
              },
              plan: usageStatus.plan
            },
            upgradeOptions: usageStatus.plan === 'basic' ? {
              suggestUpgrade: true,
              currentPlan: 'basic',
              upgradePlan: 'pro',
              benefits: 'Upgrade to Pro for double the monthly token limit.'
            } : null
          });
          return;
        }
      }

      // Apply progressive throttling
      if (usageStatus.monthlyUsage.percentage > 90) {
        // Over 90% of limit - apply strong throttling
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Send warning message with usage info
        socket.emit('message', {
          role: 'system',
          content: `You're approaching your monthly limit (${Math.round(usageStatus.monthlyUsage.percentage)}% used). Responses may be delayed.`,
          usageStatus: {
            used: usageStatus.monthlyUsage.used,
            limit: usageStatus.monthlyUsage.limit,
            percentage: Math.round(usageStatus.monthlyUsage.percentage),
            plan: usageStatus.plan
          }
        });

        // Add upgrade suggestion for basic plan users
        if (usageStatus.plan === 'basic') {
          socket.emit('message', {
            role: 'system',
            content: "Consider upgrading to Pro for double the token limit.",
            isUpgradeSuggestion: true
          });
        }
      } else if (usageStatus.monthlyUsage.percentage > 75) {
        // Over 75% of limit - apply medium throttling
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Send warning message with usage info
        socket.emit('message', {
          role: 'system',
          content: `You're using your subscription at a high rate (${Math.round(usageStatus.monthlyUsage.percentage)}% of monthly limit). Slight delays may occur.`,
          usageStatus: {
            used: usageStatus.monthlyUsage.used,
            limit: usageStatus.monthlyUsage.limit,
            percentage: Math.round(usageStatus.monthlyUsage.percentage),
            plan: usageStatus.plan
          }
        });

        // Add upgrade suggestion for basic plan users
        if (usageStatus.plan === 'basic') {
          socket.emit('message', {
            role: 'system',
            content: "Upgrade to Pro for higher limits and faster responses.",
            isUpgradeSuggestion: true
          });
        }
      } else if (usageStatus.monthlyUsage.percentage > 50) {
        // Over 50% of limit - apply light throttling
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Send info message with usage info
        socket.emit('message', {
          role: 'system',
          content: `You've used ${Math.round(usageStatus.monthlyUsage.percentage)}% of your monthly token limit.`,
          usageStatus: {
            used: usageStatus.monthlyUsage.used,
            limit: usageStatus.monthlyUsage.limit,
            percentage: Math.round(usageStatus.monthlyUsage.percentage),
            plan: usageStatus.plan
          },
          isUsageInfo: true
        });
      }

      // Optimize prompt
      const optimizedMessage = tokenUsageService.optimizePrompt(message, usageStatus.maxRequestLength);
      console.log(`[Backend] Extracted message: "${message}", modelId: ${modelId || 'default'}`);

      // Send progress updates to the client
      const sendProgress = (stage, details = []) => {
        console.log('[Backend] Sending progress update to client:', stage, details);
        socket.emit('messageResponse', { progress: stage, details });
      };

      // Initial progress update
      sendProgress('analyzing', ['Analyzing your request...']);

      // Check if the message contains keywords related to creating objects
      const isCreationRequest = message && typeof message === 'string' && (
                               message.toLowerCase().includes('create') ||
                               message.toLowerCase().includes('make') ||
                               message.toLowerCase().includes('add')
                               );

      // Set a timeout for the generating stage
      setTimeout(() => {
        if (isCreationRequest) {
          sendProgress('generating', [
            'Analyzing your request...',
            'Generating Python script...'
          ]);
        } else {
          sendProgress('generating', [
            'Analyzing your request...',
            'Preparing response...'
          ]);
        }
      }, 1000);

      // Set a timeout for the executing stage for creation requests
      if (isCreationRequest) {
        setTimeout(() => {
          sendProgress('executing', [
            'Analyzing your request...',
            'Generating Python script...',
            'Executing in Unreal Engine...'
          ]);
        }, 2000);
      }

      // Create a new McpBridge instance to handle the message
      const mcpBridgeInstance = McpBridge.getInstance();

      // Set the user ID in the McpBridge instance for token tracking
      mcpBridgeInstance.userId = socket.userId;

      console.log(`[Backend] Calling mcpBridge.sendMessage with: "${optimizedMessage}", model: ${modelId || 'gemini-flash'}, userId: ${socket.userId}`);

      // Process the message using McpBridge which handles both AI and Unreal Engine communication
      // This is the central point for all command processing - no hardcoded functions
      const response = await mcpBridgeInstance.sendMessage(optimizedMessage, modelId || 'gemini-flash');

      console.log(`[Backend] McpBridge response: ${response}`);

      // Send a finalizing progress update
      setTimeout(() => {
        if (isCreationRequest) {
          sendProgress('finalizing', [
            'Analyzing your request...',
            'Generating Python script...',
            'Executing in Unreal Engine...',
            'Finalizing response...'
          ]);
        } else {
          sendProgress('finalizing', [
            'Analyzing your request...',
            'Preparing response...',
            'Finalizing response...'
          ]);
        }
      }, 500);

      // Wait a moment before sending the final response
      setTimeout(() => {
        // Send the response back to the client
        socket.emit('messageResponse', { message: response });
      }, 1000);
    } catch (error) {
      console.error('[Backend] Error in message processing:', error);

      // Special handling for timeout errors with Python scripts
      if (error.message === 'Response timeout') {
        console.log('[Backend] Timeout error detected, but the operation might have completed successfully');

        // Send a progress update to indicate timeout but possible success
        socket.emit('messageResponse', {
          progress: 'timeout-success',
          details: [
            'Analyzing your request...',
            'Generating Python script...',
            'Executing in Unreal Engine...',
            'Operation taking longer than expected...'
          ]
        });

        // Extract object name from the error context if possible
        let objectName = 'object';
        try {
          // Try to extract the object name from the error context
          if (error.context && error.context.command && error.context.command.params && error.context.command.params.script) {
            const script = error.context.command.params.script;
            const actorLabelMatch = script.match(/set_actor_label\("([^"]+)"\)/i);
            if (actorLabelMatch && actorLabelMatch[1]) {
              objectName = actorLabelMatch[1];
            }
          }
        } catch (e) {
          console.log('[Backend] Error extracting object name from timeout context:', e);
        }

        // Wait a moment before sending the final response
        setTimeout(() => {
          // Check if the error context contains information about materials
          let materialIssue = false;
          let materialName = '';
          let objectType = 'object';

          try {
            // Try to extract material and object information from the error context
            if (error.context && error.context.command && error.context.command.params && error.context.command.params.script) {
              const script = error.context.command.params.script;

              // Check if the script mentions materials
              if (script.includes('material') || script.includes('Material')) {
                materialIssue = true;

                // Try to extract material name
                const materialMatch = script.match(/create_asset\("([^"]+)_Material"/i);
                if (materialMatch && materialMatch[1]) {
                  materialName = materialMatch[1];
                }

                // Try to extract object type
                if (script.includes('Cube')) objectType = 'cube';
                else if (script.includes('Sphere')) objectType = 'sphere';
                else if (script.includes('Cylinder')) objectType = 'cylinder';
                else if (script.includes('Cone')) objectType = 'cone';
              }
            }
          } catch (e) {
            console.log('[Backend] Error extracting material info from timeout context:', e);
          }

          // Create a more dynamic response based on the context
          let responseMessage = '';

          if (materialIssue) {
            // If there's a material issue, provide a more specific response
            const colorMatch = materialName.match(/(Red|Green|Blue|Yellow|Purple|Orange|Black|White|Gray|Grey)/i);
            const color = colorMatch ? colorMatch[1].toLowerCase() : '';

            responseMessage = `I've created the ${objectName} in Unreal Engine, but there might be an issue with the ${color ? color + ' ' : ''}material. `;
            responseMessage += `The object should still appear in the scene, but it might not have the correct material applied.`;
          } else {
            // Default response for non-material issues
            responseMessage = `I've created the ${objectName} in Unreal Engine. It might take a moment to appear in the scene.`;
          }

          socket.emit('messageResponse', {
            message: responseMessage,
            warning: materialIssue // Set warning flag if there's a material issue
          });
        }, 1000);
      } else {
        // For other errors, send an error message with details
        socket.emit('error', {
          message: `Error: ${error.message}`,
          details: error.context ? JSON.stringify(error.context) : undefined
        });
      }
    }
  });

  socket.on('disconnect', () => {
    console.log(`Client disconnected: ${socket.id}`);
    // Reset rate limit for this socket
    rateLimitMiddleware.socketRateLimiter.resetLimit(socket.userId, socket.id);
  });
});

// Create health check endpoint
app.get('/health', (_req, res) => {
  res.json({ status: 'ok' });
});

// This is a duplicate function declaration - removing it

// Test endpoint for token tracking (no authentication required)
app.post('/api/test/token-usage/track', async (req, res) => {
  try {
    const { userId, modelId, promptTokens, completionTokens, requestType } = req.body;

    // Validate required fields
    if (!userId || !modelId || promptTokens === undefined || completionTokens === undefined) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    console.log(`[TEST] Tracking token usage: ${promptTokens} prompt, ${completionTokens} completion for user ${userId}`);

    // Check if user has exceeded their usage limits
    const tokenUsageService = require('./services/tokenUsageService');
    const usageStatus = await tokenUsageService.checkUsageLimits(userId);

    // Flag to determine if we should use purchased tokens
    let usePurchasedTokens = false;

    // If user has exceeded limits, check for purchased tokens
    if (usageStatus.hasExceededLimits) {
      console.log(`[TEST] User ${userId} has exceeded usage limits, checking for purchased tokens`);

      // Check if user has purchased tokens
      const tokenPurchaseService = require('./services/tokenPurchaseService');
      const tokenBalance = await tokenPurchaseService.getUserTokenBalance(userId);

      // If user has purchased tokens, use them
      if (tokenBalance && tokenBalance.balance > 0) {
        console.log(`[TEST] User ${userId} has ${tokenBalance.balance} purchased tokens available, using them`);
        usePurchasedTokens = true;
      } else {
        console.log(`[TEST] User ${userId} has no purchased tokens available`);
      }
    }

    // Track token usage with the usePurchasedTokens flag
    const result = await tokenUsageService.trackTokenUsage(
      userId,
      modelId,
      promptTokens,
      completionTokens,
      requestType || 'chat',
      usePurchasedTokens // Pass the flag to indicate whether to use purchased tokens
    );

    if (!result) {
      return res.status(500).json({ error: 'Failed to track token usage' });
    }

    // Get updated token balance if we used purchased tokens
    let updatedBalance = null;
    if (usePurchasedTokens) {
      const tokenPurchaseService = require('./services/tokenPurchaseService');
      const tokenBalance = await tokenPurchaseService.getUserTokenBalance(userId);
      updatedBalance = tokenBalance.balance;
    }

    res.json({
      success: true,
      message: `Successfully tracked ${promptTokens + completionTokens} tokens for user ${userId}`,
      recordId: result.id,
      usedPurchasedTokens: usePurchasedTokens,
      updatedBalance: updatedBalance
    });
  } catch (error) {
    console.error('[TEST] Error tracking token usage:', error);
    res.status(500).json({ error: 'Failed to track token usage' });
  }
});

// Start the server
const PORT = process.env.PORT || 5001;
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);

  // Set up periodic connection check (every 10 seconds)
  setInterval(async () => {
    try {
      // Check the connection status using the McpBridge service
      const status = await checkUnrealEngineConnection();
      io.emit('unrealConnectionStatus', status);
      console.log(`[Backend] Emitting connection status: ${status.connected ? 'connected' : 'disconnected'}`);
    } catch (error) {
      console.error('[Backend] Error in periodic connection check:', error);
      io.emit('unrealConnectionStatus', { connected: false, error: error.message });
    }
  }, 10000); // 10 seconds
});

process.on('SIGINT', () => {
  console.log('Shutting down server...');
  server.close();
  process.exit(0);
});