# Server Configuration
PORT=3000
API_URL=http://localhost:5001

# MCP Server Connection
MCP_SERVER_URL=ws://127.0.0.1:9877
UNREAL_ENGINE_HOST=127.0.0.1
UNREAL_ENGINE_PORT=9877
UNREAL_API_KEY=your_default_api_key

# Authentication
GOOGLE_CLIENT_ID=your_google_client_id
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id

# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_supabase_service_key
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# JWT Configuration
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d

# Development Mode
USE_MOCK_USER=false
NEXT_PUBLIC_USE_MOCK_USER=false
NODE_ENV=development

# Subscription/Billing
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
STRIPE_PRICE_ID=your_stripe_price_id

# AI Model API Keys
XAI_API_KEY=your_xai_api_key
OPENAI_API_KEY=your_openai_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key
GOOGLE_API_KEY=your_google_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
GROQ_API_KEY=your_groq_api_key
DATABASE_URL=postgresql://username:password@host:port/database
