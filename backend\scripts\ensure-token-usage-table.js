// Script to ensure the token_usage table exists in the database
require('dotenv').config();
const supabase = require('../src/services/supabaseClient');

async function ensureTokenUsageTable() {
  try {
    console.log('Checking if token_usage table exists...');
    
    // Check if the table exists
    const { data, error } = await supabase
      .from('token_usage')
      .select('id')
      .limit(1);
    
    if (error && error.code === '42P01') {
      console.log('token_usage table does not exist. Creating it...');
      
      // Create the table
      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS token_usage (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID NOT NULL,
          model_id TEXT NOT NULL,
          prompt_tokens INTEGER NOT NULL,
          completion_tokens INTEGER NOT NULL,
          total_tokens INTEGER NOT NULL,
          request_type TEXT NOT NULL,
          subscription_plan TEXT,
          timestamp TIMESTAMPTZ DEFAULT NOW(),
          FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE
        );
        
        -- Add indexes for better query performance
        CREATE INDEX IF NOT EXISTS idx_token_usage_user_id ON token_usage(user_id);
        CREATE INDEX IF NOT EXISTS idx_token_usage_timestamp ON token_usage(timestamp);
        CREATE INDEX IF NOT EXISTS idx_token_usage_model_id ON token_usage(model_id);
      `;
      
      // Execute the SQL using the Supabase REST API
      const { error: createError } = await supabase.rpc('exec_sql', { sql: createTableSQL });
      
      if (createError) {
        console.error('Error creating token_usage table:', createError);
        
        // Alternative approach: use the migration file
        console.log('Trying alternative approach using migration file...');
        const fs = require('fs');
        const path = require('path');
        
        const migrationPath = path.join(__dirname, '../migrations/token_usage_table.sql');
        const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
        
        // Execute the migration SQL
        const { error: migrationError } = await supabase.rpc('exec_sql', { sql: migrationSQL });
        
        if (migrationError) {
          console.error('Error executing migration SQL:', migrationError);
          return false;
        }
        
        console.log('Successfully created token_usage table using migration file');
        return true;
      }
      
      console.log('Successfully created token_usage table');
      return true;
    } else if (error) {
      console.error('Error checking token_usage table:', error);
      return false;
    } else {
      console.log('token_usage table already exists');
      return true;
    }
  } catch (error) {
    console.error('Error ensuring token_usage table:', error);
    return false;
  }
}

// Run the function
ensureTokenUsageTable()
  .then(success => {
    console.log(`Script completed with ${success ? 'success' : 'failure'}`);
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
