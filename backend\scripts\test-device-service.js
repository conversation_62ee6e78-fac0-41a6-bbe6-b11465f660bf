require('dotenv').config();
const deviceSeatService = require('../src/services/deviceSeatService');
const subscriptionService = require('../src/services/subscriptionService');

async function testDeviceService() {
  console.log('🧪 Testing deviceSeatService directly...');
  
  const userId = '5af3ff0f-0ccb-41a2-8dba-3e460aa9bd72'; // The user we inserted device for
  
  try {
    console.log(`\n🔍 Testing getUserDevices for user: ${userId}`);
    const devices = await deviceSeatService.getUserDevices(userId);
    console.log('✅ Devices found:', devices);
    
    console.log('\n🔍 Testing subscriptionService...');
    const subscription = await subscriptionService.checkSubscription(userId);
    console.log('📋 Subscription:', subscription);
    
    const seatLimit = deviceSeatService.SEAT_LIMITS[subscription?.plan || 'basic'] || 2;
    console.log('💺 Seat limit:', seatLimit);
    
    console.log('\n📊 Expected API response:');
    const expectedResponse = {
      devices,
      seatLimit,
      activeCount: devices.filter(d => d.is_active).length
    };
    
    console.log(JSON.stringify(expectedResponse, null, 2));
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testDeviceService(); 