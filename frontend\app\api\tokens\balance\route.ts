import { NextRequest, NextResponse } from 'next/server';
import { apiRequest } from '../../../../lib/api-client';

// Completely bypass Supabase during build time
const isBuildTime = process.env.NODE_ENV === 'production' && typeof window === 'undefined';
let supabase: any = null;

// Only import and initialize Supabase if we're not in build time
if (!isBuildTime) {
  try {
    // Dynamic import to prevent build-time evaluation
    const { createClient } = require('@supabase/supabase-js');
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

    if (supabaseUrl && supabaseAnonKey) {
      supabase = createClient(supabaseUrl, supabaseAnonKey);
      console.log('[Supabase] Client initialized successfully');
    } else {
      console.log('[Supabase] Missing URL or key, client not initialized');
    }
  } catch (error) {
    console.error('[Supabase] Error initializing client:', error);
  }
}

export const dynamic = 'force-dynamic'; // Completely disable caching for this route
export const fetchCache = 'force-no-store'; // Ensure fetch requests are not cached

export async function GET(req: NextRequest) {
  // Default token balance response
  const defaultBalance = {
    user_id: 'unknown',
    balance: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  try {
    console.log('[API] Token balance endpoint called');

    // During build time or if Supabase is not initialized, return default balance
    if (isBuildTime || !supabase) {
      console.log('[API] Build-time environment or Supabase not initialized, returning default balance');
      return NextResponse.json(defaultBalance, { status: 200 });
    }

    // Get the authorization header
    const authHeader = req.headers.get('authorization');

    // Get the user ID from the x-user-id header
    const userIdHeader = req.headers.get('x-user-id');

    // Get the URL parameters
    const url = new URL(req.url);
    const userIdParam = url.searchParams.get('userId');

    console.log(`[API] Authorization header: ${authHeader ? 'present' : 'missing'}`);
    console.log(`[API] User ID header: ${userIdHeader}`);
    console.log(`[API] User ID param: ${userIdParam}`);

    // If we have a user ID from the header or URL parameter, we can proceed even without an auth token
    if (!authHeader && !userIdHeader && !userIdParam) {
      console.log('[API] No authorization header or user ID provided');
      return NextResponse.json(
        {
          ...defaultBalance,
          error: 'No authorization header or user ID provided'
        },
        { status: 200 }
      );
    }

    // Extract the token if available
    const token = authHeader ? authHeader.replace('Bearer ', '') : 'dummy-token';

    // Use the user ID from the header or URL parameter if available
    let userId = null;

    // Get user from token if available
    let user: any = null;

    if (token !== 'dummy-token') {
      const { data: userData, error: userError } = await supabase.auth.getUser(token);

      if (userError) {
        console.log('[API] Error getting user from token:', userError?.message);
      } else if (userData?.user) {
        user = userData.user;
        console.log(`[API] User found from token: ${user.id}`);
      }
    }

    // If we couldn't get the user from the token, use the user ID from the header or URL parameter
    if (!user) {
      userId = userIdHeader || userIdParam;

      if (!userId) {
        console.log('[API] No user ID available');
        return NextResponse.json(
          {
            ...defaultBalance,
            error: 'User not found'
          },
          { status: 200 }
        );
      }

      console.log(`[API] Using user ID from header or parameter: ${userId}`);
    } else {
      userId = user.id;
    }

    console.log(`[API] Using user ID: ${userId}`);

    try {
      // Get the parameters from the URL
      const url = new URL(req.url);
      const purchase = url.searchParams.get('purchase');
      const forceRefresh = url.searchParams.get('forceRefresh');
      const nocache = url.searchParams.get('nocache');

      console.log(`[API] Purchase parameter: ${purchase}`);
      console.log(`[API] Force refresh parameter: ${forceRefresh}`);
      console.log(`[API] No cache parameter: ${nocache}`);

      // Forward the request to the backend
      let backendUrl = '/api/tokens/balance';

      // We're no longer passing the purchase parameter to avoid double-counting
      // The Stripe webhook should be the only mechanism adding tokens for purchases
      backendUrl += `?userId=${userId}`;

      // Add the forceRefresh parameter if it's set
      if (forceRefresh === 'true') {
        backendUrl += `&forceRefresh=true`;
        console.log(`[API] Force refresh parameter passed to backend`);
      }

      // Add the nocache parameter if it's set
      if (nocache) {
        backendUrl += `&nocache=${nocache}`;
      }

      // Log if a purchase parameter was detected but not passed
      if (purchase === 'true' || purchase === '1') {
        console.log(`[API] Purchase parameter detected but not passed to backend to avoid double-counting`);
      }

      const backendResponse = await apiRequest(backendUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'x-user-id': userId
        }
      }, token, userId);

      console.log('[API] Backend response received:', backendResponse);

      // Return the backend response
      return NextResponse.json(backendResponse);
    } catch (backendError: any) {
      console.error('[API] Error from backend:', backendError.message);

      // Try to get the token balance directly from the backend
      try {
        const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';

        // Get the purchase parameter from the URL
        const url = new URL(req.url);
        const purchase = url.searchParams.get('purchase');

        // We're no longer passing the purchase parameter to avoid double-counting
        // The Stripe webhook should be the only mechanism adding tokens for purchases
        let backendUrl = `${apiUrl}/api/tokens/balance?userId=${userId}&forceRefresh=true&nocache=${Date.now()}`;

        // Log if a purchase parameter was detected but not passed
        if (purchase === 'true' || purchase === '1') {
          console.log(`[API] Purchase parameter detected but not passed to backend to avoid double-counting`);
        }

        console.log(`[API] Adding forceRefresh=true and nocache parameter to direct backend call`);

        console.log(`[API] User ID being sent to backend: ${userId}`);
        console.log(`[API] Direct backend request URL: ${backendUrl}`);

        const directResponse = await fetch(backendUrl, {
          headers: {
            'Content-Type': 'application/json',
            'x-user-id': userId,
            'Authorization': 'Bearer dummy-token', // Add a dummy token to avoid "No authorization header provided" error
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          },
          cache: 'no-store' // Ensure we don't get a cached response
        });

        if (directResponse.ok) {
          const data = await directResponse.json();
          console.log('Direct token balance data:', data);
          return NextResponse.json(data);
        }
      } catch (directError) {
        console.error('Error with direct token balance fetch:', directError);
      }

      // Return a default response if the backend is unavailable
      return NextResponse.json({
        ...defaultBalance,
        user_id: userId || defaultBalance.user_id,
        error: 'Backend unavailable'
      });
    }
  } catch (error: any) {
    console.error('[API] Token balance error:', error.message);

    // Try to get the token balance directly from the backend
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';

      // Get the purchase parameter from the URL
      const url = new URL(req.url);
      const purchase = url.searchParams.get('purchase');

      // Get the user ID from the URL parameter or header
      const userIdParam = url.searchParams.get('userId');
      const userIdHeader = req.headers.get('x-user-id');
      const effectiveUserId = userIdParam || userIdHeader || 'unknown';

      // We're no longer passing the purchase parameter to avoid double-counting
      // The Stripe webhook should be the only mechanism adding tokens for purchases
      let backendUrl = `${apiUrl}/api/tokens/balance?userId=${effectiveUserId}&forceRefresh=true&nocache=${Date.now()}`;

      // Log if a purchase parameter was detected but not passed
      if (purchase === 'true' || purchase === '1') {
        console.log(`[API] Purchase parameter detected but not passed to backend to avoid double-counting`);
      }

      console.log(`[API] Adding forceRefresh=true and nocache parameter to fallback direct backend call`);

      console.log(`[API] Direct backend request URL (fallback): ${backendUrl}`);
      console.log(`[API] User ID being sent to backend (fallback): ${effectiveUserId}`);

      const directResponse = await fetch(backendUrl, {
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': effectiveUserId,
          'Authorization': 'Bearer dummy-token', // Add a dummy token to avoid "No authorization header provided" error
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
        cache: 'no-store' // Ensure we don't get a cached response
      });

      if (directResponse.ok) {
        const data = await directResponse.json();
        console.log('Direct token balance data:', data);
        return NextResponse.json(data);
      }
    } catch (directError) {
      console.error('Error with direct token balance fetch:', directError);
    }

    // Return a default response in case of error
    // Get the user ID from the URL parameter or header if available
    const url = new URL(req.url);
    const userIdParam = url.searchParams.get('userId');
    const userIdHeader = req.headers.get('x-user-id');
    const effectiveUserId = userIdParam || userIdHeader || 'unknown';

    console.log(`[API] Returning default response with user ID: ${effectiveUserId}`);

    return NextResponse.json({
      ...defaultBalance,
      user_id: effectiveUserId,
      error: 'Error fetching token balance'
    });
  }
}
