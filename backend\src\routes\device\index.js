const express = require('express');
const router = express.Router();
const { authenticateJWT } = require('../../middleware/auth');
const deviceSeatService = require('../../services/deviceSeatService');
const subscriptionService = require('../../services/subscriptionService');

/**
 * @route POST /api/device/check-seat
 * @desc Check and register device seat for a user
 * @access Private
 */
router.post('/check-seat', authenticateJWT, async (req, res) => {
  try {
    const { deviceInfo, subscriptionPlan } = req.body;
    const userId = req.user.id;
    
    if (!deviceInfo || !deviceInfo.deviceId) {
      return res.status(400).json({
        canUse: false,
        error: 'Device information is required'
      });
    }
    
    // Get user's subscription plan if not provided
    let plan = subscriptionPlan;
    if (!plan) {
      const subscription = await subscriptionService.checkSubscription(userId);
      plan = subscription?.plan || 'basic';
    }
    
    // Get client IP address
    const ipAddress = req.ip || req.connection.remoteAddress;
    
    // Register or check device seat
    const result = await deviceSeatService.registerDevice(
      userId,
      deviceInfo,
      ipAddress,
      plan
    );
    
    if (result.success) {
      // Update device activity periodically
      setImmediate(() => {
        deviceSeatService.updateDeviceActivity(userId, deviceInfo.deviceId).catch(console.error);
      });
      
      return res.json({
        canUse: true,
        deviceId: result.deviceId,
        seatCount: result.seatCount,
        seatLimit: result.seatLimit,
        message: result.message
      });
    } else {
      return res.status(403).json({
        canUse: false,
        error: result.error,
        activeDevices: result.activeDevices,
        seatLimit: result.seatLimit
      });
    }
  } catch (error) {
    console.error('Device seat check error:', error);
    return res.status(500).json({
      canUse: false,
      error: 'Failed to verify device seat'
    });
  }
});

/**
 * @route GET /api/device/seats
 * @desc Get all device seats for the authenticated user
 * @access Private
 */
router.get('/seats', authenticateJWT, async (req, res) => {
  try {
    const userId = req.user.id;
    
    const devices = await deviceSeatService.getUserDevices(userId);
    const subscription = await subscriptionService.checkSubscription(userId);
    const seatLimit = deviceSeatService.SEAT_LIMITS[subscription?.plan || 'basic'] || 2;
    
    res.json({
      devices,
      seatLimit,
      activeCount: devices.filter(d => d.is_active).length
    });
  } catch (error) {
    console.error('Error fetching device seats:', error);
    res.status(500).json({ error: 'Failed to fetch device seats' });
  }
});

/**
 * @route DELETE /api/device/seats/:deviceId
 * @desc Deactivate a device seat
 * @access Private
 */
router.delete('/seats/:deviceId', authenticateJWT, async (req, res) => {
  try {
    const userId = req.user.id;
    const { deviceId } = req.params;
    
    const result = await deviceSeatService.deactivateDevice(userId, deviceId);
    
    if (result.success) {
      res.json({ message: result.message });
    } else {
      res.status(400).json({ error: 'Failed to deactivate device' });
    }
  } catch (error) {
    console.error('Error deactivating device:', error);
    res.status(500).json({ error: 'Failed to deactivate device' });
  }
});

/**
 * @route POST /api/device/heartbeat
 * @desc Update device activity (heartbeat)
 * @access Private
 */
router.post('/heartbeat', authenticateJWT, async (req, res) => {
  try {
    const userId = req.user.id;
    const { deviceId } = req.body;
    
    if (!deviceId) {
      return res.status(400).json({ error: 'Device ID is required' });
    }
    
    await deviceSeatService.updateDeviceActivity(userId, deviceId);
    res.json({ success: true });
  } catch (error) {
    console.error('Error updating device activity:', error);
    res.status(500).json({ error: 'Failed to update device activity' });
  }
});

module.exports = router; 