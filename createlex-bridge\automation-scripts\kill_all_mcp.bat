@echo off
setlocal enabledelayedexpansion

REM =============================================
REM   KILL ALL MCP AND BRIDGE PROCESSES
REM =============================================

title Kill All MCP Processes

echo ========================================
echo   KILLING ALL MCP AND BRIDGE PROCESSES
echo ========================================

echo ^> PHASE 1: Killing Electron processes...
taskkill /F /IM electron.exe >nul 2>&1
taskkill /F /T /IM electron.exe >nul 2>&1

echo ^> PHASE 2: Killing Node.js bridge processes...
for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq node.exe" /FO LIST ^| find "PID:"') do (
    for /f "tokens=*" %%j in ('wmic process where "ProcessId=%%i" get CommandLine /value 2^>nul ^| find "createlex-bridge"') do (
        echo ^>   Terminating bridge Node.js PID: %%i
        taskkill /F /PID %%i >nul 2>&1
    )
)

echo ^> PHASE 3: Killing Python MCP processes...
for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq python.exe" /FO LIST ^| find "PID:"') do (
    for /f "tokens=*" %%j in ('wmic process where "ProcessId=%%i" get CommandLine /value 2^>nul ^| find "mcp_server"') do (
        echo ^>   Terminating MCP Python PID: %%i
        taskkill /F /PID %%i >nul 2>&1
    )
)

for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq python3.exe" /FO LIST ^| find "PID:"') do (
    for /f "tokens=*" %%j in ('wmic process where "ProcessId=%%i" get CommandLine /value 2^>nul ^| find "mcp_server"') do (
        echo ^>   Terminating MCP Python3 PID: %%i
        taskkill /F /PID %%i >nul 2>&1
    )
)

echo ^> PHASE 4: Killing by command line patterns...
wmic process where "CommandLine like '%%createlex-bridge%%'" delete >nul 2>&1
wmic process where "CommandLine like '%%mcp_server_protected%%'" delete >nul 2>&1
wmic process where "CommandLine like '%%mcp_server_stdio%%'" delete >nul 2>&1
wmic process where "CommandLine like '%%mcp-server-createlexgenai%%'" delete >nul 2>&1

echo ^> PHASE 5: Freeing network ports...
REM Kill processes on MCP ports
for /f "tokens=5" %%a in ('netstat -aon ^| find ":9877" ^| find "LISTENING"') do (
    if not "%%a"=="0" (
        echo ^>   Freeing port 9877: PID %%a
        taskkill /F /PID %%a >nul 2>&1
    )
)

for /f "tokens=5" %%a in ('netstat -aon ^| find ":7891" ^| find "LISTENING"') do (
    if not "%%a"=="0" (
        echo ^>   Freeing port 7891: PID %%a  
        taskkill /F /PID %%a >nul 2>&1
    )
)

for /f "tokens=5" %%a in ('netstat -aon ^| find ":3000" ^| find "LISTENING"') do (
    if not "%%a"=="0" (
        echo ^>   Freeing port 3000: PID %%a
        taskkill /F /PID %%a >nul 2>&1
    )
)

echo ^> PHASE 6: Final cleanup wait...
timeout /t 3 /nobreak >nul

echo ^> PHASE 7: Closing terminal windows (preserving current script)...

REM Get current process PID to preserve this window AND the calling script
for /f "tokens=2" %%i in ('tasklist /FI "PID eq %~dp0" /FO LIST 2^>nul ^| find "PID:"') do set CURRENT_PID=%%i
if "%CURRENT_PID%"=="" (
    REM Alternative method to get current cmd PID
    for /f "tokens=2" %%i in ('wmic process where "name='cmd.exe' and commandline like '%%kill_all_mcp%%'" get processid /value 2^>nul ^| find "ProcessId"') do set CURRENT_PID=%%i
)

REM Also get the parent process PID (the calling script like dev_refresh_bypass.bat)
for /f "tokens=2" %%i in ('wmic process where "ProcessId=%CURRENT_PID%" get ParentProcessId /value 2^>nul ^| find "ParentProcessId"') do set PARENT_PID=%%i

REM Get all processes in the same session to protect development workflow
for /f "tokens=2" %%i in ('wmic process where "name='cmd.exe' and (commandline like '%%dev_refresh%%' or commandline like '%%kill_all_mcp%%')" get processid /value 2^>nul ^| find "ProcessId"') do set PROTECTED_PID_%%i=1

REM Export PIDs for PowerShell access
set CURRENT_PID=%CURRENT_PID%
set PARENT_PID=%PARENT_PID%

echo ^>   Protecting PIDs - Current: %CURRENT_PID%, Parent: %PARENT_PID%
echo ^>   Protecting ALL dev_refresh and kill_all_mcp windows from closure

REM Close specific window titles first (most reliable method)
echo ^>   Closing MCP/Bridge windows by title...
taskkill /F /FI "WINDOWTITLE eq MCP-Bridge-Bypass-Active" >nul 2>&1
taskkill /F /FI "WINDOWTITLE eq MCP-Server-Direct-Active" >nul 2>&1
taskkill /F /FI "WINDOWTITLE eq MCP-Bridge-Bypass-Window" >nul 2>&1
taskkill /F /FI "WINDOWTITLE eq MCP-Server-Direct-Window" >nul 2>&1

REM Close windows by broader title patterns
echo ^>   Closing by window title patterns...
for /f "tokens=*" %%i in ('tasklist /V /FI "IMAGENAME eq cmd.exe" /FO CSV ^| find "MCP"') do (
    for /f "tokens=2 delims=," %%j in ("%%i") do (
        set "TARGET_PID=%%j"
        set "TARGET_PID=!TARGET_PID:"="!"
        if not "!TARGET_PID!"=="%CURRENT_PID%" (
            echo ^>     Closing MCP window PID: !TARGET_PID!
            taskkill /F /PID !TARGET_PID! >nul 2>&1
        )
    )
)

for /f "tokens=*" %%i in ('tasklist /V /FI "IMAGENAME eq cmd.exe" /FO CSV ^| find "Bridge"') do (
    for /f "tokens=2 delims=," %%j in ("%%i") do (
        set "TARGET_PID=%%j"
        set "TARGET_PID=!TARGET_PID:"="!"
        if not "!TARGET_PID!"=="%CURRENT_PID%" (
            echo ^>     Closing Bridge window PID: !TARGET_PID!
            taskkill /F /PID !TARGET_PID! >nul 2>&1
        )
    )
)

for /f "tokens=*" %%i in ('tasklist /V /FI "IMAGENAME eq cmd.exe" /FO CSV ^| find "CreateLex"') do (
    for /f "tokens=2 delims=," %%j in ("%%i") do (
        set "TARGET_PID=%%j"
        set "TARGET_PID=!TARGET_PID:"="!"
        if not "!TARGET_PID!"=="%CURRENT_PID%" (
            echo ^>     Closing CreateLex window PID: !TARGET_PID!
            taskkill /F /PID !TARGET_PID! >nul 2>&1
        )
    )
)

REM Kill cmd.exe windows that contain MCP/Bridge processes by command line (preserve current)
echo ^>   Closing terminals by command line content...
for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq cmd.exe" /FO LIST ^| find "PID:"') do (
    if not "%%i"=="%CURRENT_PID%" if not "%%i"=="%PARENT_PID%" (
        for /f "tokens=*" %%j in ('wmic process where "ProcessId=%%i" get CommandLine /value 2^>nul ^| find "MCP"') do (
            echo ^>     Closing MCP terminal PID: %%i
            taskkill /F /PID %%i >nul 2>&1
        )
    )
)

for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq cmd.exe" /FO LIST ^| find "PID:"') do (
    if not "%%i"=="%CURRENT_PID%" if not "%%i"=="%PARENT_PID%" (
        for /f "tokens=*" %%j in ('wmic process where "ProcessId=%%i" get CommandLine /value 2^>nul ^| find "Bridge"') do (
            echo ^>     Closing Bridge terminal PID: %%i
            taskkill /F /PID %%i >nul 2>&1
        )
    )
)

for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq cmd.exe" /FO LIST ^| find "PID:"') do (
    if not "%%i"=="%CURRENT_PID%" if not "%%i"=="%PARENT_PID%" (
        for /f "tokens=*" %%j in ('wmic process where "ProcessId=%%i" get CommandLine /value 2^>nul ^| find "createlex"') do (
            echo ^>     Closing CreateLex terminal PID: %%i
            taskkill /F /PID %%i >nul 2>&1
        )
    )
)

for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq cmd.exe" /FO LIST ^| find "PID:"') do (
    if not "%%i"=="%CURRENT_PID%" if not "%%i"=="%PARENT_PID%" (
        for /f "tokens=*" %%j in ('wmic process where "ProcessId=%%i" get CommandLine /value 2^>nul ^| find "mcp_server"') do (
            echo ^>     Closing MCP server terminal PID: %%i
            taskkill /F /PID %%i >nul 2>&1
        )
    )
)

for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq cmd.exe" /FO LIST ^| find "PID:"') do (
    if not "%%i"=="%CURRENT_PID%" if not "%%i"=="%PARENT_PID%" (
        for /f "tokens=*" %%j in ('wmic process where "ProcessId=%%i" get CommandLine /value 2^>nul ^| find "npm start"') do (
            echo ^>     Closing npm start terminal PID: %%i
            taskkill /F /PID %%i >nul 2>&1
        )
    )
)

REM Fallback: Close any remaining windows with MCP-related titles using PowerShell
echo ^>   PowerShell fallback cleanup...
powershell -Command "Get-Process | Where-Object {$_.MainWindowTitle -match 'MCP|Bridge|CreateLex|mcp_server|npm.*start' -and $_.ProcessName -eq 'cmd' -and $_.Id -ne $PID} | Stop-Process -Force" >nul 2>&1

REM AGGRESSIVE CLEANUP: Close OLD MCP/Bridge terminals (exclude development workflow)
echo ^>   Aggressive cleanup of OLD bypass/bridge terminals...
for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq cmd.exe" /FO LIST ^| find "PID:"') do (
    if not "%%i"=="%CURRENT_PID%" if not "%%i"=="%PARENT_PID%" (
        REM Check if this is a protected development workflow process
        if not defined PROTECTED_PID_%%i (
            wmic process where "ProcessId=%%i" get CommandLine /value 2>nul | findstr /i "MCP-Bridge-Bypass-Active MCP-Server-Direct-Active npm.*start.*bypass" >nul
            if !errorlevel! equ 0 (
                REM Double check it's not a dev workflow script
                wmic process where "ProcessId=%%i" get CommandLine /value 2>nul | findstr /i "dev_refresh kill_all_mcp" >nul
                if !errorlevel! neq 0 (
                    echo ^>     Force closing OLD bypass/bridge terminal PID: %%i
                    taskkill /F /PID %%i >nul 2>&1
                )
            )
        )
    )
)

REM TARGETED CLEANUP: Only close OLD MCP/Bridge processes (not dev workflow)
echo ^>   Targeted cleanup - checking for OLD MCP/Bridge windows...
for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq cmd.exe" /FO LIST ^| find "PID:"') do (
    if not "%%i"=="%CURRENT_PID%" if not "%%i"=="%PARENT_PID%" (
        REM Check if this is a protected development workflow process
        if not defined PROTECTED_PID_%%i (
            REM Only target specific OLD MCP/Bridge processes, not dev scripts
            wmic process where "ProcessId=%%i" get CommandLine /value 2>nul | findstr /i "electron.*bridge node.*createlex-bridge python.*mcp_server" >nul
            if !errorlevel! equ 0 (
                REM Double check it's not a dev workflow script
                wmic process where "ProcessId=%%i" get CommandLine /value 2>nul | findstr /i "dev_refresh kill_all_mcp instant_refresh" >nul
                if !errorlevel! neq 0 (
                    echo ^>     Targeted cleanup - closing OLD MCP/Bridge cmd PID: %%i
                    taskkill /F /PID %%i >nul 2>&1
                )
            )
        )
    )
)

REM Final sweep: Close any "orphaned" cmd windows that might be hanging around
echo ^>   Final sweep for orphaned terminals...
for /f "skip=1 tokens=1,2" %%a in ('wmic process where "name='cmd.exe'" get ProcessId^,ParentProcessId /format:csv') do (
    if not "%%b"=="%CURRENT_PID%" if not "%%b"=="%PARENT_PID%" (
        if not "%%b"=="" (
            REM Check if parent process no longer exists (orphaned)
            tasklist /FI "PID eq %%a" 2>nul | find "%%a" >nul
            if !errorlevel! neq 0 (
                echo ^>     Closing orphaned cmd window PID: %%b
                taskkill /F /PID %%b >nul 2>&1
            )
        )
    )
)

REM SAFE TARGETED CLEANUP: Only close windows with specific OLD MCP/Bridge titles
echo ^>   Safe targeted cleanup - only specific OLD MCP/Bridge windows...
REM Only target windows with these EXACT titles (old MCP/Bridge windows)
for /f "tokens=*" %%i in ('tasklist /V /FI "IMAGENAME eq cmd.exe" /FO CSV ^| findstr /i "MCP-Bridge-Bypass-Active\|MCP-Server-Direct-Active\|MCP-Bridge-Bypass-Window\|MCP-Server-Direct-Window"') do (
    for /f "tokens=2 delims=," %%j in ("%%i") do (
        set "TARGET_PID=%%j"
        set "TARGET_PID=!TARGET_PID:"=!"
        if not "!TARGET_PID!"=="%CURRENT_PID%" if not "!TARGET_PID!"=="%PARENT_PID%" (
            if not defined PROTECTED_PID_!TARGET_PID! (
                echo ^>     Safe cleanup - closing OLD MCP/Bridge window PID: !TARGET_PID!
                taskkill /F /T /PID !TARGET_PID! >nul 2>&1
            )
        )
    )
)

echo ^> PHASE 8: Final verification...
netstat -an | find ":9877" >nul
if %errorlevel% neq 0 (
    echo ^>   ✅ Port 9877 is free
) else (
    echo ^>   ⚠️  Port 9877 still occupied
)

netstat -an | find ":7891" >nul  
if %errorlevel% neq 0 (
    echo ^>   ✅ Port 7891 is free
) else (
    echo ^>   ⚠️  Port 7891 still occupied
)

echo.
echo ========================================
echo   CLEANUP COMPLETE
echo ========================================
echo.
echo All MCP and Bridge processes terminated.
echo Terminal windows closed.
echo Ports 9877, 7891, 3000 freed.
echo Ready for fresh startup.
echo.

timeout /t 2 /nobreak >nul
exit /b 0