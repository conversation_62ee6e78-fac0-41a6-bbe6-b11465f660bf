const { shell } = require('electron');
const express = require('express');
const http = require('http');
const { URL } = require('url');

class OAuthFlow {
  constructor() {
    this.server = null;
    this.port = 7891; // Fixed port for callback
    this.authPromise = null;
    // Use localhost for development/testing
    this.baseURL = process.env.CREATELEX_BASE_URL || 
                   (process.env.NODE_ENV === 'development' ? 'http://localhost:3000' : 'https://createlex.com');
  }

  /**
   * Start the OAuth flow by opening browser and starting callback server
   */
  async authenticate() {
    if (this.authPromise) {
      return this.authPromise;
    }

    this.authPromise = new Promise((resolve, reject) => {
      this.startCallbackServer((result) => {
        this.authPromise = null;
        if (result.success) {
          resolve(result);
        } else {
          reject(new Error(result.error));
        }
      });

      // Open browser to login page with callback URL pointing to frontend bridge-callback page
      const frontendCallbackUrl = `${this.baseURL}/bridge-callback`;
      
      const authUrl = `${this.baseURL}/login?redirect=${encodeURIComponent(frontendCallbackUrl)}&source=bridge`;
      
      console.log('=== OAuth Flow Configuration ===');
      console.log('Base URL:', this.baseURL);
      console.log('Opening browser for authentication:', authUrl);
      console.log('Frontend callback will be at:', frontendCallbackUrl);
      console.log('Bridge server listening on:', `http://localhost:${this.port}`);
      console.log('================================');
      
      shell.openExternal(authUrl);

      // Set timeout for authentication
      setTimeout(() => {
        if (this.authPromise) {
          this.stopCallbackServer();
          this.authPromise = null;
          reject(new Error('Authentication timed out after 5 minutes'));
        }
      }, 5 * 60 * 1000); // 5 minutes timeout
    });

    return this.authPromise;
  }

  /**
   * Start local HTTP server to handle callback
   */
  startCallbackServer(callback) {
    const app = express();
    const path = require('path');
    
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));
    
    // Serve static files from the web directory
    const webDir = path.join(__dirname, '..', '..', 'web');
    app.use('/web', express.static(webDir));
    console.log('Serving web files from:', webDir);

    // Add CORS headers middleware
    app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      next();
    });

    // Handle the callback from the local auth-callback page
    app.post('/auth/success', async (req, res) => {
      try {
        const { token, userId, email, hasSubscription } = req.body;
        
        if (!token || !userId) {
          throw new Error('Missing authentication token or user ID');
        }

        console.log('Received authentication from local callback:', { 
          userId, 
          email: email || 'unknown',
          hasSubscription 
        });

        // Send success response BEFORE processing (important for browser callback)
        res.json({
          success: true,
          message: 'Authentication received successfully'
        });

        console.log('Sent success response to callback page');

        // Small delay to ensure response is sent
        setTimeout(() => {
          console.log('Processing authentication data for bridge...');
          
                  // Stop the server and call callback
        this.stopCallbackServer();
        
        // Note: We ignore the hasSubscription value from the frontend callback
        // The bridge will do its own subscription validation after authentication
        callback({
          success: true,
          token,
          userId,
          email: email || null,
          // Don't trust frontend subscription status - bridge will validate this
          hasActiveSubscription: null, // Will be validated by bridge
          subscriptionStatus: 'pending_validation',
          userData: {
            id: userId,
            email: email || null,
            subscription: {
              hasActiveSubscription: null, // Will be validated by bridge
              status: 'pending_validation'
            }
          }
        });
        }, 100); // 100ms delay

      } catch (error) {
        console.error('Error in auth callback:', error);
        
        res.status(400).json({
          success: false,
          error: error.message
        });

        this.stopCallbackServer();
        callback({
          success: false,
          error: error.message
        });
      }
    });

    // Handle direct callback (fallback for URL parameters)
    app.get('/auth/callback', async (req, res) => {
      try {
        const { token, userId, email, hasSubscription } = req.query;
        
        if (!token || !userId) {
          throw new Error('Missing authentication token or user ID');
        }

        console.log('Received direct authentication callback:', { userId, email: email || 'unknown' });

        // Send success response to browser
        res.send(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>Authentication Successful</title>
              <style>
                body { 
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
                  text-align: center; 
                  padding: 50px; 
                  background: #f5f5f5; 
                }
                .success { 
                  background: white; 
                  padding: 40px; 
                  border-radius: 12px; 
                  box-shadow: 0 4px 20px rgba(0,0,0,0.1); 
                  max-width: 500px; 
                  margin: 0 auto; 
                }
                .checkmark { color: #28a745; font-size: 48px; margin-bottom: 20px; }
                h1 { color: #333; margin-bottom: 10px; }
                p { color: #666; margin-bottom: 30px; }
                .button { 
                  background: #007bff; 
                  color: white; 
                  padding: 12px 24px; 
                  border: none; 
                  border-radius: 6px; 
                  cursor: pointer; 
                  text-decoration: none; 
                  display: inline-block; 
                }
              </style>
            </head>
            <body>
              <div class="success">
                <div class="checkmark">✅</div>
                <h1>Authentication Successful!</h1>
                <p>CreateLex Bridge has been authenticated successfully.</p>
                <p>You can now close this window and return to the application.</p>
                <button onclick="window.close()" class="button">Close Window</button>
              </div>
              <script>
                // Auto-close after 3 seconds
                setTimeout(() => {
                  window.close();
                }, 3000);
              </script>
            </body>
          </html>
        `);

        // Stop the server and call callback
        this.stopCallbackServer();
        
        // Note: We ignore the hasSubscription value from the frontend callback
        // The bridge will do its own subscription validation after authentication
        callback({
          success: true,
          token,
          userId,
          email: email || null,
          // Don't trust frontend subscription status - bridge will validate this
          hasActiveSubscription: null, // Will be validated by bridge
          subscriptionStatus: 'pending_validation',
          userData: {
            id: userId,
            email: email || null,
            subscription: {
              hasActiveSubscription: null, // Will be validated by bridge
              status: 'pending_validation'
            }
          }
        });

      } catch (error) {
        console.error('Error in direct auth callback:', error);
        
        res.status(500).send(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>Authentication Error</title>
              <style>
                body { 
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
                  text-align: center; 
                  padding: 50px; 
                  background: #f5f5f5; 
                }
                .error { 
                  background: white; 
                  padding: 40px; 
                  border-radius: 12px; 
                  box-shadow: 0 4px 20px rgba(0,0,0,0.1); 
                  max-width: 500px; 
                  margin: 0 auto; 
                }
                .x-mark { color: #dc3545; font-size: 48px; margin-bottom: 20px; }
              </style>
            </head>
            <body>
              <div class="error">
                <div class="x-mark">❌</div>
                <h1>Authentication Failed</h1>
                <p>${error.message}</p>
                <p>Please try again.</p>
              </div>
            </body>
          </html>
        `);

        this.stopCallbackServer();
        callback({
          success: false,
          error: error.message
        });
      }
    });

    // Handle preflight OPTIONS requests
    app.options('*', (req, res) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      res.sendStatus(200);
    });

    this.server = http.createServer(app);
    
    this.server.listen(this.port, 'localhost', () => {
      console.log(`OAuth callback server started on http://localhost:${this.port}`);
    });

    this.server.on('error', (error) => {
      console.error('OAuth server error:', error);
      callback({
        success: false,
        error: `Server error: ${error.message}`
      });
    });
  }

  /**
   * Stop the callback server
   */
  stopCallbackServer() {
    if (this.server) {
      this.server.close();
      this.server = null;
      console.log('OAuth callback server stopped');
    }
  }

  /**
   * Get subscription status from the API
   */
  async getSubscriptionStatus(token) {
    const axios = require('axios');
    
    const apiUrls = [
      process.env.API_BASE_URL,
      'https://createlex.com/api',
      'https://api.createlex.com/api'
    ].filter(Boolean);

    let lastError = null;
    
    for (const baseUrl of apiUrls) {
      try {
        console.log(`Checking subscription status at: ${baseUrl}/subscription/status`);
        
        const response = await axios({
          url: `${baseUrl}/subscription/status`,
          method: 'GET',
          headers: { 
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          timeout: 10000
        });
        
        console.log('Subscription status response:', response.data);
        return response.data;
        
      } catch (error) {
        console.log(`Failed to get subscription status from ${baseUrl}:`, error.response?.status || error.message);
        lastError = error;
        continue;
      }
    }
    
    // If all endpoints failed, return default
    console.warn('All subscription status endpoints failed, returning default');
    return {
      hasActiveSubscription: false,
      subscriptionStatus: 'inactive',
      error: lastError?.message || 'Unable to check subscription status'
    };
  }

  /**
   * Cancel any ongoing authentication
   */
  cancel() {
    if (this.authPromise) {
      this.stopCallbackServer();
      this.authPromise = null;
    }
  }
}

module.exports = { OAuthFlow }; 