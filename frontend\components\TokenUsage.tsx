import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { FiAlertCircle, FiCheckCircle, FiInfo, FiShoppingCart } from 'react-icons/fi';
import { toast } from 'react-hot-toast';
import { fetchWithAuth } from '../lib/authUtils';

interface TokenUsageProps {
  className?: string;
  purchaseSuccess?: boolean;
}

interface TokenUsageData {
  dailyUsage: {
    used: number;
    limit: number;
    percentage: number;
    exceeded: boolean;
  };
  monthlyUsage: {
    used: number;
    limit: number;
    percentage: number;
    exceeded: boolean;
  };
  plan: string;
  maxRequestLength: number;
}

interface TokenBalance {
  id?: string;
  user_id: string;
  balance: number;
  created_at: string;
  updated_at: string;
}

interface TokenPackage {
  tokens: number;
  price: number;
  priceId: string;
}

interface TokenPackages {
  [key: string]: TokenPackage;
}

const TokenUsage: React.FC<TokenUsageProps> = ({ className = '', purchaseSuccess = false }) => {
  const { user, token } = useAuth();
  const [tokenUsage, setTokenUsage] = useState<TokenUsageData | null>(null);
  const [tokenBalance, setTokenBalance] = useState<TokenBalance | null>(null);
  const [tokenPackages, setTokenPackages] = useState<TokenPackages | null>(null);
  const [loading, setLoading] = useState(true);
  const [showPurchaseOptions, setShowPurchaseOptions] = useState(false);

  useEffect(() => {
    if (user && token) {
      console.log('TokenUsage: User and token available, fetching data');
      console.log('TokenUsage: User ID:', user.id);

      if (purchaseSuccess) {
        console.log('TokenUsage: Purchase success detected, refreshing data with priority on token balance');
        refreshData(true); // Use refreshData with isPurchase=true to prioritize token balance refresh
      } else {
        // Regular data fetch
        fetchTokenUsage();
        fetchTokenBalance(false);
        fetchTokenPackages();
      }
    } else {
      console.log('TokenUsage: User or token not available', { user: !!user, token: !!token });
    }
  }, [user, token, purchaseSuccess]);

  const fetchTokenUsage = async () => {
    try {
      if (!user?.id) {
        console.error('Cannot fetch token usage: missing user ID');
        setLoading(false);
        return;
      }

      console.log('fetchTokenUsage: Starting request with user ID:', user.id);
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';

      // Try the test endpoint directly first
      console.log('fetchTokenUsage: Trying test endpoint directly');
      const testUrl = `${apiUrl}/api/tokens/test-usage?userId=${user.id}`;
      console.log('fetchTokenUsage: Test endpoint URL:', testUrl);

      try {
        const testResponse = await fetch(testUrl, {
          headers: {
            'Content-Type': 'application/json'
          }
        });

        console.log('fetchTokenUsage: Test endpoint response status:', testResponse.status);

        if (testResponse.ok) {
          const testData = await testResponse.json();
          console.log('fetchTokenUsage: Test endpoint data:', testData);

          if (testData && testData.dailyUsage && testData.monthlyUsage) {
            console.log('fetchTokenUsage: Setting token usage data from test endpoint');
            setTokenUsage(testData);
            setLoading(false);
            return;
          } else {
            console.error('fetchTokenUsage: Invalid data format from test endpoint:', testData);
          }
        } else {
          console.error('fetchTokenUsage: Failed to fetch from test endpoint, status:', testResponse.status);
        }
      } catch (testError) {
        console.error('fetchTokenUsage: Error with test endpoint:', testError);
      }

      // If test endpoint failed, try the subscription check endpoint
      console.log('fetchTokenUsage: Trying subscription check endpoint');
      const url = `${apiUrl}/api/subscription/check?userId=${user.id}`;
      console.log('fetchTokenUsage: Subscription check URL:', url);

      const response = await fetchWithAuth(url, token);
      console.log('fetchTokenUsage: Subscription check response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('fetchTokenUsage: Subscription check data received:', data);

        if (data && data.dailyUsage && data.monthlyUsage) {
          console.log('fetchTokenUsage: Daily usage:', data.dailyUsage.used);
          console.log('fetchTokenUsage: Monthly usage:', data.monthlyUsage.used);
          setTokenUsage(data);
        } else {
          console.error('fetchTokenUsage: Invalid data format received from subscription check:', data);
        }
      } else {
        console.error('fetchTokenUsage: Failed to fetch from subscription check, status:', response.status);
        const errorText = await response.text();
        console.error('fetchTokenUsage: Error response from subscription check:', errorText);
      }
    } catch (error) {
      console.error('fetchTokenUsage: Error fetching token usage:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchTokenBalance = async (isPurchase = false) => {
    try {
      if (!user?.id) {
        console.error('Cannot fetch token balance: missing user ID');
        return;
      }

      // Try direct backend call first
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
      console.log(`Fetching token balance directly from backend${isPurchase ? ' after purchase' : ''}`);

      try {
        // Add timestamp to prevent caching and forceRefresh=true to ensure we get the latest data
        const timestamp = Date.now();
        const directResponse = await fetch(`${apiUrl}/api/tokens/balance?userId=${user.id}&forceRefresh=true&nocache=${timestamp}`, {
          headers: {
            'Content-Type': 'application/json',
            'x-user-id': user.id,
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        });

        if (directResponse.ok) {
          const data = await directResponse.json();
          console.log('Direct token balance data:', data);
          if (data && typeof data.balance === 'number') {
            setTokenBalance(data);
            return;
          }
        }
      } catch (directError) {
        console.error('Error with direct token balance fetch:', directError);
      }

      // Fallback to Next.js API route
      console.log(`Falling back to Next.js API route for token balance${isPurchase ? ' after purchase' : ''}`);

      // Add timestamp to prevent caching and forceRefresh=true to ensure we get the latest data
      const timestamp = Date.now();
      const response = await fetch(`/api/tokens/balance?forceRefresh=true&nocache=${timestamp}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'x-user-id': user.id,
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Token balance data from Next.js API:', data);
        setTokenBalance(data);
      } else {
        console.error('Failed to fetch token balance');
      }
    } catch (error) {
      console.error('Error fetching token balance:', error);
    }
  };

  const fetchTokenPackages = async () => {
    try {
      if (!user?.id) {
        console.error('Cannot fetch token packages: missing user ID');
        return;
      }

      // Try direct backend call first
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
      console.log('Fetching token packages directly from backend');

      try {
        const directResponse = await fetch(`${apiUrl}/api/tokens/packages`, {
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (directResponse.ok) {
          const data = await directResponse.json();
          console.log('Direct token packages data:', data);
          if (data && Object.keys(data).length > 0) {
            setTokenPackages(data);
            return;
          }
        }
      } catch (directError) {
        console.error('Error with direct token packages fetch:', directError);
      }

      // Fallback to Next.js API route
      console.log('Falling back to Next.js API route for token packages');
      const response = await fetch('/api/tokens/packages', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Token packages data from Next.js API:', data);
        setTokenPackages(data);
      } else {
        console.error('Failed to fetch token packages');
      }
    } catch (error) {
      console.error('Error fetching token packages:', error);
    }
  };

  const handlePurchaseTokens = async (packageId: string) => {
    try {
      if (!user?.id) {
        console.error('Cannot purchase tokens: missing user ID');
        toast.error('User ID is missing. Please try again later.');
        return;
      }

      if (!token) {
        console.error('Cannot purchase tokens: missing authentication token');
        toast.error('Authentication token is missing. Please try again later or log in again.');
        return;
      }

      // Show loading toast
      const loadingToast = toast.loading(`Preparing ${packageId} token package checkout...`);

      try {
        // Create the request body
        const requestBody = {
          packageId,
          successUrl: `${window.location.origin}/dashboard?purchase=success&package=${packageId}`,
          cancelUrl: `${window.location.origin}/dashboard?purchase=cancelled`,
        };

        console.log(`Sending token purchase request for package: ${packageId}`);
        console.log(`User ID: ${user.id}`);

        // Use the Next.js API route instead of directly calling the backend
        const response = await fetch('/api/tokens/purchase', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(requestBody)
        });

        // Log the response status
        console.log('Response status:', response.status);

        if (response.ok) {
          const data = await response.json();
          console.log('Token purchase response:', data);

          if (data.url) {
            // Show success toast before redirecting
            toast.success('Redirecting to secure checkout...');

            // Short delay before redirect for better UX
            setTimeout(() => {
              // Redirect to Stripe checkout
              window.location.href = data.url;
            }, 1000);
          } else {
            console.error('No checkout URL returned:', data);
            toast.error('Failed to create checkout session. Please try again.');
          }
        } else {
          let errorMessage = 'Failed to create checkout session';

          try {
            const errorData = await response.json();
            console.error('Error response:', errorData);
            errorMessage = errorData.error || errorMessage;

            if (errorData.details) {
              console.error('Error details:', errorData.details);
            }
          } catch (parseError) {
            console.error('Error parsing error response:', parseError);
          }

          toast.error(errorMessage);
        }
      } catch (fetchError) {
        console.error('Error making purchase request:', fetchError);
        toast.error('Failed to connect to the server. Please try again later.');
      }

      // Dismiss loading toast
      toast.dismiss(loadingToast);
    } catch (error) {
      console.error('Error purchasing tokens:', error);
      toast.error('An error occurred while processing your request. Please try again later.');
    }
  };

  if (loading) {
    return (
      <div className={`p-4 bg-white rounded-lg shadow-md ${className}`}>
        <h2 className="text-lg font-semibold mb-4">Token Usage</h2>
        <p>Loading usage data...</p>
      </div>
    );
  }

  const refreshData = async (isPurchase = false) => {
    setLoading(true);
    console.log(`refreshData: Refreshing token usage data${isPurchase ? ' after purchase' : ''}`);

    // Always refresh token balance first if this is after a purchase
    if (isPurchase && user?.id) {
      console.log('refreshData: Purchase detected, refreshing token balance first');
      await fetchTokenBalance(true);
    }

    // Try a direct fetch to the test endpoint with no-cache
    if (user?.id) {
      try {
        console.log('refreshData: Trying test endpoint with user ID:', user.id);
        const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
        const url = `${apiUrl}/api/tokens/test-usage?userId=${user.id}&t=${Date.now()}`; // Add timestamp to prevent caching

        const response = await fetch(url, {
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
            'x-user-id': user.id
          }
        });

        console.log('refreshData: Test endpoint response status:', response.status);

        if (response.ok) {
          const data = await response.json();
          console.log('refreshData: Test endpoint data:', data);

          if (data && data.dailyUsage && data.monthlyUsage) {
            console.log('refreshData: Setting token usage data from test endpoint');
            console.log('refreshData: Daily usage:', data.dailyUsage.used);
            console.log('refreshData: Monthly usage:', data.monthlyUsage.used);
            setTokenUsage(data);

            // Also refresh token balance and packages (if not already refreshed)
            if (!isPurchase) {
              fetchTokenBalance(false);
            }
            fetchTokenPackages();

            setLoading(false);
            return;
          } else {
            console.error('refreshData: Invalid data format from test endpoint:', data);
          }
        } else {
          console.error('refreshData: Failed to fetch from test endpoint, status:', response.status);
        }
      } catch (error) {
        console.error('refreshData: Error with test endpoint:', error);
      }
    }

    // If test endpoint failed, try the normal fetch methods
    fetchTokenUsage();

    // Only fetch token balance if not already fetched for a purchase
    if (!isPurchase) {
      fetchTokenBalance(false);
    }

    fetchTokenPackages();
  };

  // Add a direct debug function to fetch and display data from the test endpoint
  const debugFetchDirectData = async () => {
    if (!user?.id) {
      console.error('Cannot fetch debug data: missing user ID');
      return;
    }

    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
      const url = `${apiUrl}/api/tokens/test-usage?userId=${user.id}&t=${Date.now()}`;

      console.log('debugFetchDirectData: Fetching from URL:', url);

      const response = await fetch(url);
      console.log('debugFetchDirectData: Response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('debugFetchDirectData: Data received:', data);

        // Force update the token usage data
        if (data && data.dailyUsage && data.monthlyUsage) {
          console.log('debugFetchDirectData: Setting token usage data');
          setTokenUsage(data);
        }
      }
    } catch (error) {
      console.error('debugFetchDirectData: Error:', error);
    }
  };

  return (
    <div className={`p-4 bg-white rounded-lg shadow-md ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">Token Usage</h2>
        <div className="flex space-x-2">
          <button
            onClick={debugFetchDirectData}
            className="flex items-center space-x-1 px-3 py-1 rounded-md text-sm transition-colors bg-green-50 text-green-600 hover:bg-green-100"
          >
            <span>Debug Fetch</span>
          </button>
          <button
            onClick={() => refreshData(false)}
            className="flex items-center space-x-1 px-3 py-1 rounded-md text-sm transition-colors bg-blue-50 text-blue-600 hover:bg-blue-100"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Debug section */}
      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
        <h3 className="text-sm font-medium mb-2">API Data</h3>
        <div className="grid grid-cols-2 gap-2">
          <div className="p-2 bg-white rounded border border-blue-100">
            <p className="text-xs font-medium">Daily Usage:</p>
            {tokenUsage ? (
              <p className="text-sm font-bold">
                {tokenUsage.dailyUsage?.used?.toLocaleString() || 0} / {tokenUsage.dailyUsage?.limit?.toLocaleString() || 0} tokens ({(tokenUsage.dailyUsage?.percentage || 0).toFixed(3)}%)
              </p>
            ) : (
              <p className="text-sm font-bold text-gray-400">No data available</p>
            )}
          </div>
          <div className="p-2 bg-white rounded border border-blue-100">
            <p className="text-xs font-medium">Monthly Usage:</p>
            {tokenUsage ? (
              <p className="text-sm font-bold">
                {tokenUsage.monthlyUsage?.used?.toLocaleString() || 0} / {tokenUsage.monthlyUsage?.limit?.toLocaleString() || 0} tokens ({(tokenUsage.monthlyUsage?.percentage || 0).toFixed(2)}%)
              </p>
            ) : (
              <p className="text-sm font-bold text-gray-400">No data available</p>
            )}
          </div>
        </div>
      </div>

      {tokenUsage ? (
        <div className="mb-4">
          <div className="mb-2">
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium">Daily Usage</span>
              <span className="text-sm font-medium">
                {tokenUsage.dailyUsage?.used?.toLocaleString() || 0} / {tokenUsage.dailyUsage?.limit?.toLocaleString() || 0} tokens
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className={`h-2.5 rounded-full ${
                  (tokenUsage.dailyUsage?.percentage || 0) > 90 ? 'bg-red-500' :
                  (tokenUsage.dailyUsage?.percentage || 0) > 75 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                style={{ width: `${Math.min(tokenUsage.dailyUsage?.percentage || 0, 100)}%` }}
              ></div>
            </div>
          </div>

          <div className="mb-2">
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium">Monthly Usage2</span>
              <span className="text-sm font-medium">
                {tokenUsage.monthlyUsage?.used?.toLocaleString() || 0} / {tokenUsage.monthlyUsage?.limit?.toLocaleString() || 0} tokens
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className={`h-2.5 rounded-full ${
                  (tokenUsage.monthlyUsage?.percentage || 0) > 90 ? 'bg-red-500' :
                  (tokenUsage.monthlyUsage?.percentage || 0) > 75 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                style={{ width: `${Math.min(tokenUsage.monthlyUsage?.percentage || 0, 100)}%` }}
              ></div>
            </div>
          </div>

          <div className="mt-4 text-sm">
            <p className="font-medium">Subscription Plan: {tokenUsage.plan === 'pro' ? 'Pro' : 'Basic'}</p>
            {(tokenUsage.monthlyUsage?.percentage || 0) > 75 && (
              <div className="mt-2 flex items-start p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                <FiAlertCircle className="text-yellow-500 mr-2 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-yellow-700">
                    You're using your subscription at a high rate ({Math.round(tokenUsage.monthlyUsage?.percentage || 0)}% of monthly limit).
                  </p>
                  {tokenUsage.plan !== 'pro' && (
                    <p className="text-yellow-700 mt-1">
                      Consider upgrading to Pro for double the token limit.
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Debug section */}
          <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-md">
            <h3 className="text-sm font-medium mb-2">Debug Information</h3>
            <pre className="text-xs overflow-auto max-h-40 bg-gray-100 p-2 rounded">
              {JSON.stringify(tokenUsage, null, 2)}
            </pre>
          </div>
        </div>
      ) : (
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
          <p className="text-sm text-yellow-700">No token usage data available. Click refresh to try again.</p>
        </div>
      )}

      <div className="mt-4 p-3 bg-blue-50 border border-blue-100 rounded-md">
        <h3 className="text-md font-medium mb-2 flex items-center">
          <FiInfo className="mr-2 text-blue-500" />
          Additional Tokens
        </h3>
        <p className="text-sm mb-2">
          You have <span className="font-bold">{tokenBalance && tokenBalance.balance > 0 ? tokenBalance.balance.toLocaleString() : '0'}</span> additional tokens available.
        </p>
        <p className="text-xs text-gray-600">
          These tokens are used when you exceed your subscription limits.
        </p>
      </div>

      <div className="mt-4">
        <button
          onClick={() => setShowPurchaseOptions(!showPurchaseOptions)}
          className="flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <FiShoppingCart className="mr-2" />
          {showPurchaseOptions ? 'Hide Purchase Options' : 'Purchase Additional Tokens'}
        </button>
      </div>

      {showPurchaseOptions && (
        <div className="mt-4">
          {tokenPackages ? (
            <div>
              <h3 className="text-lg font-medium mb-3">Purchase Additional Tokens</h3>
              <p className="text-sm text-gray-600 mb-4">
                Additional tokens are used when you exceed your subscription limits. They never expire.
              </p>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                {Object.entries(tokenPackages).map(([id, pkg]) => (
                  <div key={id} className="border rounded-md p-4 hover:shadow-md transition-shadow bg-white">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-medium text-lg">{id.charAt(0).toUpperCase() + id.slice(1)}</h3>
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-semibold rounded-full">
                        Best {id === 'large' ? 'Value' : id === 'medium' ? 'Seller' : 'Starter'}
                      </span>
                    </div>
                    <p className="text-3xl font-bold mb-1">${pkg.price}</p>
                    <p className="text-sm text-gray-600 mb-3">{(pkg.tokens / 1000).toLocaleString()}K tokens</p>
                    <p className="text-xs text-gray-500 mb-4">
                      {id === 'small' ? 'Perfect for occasional use' :
                       id === 'medium' ? 'Great for regular users' :
                       'Ideal for power users'}
                    </p>
                    <button
                      onClick={() => handlePurchaseTokens(id)}
                      className={`mt-2 w-full px-3 py-2 text-sm font-medium text-white rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                        id === 'medium'
                          ? 'bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500'
                          : 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
                      }`}
                    >
                      Purchase Now
                    </button>
                  </div>
                ))}
              </div>
              <p className="text-xs text-gray-500 mt-4">
                Tokens are processed securely through Stripe. You'll be redirected to a secure checkout page.
              </p>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="flex justify-center mb-4">
                <svg className="animate-spin h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
              <p className="text-gray-600">Loading token packages...</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TokenUsage;
