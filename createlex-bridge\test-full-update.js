const axios = require('axios');
const { MCPUpdater } = require('./src/updater/mcp-updater');

// Custom updater that uses development endpoints
class DevMCPUpdater extends MCPUpdater {
  constructor() {
    super();
    // Override to use dev endpoints
    this.updateServerUrl = 'http://localhost:5001/api/mcp-updates';
  }

  async checkForUpdates() {
    try {
      const currentVersion = await this.getCurrentVersion();
      console.log(`Current MCP version: ${currentVersion}`);

      // Use development endpoint (no auth required)
      const response = await axios.get(`${this.updateServerUrl}/check-dev`, {
        params: { current_version: currentVersion },
        timeout: 10000
      });

      const data = response.data;
      console.log('Update check response:', data);

      return data;
    } catch (error) {
      console.error('Failed to check for updates:', error.response?.data || error.message);
      throw new Error(`Update check failed: ${error.response?.data?.error || error.message}`);
    }
  }

  async downloadUpdate(version) {
    try {
      console.log(`Downloading MCP server update v${version}...`);
      
      const downloadUrl = `${this.updateServerUrl}/download-dev/${version}`;
      console.log(`Download URL: ${downloadUrl}`);

      const response = await axios({
        method: 'GET',
        url: downloadUrl,
        responseType: 'stream',
        timeout: 30000
      });

      // Get checksum from headers
      const expectedChecksum = response.headers['x-checksum'];
      console.log(`Expected checksum: ${expectedChecksum}`);

      const fs = require('fs');
      const path = require('path');
      const crypto = require('crypto');

      const tempDir = path.join(__dirname, 'temp');
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      const tempFile = path.join(tempDir, `mcp-server-v${version}.zip`);
      const writer = fs.createWriteStream(tempFile);

      // Track download progress
      const totalLength = parseInt(response.headers['content-length'], 10);
      let downloadedLength = 0;
      const hash = crypto.createHash('sha256');

      response.data.on('data', (chunk) => {
        downloadedLength += chunk.length;
        hash.update(chunk);
        
        if (totalLength) {
          const percent = Math.round((downloadedLength / totalLength) * 100);
          this.emit('download-progress', { percent, downloaded: downloadedLength, total: totalLength });
        }
      });

      response.data.pipe(writer);

      return new Promise((resolve, reject) => {
        writer.on('finish', () => {
          const actualChecksum = hash.digest('hex');
          console.log(`\nDownload completed. File size: ${downloadedLength} bytes`);
          console.log(`Actual checksum: ${actualChecksum}`);
          
          if (expectedChecksum && actualChecksum !== expectedChecksum) {
            reject(new Error(`Checksum mismatch. Expected: ${expectedChecksum}, Got: ${actualChecksum}`));
            return;
          }
          
          console.log('✅ Checksum verified');
          resolve(tempFile);
        });

        writer.on('error', reject);
        response.data.on('error', reject);
      });

    } catch (error) {
      console.error('Download failed:', error.response?.data || error.message);
      throw new Error(`Download failed: ${error.response?.data?.error || error.message}`);
    }
  }
}

async function testFullUpdate() {
  console.log('🚀 Testing Full MCP Update Process...\n');

  const updater = new DevMCPUpdater();

  // Set up event listeners
  updater.on('update-start', () => {
    console.log('📡 Update process started');
  });

  updater.on('download-progress', ({ percent, downloaded, total }) => {
    process.stdout.write(`\r📥 Download progress: ${percent}% (${(downloaded/1024).toFixed(1)}KB / ${(total/1024).toFixed(1)}KB)`);
  });

  updater.on('update-complete', ({ version, message }) => {
    console.log(`\n🎉 Update completed: ${message}`);
  });

  updater.on('update-error', ({ error, restored }) => {
    console.log(`\n❌ Update error: ${error}`);
    if (restored) {
      console.log('🔄 Successfully restored from backup');
    }
  });

  try {
    // Step 1: Check for updates
    console.log('1️⃣ Checking for updates...');
    const updateCheck = await updater.checkForUpdates();
    
    if (!updateCheck.hasUpdate) {
      console.log('✅ No updates available. System is up to date.');
      return;
    }

    console.log(`📦 Update available: ${updateCheck.latestVersion}`);
    console.log(`📝 Description: ${updateCheck.updateInfo.description}`);
    console.log(`📏 Size: ${(updateCheck.updateInfo.size / 1024).toFixed(2)} KB`);
    console.log();

    // Step 2: Create backup
    console.log('2️⃣ Creating backup...');
    await updater.createBackup();
    console.log('✅ Backup created successfully');
    console.log();

    // Step 3: Download update
    console.log('3️⃣ Downloading update...');
    const downloadedFile = await updater.downloadUpdate(updateCheck.latestVersion);
    console.log(`\n✅ Update downloaded: ${downloadedFile}`);
    console.log();

    // Step 4: Extract and install (simulate)
    console.log('4️⃣ Extracting update...');
    const AdmZip = require('adm-zip');
    const fs = require('fs');
    const path = require('path');

    const zip = new AdmZip(downloadedFile);
    const extractPath = path.join(__dirname, 'temp', 'extracted');
    
    if (fs.existsSync(extractPath)) {
      fs.rmSync(extractPath, { recursive: true, force: true });
    }
    
    zip.extractAllTo(extractPath, true);
    console.log(`✅ Update extracted to: ${extractPath}`);

    // List extracted files
    const extractedFiles = fs.readdirSync(extractPath);
    console.log(`📁 Extracted ${extractedFiles.length} files:`);
    extractedFiles.slice(0, 5).forEach(file => {
      console.log(`   - ${file}`);
    });
    if (extractedFiles.length > 5) {
      console.log(`   ... and ${extractedFiles.length - 5} more files`);
    }
    console.log();

    // Step 5: Simulate installation (don't actually replace files in test)
    console.log('5️⃣ Simulating installation...');
    console.log('⚠️ In production, this would:');
    console.log('   - Stop the MCP server');
    console.log('   - Replace MCP server files');
    console.log('   - Update version.json');
    console.log('   - Restart the MCP server');
    console.log('✅ Installation simulation completed');
    console.log();

    // Step 6: Cleanup
    console.log('6️⃣ Cleaning up...');
    if (fs.existsSync(downloadedFile)) {
      fs.unlinkSync(downloadedFile);
    }
    if (fs.existsSync(extractPath)) {
      fs.rmSync(extractPath, { recursive: true, force: true });
    }
    await updater.cleanup();
    console.log('✅ Cleanup completed');
    console.log();

    // Summary
    console.log('🎉 Full Update Test Summary:');
    console.log('   ✅ Update check successful');
    console.log('   ✅ Backup creation successful');
    console.log('   ✅ Download with progress tracking successful');
    console.log('   ✅ Checksum verification successful');
    console.log('   ✅ File extraction successful');
    console.log('   ✅ Installation simulation successful');
    console.log('   ✅ Cleanup successful');
    console.log();
    console.log('🚀 The MCP update system is fully functional!');
    console.log('💡 Ready for production deployment with authentication.');

  } catch (error) {
    console.error('\n❌ Update test failed:', error.message);
    
    // Try to restore from backup
    try {
      console.log('🔄 Attempting to restore from backup...');
      await updater.restoreFromBackup();
      console.log('✅ Successfully restored from backup');
    } catch (restoreError) {
      console.error('❌ Failed to restore from backup:', restoreError.message);
    }
    
    process.exit(1);
  }
}

// Run the test
testFullUpdate(); 