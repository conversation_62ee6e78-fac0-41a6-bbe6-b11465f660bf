const express = require('express');
const router = express.Router();
const { authenticateJWT } = require('../../middleware/auth');
const tokenUsageService = require('../../services/tokenUsageService');

// Middleware to check if user is an admin
const isAdmin = (req, res, next) => {
  const adminEmails = ['<EMAIL>', '<EMAIL>'];

  // Check if user has is_admin flag set
  if (req.user && req.user.is_admin === true) {
    console.log('[Admin] User is admin by flag:', req.user.email);
    return next();
  }

  // Check if user email is in admin list
  if (req.user && req.user.email && adminEmails.includes(req.user.email.toLowerCase())) {
    console.log('[Admin] User is admin by email:', req.user.email);
    return next();
  }

  // For development, check environment variable
  if (process.env.NODE_ENV === 'development' && process.env.BYPASS_ADMIN_CHECK === 'true') {
    console.log('[Admin] Bypassing admin check in development mode');
    return next();
  }

  console.log('[Admin] Access denied for user:', req.user?.email || 'unknown');
  return res.status(403).json({ error: 'Admin access required' });
};

/**
 * Get token usage statistics
 * GET /api/admin/token-usage/statistics?period=month
 */
router.get('/statistics', authenticateJWT, isAdmin, async (req, res) => {
  try {
    console.log('[Admin] Getting token usage statistics');
    console.log('[Admin] User:', req.user);

    const period = req.query.period || 'month';
    console.log(`[Admin] Period: ${period}`);

    const statistics = await tokenUsageService.getUsageStatistics(period);
    console.log(`[Admin] Statistics retrieved: ${!!statistics}`);

    if (!statistics) {
      console.log('[Admin] No statistics returned');
      // Return empty statistics instead of error
      return res.json({
        period,
        totalUsage: { promptTokens: 0, completionTokens: 0, totalTokens: 0 },
        costs: { promptCost: '0.00', completionCost: '0.00', totalCost: '0.00' },
        revenue: { basicUsers: 0, proUsers: 0, totalUsers: 0, basicRevenue: '0.00', proRevenue: '0.00', totalRevenue: '0.00' },
        profit: { totalProfit: '0.00', margin: '0.00%' },
        userBreakdown: []
      });
    }

    console.log('[Admin] Returning token usage statistics');
    res.json(statistics);
  } catch (error) {
    console.error('Error getting usage statistics:', error);
    console.error('Error stack:', error.stack);
    // Return empty statistics instead of error
    res.json({
      period: req.query.period || 'month',
      totalUsage: { promptTokens: 0, completionTokens: 0, totalTokens: 0 },
      costs: { promptCost: '0.00', completionCost: '0.00', totalCost: '0.00' },
      revenue: { basicUsers: 0, proUsers: 0, totalUsers: 0, basicRevenue: '0.00', proRevenue: '0.00', totalRevenue: '0.00' },
      profit: { totalProfit: '0.00', margin: '0.00%' },
      userBreakdown: []
    });
  }
});

/**
 * Get high-usage users
 * GET /api/admin/token-usage/high-usage-users?limit=10
 */
router.get('/high-usage-users', authenticateJWT, isAdmin, async (req, res) => {
  try {
    console.log('[Admin] Getting high-usage users');
    console.log('[Admin] User:', req.user);

    const limit = parseInt(req.query.limit) || 10;
    console.log(`[Admin] Limit: ${limit}`);

    const statistics = await tokenUsageService.getUsageStatistics('month');
    console.log(`[Admin] Statistics retrieved: ${!!statistics}`);

    if (!statistics) {
      console.log('[Admin] No statistics returned');
      return res.json([]);
    }

    if (!statistics.userBreakdown) {
      console.log('[Admin] No user breakdown in statistics');
      return res.json([]);
    }

    console.log(`[Admin] User breakdown count: ${statistics.userBreakdown.length}`);

    // Sort by total tokens and take the top users
    const highUsageUsers = statistics.userBreakdown
      .sort((a, b) => b.totalTokens - a.totalTokens)
      .slice(0, limit);

    console.log(`[Admin] Returning ${highUsageUsers.length} high-usage users`);

    res.json(highUsageUsers);
  } catch (error) {
    console.error('Error getting high-usage users:', error);
    console.error('Error stack:', error.stack);
    // Return empty array instead of error
    res.json([]);
  }
});

/**
 * Get usage for a specific user
 * GET /api/admin/token-usage/user/:userId?period=month
 */
router.get('/user/:userId', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const period = req.query.period || 'month';

    // Get the start date based on period
    const startDate = new Date();
    switch (period) {
      case 'day':
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'week':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case 'month':
        startDate.setDate(1);
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'year':
        startDate.setMonth(0, 1);
        startDate.setHours(0, 0, 0, 0);
        break;
    }

    // Query Supabase for user's usage
    const supabase = require('../../services/supabaseClient');
    const { data, error } = await supabase
      .from('token_usage')
      .select('*')
      .eq('user_id', userId)
      .gte('timestamp', startDate.toISOString())
      .order('timestamp', { ascending: false });

    if (error) {
      console.error('Error getting user usage:', error);
      return res.status(500).json({ error: 'Failed to get user usage' });
    }

    // Calculate totals
    const totalPromptTokens = data.reduce((sum, record) => sum + record.prompt_tokens, 0);
    const totalCompletionTokens = data.reduce((sum, record) => sum + record.completion_tokens, 0);
    const totalTokens = data.reduce((sum, record) => sum + record.total_tokens, 0);

    // Calculate costs
    const promptCost = (totalPromptTokens / 1000) * tokenUsageService.COST_PER_1K_TOKENS.prompt;
    const completionCost = (totalCompletionTokens / 1000) * tokenUsageService.COST_PER_1K_TOKENS.completion;
    const totalCost = promptCost + completionCost;

    // Get user details
    const authService = require('../../services/supabaseAuthService');
    const user = await authService.getUserById(userId);

    res.json({
      user: {
        id: userId,
        email: user?.email || 'Unknown',
        name: user?.name || 'Unknown',
        plan: user?.subscriptionPlan || 'basic'
      },
      period,
      usage: {
        records: data,
        totals: {
          promptTokens: totalPromptTokens,
          completionTokens: totalCompletionTokens,
          totalTokens: totalTokens
        },
        costs: {
          promptCost: promptCost.toFixed(2),
          completionCost: completionCost.toFixed(2),
          totalCost: totalCost.toFixed(2)
        }
      }
    });
  } catch (error) {
    console.error('Error getting user usage:', error);
    res.status(500).json({ error: 'Failed to get user usage' });
  }
});

/**
 * Apply usage restriction to a user
 * POST /api/admin/token-usage/restrict-user/:userId
 * Body: { restriction: 'none'|'throttled'|'limited'|'blocked', reason: 'string' }
 */
router.post('/restrict-user/:userId', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { restriction, reason } = req.body;

    // Validate restriction type
    const validRestrictions = ['none', 'throttled', 'limited', 'blocked'];
    if (!validRestrictions.includes(restriction)) {
      return res.status(400).json({
        error: 'Invalid restriction type',
        validRestrictions
      });
    }

    // Update user with restriction
    const authService = require('../../services/supabaseAuthService');
    const updated = await authService.updateUser(userId, {
      usageRestriction: restriction,
      usageRestrictionReason: reason,
      usageRestrictionDate: new Date().toISOString()
    });

    if (!updated) {
      return res.status(500).json({ error: 'Failed to update user' });
    }

    // If restriction is 'blocked', also update subscription status
    if (restriction === 'blocked') {
      await authService.updateSubscription(userId, 'restricted');
    }

    // TODO: Send email notification to user about restriction

    res.json({
      success: true,
      message: `User ${userId} has been ${restriction === 'none' ? 'unrestricted' : restriction}`
    });
  } catch (error) {
    console.error('Error restricting user:', error);
    res.status(500).json({ error: 'Failed to restrict user' });
  }
});

/**
 * Track token usage directly from frontend
 * POST /api/admin/token-usage/track
 * Body: { userId, modelId, promptTokens, completionTokens, requestType }
 */
router.post('/track', async (req, res) => {
  try {
    const { userId, modelId, promptTokens, completionTokens, requestType } = req.body;

    // Validate required fields
    if (!userId || !modelId || promptTokens === undefined || completionTokens === undefined) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    console.log(`Tracking token usage from frontend: ${promptTokens} prompt, ${completionTokens} completion for user ${userId}`);

    // Track token usage
    const result = await tokenUsageService.trackTokenUsage(
      userId,
      modelId,
      promptTokens,
      completionTokens,
      requestType || 'chat'
    );

    if (!result) {
      return res.status(500).json({ error: 'Failed to track token usage' });
    }

    res.json({
      success: true,
      message: `Successfully tracked ${promptTokens + completionTokens} tokens for user ${userId}`,
      recordId: result.id
    });
  } catch (error) {
    console.error('Error tracking token usage:', error);
    res.status(500).json({ error: 'Failed to track token usage' });
  }
});

/**
 * Add test token usage data
 * POST /api/admin/token-usage/add-test-data
 */
router.post('/add-test-data', authenticateJWT, isAdmin, async (req, res) => {
  try {
    console.log('Adding test token usage data...');

    // Get all users from the database
    const supabase = require('../../services/supabaseClient');
    const authService = require('../../services/supabaseAuthService');

    console.log('Fetching users from Supabase...');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email')
      .limit(5);

    if (usersError) {
      console.error('Error fetching users:', usersError);
      return res.status(500).json({ error: 'Failed to fetch users' });
    }

    console.log(`Found ${users?.length || 0} users`);

    // Use the current user if no users found
    const testUsers = users?.length > 0 ? users : [{ id: req.user.id, email: req.user.email }];

    // Models to use for test data
    const models = [
      'claude-3.7-sonnet-20240307',
      'claude-3-opus-20240229',
      'claude-3-sonnet-20240229',
      'claude-3-haiku-20240307'
    ];

    let recordCount = 0;

    // Generate random token usage data for each user
    for (const user of testUsers) {
      console.log(`Generating token usage data for user: ${user.email || user.id}`);

      // Generate 5 records per user
      for (let i = 0; i < 5; i++) {
        const modelId = models[Math.floor(Math.random() * models.length)];
        const promptTokens = Math.floor(Math.random() * 1000) + 100;
        const completionTokens = Math.floor(Math.random() * 2000) + 200;
        const totalTokens = promptTokens + completionTokens;

        // Get user's subscription plan
        let subscriptionPlan = 'basic';
        try {
          const userDetails = await authService.getUserById(user.id);
          subscriptionPlan = userDetails?.subscriptionPlan || 'basic';
        } catch (error) {
          console.log(`Could not get subscription plan for user ${user.id}, using 'basic'`);
        }

        // Insert the record
        const result = await tokenUsageService.trackTokenUsage(
          user.id,
          modelId,
          promptTokens,
          completionTokens,
          'chat'
        );

        if (result) {
          console.log(`Added token usage record for user ${user.id}: ${promptTokens} prompt tokens, ${completionTokens} completion tokens`);
          recordCount++;
        } else {
          console.error(`Error inserting token usage record for user ${user.id}`);
        }
      }
    }

    console.log(`Finished adding ${recordCount} test token usage records`);

    res.json({
      success: true,
      message: `Successfully added ${recordCount} test token usage records`,
      count: recordCount
    });
  } catch (error) {
    console.error('Error adding test token usage data:', error);
    res.status(500).json({ error: 'Failed to add test token usage data' });
  }
});

/**
 * Get token usage records
 * GET /api/admin/token-usage/records?from=2023-01-01&to=2023-01-31&page=1&pageSize=10
 */
router.get('/records', authenticateJWT, isAdmin, async (req, res) => {
  try {
    console.log('[Admin] Getting token usage records');

    const { from, to, page = 1, pageSize = 10 } = req.query;

    if (!from || !to) {
      return res.status(400).json({ error: 'Missing required parameters: from, to' });
    }

    // Parse dates
    const fromDate = new Date(from);
    const toDate = new Date(to);

    if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
      return res.status(400).json({ error: 'Invalid date format. Use YYYY-MM-DD' });
    }

    console.log(`[Admin] Date range: ${fromDate.toISOString()} to ${toDate.toISOString()}`);
    console.log(`[Admin] Pagination: page ${page}, pageSize ${pageSize}`);

    // Calculate offset
    const offset = (parseInt(page) - 1) * parseInt(pageSize);

    // Query token usage data
    const { data, error, count } = await supabase
      .from('token_usage')
      .select('*, users!inner(email)', { count: 'exact' })
      .gte('timestamp', fromDate.toISOString())
      .lte('timestamp', toDate.toISOString())
      .order('timestamp', { ascending: false })
      .range(offset, offset + parseInt(pageSize) - 1);

    if (error) {
      console.error('[Admin] Error querying token usage records:', error);
      return res.status(500).json({ error: 'Failed to query token usage records' });
    }

    // Format records
    const records = data.map(record => ({
      id: record.id,
      user_id: record.user_id,
      user_email: record.users?.email,
      model_id: record.model_id,
      prompt_tokens: record.prompt_tokens,
      completion_tokens: record.completion_tokens,
      total_tokens: record.total_tokens,
      timestamp: record.timestamp,
      request_type: record.request_type,
      subscription_plan: record.subscription_plan
    }));

    return res.json({
      records,
      total: count || records.length,
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      totalPages: Math.ceil((count || records.length) / parseInt(pageSize))
    });
  } catch (error) {
    console.error('[Admin] Error getting token usage records:', error);
    return res.status(500).json({ error: 'Failed to get token usage records' });
  }
});

/**
 * Get usage trends over time
 * GET /api/admin/token-usage/trends?period=month&interval=day
 */
router.get('/trends', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const period = req.query.period || 'month';
    const interval = req.query.interval || 'day';

    console.log(`[Admin] Getting usage trends for period: ${period}, interval: ${interval}`);

    // Calculate start date based on period
    const startDate = new Date();
    switch (period) {
      case 'week':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(startDate.getMonth() - 1);
        break;
      case 'quarter':
        startDate.setMonth(startDate.getMonth() - 3);
        break;
      case 'year':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      default:
        startDate.setMonth(startDate.getMonth() - 1); // Default to month
    }

    // Query Supabase for usage data
    const supabase = require('../../services/supabaseClient');
    const { data, error } = await supabase
      .from('token_usage')
      .select('*')
      .gte('timestamp', startDate.toISOString())
      .order('timestamp', { ascending: true });

    if (error) {
      console.error('Error getting usage trends:', error);
      return res.status(500).json({ error: 'Failed to get usage trends' });
    }

    // Group data by interval
    const trendData = [];
    const intervalMap = {};

    // Create interval formatter based on selected interval
    let getIntervalKey;
    switch (interval) {
      case 'hour':
        getIntervalKey = (date) => {
          return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:00`;
        };
        break;
      case 'day':
        getIntervalKey = (date) => {
          return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
        };
        break;
      case 'week':
        getIntervalKey = (date) => {
          const firstDayOfWeek = new Date(date);
          const day = date.getDay();
          const diff = date.getDate() - day + (day === 0 ? -6 : 1); // Adjust for Sunday
          firstDayOfWeek.setDate(diff);
          return `${firstDayOfWeek.getFullYear()}-${String(firstDayOfWeek.getMonth() + 1).padStart(2, '0')}-${String(firstDayOfWeek.getDate()).padStart(2, '0')}`;
        };
        break;
      case 'month':
        getIntervalKey = (date) => {
          return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        };
        break;
      default:
        getIntervalKey = (date) => {
          return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
        };
    }

    // Process data
    for (const record of data) {
      const date = new Date(record.timestamp);
      const intervalKey = getIntervalKey(date);

      if (!intervalMap[intervalKey]) {
        intervalMap[intervalKey] = {
          interval: intervalKey,
          promptTokens: 0,
          completionTokens: 0,
          totalTokens: 0,
          uniqueUsers: new Set(),
          basicUsers: new Set(),
          proUsers: new Set()
        };
      }

      // Add tokens
      intervalMap[intervalKey].promptTokens += record.prompt_tokens;
      intervalMap[intervalKey].completionTokens += record.completion_tokens;
      intervalMap[intervalKey].totalTokens += record.total_tokens;

      // Track unique users
      intervalMap[intervalKey].uniqueUsers.add(record.user_id);

      // Track users by plan
      if (record.subscription_plan === 'pro') {
        intervalMap[intervalKey].proUsers.add(record.user_id);
      } else {
        intervalMap[intervalKey].basicUsers.add(record.user_id);
      }
    }

    // Convert to array and calculate costs
    for (const key in intervalMap) {
      const item = intervalMap[key];

      // Calculate costs
      const promptCost = (item.promptTokens / 1000) * tokenUsageService.COST_PER_1K_TOKENS.prompt;
      const completionCost = (item.completionTokens / 1000) * tokenUsageService.COST_PER_1K_TOKENS.completion;
      const totalCost = promptCost + completionCost;

      // Convert Sets to counts
      const uniqueUserCount = item.uniqueUsers.size;
      const basicUserCount = item.basicUsers.size;
      const proUserCount = item.proUsers.size;

      // Calculate revenue (rough estimate)
      const basicRevenue = basicUserCount * 20 / 30; // Prorated for daily revenue
      const proRevenue = proUserCount * 30 / 30; // Prorated for daily revenue
      const totalRevenue = basicRevenue + proRevenue;

      // Calculate profit
      const profit = totalRevenue - totalCost;

      // Add to trend data
      trendData.push({
        interval: item.interval,
        usage: {
          promptTokens: item.promptTokens,
          completionTokens: item.completionTokens,
          totalTokens: item.totalTokens
        },
        costs: {
          promptCost: promptCost.toFixed(2),
          completionCost: completionCost.toFixed(2),
          totalCost: totalCost.toFixed(2)
        },
        users: {
          uniqueUsers: uniqueUserCount,
          basicUsers: basicUserCount,
          proUsers: proUserCount
        },
        revenue: {
          basicRevenue: basicRevenue.toFixed(2),
          proRevenue: proRevenue.toFixed(2),
          totalRevenue: totalRevenue.toFixed(2)
        },
        profit: profit.toFixed(2)
      });
    }

    // Sort by interval
    trendData.sort((a, b) => a.interval.localeCompare(b.interval));

    res.json({
      period,
      interval,
      trends: trendData
    });
  } catch (error) {
    console.error('Error getting usage trends:', error);
    res.status(500).json({ error: 'Failed to get usage trends' });
  }
});

/**
 * Get user growth trends
 * GET /api/admin/token-usage/user-growth?period=month
 */
router.get('/user-growth', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const period = req.query.period || 'month';

    console.log(`[Admin] Getting user growth trends for period: ${period}`);

    // Calculate start date based on period
    const startDate = new Date();
    switch (period) {
      case 'week':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(startDate.getMonth() - 1);
        break;
      case 'quarter':
        startDate.setMonth(startDate.getMonth() - 3);
        break;
      case 'year':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      default:
        startDate.setMonth(startDate.getMonth() - 1); // Default to month
    }

    // Query Supabase for user data
    const supabase = require('../../services/supabaseClient');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, created_at, subscription_status, subscription_plan')
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: true });

    if (usersError) {
      console.error('Error getting user growth data:', usersError);
      return res.status(500).json({ error: 'Failed to get user growth data' });
    }

    // Group data by day
    const growthData = [];
    const dailyMap = {};

    // Process user data
    for (const user of users) {
      const date = new Date(user.created_at);
      const dayKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;

      if (!dailyMap[dayKey]) {
        dailyMap[dayKey] = {
          date: dayKey,
          newUsers: 0,
          newBasicUsers: 0,
          newProUsers: 0
        };
      }

      // Count new users
      dailyMap[dayKey].newUsers++;

      // Count by plan
      if (user.subscription_plan === 'pro' || user.subscription_status === 'pro') {
        dailyMap[dayKey].newProUsers++;
      } else {
        dailyMap[dayKey].newBasicUsers++;
      }
    }

    // Convert to array
    for (const key in dailyMap) {
      growthData.push(dailyMap[key]);
    }

    // Sort by date
    growthData.sort((a, b) => a.date.localeCompare(b.date));

    // Calculate cumulative growth
    let totalUsers = 0;
    let totalBasicUsers = 0;
    let totalProUsers = 0;

    for (const item of growthData) {
      totalUsers += item.newUsers;
      totalBasicUsers += item.newBasicUsers;
      totalProUsers += item.newProUsers;

      item.totalUsers = totalUsers;
      item.totalBasicUsers = totalBasicUsers;
      item.totalProUsers = totalProUsers;
    }

    res.json({
      period,
      growth: growthData
    });
  } catch (error) {
    console.error('Error getting user growth trends:', error);
    res.status(500).json({ error: 'Failed to get user growth trends' });
  }
});

module.exports = router;
