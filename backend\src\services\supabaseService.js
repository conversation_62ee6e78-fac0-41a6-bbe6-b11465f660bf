const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client if credentials are available
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

let supabase = null;
let initializationError = null;

// Function to check if Supabase is properly initialized
const isSupabaseInitialized = () => {
  return supabase !== null;
};

// Function to get the initialization error if any
const getInitializationError = () => {
  return initializationError;
};

// Function to test the Supabase connection
const testSupabaseConnection = async () => {
  if (!supabase) {
    throw new Error('Supabase client not initialized');
  }

  try {
    // Try a simple query to verify the connection
    const { data, error } = await supabase
      .from('token_balance')
      .select('count')
      .limit(1);

    if (error) {
      throw new Error(`Supabase connection test failed: ${error.message}`);
    }

    return { success: true, message: 'Supabase connection test successful' };
  } catch (error) {
    console.error('Supabase connection test failed:', error);
    throw error;
  }
};

// Initialize Supabase client
if (!supabaseUrl || !supabaseKey) {
  console.error('SUPABASE_URL or SUPABASE_SERVICE_KEY is not set. Supabase client not initialized.');
  initializationError = new Error('SUPABASE_URL or SUPABASE_SERVICE_KEY is not set');
} else {
  try {
    console.log('Initializing Supabase client with URL:', supabaseUrl);
    console.log('Supabase key length:', supabaseKey ? supabaseKey.length : 0);

    // Create the Supabase client with additional options
    supabase = createClient(supabaseUrl, supabaseKey, {
      auth: {
        persistSession: false // Don't persist session in browser storage
      },
      db: {
        schema: 'public'
      },
      global: {
        headers: {
          'x-application-name': 'createlex-backend'
        }
      }
    });

    console.log('Supabase client initialized successfully');

    // Test the connection immediately to verify it works
    testSupabaseConnection()
      .then(result => {
        console.log('Initial Supabase connection test:', result.message);
      })
      .catch(error => {
        console.error('Initial Supabase connection test failed:', error.message);
        // Don't set supabase to null here, as the connection might work later
      });
  } catch (error) {
    console.error('Error initializing Supabase client:', error);
    initializationError = error;
  }
}

module.exports = {
  supabase,
  isSupabaseInitialized,
  getInitializationError,
  testSupabaseConnection
};
