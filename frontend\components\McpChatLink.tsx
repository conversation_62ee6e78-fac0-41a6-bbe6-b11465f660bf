'use client';

import { useSupabaseAuth } from '../contexts/SupabaseAuthContext';
import { useAuth } from '../contexts/AuthContext';
import Link from 'next/link';
import { useState, useEffect } from 'react';

interface McpChatLinkProps {
  className?: string;
  children?: React.ReactNode;
}

export default function McpChatLink({ className, children }: McpChatLinkProps) {
  const { user, isLoading: supabaseLoading } = useSupabaseAuth();
  const { hasActiveSubscription, isLoading: authLoading, refreshSubscriptionStatus } = useAuth();
  const [isCheckingSubscription, setIsCheckingSubscription] = useState(false);
  const [subscriptionChecked, setSubscriptionChecked] = useState(false);

  // Check subscription status when component mounts
  useEffect(() => {
    const checkSubscription = async () => {
      if (!supabaseLoading && user && !subscriptionChecked) {
        setIsCheckingSubscription(true);
        try {
          await refreshSubscriptionStatus();
          setSubscriptionChecked(true);
        } catch (error) {
          console.error('Error checking subscription:', error);
        } finally {
          setIsCheckingSubscription(false);
        }
      }
    };

    checkSubscription();
  }, [user, supabaseLoading, refreshSubscriptionStatus, subscriptionChecked]);

  // Show loading state while checking auth or subscription
  if (supabaseLoading || authLoading || isCheckingSubscription || !subscriptionChecked) {
    return (
      <button
        className={`inline-block px-4 py-2 bg-gray-300 text-gray-600 rounded-md cursor-not-allowed ${className}`}
        disabled
      >
        Loading...
      </button>
    );
  }

  // Show disabled button if no subscription
  if (!hasActiveSubscription) {
    return (
      <button
        className={`inline-block px-4 py-2 bg-gray-300 text-gray-600 rounded-md cursor-not-allowed ${className}`}
        disabled
      >
        Requires Subscription
      </button>
    );
  }

  // Show active link if authenticated and has subscription
  return (
    <Link
      href="/chat"
      className={className}
      onClick={() => {
        if (user) {
          console.log('McpChatLink: Opening chat with user ID:', user.id);
        }
      }}
    >
      {children || 'Open CreateLex AI'}
    </Link>
  );
}
