const fs = require('fs-extra');
const path = require('path');

const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function checkFile(filePath, description) {
  if (fs.existsSync(filePath)) {
    log(`✅ ${description}`, colors.green);
    return true;
  } else {
    log(`❌ ${description} - Missing: ${filePath}`, colors.red);
    return false;
  }
}

function checkDirectory(dirPath, description) {
  if (fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory()) {
    const files = fs.readdirSync(dirPath);
    log(`✅ ${description} (${files.length} files)`, colors.green);
    return true;
  } else {
    log(`❌ ${description} - Missing: ${dirPath}`, colors.red);
    return false;
  }
}

async function runTests() {
  log('🧪 Running CreateLex Bridge Tests...', colors.blue);
  let passed = 0;
  let total = 0;

  // Core files
  log('\n📁 Core Application Files:', colors.yellow);
  total++; passed += checkFile('main.js', 'Main Electron process');
  total++; passed += checkFile('preload.js', 'IPC preload script');
  total++; passed += checkFile('claude-bridge.js', 'Claude Desktop bridge');
  total++; passed += checkFile('package.json', 'Package configuration');

  // Source directories
  log('\n📂 Source Directories:', colors.yellow);
  total++; passed += checkDirectory('src/auth', 'Authentication system');
  total++; passed += checkDirectory('src/mcp', 'MCP server wrapper');

  // Build scripts
  log('\n🔧 Build Scripts:', colors.yellow);
  total++; passed += checkFile('scripts/build-frontend.js', 'Frontend build script');
  total++; passed += checkFile('scripts/copy-python.js', 'Python copy script');
  total++; passed += checkFile('scripts/deploy.js', 'Deployment script');

  // Assets
  log('\n🎨 Assets:', colors.yellow);
  total++; passed += checkDirectory('assets/icons', 'Application icons');

  // Check if Python MCP server exists (after build)
  log('\n🐍 MCP Server:', colors.yellow);
  const pythonDir = 'src/python';
  if (fs.existsSync(pythonDir)) {
    total++; passed += checkDirectory(pythonDir, 'Python MCP server');
    total++; passed += checkFile(path.join(pythonDir, 'mcp_server_protected.py'), 'MCP server entry point');
  } else {
    log('⚠️  Python MCP server not copied yet - run "npm run build:python"', colors.yellow);
  }

  // Check if frontend is built
  log('\n🎨 Frontend:', colors.yellow);
  const webDir = 'web';
  if (fs.existsSync(webDir)) {
    total++; passed += checkDirectory(webDir, 'Built frontend files');
  } else {
    log('⚠️  Frontend not built yet - run "npm run build:frontend"', colors.yellow);
  }

  // Package.json validation
  log('\n📦 Package Configuration:', colors.yellow);
  try {
    const pkg = require('../package.json');
    total++; passed += pkg.main === 'main.js' ? 1 : 0;
    if (pkg.main === 'main.js') {
      log('✅ Main entry point correct', colors.green);
    } else {
      log('❌ Main entry point incorrect', colors.red);
    }

    total++; passed += pkg.scripts.deploy ? 1 : 0;
    if (pkg.scripts.deploy) {
      log('✅ Deploy script configured', colors.green);
    } else {
      log('❌ Deploy script missing', colors.red);
    }
  } catch (error) {
    log('❌ Package.json validation failed', colors.red);
  }

  // Summary
  log('\n📊 Test Summary:', colors.blue);
  const percentage = Math.round((passed / total) * 100);
  log(`Passed: ${passed}/${total} (${percentage}%)`, 
      percentage >= 80 ? colors.green : colors.red);

  if (percentage >= 80) {
    log('\n🎉 CreateLex Bridge is ready!', colors.green);
    log('Next steps:', colors.yellow);
    log('  1. npm start - Test in development mode');
    log('  2. npm run deploy - Build for production');
  } else {
    log('\n⚠️  Some components are missing', colors.yellow);
    log('Run the build commands to complete setup', colors.yellow);
  }

  return percentage >= 80;
}

runTests().catch(error => {
  log('❌ Test failed:', colors.red);
  log(error.message, colors.red);
  process.exit(1);
}); 