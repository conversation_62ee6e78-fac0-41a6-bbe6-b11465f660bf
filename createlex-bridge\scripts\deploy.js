const { execSync } = require('child_process');
const fs = require('fs-extra');
const path = require('path');

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  blue: '\x1b[34m',
  yellow: '\x1b[33m',
  red: '\x1b[31m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function runCommand(command, cwd = process.cwd()) {
  log(`Running: ${command}`, colors.blue);
  try {
    execSync(command, { cwd, stdio: 'inherit' });
    return true;
  } catch (error) {
    log(`❌ Command failed: ${command}`, colors.red);
    log(error.message, colors.red);
    return false;
  }
}

async function main() {
  const startTime = Date.now();
  log('🚀 Starting CreateLex Bridge deployment...', colors.bright);

  // Check prerequisites
  log('\n📋 Checking prerequisites...', colors.yellow);
  
  const nodeVersion = process.version;
  log(`Node.js version: ${nodeVersion}`);
  
  try {
    execSync('python --version', { stdio: 'pipe' });
    log('✅ Python found');
  } catch {
    log('❌ Python not found - required for MCP server', colors.red);
    process.exit(1);
  }

  // Check if frontend exists
  const frontendDir = path.resolve(__dirname, '..', '..', 'frontend');
  if (!fs.existsSync(frontendDir)) {
    log('❌ Frontend directory not found', colors.red);
    log('Expected: ' + frontendDir, colors.red);
    process.exit(1);
  }

  // Install dependencies if needed
  log('\n📦 Installing dependencies...', colors.yellow);
  if (!runCommand('npm install')) {
    process.exit(1);
  }

  // Build frontend
  log('\n🎨 Building frontend...', colors.yellow);
  if (!runCommand('npm run build:frontend')) {
    process.exit(1);
  }

  // Copy Python MCP server
  log('\n🐍 Copying MCP server...', colors.yellow);
  if (!runCommand('npm run build:python')) {
    process.exit(1);
  }

  // Build Electron app
  log('\n⚡ Building Electron application...', colors.yellow);
  const platform = process.platform;
  let buildCommand;
  
  switch (platform) {
    case 'win32':
      buildCommand = 'npm run build-win';
      break;
    case 'darwin':
      buildCommand = 'npm run build-mac';
      break;
    case 'linux':
      buildCommand = 'npm run build-linux';
      break;
    default:
      buildCommand = 'npm run build';
  }

  if (!runCommand(buildCommand)) {
    process.exit(1);
  }

  // Success summary
  const endTime = Date.now();
  const duration = Math.round((endTime - startTime) / 1000);
  
  log('\n🎉 Deployment completed successfully!', colors.green);
  log(`⏱️  Total time: ${duration}s`, colors.green);
  log('\n📁 Output files:', colors.bright);
  
  const distDir = path.resolve(__dirname, '..', 'dist');
  if (fs.existsSync(distDir)) {
    const files = fs.readdirSync(distDir);
    files.forEach(file => {
      const filePath = path.join(distDir, file);
      const stats = fs.statSync(filePath);
      const size = Math.round(stats.size / 1024 / 1024 * 100) / 100;
      log(`  📦 ${file} (${size} MB)`, colors.blue);
    });
  }

  log('\n🚀 Ready for distribution!', colors.bright);
  log('Next steps:', colors.yellow);
  log('  1. Test the built application');
  log('  2. Update version in package.json if needed');
  log('  3. Create release notes');
  log('  4. Distribute to users');
}

// Handle errors gracefully
process.on('uncaughtException', (error) => {
  log('❌ Uncaught exception:', colors.red);
  log(error.message, colors.red);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  log('❌ Unhandled rejection:', colors.red);
  log(reason, colors.red);
  process.exit(1);
});

main().catch(error => {
  log('❌ Deployment failed:', colors.red);
  log(error.message, colors.red);
  process.exit(1);
}); 