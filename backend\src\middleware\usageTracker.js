const supabase = require('../services/supabaseClient');

/**
 * Middleware to track token usage for API requests
 * This will log usage data to the usage_logs table in Supabase
 */
function usageTracker() {
  return async (req, res, next) => {
    // Store the original send function
    const originalSend = res.send;

    // Get request start time
    const requestStartTime = Date.now();

    // Generate a unique request ID
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

    // Extract user ID from request (assuming it's set by auth middleware)
    const userId = req.user?.id;

    // Create a function to log usage
    const logUsage = async (responseData, status) => {
      try {
        // Skip logging for non-AI requests
        if (!req.path.includes('/api/chat') && !req.path.includes('/api/completion')) {
          return;
        }

        // Extract token count and model from response
        let tokenCount = 0;
        let model = 'unknown';
        let cost = 0;

        if (responseData) {
          try {
            const data = typeof responseData === 'string' ? JSON.parse(responseData) : responseData;

            // Extract token count based on response format
            if (data.usage) {
              // OpenAI format
              tokenCount = data.usage.total_tokens || 0;
              model = data.model || 'unknown';

              // Calculate cost based on model and token count
              cost = calculateCost(model, tokenCount);
            } else if (data.tokenCount) {
              // Our custom format
              tokenCount = data.tokenCount;
              model = data.model || 'unknown';
              cost = calculateCost(model, tokenCount);
            }
          } catch (parseError) {
            console.error('Error parsing response data:', parseError);
          }
        }

        // Check if usage_logs table exists
        const { error: tableCheckError } = await supabase
          .from('usage_logs')
          .select('count')
          .limit(1);

        if (tableCheckError && tableCheckError.code === '42P01') {
          console.log('usage_logs table does not exist yet, skipping usage tracking');
          return;
        }

        // Log usage to Supabase
        const { error } = await supabase
          .from('usage_logs')
          .insert({
            user_id: userId || null, // Ensure it's null if userId is undefined
            request_id: requestId,
            endpoint: req.path,
            token_count: tokenCount,
            model: model,
            cost: cost,
            status: status,
            ip_address: req.ip,
            user_agent: req.headers['user-agent'],
            request_data: {
              method: req.method,
              path: req.path,
              query: req.query,
              body: sanitizeRequestBody(req.body)
            },
            response_data: sanitizeResponseData(responseData)
          });

        if (error) {
          console.error('Error logging usage:', error);
        } else {
          console.log(`Logged usage: ${tokenCount} tokens for ${model} (${cost})`);
        }
      } catch (error) {
        console.error('Error in usage tracking:', error);
      }
    };

    // Override the send function to capture the response
    res.send = function(data) {
      // Get response status
      const status = res.statusCode.toString();

      // Log usage asynchronously (don't wait for it)
      logUsage(data, status).catch(err => {
        console.error('Error in async usage logging:', err);
      });

      // Call the original send function
      return originalSend.call(this, data);
    };

    // Continue to the next middleware
    next();
  };
}

/**
 * Calculate cost based on model and token count
 * These are approximate costs and should be updated based on actual pricing
 */
function calculateCost(model, tokenCount) {
  // Default cost per 1K tokens
  let costPer1K = 0.002;

  // Set cost based on model
  if (model.includes('gpt-4')) {
    costPer1K = 0.06; // GPT-4 input cost (output is higher but we're simplifying)
  } else if (model.includes('gpt-3.5')) {
    costPer1K = 0.002; // GPT-3.5 Turbo
  } else if (model.includes('claude-3')) {
    costPer1K = 0.025; // Claude 3 Opus (approximation)
  } else if (model.includes('claude-2')) {
    costPer1K = 0.015; // Claude 2 (approximation)
  } else if (model.includes('gemini')) {
    costPer1K = 0.0035; // Gemini Pro (approximation)
  }

  // Calculate cost
  return (tokenCount / 1000) * costPer1K;
}

/**
 * Sanitize request body to remove sensitive information
 */
function sanitizeRequestBody(body) {
  if (!body) return null;

  // Create a copy to avoid modifying the original
  const sanitized = { ...body };

  // Remove sensitive fields
  if (sanitized.apiKey) sanitized.apiKey = '[REDACTED]';
  if (sanitized.api_key) sanitized.api_key = '[REDACTED]';
  if (sanitized.key) sanitized.key = '[REDACTED]';
  if (sanitized.token) sanitized.token = '[REDACTED]';
  if (sanitized.password) sanitized.password = '[REDACTED]';

  return sanitized;
}

/**
 * Sanitize response data to reduce size and remove sensitive information
 */
function sanitizeResponseData(data) {
  if (!data) return null;

  try {
    // Parse if it's a string
    const parsed = typeof data === 'string' ? JSON.parse(data) : data;

    // Create a simplified version with just the essential information
    const simplified = {
      model: parsed.model,
      usage: parsed.usage,
      status: parsed.status,
      timestamp: new Date().toISOString()
    };

    return simplified;
  } catch (error) {
    console.error('Error sanitizing response data:', error);
    return null;
  }
}

module.exports = usageTracker;
