'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Users } from 'lucide-react';
import { supabase } from '@/lib/supabase';

export default function UserStatsCard() {
  const [totalUsers, setTotalUsers] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchUserStats() {
      try {
        setLoading(true);
        
        // Get total count of users
        const { count, error } = await supabase
          .from('users')
          .select('*', { count: 'exact', head: true });
          
        if (error) {
          throw error;
        }
        
        setTotalUsers(count || 0);
      } catch (err: any) {
        console.error('Error fetching user stats:', err);
        setError(err.message || 'Failed to load user statistics');
      } finally {
        setLoading(false);
      }
    }

    fetchUserStats();
  }, []);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">Total Users</CardTitle>
        <Users className="h-4 w-4 text-gray-500" />
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="h-9 animate-pulse bg-gray-200 rounded" />
        ) : error ? (
          <div className="text-sm text-red-500">{error}</div>
        ) : (
          <div className="text-2xl font-bold">{totalUsers}</div>
        )}
        <p className="text-xs text-gray-500 mt-1">
          Registered users on the platform
        </p>
      </CardContent>
    </Card>
  );
}
