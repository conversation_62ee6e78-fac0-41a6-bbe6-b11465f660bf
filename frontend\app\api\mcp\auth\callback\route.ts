import { NextRequest, NextResponse } from 'next/server';

// Helper function to check user subscription status
async function checkUserSubscription(email: string, accessToken: string): Promise<boolean> {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/subscription/status`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      return data.hasActiveSubscription || false;
    }

    return false;
  } catch (error) {
    console.warn('Failed to check subscription status:', error);
    return false;
  }
}

// Helper function to notify MCP server
async function notifyMCPServer(userData: { userId: string; email: string; hasSubscription: boolean }) {
  try {
    const mcpResponse = await fetch('http://localhost:8080/auth/success', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_id: userData.userId,
        email: userData.email,
        subscription_status: userData.hasSubscription,
        timestamp: Date.now()
      }),
    });

    if (mcpResponse.ok) {
      const result = await mcpResponse.json();
      console.log('✅ MCP Server notified successfully:', result);
      return true;
    } else {
      console.warn('⚠️ MCP Server notification failed:', mcpResponse.status);
      return false;
    }
  } catch (error) {
    console.warn('⚠️ Failed to notify MCP server:', error);
    return false;
  }
}

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    
    // Check if this is a direct Supabase OAuth callback
    const code = searchParams.get('code');
    const directMcp = searchParams.get('direct_mcp');
    
    // Handle Supabase OAuth callback
    if (code) {
      // Import Supabase here to avoid edge runtime issues
      const { createClient } = await import('@supabase/supabase-js');
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
      const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
      const supabase = createClient(supabaseUrl, supabaseAnonKey);
      
      try {
        // Exchange code for session
        const { data, error } = await supabase.auth.exchangeCodeForSession(code);
        
        if (error) {
          console.error('Supabase auth error:', error);
          return NextResponse.redirect(`${request.nextUrl.origin}/login?error=auth_failed`);
        }
        
        if (data.user) {
          // Check subscription status
          const hasSubscription = await checkUserSubscription(data.user.email!, data.session.access_token);
          
          console.log('Direct MCP Auth Success:', { 
            userId: data.user.id, 
            email: data.user.email, 
            hasSubscription 
          });
          
          // Notify MCP server of successful authentication
          await notifyMCPServer({
            userId: data.user.id,
            email: data.user.email!,
            hasSubscription
          });
          
          // Return success response for AI editors
          return new NextResponse(`
            <!DOCTYPE html>
            <html>
              <head>
                <title>Authentication Successful</title>
                <style>
                  body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
                  .success { background: white; padding: 40px; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); max-width: 500px; margin: 0 auto; }
                  .checkmark { color: #28a745; font-size: 48px; margin-bottom: 20px; }
                  h1 { color: #333; margin-bottom: 10px; }
                  p { color: #666; margin-bottom: 30px; }
                  .button { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 6px; cursor: pointer; text-decoration: none; display: inline-block; }
                </style>
              </head>
              <body>
                <div class="success">
                  <div class="checkmark">✅</div>
                  <h1>Authentication Successful!</h1>
                  <p>Your AI editor has been authenticated with CreateLex.</p>
                  <p>You can now return to your AI editor and start using CreateLex MCP tools.</p>
                  <a href="#" onclick="window.close()" class="button">Close Window</a>
                </div>
                <script>
                  // Auto-close after 3 seconds
                  setTimeout(() => {
                    window.close();
                  }, 3000);
                </script>
              </body>
            </html>
          `, {
            headers: { 'Content-Type': 'text/html' }
          });
        }
      } catch (authError) {
        console.error('Authentication processing error:', authError);
        return NextResponse.redirect(`${request.nextUrl.origin}/login?error=auth_processing_failed`);
      }
    }
    
    // Handle manual callback (from dashboard button)
    const success = searchParams.get('success');
    const userId = searchParams.get('userId');
    const subscriptionStatus = searchParams.get('subscriptionStatus');
    
    console.log('Manual MCP Auth Callback:', { success, userId, subscriptionStatus });

    // Signal to the local MCP server that authentication was successful
    if (success === 'true' && userId) {
      try {
        // Notify the server-side MCP server at localhost:8080
        const mcpResponse = await fetch('http://localhost:8080/auth/success', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId,
            subscriptionStatus: subscriptionStatus === 'true',
            timestamp: new Date().toISOString(),
            source: 'createlex-dashboard'
          })
        });

        console.log('MCP Server notification response:', mcpResponse.status);
      } catch (mcpError) {
        console.error('Failed to notify MCP server:', mcpError);
        // Continue anyway - the user still authenticated successfully
      }

      // Create a success page that closes itself and signals the AI editor
      const successPage = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>CreateLex Authentication Successful</title>
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              display: flex;
              justify-content: center;
              align-items: center;
              height: 100vh;
              margin: 0;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
            }
            .container {
              text-align: center;
              background: rgba(255, 255, 255, 0.1);
              padding: 2rem;
              border-radius: 20px;
              backdrop-filter: blur(10px);
              border: 1px solid rgba(255, 255, 255, 0.2);
            }
            .icon {
              font-size: 4rem;
              margin-bottom: 1rem;
            }
            .title {
              font-size: 2rem;
              margin-bottom: 1rem;
            }
            .message {
              font-size: 1.2rem;
              margin-bottom: 2rem;
              opacity: 0.9;
            }
            .countdown {
              font-size: 1rem;
              opacity: 0.8;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="icon">✅</div>
            <div class="title">Authentication Successful!</div>
            <div class="message">
              Your CreateLex MCP integration is now authenticated.<br>
              You can return to your AI editor and try MCP commands.
            </div>
            <div class="countdown">This window will close automatically in <span id="timer">3</span> seconds...</div>
          </div>
          
          <script>
            let timeLeft = 3;
            const timer = document.getElementById('timer');
            
            const countdown = setInterval(() => {
              timeLeft--;
              timer.textContent = timeLeft;
              
              if (timeLeft <= 0) {
                clearInterval(countdown);
                
                // Try to notify parent window/opener
                if (window.opener) {
                  window.opener.postMessage({
                    type: 'CREATELEX_AUTH_SUCCESS',
                    userId: '${userId}',
                    subscriptionStatus: ${subscriptionStatus === 'true'}
                  }, '*');
                }
                
                // Close the window
                window.close();
                
                // If window.close() doesn't work, redirect back to dashboard
                setTimeout(() => {
                  window.location.href = '/dashboard?mcp_auth=success';
                }, 1000);
              }
            }, 1000);
            
            // Also try to close immediately on page load
            setTimeout(() => {
              if (window.opener) {
                window.opener.postMessage({
                  type: 'CREATELEX_AUTH_SUCCESS',
                  userId: '${userId}',
                  subscriptionStatus: ${subscriptionStatus === 'true'}
                }, '*');
              }
            }, 100);
          </script>
        </body>
        </html>
      `;

      return new NextResponse(successPage, {
        headers: {
          'Content-Type': 'text/html',
        },
      });
    }

    // Handle failure case
    const failurePage = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>CreateLex Authentication Failed</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
          }
          .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
          }
          .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
          }
          .title {
            font-size: 2rem;
            margin-bottom: 1rem;
          }
          .message {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
          }
          .button {
            display: inline-block;
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
          }
          .button:hover {
            background: rgba(255, 255, 255, 0.3);
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="icon">❌</div>
          <div class="title">Authentication Failed</div>
          <div class="message">
            There was an issue with your CreateLex authentication.<br>
            Please try again.
          </div>
          <a href="/dashboard" class="button">Return to Dashboard</a>
        </div>
      </body>
      </html>
    `;

    return new NextResponse(failurePage, {
      headers: {
        'Content-Type': 'text/html',
      },
    });

  } catch (error) {
    console.error('MCP Auth Callback Error:', error);
    
    return new NextResponse(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('MCP Auth Callback POST:', body);

    // Handle POST requests for programmatic authentication
    const { userId, subscriptionStatus, action } = body;

    if (action === 'verify') {
      // Verify the current authentication status
      return NextResponse.json({
        success: true,
        authenticated: !!userId,
        hasSubscription: !!subscriptionStatus,
        timestamp: new Date().toISOString()
      });
    }

    return NextResponse.json({
      success: true,
      message: 'MCP authentication status received'
    });

  } catch (error) {
    console.error('MCP Auth Callback POST Error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 