# Claude Desktop MCP Tool Recognition Troubleshooting

## Problem
Claude <PERSON> is connected to the MCP server but not recognizing or using the available tools. Instead, it tries to use JavaScript or other methods instead of the MCP tools.

## Current Status
✅ MCP Server: Running correctly  
✅ Protocol Methods: All working (initialize, tools/list, prompts/list, resources/list)  
✅ Tool Definitions: Properly formatted with detailed descriptions  
❌ Tool Recognition: <PERSON> not using the tools  

## Available Tools
The MCP server provides these tools:

1. **`spawn_actor`** - Create and spawn 3D objects (cubes, spheres, cylinders) in Unreal Engine
2. **`handshake_test`** - Test connection to Unreal Engine
3. **`get_scene_objects`** - List all objects in the Unreal Engine scene
4. **`server_status`** - Check MCP server status

## Troubleshooting Steps

### Step 1: Restart Claude Desktop
1. **Completely close Claude Des<PERSON>op** (not just minimize)
2. **Wait 10 seconds**
3. **Restart Claude Desktop**
4. **Check MCP server status** in Claude <PERSON> settings

### Step 2: Verify MCP Server Status
In <PERSON>, the `unreal-ai-support` server should show as **"enabled"**, not "disabled".

If it shows as "disabled":
- Check Docker container is running: `docker ps`
- Check container logs: `docker logs unreal-mcp-server`
- Restart the container: `docker-compose restart`

### Step 3: Force Tool Discovery
Try these specific prompts in Claude Desktop:

```
"What MCP tools do you have available? Please list all available tools and their descriptions."
```

```
"I want to create a cube in Unreal Engine. Do you have any tools for that?"
```

```
"Use the spawn_actor tool to create a cube at coordinates 0,0,0"
```

### Step 4: Test Tool Execution
If Claude Desktop recognizes the tools, test them:

```
"Use the handshake_test tool to verify the connection to Unreal Engine"
```

```
"Use the spawn_actor tool with these parameters:
- actor_class: Cube
- x: 100
- y: 200  
- z: 50"
```

### Step 5: Check Configuration
Verify the Claude Desktop configuration file:

**Location**: `%APPDATA%\Claude\claude_desktop_config.json`

**Expected content**:
```json
{
  "mcpServers": {
    "unreal-ai-support": {
      "command": "docker",
      "args": ["exec", "-i", "unreal-mcp-server", "python", "/app/server/mcp_stdio.py"]
    }
  }
}
```

### Step 6: Manual Testing
Test the MCP server manually:

```bash
# Test tools list
echo '{"jsonrpc":"2.0","method":"tools/list","params":{},"id":1}' | docker exec -i unreal-mcp-server python /app/server/mcp_stdio.py

# Run comprehensive test
python test_mcp_stdio.py
```

### Step 7: Check Docker Container
Ensure the Docker container is running properly:

```bash
# Check container status
docker ps

# Check container logs
docker logs unreal-mcp-server

# Restart if needed
docker-compose restart
```

## Common Issues and Solutions

### Issue: Server shows as "disabled"
**Solution**: 
- Restart Docker container
- Check Docker Desktop is running
- Verify the command in claude_desktop_config.json

### Issue: Tools not appearing in Claude Desktop
**Solution**:
- Restart Claude Desktop completely
- Wait 30 seconds after restart before testing
- Try explicit prompts asking for available tools

### Issue: Claude Desktop uses JavaScript instead of MCP tools
**Solution**:
- Be explicit: "Use the spawn_actor MCP tool to create a cube"
- Ask: "What MCP tools do you have for creating 3D objects?"
- Restart Claude Desktop and try again

### Issue: Tool execution fails
**Solution**:
- Ensure Unreal Engine is running with the plugin loaded
- Check that the Unreal Engine socket server is running on port 9877
- Test the connection manually

## Verification Commands

Run these to verify everything is working:

```bash
# 1. Check Docker container
docker ps | grep unreal-mcp-server

# 2. Test MCP protocol
python test_mcp_stdio.py

# 3. Test tool listing
echo '{"jsonrpc":"2.0","method":"tools/list","params":{},"id":1}' | docker exec -i unreal-mcp-server python /app/server/mcp_stdio.py
```

## Expected Behavior

When working correctly:
1. Claude Desktop shows `unreal-ai-support` as "enabled"
2. When asked about available tools, Claude Desktop lists the 4 MCP tools
3. When asked to create a cube, Claude Desktop uses the `spawn_actor` tool
4. Tool execution returns results from Unreal Engine

## Next Steps

If tools are still not recognized after following all steps:
1. Check Claude Desktop version (ensure it supports MCP)
2. Try removing other MCP servers temporarily to isolate the issue
3. Check Claude Desktop logs/console for any error messages
4. Verify the MCP protocol version compatibility

## Files Modified
- `mcp_stdio.py` - Enhanced tool descriptions and enum values
- `test_mcp_stdio.py` - Fixed notification handling
- `CLAUDE_DESKTOP_TROUBLESHOOTING.md` - This guide 