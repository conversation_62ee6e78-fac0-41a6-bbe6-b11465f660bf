# 🌉 CreateLex Bridge

A cross-platform desktop application that connects your local development environment with the CreateLex platform, enabling seamless MCP (Model Context Protocol) integration.

## 📥 **Download & Install**

### **Quick Download**
- **Windows:** [Download Setup.exe](https://github.com/AlexKissiJr/AiWebplatform/releases/latest/download/CreateLex-Bridge-Setup-Windows.exe) (356 MB)
- **macOS:** [Download DMG](https://github.com/AlexKissiJr/AiWebplatform/releases/latest/download/CreateLex-Bridge-macOS.dmg) (~400 MB)
- **Linux:** [Download AppImage](https://github.com/AlexKissiJr/AiWebplatform/releases/latest/download/CreateLex-Bridge-Linux.AppImage) (~350 MB)

📖 **[Full Download Guide](DOWNLOAD.md)** - Detailed installation instructions for all platforms  
🌍 **[Cross-Platform Setup](CROSS_PLATFORM_SETUP.md)** - Setup guide for macOS and Linux developers

## ✨ **Features**

- 🔐 **Secure Authentication** - OAuth integration with CreateLex
- 💳 **Subscription Management** - Automatic subscription validation
- 🖥️ **Cross-Platform** - Windows, Mac, and Linux support
- 🔄 **Auto-Updates** - Automatic application updates

## 🎯 **Development Automation**

For **CreatelexGenAI plugin development**, we have complete automation:

**📁 [`./automation-scripts/`](./automation-scripts/)** - Professional development workflow automation
- **`dev_refresh_bypass.bat`** - Complete plugin refresh + bypass mode startup  
- **`kill_all_mcp.bat`** - Safe terminal/process cleanup
- **Full documentation** with usage guides and troubleshooting

**🔄 Development Rule**: After ANY plugin code changes → Run `dev_refresh_bypass.bat` → Check UE logs

✅ **Zero authentication required** | ✅ **One-command refresh** | ✅ **Terminal protection** | ✅ **Complete automation**
- 🎯 **System Tray** - Runs quietly in background
- 📡 **MCP Server** - Local Model Context Protocol server
- 🌐 **Offline Mode** - Works offline after initial authentication

## 🚀 **Quick Start**

1. **Download** the installer for your platform
2. **Install** following the platform-specific instructions
3. **Launch** the CreateLex Bridge application
4. **Sign in** with your CreateLex account
5. **Start** the MCP server to begin using features

## 🔧 **Development**

### **Build from Source**
```bash
# Clone the repository
git clone https://github.com/AlexKissiJr/AiWebplatform.git
cd AiWebplatform/createlex-bridge

# Install dependencies
npm install

# Development mode
npm start

# Build for your platform (protected builds)
npm run build-win-protected    # Windows (with MCP code protection)
npm run build-mac-protected    # macOS (with MCP code protection)
npm run build-linux-protected  # Linux (with MCP code protection)

# Alternative builds (source code exposed)
npm run build-win-simple       # Windows (development/testing only)
npm run build-mac-simple       # macOS (development/testing only)
npm run build-linux-simple     # Linux (development/testing only)
```

📖 **[Build Guide](BUILD_GUIDE.md)** - Complete build instructions for all platforms

### **Scripts**
- `npm start` - Start in development mode (localhost:3000)
- `npm run start:prod` - Start in production mode (createlex.com)
- `npm run build-win-protected` - Build protected Windows installer (recommended)
- `npm run build-mac-protected` - Build protected macOS DMG (recommended)
- `npm run build-linux-protected` - Build protected Linux AppImage (recommended)
- `npm run build-win-simple` - Build Windows installer (source exposed)
- `npm run build-mac-simple` - Build macOS DMG (source exposed)
- `npm run build-linux-simple` - Build Linux AppImage (source exposed)
- `npm run release` - Prepare release files and notes

## 🏗️ **Architecture**

```
CreateLex Bridge
├── 🖥️ Electron App (main.js)
├── 🌐 Web UI (web/)
│   ├── Login page
│   ├── Dashboard
│   └── Auth callback
├── 🐍 Python MCP Server (src/python/)
├── 🔐 Authentication (src/auth/)
├── 🔄 Auto-updater (src/updater/)
└── 📦 Build system (scripts/)
```

## 🔒 **Code Protection**

CreateLex Bridge includes advanced code protection for production builds:

- **🛡️ MCP Server Protection:** Python MCP server compiled to executable using PyInstaller
- **📦 Single Binary:** All MCP logic bundled into platform-specific executables 
- **🔐 Source Code Hidden:** Business logic and subscription validation protected
- **⚡ Performance:** No Python runtime dependency in production
- **🔧 Development Mode:** Still uses readable Python files for easy debugging

### **Protected vs Simple Builds:**
- **Protected builds** (`build-*-protected`): MCP code compiled to executable (recommended for releases)
- **Simple builds** (`build-*-simple`): Python source files exposed (development/testing only)

### **Platform-Specific Executables:**
- **Windows:** `mcp_server.exe`
- **macOS:** `mcp_server_mac` 
- **Linux:** `mcp_server_linux`

## 🔐 **Authentication Flow**

1. **User clicks "Sign in"** → Opens browser to createlex.com/login
2. **OAuth authentication** → User signs in with Google/GitHub
3. **Callback to bridge** → localhost:3000/bridge-callback
4. **Subscription validation** → Server validates active subscription
5. **MCP server start** → Local server starts on port 9877

## 🎯 **System Requirements**

### **Minimum:**
- **RAM:** 4 GB
- **Storage:** 1 GB free space
- **Network:** Internet for authentication

### **Recommended:**
- **RAM:** 8 GB+
- **Storage:** 2 GB free space
- **CPU:** Multi-core processor

## 🐛 **Troubleshooting**

### **Common Issues:**
- **App won't start:** Check system requirements, run as admin (Windows)
- **Authentication failed:** Verify internet connection and CreateLex account
- **Subscription required:** Ensure active subscription at createlex.com
- **MCP server issues:** Check port 9877 availability, restart app

## 📞 **Support**

- **📧 Email:** <EMAIL>
- **💬 Discord:** [discord.gg/createlex](https://discord.gg/createlex)
- **🐛 Issues:** [GitHub Issues](https://github.com/AlexKissiJr/AiWebplatform/issues)
- **📖 Docs:** [docs.createlex.com](https://docs.createlex.com)

## 🔄 **Auto-Updates**

The CreateLex Bridge includes automatic update functionality:
- **Check for updates** on startup
- **Download updates** in background
- **Install updates** on next restart
- **Update server:** createlex.com/updates/

## 🏷️ **Release Information**

- **Current Version:** 1.0.0
- **Release Date:** June 19, 2025
- **Build System:** Electron Builder + GitHub Actions
- **Supported Platforms:** Windows 10/11, macOS 10.15+, Linux (Ubuntu 18.04+)

## 📋 **Development Status**

- ✅ **Windows Build** - Successfully built and tested (430 MB installer)
- ✅ **macOS Build** - Cross-platform ready (requires macOS for building)
- ✅ **Linux Build** - Cross-platform ready (requires Linux for building)
- ✅ **Cross-Platform Support** - Platform detection and executable naming
- ✅ **GitHub Actions** - Automated CI/CD pipeline configured
- ✅ **Release System** - Automated release preparation

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test on your platform
5. Submit a pull request

## 📄 **License**

MIT License - see [LICENSE](LICENSE) file for details.

---

**Repository:** https://github.com/AlexKissiJr/AiWebplatform  
**Website:** https://createlex.com  
**Version:** 1.0.0 