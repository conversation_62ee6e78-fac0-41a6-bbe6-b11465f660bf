require('dotenv').config();
const supabase = require('./src/services/supabaseClient');

async function checkUsers() {
  console.log('Checking users in Supabase...');
  
  try {
    // Get total count of users
    const { count: totalCount, error: countError } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true });
      
    if (countError) {
      console.error('Error getting user count:', countError);
      return;
    }
    
    console.log(`Total users in database: ${totalCount}`);
    
    // Check for admin users with specific emails
    const { data: adminUsers, error: adminError } = await supabase
      .from('users')
      .select('*')
      .or('<EMAIL>,<EMAIL>');
      
    if (adminError) {
      console.error('Error getting admin users:', adminError);
      return;
    }
    
    console.log(`Found ${adminUsers.length} admin users:`);
    adminUsers.forEach(user => {
      console.log(`- ${user.name} (${user.email}), ID: ${user.id}`);
    });
    
    // Check database schema
    const { data: tables, error: tablesError } = await supabase
      .rpc('get_tables');
      
    if (tablesError) {
      console.log('Error getting tables (RPC method not available):', tablesError);
      console.log('Checking if usage_logs table exists...');
      
      // Alternative way to check if a specific table exists
      const { data: usageCheck, error: usageError } = await supabase
        .from('usage_logs')
        .select('*')
        .limit(1);
        
      if (usageError && usageError.code === '42P01') {
        console.log('usage_logs table does not exist yet');
      } else if (usageError) {
        console.log('Error checking usage_logs table:', usageError);
      } else {
        console.log('usage_logs table exists');
      }
    } else {
      console.log('Database tables:', tables);
    }
  } catch (error) {
    console.error('Exception when checking users:', error);
  }
}

// Run the check
checkUsers()
  .then(() => {
    console.log('Check completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
