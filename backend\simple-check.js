require('dotenv').config();
const supabase = require('./src/services/supabaseClient');

async function simpleCheck() {
  console.log('Running simple Supabase check...');
  
  try {
    // Get total count of users
    const { data, error } = await supabase
      .from('users')
      .select('count');
      
    if (error) {
      console.error('Error querying users:', error);
      return;
    }
    
    console.log('Query result:', data);
    
    // Check for specific tables
    console.log('Checking for tables...');
    const tables = ['usage_logs', 'api_keys', 'billing_records'];
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('count');
          
        if (error && error.code === '42P01') {
          console.log(`Table '${table}' does not exist`);
        } else if (error) {
          console.log(`Error checking table '${table}':`, error);
        } else {
          console.log(`Table '${table}' exists`);
        }
      } catch (err) {
        console.log(`Exception checking table '${table}':`, err);
      }
    }
  } catch (error) {
    console.error('Exception in simple check:', error);
  }
}

// Run the check
simpleCheck()
  .then(() => {
    console.log('Simple check completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
