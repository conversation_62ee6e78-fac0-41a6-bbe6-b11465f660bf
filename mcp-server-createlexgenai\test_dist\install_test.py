#!/usr/bin/env python3
"""
UnrealGenAI MCP Server - TEST Installation
"""

import os
import shutil
import json
from pathlib import Path

def install_test_mcp():
    print("🧪 Installing UnrealGenAI MCP Server - TEST VERSION...")
    
    # Create installation directory
    install_dir = Path.home() / ".unrealgenai" / "mcp_server_test"
    install_dir.mkdir(parents=True, exist_ok=True)
    
    # Copy test executable
    shutil.copy2("unrealgenai_mcp_server_test.exe", install_dir)
    
    # Create Claude Desktop config
    config = {
        "mcpServers": {
            "unreal-ai-support-test": {
                "command": str(install_dir / "unrealgenai_mcp_server_test.exe"),
                "args": []
            }
        }
    }
    
    config_file = install_dir / "claude_config_test.json"
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print("✅ TEST Installation complete!")
    print(f"📁 Installed to: {install_dir}")
    print(f"📋 Config file: {config_file}")
    print("🔄 Copy config to <PERSON>op and restart")
    
    # Show config content
    print("\n📋 Claude Desktop Configuration:")
    print("-" * 40)
    print(json.dumps(config, indent=2))
    print("-" * 40)

if __name__ == "__main__":
    install_test_mcp() 