import { redirect } from 'next/navigation';
import { Suspense } from 'react';
import { getCurrentUser } from '@/lib/supabase';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

export const metadata = {
  title: 'CreateLex AI Admin Dashboard',
  description: 'Admin dashboard for CreateLex AI platform',
};

async function getIsAdmin() {
  // Check if bypass auth is enabled
  if (process.env.NEXT_PUBLIC_BYPASS_AUTH === 'true') {
    return true;
  }

  const user = await getCurrentUser();

  if (!user) {
    return false;
  }

  try {
    // First check if user has is_admin flag in Supabase
    const { supabase } = await import('@/lib/supabase');
    const { data, error } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single();

    if (!error && data && data.is_admin === true) {
      console.log('User is admin based on is_admin flag');
      return true;
    }

    // Fallback to email check if there was an error or is_admin is not true
    console.log('Falling back to email check for admin access');
    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    return adminEmails.includes(user.email || '');
  } catch (error) {
    console.error('Error checking admin status:', error);
    // Fallback to email check
    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    return adminEmails.includes(user.email || '');
  }
}

import AdminClientCheck from './client-check';

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const isAdmin = await getIsAdmin();

  if (!isAdmin) {
    redirect('/dashboard');
  }

  return (
    <AdminClientCheck>
      <div className="flex h-screen bg-gray-100">
        <AdminSidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <AdminHeader />
          <main className="flex-1 overflow-y-auto p-4">
            <Suspense fallback={<LoadingSpinner />}>
              {children}
            </Suspense>
          </main>
        </div>
      </div>
    </AdminClientCheck>
  );
}
