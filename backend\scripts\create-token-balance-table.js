require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('SUPABASE_URL or SUPABASE_SERVICE_KEY is not set in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function createTokenBalanceTable() {
  try {
    console.log('Creating token_balance table...');

    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../migrations/create_token_balance_table.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');

    // Execute the SQL
    const { error } = await supabase.rpc('exec_sql', { sql });

    if (error) {
      console.error('Error creating token_balance table:', error);

      // Try an alternative approach if the RPC method fails
      console.log('Trying alternative approach...');

      // Split the SQL into individual statements
      const statements = sql.split(';').filter(stmt => stmt.trim() !== '');

      for (const statement of statements) {
        const { error } = await supabase.rpc('exec_sql', { sql: statement + ';' });
        if (error) {
          console.error('Error executing statement:', statement);
          console.error('Error details:', error);
        }
      }

      return;
    }

    console.log('Token balance table created successfully!');
  } catch (error) {
    console.error('Error creating token_balance table:', error);
  }
}

createTokenBalanceTable();
