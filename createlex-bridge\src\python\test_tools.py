#!/usr/bin/env python3

import json
import subprocess
import sys

def test_tools_list():
    """Test the tools/list method to see all available tools"""
    
    # JSON-RPC request for tools/list
    request = {
        "jsonrpc": "2.0",
        "method": "tools/list",
        "id": 1
    }
    
    try:
        # Send request to MCP server via docker
        process = subprocess.Popen(
            ["docker", "exec", "-i", "unreal-mcp-server", "python", "/app/mcp_server.py"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Send the request
        stdout, stderr = process.communicate(input=json.dumps(request))
        
        if stderr:
            print(f"Error: {stderr}")
            return
            
        # Parse response
        try:
            response = json.loads(stdout)
            if "result" in response and "tools" in response["result"]:
                tools = response["result"]["tools"]
                print(f"Found {len(tools)} MCP tools:")
                print("=" * 50)
                
                for i, tool in enumerate(tools, 1):
                    print(f"{i:2d}. {tool['name']}")
                    print(f"    Description: {tool['description']}")
                    
                    # Show required parameters
                    if 'inputSchema' in tool and 'required' in tool['inputSchema']:
                        required = tool['inputSchema']['required']
                        if required:
                            print(f"    Required params: {', '.join(required)}")
                    
                    print()
            else:
                print("No tools found in response")
                print(f"Response: {response}")
                
        except json.JSONDecodeError as e:
            print(f"Failed to parse JSON response: {e}")
            print(f"Raw output: {stdout}")
            
    except Exception as e:
        print(f"Failed to test tools: {e}")

if __name__ == "__main__":
    test_tools_list() 