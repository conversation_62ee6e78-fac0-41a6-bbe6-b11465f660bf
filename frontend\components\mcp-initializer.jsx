'use client';

import { useEffect, useState } from 'react';
import { isElectron, getMcpBridgeOrMock } from '../lib/electron-utils';
import { overrideWebSocket } from '../lib/mcp-websocket';
import { injectUnrealEngineServer } from '../lib/mcp-utils';

/**
 * MCP Initializer Component
 *
 * This component initializes the MCP bridge when the app loads.
 * It overrides the WebSocket constructor and injects the Unreal Engine server.
 */
export function McpInitializer() {
  const [initialized, setInitialized] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Only run once
    if (initialized) return;

    async function initializeMcp() {
      try {
        console.log('[MCP Initializer] Initializing MCP bridge');

        // Always override the WebSocket constructor to handle MCP connections
        console.log('[MCP Initializer] Initializing WebSocket override');
        const overrideResult = overrideWebSocket();
        console.log('[MCP Initializer] WebSocket override result:', overrideResult);

        // Wait for the DOM to be ready
        await new Promise(resolve => {
          if (document.readyState === 'complete') {
            resolve();
          } else {
            window.addEventListener('load', resolve);
          }
        });

        // Check if we're running in Electron
        if (isElectron()) {
          console.log('[MCP Initializer] Running in Electron');

          // Get the MCP bridge
          const mcpBridge = getMcpBridgeOrMock();

          if (mcpBridge) {
            console.log('[MCP Initializer] MCP bridge available, injecting Unreal Engine server');

            // Wait for the MCP context to be initialized
            await new Promise(resolve => {
              // Check if the MCP context is already initialized
              if (document.querySelector('[data-mcp-server-button]')) {
                resolve();
                return;
              }

              // Create a MutationObserver to detect when the MCP context is initialized
              const mcpObserver = new MutationObserver(() => {
                if (document.querySelector('[data-mcp-server-button]')) {
                  mcpObserver.disconnect();
                  resolve();
                }
              });

              // Start observing
              mcpObserver.observe(document.body, {
                childList: true,
                subtree: true
              });

              // Set a timeout to resolve anyway after 10 seconds
              setTimeout(() => {
                mcpObserver.disconnect();
                resolve();
              }, 10000);
            });

            // Try to use the mcpBridge first, fall back to our utility function
            let injectionResult;
            if (mcpBridge.injectUnrealEngineServer) {
              injectionResult = await mcpBridge.injectUnrealEngineServer();
            } else {
              injectionResult = await injectUnrealEngineServer();
            }
            console.log('[MCP Initializer] Unreal Engine server injection result:', injectionResult);
          } else {
            console.error('[MCP Initializer] MCP bridge not available');
            setError('MCP bridge not available');
          }
        } else {
          console.log('[MCP Initializer] Not running in Electron, using direct WebSocket connection');

          // Wait for the MCP context to be initialized
          await new Promise(resolve => {
            // Check if the MCP context is already initialized
            if (document.querySelector('[data-mcp-server-button]')) {
              resolve();
              return;
            }

            // Create a MutationObserver to detect when the MCP context is initialized
            const mcpObserver = new MutationObserver(() => {
              if (document.querySelector('[data-mcp-server-button]')) {
                mcpObserver.disconnect();
                resolve();
              }
            });

            // Start observing
            mcpObserver.observe(document.body, {
              childList: true,
              subtree: true
            });

            // Set a timeout to resolve anyway after 10 seconds
            setTimeout(() => {
              mcpObserver.disconnect();
              resolve();
            }, 10000);
          });

          // Inject the Unreal Engine server using our utility function
          const injectionResult = await injectUnrealEngineServer();
          console.log('[MCP Initializer] Unreal Engine server injection result:', injectionResult);
        }

        setInitialized(true);
      } catch (error) {
        console.error('[MCP Initializer] Error initializing MCP bridge:', error);
        setError(error.message);
      }
    }

    initializeMcp();
  }, [initialized]);

  // This component doesn't render anything
  return null;
}

export default McpInitializer;
