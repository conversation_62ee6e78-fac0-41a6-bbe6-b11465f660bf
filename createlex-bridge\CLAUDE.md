# Claude Code Development Rules - CreatelexGenAI

## 🎯 PRIMARY DEVELOPMENT RULE
**ABSOLUTE REQUIREMENT**: Every time we make ANY code changes to the CreatelexGenAI Unreal Engine plugin, we MUST:
1. Run the instant refresh script: `./automation-scripts/dev_refresh_bypass.bat`
2. Check Unreal Engine project logs: `C:\Dev\YourLife\Saved\Logs\YourLife.log`
3. Debug and fix any issues found before proceeding

## 🔄 Automated Development Workflow

### Core Scripts (Location: `./automation-scripts/`)
- **`dev_refresh_bypass.bat`** - Main development refresh (run after every code change)
- **`kill_all_mcp.bat`** - Safe process/terminal cleanup  
- **`start_mcp_bypass_simple.bat`** - Standalone bridge startup
- **`README.md`** - Comprehensive documentation

### Workflow Execution
```bash
# After making plugin code changes:
cd /mnt/c/Dev/AiWebplatform/createlex-bridge
./automation-scripts/dev_refresh_bypass.bat

# Check logs for issues:
# C:\Dev\YourLife\Saved\Logs\YourLife.log
```

## 🚀 MCP Bridge Bypass Mode Configuration

### Environment Variables (Auto-set by scripts)
```bash
NODE_ENV=development
DEV_MODE=true
BYPASS_SUBSCRIPTION=true
BYPASS_LOGIN=true
AUTO_START_MCP=true
SKIP_AUTH=true
CREATELEX_BASE_URL=http://localhost:3000
API_BASE_URL=http://localhost:5001/api
```

### Key Benefits
- ✅ No OAuth authentication required
- ✅ No manual button clicking
- ✅ Automatic MCP server startup
- ✅ Complete development automation
- ✅ Consistent clean testing environment

## 🛡️ Terminal Protection System

### Safety Features
- **Smart PID Detection**: Protects current and parent processes
- **Conservative Cleanup**: Only targets specific OLD MCP/Bridge windows
- **Window Handle Management**: Direct window closing for stuck terminals
- **Triple Protection Layer**: Current + Parent + Protected PID lists

### Protected Processes
- Development workflow scripts (`dev_refresh_bypass.bat`)
- Cleanup scripts (`kill_all_mcp.bat`)
- Any script containing `dev_refresh` or `kill_all_mcp` in command line

## 📊 Verification Protocol

### Required Checks After Refresh
1. **MCP Bridge Status**: Port 9877 active
   ```bash
   netstat -an | find ":9877"
   ```
2. **Unreal Editor**: Process running
   ```bash
   tasklist | find "UnrealEditor.exe"
   ```
3. **Plugin Loading**: Check UE logs for CreatelexGenAI
4. **Connection Test**: Verify MCP bridge communication

## 🔧 Path Configuration

### Development Paths
```batch
PROJECT_DIR=C:\Dev\YourLife
SOURCE_PLUGIN=C:\Dev\AiWebplatform\CreatelexGenAI
BRIDGE_DIR=C:\Dev\AiWebplatform\createlex-bridge
UE_EDITOR="C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor.exe"
UBT="C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.exe"
PROJECT_FILE=C:\Dev\YourLife\YourLife.uproject
```

## 🎯 Development Iteration Rules

### Mandatory Workflow Steps
1. **Code Modification** - Make changes to CreatelexGenAI plugin
2. **Script Execution** - Run `dev_refresh_bypass.bat` immediately
3. **Log Analysis** - Check UE logs for errors/warnings
4. **Issue Resolution** - Fix any problems before continuing
5. **Connection Verification** - Ensure MCP bridge functionality
6. **Cycle Repetition** - Repeat for each change iteration

### Script Execution Phases
1. **Phase 1**: Complete process/terminal cleanup
2. **Phase 2**: Plugin refresh and build
3. **Phase 3**: Service startup (bypass mode)
4. **Phase 4**: Verification and status check

## 🔍 Troubleshooting Guidelines

### Common Issues & Solutions
- **Script Self-Termination**: Check terminal protection logic
- **Bridge Startup Failure**: Verify bypass environment setup
- **UE Compilation Error**: Validate plugin paths and dependencies
- **Port Occupation**: Run cleanup script to free resources

### Debug Information Sources
- Script output provides detailed phase information
- UE Editor logs show plugin loading status
- Network status shows MCP bridge connectivity
- Process lists confirm proper service startup

## 📝 Maintenance Standards

### Script Modification Rules
- **Never modify bypass logic** without comprehensive testing
- **Maintain terminal protection** for all workflow scripts
- **Update documentation** when changing behavior
- **Test cleanup safety** before committing changes
- **Preserve backward compatibility** with existing workflows

### Version Control
- Keep working script versions as backups
- Document all changes in README.md
- Test modifications in isolated environment
- Verify terminal protection after updates

## 🎪 Integration Points

### Unreal Engine Integration
- Plugin automatically refreshed and rebuilt
- Project configuration updated for bypass mode
- Build artifacts cleaned before fresh compilation
- Editor launched with proper development flags

### MCP Bridge Integration
- Bypass mode eliminates authentication requirements
- Automatic server startup within bridge application
- Environment variables configure development mode
- Port management ensures clean connectivity

---

*This document defines the essential development workflow for CreatelexGenAI plugin integration with MCP Bridge, ensuring consistent, automated, and reliable development iterations.*