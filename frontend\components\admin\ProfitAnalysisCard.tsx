'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { fetchWithAuth, getApiUrl } from '@/lib/auth-utils';

export default function ProfitAnalysisCard() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<any>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const apiUrl = getApiUrl();
        const requestUrl = `${apiUrl}/api/admin/token-usage/statistics?period=month`;

        const response = await fetchWithAuth(requestUrl);

        if (!response.ok) {
          throw new Error('Failed to fetch profit analysis');
        }

        const data = await response.json();
        setData(data);
      } catch (error) {
        console.error('Error fetching profit analysis:', error);
        setError('Failed to load profit data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Profit Analysis</CardTitle>
          <CardDescription>Current month profit and margin</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Profit Analysis</CardTitle>
          <CardDescription>Current month profit and margin</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-red-500">{error}</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Profit Analysis</CardTitle>
        <CardDescription>Current month profit and margin</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <div className="text-sm font-medium">Revenue</div>
            <div className="text-2xl font-bold">${data?.revenue?.totalRevenue || '0.00'}</div>
            <div className="text-sm text-muted-foreground">
              {data?.revenue?.totalUsers || 0} active users
            </div>
          </div>
          <div>
            <div className="text-sm font-medium">API Cost</div>
            <div className="text-2xl font-bold">${data?.costs?.totalCost || '0.00'}</div>
            <div className="text-sm text-muted-foreground">
              {data?.totalUsage?.totalTokens?.toLocaleString() || 0} tokens
            </div>
          </div>
          <div>
            <div className="text-sm font-medium">Profit</div>
            <div className="text-2xl font-bold">${data?.profit?.totalProfit || '0.00'}</div>
            <div className="text-sm text-muted-foreground">
              Margin: {data?.profit?.margin || '0%'}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
