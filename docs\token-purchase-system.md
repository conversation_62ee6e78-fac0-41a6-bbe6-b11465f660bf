# Token Purchase System Documentation

This document provides a comprehensive overview of the token purchase system in the CreateLex AI platform.

## Table of Contents

1. [System Overview](#system-overview)
2. [Architecture](#architecture)
3. [Token Purchase Flow](#token-purchase-flow)
4. [Webhook Handling](#webhook-handling)
5. [Error Recovery Mechanisms](#error-recovery-mechanisms)
6. [Production Considerations](#production-considerations)
7. [Troubleshooting](#troubleshooting)
8. [API Reference](#api-reference)

## System Overview

The token purchase system allows users to buy additional tokens beyond their subscription limits. These tokens are used for AI interactions and are consumed as users engage with the AI assistant.

Key components:
- Stripe integration for payment processing
- Webhook handling for asynchronous payment confirmation
- Token balance management in the database
- Frontend components for displaying token usage and purchase options

## Architecture

The token purchase system consists of the following components:

1. **Frontend Components**:
   - `TokenUsage.js`: Displays current token usage and balance
   - `TokenPurchaseProcessor.js`: Handles post-purchase processing
   - `PurchaseTokensModal.js`: UI for selecting and purchasing token packages

2. **Frontend API Routes**:
   - `/api/tokens/purchase`: Creates Stripe checkout sessions
   - `/api/tokens/process-checkout`: Processes checkout sessions manually
   - `/api/tokens/simulate-webhook`: Simulates webhook events (development only)

3. **Backend Routes**:
   - `/api/tokens/purchase`: Creates Stripe checkout sessions
   - `/api/tokens/process-checkout`: Processes checkout sessions manually
   - `/api/tokens/purchase/simulate-webhook`: Simulates webhook events (development only)
   - `/api/webhook/stripe`: Handles Stripe webhook events

4. **Backend Services**:
   - `tokenPurchaseService.js`: Manages token balance updates
   - `stripeService.js`: Handles Stripe API interactions

5. **Database Tables**:
   - `token_balance`: Stores user token balances
   - `token_transactions`: Records token purchase transactions

## Token Purchase Flow

### Standard Flow (Production)

1. User selects a token package on the dashboard
2. Frontend creates a Stripe checkout session via `/api/tokens/purchase`
3. User completes payment on Stripe checkout page
4. Stripe sends a webhook event to `/api/webhook/stripe`
5. Backend processes the webhook, verifies the payment, and adds tokens to the user's balance
6. User is redirected back to the dashboard
7. Frontend displays updated token balance

### Alternative Flow (Webhook Failure)

If the webhook fails to process the payment:

1. User is still redirected back to the dashboard
2. `TokenPurchaseProcessor` component detects the completed purchase
3. Component calls `/api/tokens/process-checkout` to manually process the payment
4. Backend verifies the payment with Stripe and adds tokens to the user's balance
5. Frontend refreshes to display updated token balance

### Development Flow

In development environments where webhooks can't reach the local server:

1. User completes payment on Stripe checkout page
2. User is redirected back to the dashboard with query parameters
3. `TokenPurchaseProcessor` component detects the completed purchase
4. Component calls `/api/tokens/simulate-webhook` to simulate a webhook event
5. Backend adds tokens to the user's balance
6. Frontend refreshes to display updated token balance

## Webhook Handling

### Webhook Verification

In production, webhooks are verified using the Stripe signature:

```javascript
const event = stripe.webhooks.constructEvent(
  req.body,
  req.headers['stripe-signature'],
  process.env.STRIPE_WEBHOOK_SECRET
);
```

In development, signature verification can be bypassed using the `BYPASS_WEBHOOK_SIGNATURE` environment variable.

### Webhook Events

The system handles the following Stripe webhook events:

- `checkout.session.completed`: Processes token purchases
- `customer.subscription.created`: Handles new subscriptions
- `customer.subscription.updated`: Updates subscription status
- `customer.subscription.deleted`: Handles subscription cancellations

### Idempotency

To prevent duplicate processing, each transaction is recorded with a unique ID (the Stripe payment intent ID). Before adding tokens, the system checks if the transaction has already been processed.

## Error Recovery Mechanisms

The system includes several mechanisms to ensure token purchases are processed correctly:

1. **Webhook Retries**: Stripe automatically retries failed webhook deliveries up to 3 times.

2. **Manual Processing**: If webhooks fail, the `TokenPurchaseProcessor` component can manually process the payment.

3. **Multiple Refresh Attempts**: The frontend makes multiple attempts to refresh the token balance after a purchase.

4. **Fallback Mechanisms**: If the Stripe API is unavailable, the system can use alternative methods to add tokens.

5. **Transaction Tracking**: Each token purchase is recorded with a unique transaction ID to prevent duplicate processing.

## Production Considerations

### Environment Variables

Required environment variables for production:

```
STRIPE_SECRET_KEY=sk_live_...
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
BYPASS_WEBHOOK_SIGNATURE=false
```

### Webhook Configuration

In the Stripe dashboard:
1. Set up a webhook endpoint pointing to `https://yourdomain.com/api/webhook/stripe`
2. Enable the following events:
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`

### Security Considerations

1. Always verify webhook signatures in production
2. Store Stripe API keys securely
3. Use HTTPS for all API endpoints
4. Implement rate limiting for token purchase endpoints

### Monitoring

Monitor the following:
1. Webhook receipt and processing
2. Token balance updates
3. Discrepancies between Stripe charges and token balances
4. Error rates in token purchase processing

## Troubleshooting

### Common Issues

1. **Webhook Not Received**:
   - Check Stripe dashboard for webhook delivery status
   - Verify webhook endpoint URL is correct
   - Check server logs for webhook receipt

2. **Webhook Verification Failed**:
   - Verify `STRIPE_WEBHOOK_SECRET` is correct
   - Check if webhook was sent to the correct environment

3. **Token Balance Not Updated**:
   - Check transaction records in the database
   - Verify the user ID in the webhook matches the expected user
   - Check for database connection issues

4. **Frontend Not Refreshing**:
   - Check browser console for errors
   - Verify the `refreshTokenBalance` event is being dispatched
   - Check network requests for token balance API calls

### Manual Reconciliation

If automatic processing fails, follow these steps for manual reconciliation:

1. Identify the affected user and transaction in the Stripe dashboard
2. Verify the payment was successful
3. Check if the transaction exists in the `token_transactions` table
4. If not, manually add the tokens using the admin dashboard or database query

## API Reference

### Frontend API Routes

#### `POST /api/tokens/purchase`

Creates a Stripe checkout session for token purchase.

**Request Body**:
```json
{
  "packageId": "small|medium|large",
  "userId": "user-id"
}
```

**Response**:
```json
{
  "sessionId": "cs_...",
  "url": "https://checkout.stripe.com/..."
}
```

#### `POST /api/tokens/process-checkout`

Manually processes a completed checkout session.

**Request Body**:
```json
{
  "sessionId": "cs_...",
  "userId": "user-id"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Token purchase processed successfully",
  "tokens": 100000,
  "balance": 2500000
}
```

### Backend API Routes

#### `POST /api/tokens/purchase`

Creates a Stripe checkout session for token purchase.

**Request Headers**:
```
x-user-id: user-id
```

**Request Body**:
```json
{
  "packageId": "small|medium|large"
}
```

**Response**:
```json
{
  "sessionId": "cs_...",
  "url": "https://checkout.stripe.com/..."
}
```

#### `POST /api/webhook/stripe`

Handles Stripe webhook events.

**Request Headers**:
```
stripe-signature: t=timestamp,v1=signature
```

**Request Body**: Raw Stripe event JSON

**Response**:
```json
{
  "received": true,
  "webhookId": "webhook-id"
}
```

#### `GET /api/tokens/balance`

Gets the current token balance for a user.

**Query Parameters**:
```
userId=user-id
forceRefresh=true|false
```

**Response**:
```json
{
  "id": "balance-id",
  "user_id": "user-id",
  "balance": 2500000,
  "created_at": "2023-01-01T00:00:00.000Z",
  "updated_at": "2023-01-01T00:00:00.000Z"
}
```
