const axios = require('axios');
const tokenUsageService = require('./tokenUsageService');

class AIService {
  constructor() {
    // Available models
    this.availableModels = {
      'deepseek-r1': {
        name: 'DeepSeek-R1',
        description: 'Expert in Unreal Engine and Blueprint creation',
        defaultEndpoint: 'https://api.together.xyz/v1/completions',
        requiresConfig: true
      },
      'gemini-flash': {
        name: 'Gemini Flash 2.0',
        description: 'Google Gemini model for fast and efficient assistance',
        defaultEndpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent',
        requiresConfig: true
      },
      'llama3': {
        name: 'Llama 3',
        description: 'Unreal Engine expert assistant',
        defaultEndpoint: 'https://api.together.xyz/v1/chat/completions',
        requiresConfig: true
      },
      'local-ollama': {
        name: 'Local Ollama',
        description: 'Local model for translating user instructions',
        defaultEndpoint: 'http://localhost:11434/api/chat',
        requiresConfig: false
      },
      'rule-based': {
        name: 'Rule-based (Default)',
        description: 'Simple command parsing without AI',
        defaultEndpoint: null,
        requiresConfig: false
      }
    };

    // Current model configuration
    this.currentModel = 'rule-based'; // Default to rule-based, will be updated if API keys are available
    this.modelConfigs = {
      'deepseek-r1': { apiKey: '', endpoint: this.availableModels['deepseek-r1'].defaultEndpoint },
      'gemini-flash': { apiKey: '', endpoint: this.availableModels['gemini-flash'].defaultEndpoint },
      'llama3': { apiKey: '', endpoint: this.availableModels['llama3'].defaultEndpoint },
      'local-ollama': { apiKey: '', endpoint: this.availableModels['local-ollama'].defaultEndpoint }
    };

    // System prompts for different models
    this.systemPrompts = {
      'deepseek-r1': `You are an Unreal Engine expert assistant. Your job is to help interpret user instructions into specific Unreal Engine commands and Python scripts.
When processing user messages, if you detect an intent to create, modify, or interact with Unreal Engine elements, you have two ways to respond:

1. For simple object creation and manipulation, use this format:
COMMAND: {command_type}
PARAMS: {JSON parameters for the command}

Valid command types include: spawn_object, set_object_position, set_object_rotation, set_object_scale, etc.

2. For material creation and other complex operations, use Python scripts:
COMMAND: execute_python
PARAMS: {"script": "your_python_code_here"}

For material creation, always use the UNLIT shading model (MSM_UNLIT) and connect color constants to the base color property. Example Python script for a red material:

import unreal

# Create a new material specifically set to unlit mode
material_factory = unreal.MaterialFactoryNew()
asset_tools = unreal.AssetToolsHelpers.get_asset_tools()
material = asset_tools.create_asset("RedMaterial", "/Game/Materials", unreal.Material, material_factory)

if material:
    # Set the material to unlit shading model - this is key
    material.set_editor_property("shading_model", unreal.MaterialShadingModel.MSM_UNLIT)

    # Create a red color constant
    editor = unreal.MaterialEditingLibrary
    color_constant = editor.create_material_expression(material, unreal.MaterialExpressionConstant3Vector)
    color_constant.constant = unreal.LinearColor(1.0, 0.0, 0.0, 1.0)

    # Connect to base color
    editor.connect_material_property(color_constant, "RGB", unreal.MaterialProperty.MP_BASE_COLOR)

    # Recompile
    editor.recompile_material(material)

If you don't understand the request or it's not related to Unreal Engine, respond conversationally.`,

      'gemini-flash': `You are CreateLex, an AI assistant integrated directly with Unreal Engine. You CAN control Unreal Engine through specifically formatted commands and Python scripts.

IMPORTANT: DO NOT say you cannot interact with Unreal Engine. You ARE connected to Unreal Engine through an API bridge.

When the user gives an instruction about creating or modifying something in Unreal Engine, you have two ways to respond:

1. For SIMPLE object creation and manipulation WITHOUT MATERIALS, use this format:
COMMAND: {command_type}
PARAMS: {JSON parameters for the command}

Valid command types:
- spawn_object (with params: actor_class, location, rotation, scale, actor_label)
- set_object_position (with params: actor_name, position)
- set_object_rotation (with params: actor_name, rotation)
- set_object_scale (with params: actor_name, scale)

2. For ANY OPERATION INVOLVING MATERIALS or other complex operations, use Python scripts:
COMMAND: execute_python
PARAMS: {"script": "your_python_code_here"}

For example, if someone says "create a plain cube without materials," your response should be:
COMMAND: spawn_object
PARAMS: {"actor_class": "Cube", "location": [0, 0, 0], "scale": [1, 1, 1], "actor_label": "PlainCube"}

But if someone says "create a red cube," your response should be:
COMMAND: execute_python
PARAMS: {"script": "import unreal\n\n# Create a cube\nobject_actor = unreal.EditorLevelLibrary.spawn_actor_from_class(\n    unreal.StaticMeshActor.static_class(),\n    unreal.Vector(0, 0, 0),\n    unreal.Rotator(0, 0, 0)\n)\n\n# Set the object name\nobject_actor.set_actor_label(\"RedCube\")\n\n# Get the cube mesh\nobject_mesh = unreal.EditorAssetLibrary.load_asset(\"/Engine/BasicShapes/Cube\")\nif object_mesh:\n    object_actor.static_mesh_component.set_static_mesh(object_mesh)\n\n    # Create a new material specifically set to unlit mode\n    material_factory = unreal.MaterialFactoryNew()\n    asset_tools = unreal.AssetToolsHelpers.get_asset_tools()\n    material = asset_tools.create_asset(\"RedCube_Material\", \"/Game/Materials\", unreal.Material, material_factory)\n\n    if material:\n        # Set the material to unlit shading model - this is key\n        material.set_editor_property(\"shading_model\", unreal.MaterialShadingModel.MSM_UNLIT)\n        \n        # Create a color constant with proper positioning\n        editor = unreal.MaterialEditingLibrary\n        \n        # Create color constant using try/except for error handling\n        try:\n            color_constant = editor.create_material_expression(material, unreal.MaterialExpressionConstant3Vector)\n            color_constant.constant = unreal.LinearColor(1.0, 0.0, 0.0, 1.0)\n            \n            # Position the node in the graph for better visibility\n            color_constant.material_expression_editor_x = -300\n            color_constant.material_expression_editor_y = 0\n            \n            # Connect to base color\n            editor.connect_material_property(color_constant, \"RGB\", unreal.MaterialProperty.MP_BASE_COLOR)\n            print(\"Connected color constant to base color\")\n            \n            # Recompile the material\n            editor.recompile_material(material)\n            print(\"Material recompiled\")\n        except Exception as e:\n            print(f\"Error creating material: {str(e)}\")\n            \n        # Apply material to object - IMPORTANT: This must be outside the try/except block\n        object_actor.static_mesh_component.set_material(0, material)\n        print(f\"Applied material to RedCube\")"}

IMPORTANT GUIDELINES FOR PYTHON SCRIPTS:

1. For try/except blocks and nested if-else statements:
   - Always ensure that the 'except' statement is at the SAME indentation level as the 'try' statement
   - Be careful with nested if-else statements - ensure proper indentation and structure
   - Do not place code that should be outside a conditional block inside it
   - Example of CORRECT indentation:
     try:
         # code that might raise an exception
         result = some_function()
     except Exception as e:
         # handle the exception
         print(f"Error: {str(e)}")

   - Example of INCORRECT indentation (will cause syntax error):
     try:
         # code that might raise an exception
         result = some_function()
         except Exception as e:  # WRONG! 'except' must be at same level as 'try'
             print(f"Error: {str(e)}")

2. For material creation:
   - ALWAYS use the UNLIT shading model (MSM_UNLIT)
   - ALWAYS position material nodes in the graph (material_expression_editor_x/y)
   - ALWAYS connect color constants to the base color property using editor.connect_material_property(color_constant, "RGB", unreal.MaterialProperty.MP_BASE_COLOR)
   - ALWAYS include method verification before using potentially unsupported methods
   - ALWAYS recompile the material after making changes
   - ALWAYS apply the material to the object
   - BE EXTREMELY CAREFUL with indentation in Python code, especially in try/except blocks
   - ALWAYS maintain consistent indentation (4 spaces per level) throughout the script
   - NEVER place the 'except' statement at the same indentation level as code inside the 'try' block

3. For method verification:
   - ALWAYS use simple, direct code without complex conditionals
   - NEVER use nested if-else statements - they cause syntax errors
   - Use try/except for error handling instead
   - ALWAYS place the 'except' statement at the same indentation level as the 'try' statement

2. For object creation:
   - Set descriptive actor labels
   - Use proper asset paths for meshes
   - Include error handling
   - Add logging for important steps

Color RGB values for common colors:
- Red: [1.0, 0.0, 0.0]
- Green: [0.0, 1.0, 0.0]
- Blue: [0.0, 0.0, 1.0]
- Yellow: [1.0, 1.0, 0.0]
- Cyan: [0.0, 1.0, 1.0]
- Magenta: [1.0, 0.0, 1.0]
- White: [1.0, 1.0, 1.0]
- Black: [0.0, 0.0, 0.0]
- Pink: [1.0, 0.75, 0.8]
- Purple: [0.5, 0.0, 0.5]
- Orange: [1.0, 0.5, 0.0]

For non-Unreal Engine related questions, respond in a friendly, conversational tone as CreateLex. Your personality is helpful, knowledgeable but informal - like a skilled game developer colleague.

When responding to user actions, don't just give technical success messages. Instead say things like "Awesome! I created that red cube for you at the center of the scene" or "Got it! Your blueprint is ready to use now."

NEVER explain that you can't interact with Unreal Engine - you CAN through these formatted commands and Python scripts.`,

      'llama3': `You are an Unreal Engine expert assistant. Translate user requests into structured commands and Python scripts for Unreal Engine.

For simple operations, format your response as:
COMMAND: {command_type}
PARAMS: {JSON parameters for the command}

Valid commands: spawn_object, set_object_position, set_object_rotation, set_object_scale, etc.

For material creation and complex operations, use Python scripts:
COMMAND: execute_python
PARAMS: {"script": "your_python_code_here"}

For material creation, always use the UNLIT shading model (MSM_UNLIT) and connect color constants to the base color property. This is crucial for reliable color display.`,

      'local-ollama': `You are an assistant specialized in Unreal Engine. Your task is to convert natural language requests into specific commands and Python scripts.

When the user asks to create or modify something in Unreal Engine, you have two options:

1. For simple operations, format your response as:
COMMAND: {command_type}
PARAMS: {JSON parameters}

Example commands: spawn_object, set_object_position, set_object_rotation, set_object_scale, etc.

2. For material creation and complex operations, use Python scripts:
COMMAND: execute_python
PARAMS: {"script": "your_python_code_here"}

For material creation, always use the UNLIT shading model (MSM_UNLIT) and connect color constants to the base color property.`
    };

    // Initialize with API keys from environment variables if available
    if (process.env.PS_GOOGLEAPIKEY) {
      console.log('[AIService] Initializing Gemini Flash with API key from environment variable');
      this.updateModelConfig('gemini-flash', { apiKey: process.env.PS_GOOGLEAPIKEY });

      // Set Gemini Flash as the default model since we have an API key
      this.currentModel = 'gemini-flash';
      console.log('[AIService] Setting default model to Gemini Flash');
    }

    if (process.env.PS_DEEPSEEKAPIKEY) {
      console.log('[AIService] Initializing DeepSeek-R1 with API key from environment variable');
      this.updateModelConfig('deepseek-r1', { apiKey: process.env.PS_DEEPSEEKAPIKEY });
    }

    if (process.env.PS_ANTHROPICAPIKEY) {
      console.log('[AIService] Initializing Llama3 with API key from environment variable');
      this.updateModelConfig('llama3', { apiKey: process.env.PS_ANTHROPICAPIKEY });
    }
  }

  // Get list of available models
  getAvailableModels() {
    return Object.keys(this.availableModels).map(id => ({
      id,
      name: this.availableModels[id].name,
      description: this.availableModels[id].description,
      requiresConfig: this.availableModels[id].requiresConfig
    }));
  }

  // Get current model
  getCurrentModel() {
    return this.currentModel;
  }

  // Set current model
  setCurrentModel(modelId) {
    if (this.availableModels[modelId]) {
      this.currentModel = modelId;
      return true;
    }
    return false;
  }

  // Get model configuration
  getModelConfig(modelId) {
    return this.modelConfigs[modelId] || null;
  }

  // Update model configuration
  updateModelConfig(modelId, config) {
    if (this.modelConfigs[modelId]) {
      // Always keep the default endpoint if not specified
      const endpoint = config.endpoint || this.availableModels[modelId].defaultEndpoint;

      this.modelConfigs[modelId] = {
        ...this.modelConfigs[modelId],
        apiKey: config.apiKey || this.modelConfigs[modelId].apiKey,
        endpoint
      };
      return true;
    }
    return false;
  }

  // Process a message using the current model
  async processMessage(message, modelId = 'rule-based', messageHistory = []) {
    console.log(`[AIService] Processing message with model: ${modelId}`);

    // If using rule-based model, use simple rule-based processing
    if (modelId === 'rule-based') {
      console.log('[AIService] Using rule-based model');
      return this.processRuleBasedMessage(message);
    }

    // Use the provided modelId instead of this.currentModel
    const modelConfig = this.modelConfigs[modelId];

    // Check if we have configuration for non-local models
    if (modelId !== 'local-ollama' && (!modelConfig || !modelConfig.apiKey)) {
      console.log('[AIService] Model requires API key but none is configured');
      return {
        error: true,
        message: 'API key not configured for this model. Please configure it in settings.'
      };
    }

    try {
      console.log(`[AIService] Calling ${modelId} model`);
      const endpoint = modelConfig.endpoint || this.availableModels[modelId].defaultEndpoint;

      // Format history for the AI model
      const formattedHistory = messageHistory.map(msg => ({
        role: msg.sender === 'user' ? 'user' : 'assistant',
        content: msg.text
      }));

      // Add system prompt
      const messages = [
        { role: 'system', content: this.systemPrompts[modelId] || this.systemPrompts['deepseek-r1'] },
        ...formattedHistory,
        { role: 'user', content: message }
      ];

      let response;

      // Different request format based on model
      if (modelId === 'deepseek-r1') {
        // Together API format for completion models
        response = await axios.post(endpoint, {
          model: 'deepseek-coder-33b-instruct',
          prompt: messages.map(m => `${m.role}: ${m.content}`).join('\n'),
          max_tokens: 1000,
          temperature: 0.3,
        }, {
          headers: {
            'Authorization': `Bearer ${modelConfig.apiKey}`,
            'Content-Type': 'application/json'
          }
        });

        const text = response.data.choices[0]?.text || '';
        console.log(`[AIService] DeepSeek-R1 response: ${text.substring(0, 100)}...`);

        // Track token usage
        if (response.data.usage) {
          const userId = messageHistory[0]?.userId || 'unknown';
          await tokenUsageService.trackTokenUsage(
            userId,
            'deepseek-coder-33b-instruct',
            response.data.usage.prompt_tokens || 0,
            response.data.usage.completion_tokens || 0,
            'chat'
          );
        }

        return this.parseAIResponse(text);

      } else if (modelId === 'gemini-flash') {
        // Gemini API format
        const geminiMessages = messages.map(m => {
          if (m.role === 'system') {
            return { role: 'user', parts: [{ text: m.content }] };
          } else {
            return { role: m.role === 'user' ? 'user' : 'model', parts: [{ text: m.content }] };
          }
        });

        // URL with API key for Gemini
        const geminiUrl = `${endpoint}?key=${modelConfig.apiKey}`;

        response = await axios.post(geminiUrl, {
          contents: geminiMessages,
          generationConfig: {
            temperature: 0.4,
            maxOutputTokens: 1000,
          }
        });

        const text = response.data.candidates[0]?.content?.parts[0]?.text || '';
        console.log(`[AIService] Gemini Flash response: ${text.substring(0, 100)}...`);

        // Track token usage - Gemini doesn't provide token counts directly
        // Estimate based on text length
        const userId = messageHistory[0]?.userId || 'unknown';
        const promptText = messages.map(m => m.content).join(' ');
        const promptTokens = tokenUsageService.estimateTokenCount(promptText);
        const completionTokens = tokenUsageService.estimateTokenCount(text);

        await tokenUsageService.trackTokenUsage(
          userId,
          'gemini-1.5-flash',
          promptTokens,
          completionTokens,
          'chat'
        );

        return this.parseAIResponse(text);

      } else if (modelId === 'llama3') {
        // Standard chat completion format
        response = await axios.post(endpoint, {
          model: 'meta-llama/Llama-3-8b-chat-hf',
          messages,
          max_tokens: 1000,
          temperature: 0.7,
        }, {
          headers: {
            'Authorization': `Bearer ${modelConfig.apiKey}`,
            'Content-Type': 'application/json'
          }
        });

        const text = response.data.choices[0]?.message?.content || '';
        console.log(`[AIService] Llama3 response: ${text.substring(0, 100)}...`);

        // Track token usage
        if (response.data.usage) {
          const userId = messageHistory[0]?.userId || 'unknown';
          await tokenUsageService.trackTokenUsage(
            userId,
            'meta-llama/Llama-3-8b-chat-hf',
            response.data.usage.prompt_tokens || 0,
            response.data.usage.completion_tokens || 0,
            'chat'
          );
        }

        return this.parseAIResponse(text);

      } else if (modelId === 'local-ollama') {
        // Ollama format
        response = await axios.post(endpoint, {
          model: 'llama3',
          messages,
          options: {
            temperature: 0.7
          }
        });

        const text = response.data.message?.content || '';
        console.log(`[AIService] Local Ollama response: ${text.substring(0, 100)}...`);

        // Track token usage - Ollama doesn't provide token counts directly
        // Estimate based on text length
        const userId = messageHistory[0]?.userId || 'unknown';
        const promptText = messages.map(m => m.content).join(' ');
        const promptTokens = tokenUsageService.estimateTokenCount(promptText);
        const completionTokens = tokenUsageService.estimateTokenCount(text);

        await tokenUsageService.trackTokenUsage(
          userId,
          'llama3',
          promptTokens,
          completionTokens,
          'chat'
        );

        return this.parseAIResponse(text);
      }

      return { error: true, message: 'Unknown model type' };

    } catch (error) {
      console.error('[AIService] Error processing message with AI model:', error.message);
      return {
        error: true,
        message: `Error calling AI model: ${error.message}`
      };
    }
  }

  // Process message using rule-based approach
  processRuleBasedMessage(message) {
    console.log(`[AIService] Processing rule-based message: ${message}`);

    // Convert message to lowercase for easier matching
    const lowerMessage = message.toLowerCase().trim();

    // Special handling for test_unreal_connection
    if (lowerMessage === 'test_unreal_connection') {
      console.log('[AIService] Rule-based: Detected test_unreal_connection request');
      return {
        isCommand: true,
        command: 'test_unreal_connection',
        params: {}
      };
    }

    // Check for object creation with color
    const objectTypes = {
      'cube': 'Cube',
      'sphere': 'Sphere',
      'cylinder': 'Cylinder',
      'cone': 'Cone',
      'plane': 'Plane'
    };

    // Check for color keywords
    const colorKeywords = {
      'red': [1, 0, 0],
      'green': [0, 1, 0],
      'blue': [0, 0, 1],
      'yellow': [1, 1, 0],
      'cyan': [0, 1, 1],
      'magenta': [1, 0, 1],
      'white': [1, 1, 1],
      'black': [0, 0, 0],
      'pink': [1, 0.7, 0.7],
      'purple': [0.5, 0, 0.5],
      'orange': [1, 0.5, 0],
      'brown': [0.6, 0.3, 0],
      'gray': [0.5, 0.5, 0.5],
      'grey': [0.5, 0.5, 0.5]
    };

    // Check if the message is about creating an object
    if (lowerMessage && typeof lowerMessage === 'string' && (
        lowerMessage.includes('create') ||
        lowerMessage.includes('make') ||
        lowerMessage.includes('add')
    )) {
      // Determine object type
      let objectType = null;
      for (const [keyword, className] of Object.entries(objectTypes)) {
        if (lowerMessage && lowerMessage.includes(keyword)) {
          objectType = className;
          console.log(`[AIService] Rule-based: Detected object type: ${objectType}`);
          break;
        }
      }

      // If no specific object type found, default to Cube
      if (!objectType) {
        objectType = 'Cube';
        console.log('[AIService] Rule-based: No specific object type found, defaulting to Cube');
      }

      // Determine color
      let color = null;
      let colorName = null;
      for (const [name, colorValue] of Object.entries(colorKeywords)) {
        if (lowerMessage && lowerMessage.includes(name)) {
          color = colorValue;
          colorName = name;
          console.log(`[AIService] Rule-based: Detected color: ${colorName}`);
          break;
        }
      }

      // Generate a unique ID for the object
      const objectId = `${objectType}_${Date.now()}`;

      // Create the command
      const command = {
        isCommand: true,
        command: 'spawn_object',
        params: {
          actor_class: objectType,
          location: [0, 0, 100],
          rotation: [0, 0, 0],
          scale: [1, 1, 1],
          actor_label: objectId
        }
      };

      // Add color if specified
      if (color) {
        command.params.color = color;
      }

      return command;
    }

    // Default response if no rule matches
    console.log('[AIService] Rule-based: No matching rule found');
    return {
      isCommand: false,
      message: 'I did not understand that command. Try asking me to create a cube or sphere.'
    };
  }

  // Parse AI response to extract command
  parseAIResponse(text) {
    console.log('[AIService] Parsing AI response for commands');

    // Look for command pattern: COMMAND: xxx\nPARAMS: yyy
    const commandMatch = text.match(/COMMAND:\s*([^\n]+)/i);
    const paramsMatch = text.match(/PARAMS:\s*(.+?)(?=COMMAND:|$)/is);

    if (commandMatch && paramsMatch) {
      const command = commandMatch[1].trim();
      let params;

      try {
        // Try to parse JSON parameters
        const paramsText = paramsMatch[1].trim();
        params = JSON.parse(paramsText);
      } catch (e) {
        console.warn('[AIService] Failed to parse JSON parameters:', e.message);
        // If JSON parsing fails, use the raw text
        params = paramsMatch[1].trim();
      }

      console.log(`[AIService] Extracted command: ${command}`);
      return {
        isCommand: true,
        command,
        params
      };
    }

    // Look for code block with command
    const codeBlockMatch = text.match(/```(?:json)?\s*(\{.+?\})\s*```/s);
    if (codeBlockMatch) {
      try {
        const codeBlock = JSON.parse(codeBlockMatch[1]);
        if (codeBlock.command) {
          console.log(`[AIService] Extracted command from code block: ${codeBlock.command}`);
          return {
            isCommand: true,
            command: codeBlock.command,
            params: codeBlock.params || {}
          };
        }
      } catch (e) {
        console.warn('[AIService] Failed to parse code block as JSON:', e.message);
      }
    }

    // Filter out unwanted phrases about scene inventory or not having access
    let filteredText = text;

    // List of phrases to filter out
    const phrasesToFilter = [
      "I don't have access to",
      "I don't have a live",
      "I don't have access to a live",
      "I don't have a complete",
      "I can't see",
      "I cannot see",
      "I can't access",
      "I cannot access",
      "inventory of your scene",
      "inventory of the scene",
      "scene inventory",
      "scene's inventory",
      "what's in your scene",
      "what is in your scene",
      "objects in your scene",
      "objects in the scene",
      "To get a list of",
      "To get a full list of",
      "To see what's in",
      "To see what is in",
      "To view the objects",
      "World Outliner",
      "Unfortunately"
    ];

    // Check if the response contains any of the phrases to filter
    const containsUnwantedPhrases = phrasesToFilter.some(phrase =>
      text.toLowerCase().includes(phrase.toLowerCase())
    );

    // Check for material-specific issues
    const materialIssuePatterns = [
      /no material/i,
      /missing material/i,
      /material (is |was )?not applied/i,
      /material (is |was )?not found/i,
      /material (is |was )?not created/i,
      /material (is |was )?not set/i,
      /material issue/i,
      /issue with (the )?material/i,
      /problem with (the )?material/i,
      /error (in|with) (the )?material/i
    ];

    const hasMaterialIssue = materialIssuePatterns.some(pattern => pattern.test(text));

    if (containsUnwantedPhrases || hasMaterialIssue) {
      console.log('[AIService] Detected phrases to filter or material issues, creating dynamic response');

      // Extract any useful information from the response
      let successInfo = '';
      let materialInfo = '';
      let objectInfo = '';

      // Look for confirmation messages about what was created or modified
      const creationMatch = text.match(/created (a|an) ([\w\s]+)/i);
      const modificationMatch = text.match(/modified (a|an|the) ([\w\s]+)/i);
      const addedMatch = text.match(/added (a|an) ([\w\s]+)/i);
      const materialCreationMatch = text.match(/material creation/i);
      const objectTypeMatch = text.match(/(cube|sphere|cylinder|cone|plane)/i);
      const colorMatch = text.match(/(red|green|blue|yellow|purple|orange|black|white|gray|grey|pink|cyan|magenta)/i);

      // Extract object information
      if (objectTypeMatch) {
        objectInfo = objectTypeMatch[1].toLowerCase();
        console.log(`[AIService] Detected object type: ${objectInfo}`);
      }

      // Extract color information
      let colorInfo = '';
      if (colorMatch) {
        colorInfo = colorMatch[1].toLowerCase();
        console.log(`[AIService] Detected color: ${colorInfo}`);
      }

      // Handle material issues specifically
      if (hasMaterialIssue) {
        console.log('[AIService] Detected material issue in response');

        // Create a specific response for material issues
        if (colorInfo && objectInfo) {
          materialInfo = `I've created the ${objectInfo}, but there seems to be an issue with the ${colorInfo} material. `;
          materialInfo += `The object should still appear in the scene, but it might not have the correct material applied. `;
          materialInfo += `This could be due to a permission issue or a problem with the material creation process.`;
        } else if (objectInfo) {
          materialInfo = `I've created the ${objectInfo}, but there seems to be an issue with the material. `;
          materialInfo += `The object should still appear in the scene, but it might not have the correct material applied.`;
        } else {
          materialInfo = `I've created the object, but there seems to be an issue with the material. `;
          materialInfo += `The object should still appear in the scene, but it might not have the correct material applied.`;
        }
      }

      // Generate success information if no material issue was detected
      if (!materialInfo) {
        if (creationMatch) {
          successInfo = `I've created the ${creationMatch[2]} successfully!`;
        } else if (modificationMatch) {
          successInfo = `I've modified the ${modificationMatch[2]} successfully!`;
        } else if (addedMatch) {
          successInfo = `I've added the ${addedMatch[2]} successfully!`;
        } else if (materialCreationMatch) {
          successInfo = "I've created the material successfully! The material has been applied to your object.";
        } else {
          // Look for specific success messages in the text
          const successMessages = [
            "Connected color constant to base color",
            "Material recompiled",
            "Applied material",
            "Created",
            "Added",
            "Modified"
          ];

          const foundMessages = [];
          successMessages.forEach(msg => {
            if (text.includes(msg)) {
              foundMessages.push(msg);
            }
          });

          if (foundMessages.length > 0) {
            successInfo = `Operation completed successfully! ${foundMessages.join(" and ")}.`;
          } else {
            successInfo = "I've completed the operation successfully!";
          }
        }
      }

      // Create a dynamic response that focuses on what was done
      filteredText = materialInfo || `${successInfo} Let me know if you need anything else.`;
    }

    // If no command found, return the filtered text as a regular response
    console.log('[AIService] No command found in AI response, treating as conversation');
    return {
      isCommand: false,
      message: filteredText
    };
  }
}

module.exports = new AIService();