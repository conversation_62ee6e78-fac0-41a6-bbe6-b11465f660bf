'use client';

import { useEffect, useState } from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/lib/supabase';
import { formatDistanceToNow } from 'date-fns';

interface User {
  id: string;
  name: string;
  email: string;
  subscription_status: string;
  created_at: string;
}

export default function RecentUsersTable() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchRecentUsers() {
      try {
        setLoading(true);
        
        const { data, error } = await supabase
          .from('users')
          .select('id, name, email, subscription_status, created_at')
          .order('created_at', { ascending: false })
          .limit(10);
          
        if (error) {
          throw error;
        }
        
        setUsers(data || []);
      } catch (err: any) {
        console.error('Error fetching recent users:', err);
        setError(err.message || 'Failed to load recent users');
      } finally {
        setLoading(false);
      }
    }

    fetchRecentUsers();
  }, []);

  function getSubscriptionBadge(status: string) {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'canceled':
        return <Badge variant="outline" className="text-orange-500 border-orange-500">Canceled</Badge>;
      case 'inactive':
        return <Badge variant="outline" className="text-gray-500">Inactive</Badge>;
      default:
        return <Badge variant="outline" className="text-gray-500">None</Badge>;
    }
  }

  if (loading) {
    return (
      <div className="space-y-2">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-10 animate-pulse bg-gray-200 rounded" />
        ))}
      </div>
    );
  }

  if (error) {
    return <div className="text-red-500 p-4">{error}</div>;
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Name</TableHead>
          <TableHead>Email</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Joined</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {users.length === 0 ? (
          <TableRow>
            <TableCell colSpan={4} className="text-center py-4 text-gray-500">
              No users found
            </TableCell>
          </TableRow>
        ) : (
          users.map((user) => (
            <TableRow key={user.id}>
              <TableCell className="font-medium">{user.name}</TableCell>
              <TableCell>{user.email}</TableCell>
              <TableCell>{getSubscriptionBadge(user.subscription_status)}</TableCell>
              <TableCell className="text-gray-500">
                {formatDistanceToNow(new Date(user.created_at), { addSuffix: true })}
              </TableCell>
            </TableRow>
          ))
        )}
      </TableBody>
    </Table>
  );
}
