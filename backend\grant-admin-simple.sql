-- Grant admin access to your user account
-- Run this in Supabase Dashboard > SQL Editor

-- 1. Add is_admin column if it doesn't exist
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_admin BOOLEAN NOT NULL DEFAULT FALSE;

-- 2. Grant admin privileges to your user ID
UPDATE users SET is_admin = TRUE WHERE id = '5af3ff0f-0ccb-41a2-8dba-3e460aa9bd72';

-- 3. Verify the update
SELECT id, email, name, is_admin FROM users WHERE id = '5af3ff0f-0ccb-41a2-8dba-3e460aa9bd72';

-- 4. Also grant admin to the standard admin emails (if they exist)
UPDATE users SET is_admin = TRUE WHERE email IN ('<EMAIL>', '<EMAIL>');

-- 5. Show all admin users
SELECT id, email, name, is_admin FROM users WHERE is_admin = TRUE;
