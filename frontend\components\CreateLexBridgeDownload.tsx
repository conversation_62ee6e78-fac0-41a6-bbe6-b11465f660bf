'use client';

import React from 'react';
import { useAuth } from '../contexts/AuthContext';

interface CreateLexBridgeDownloadProps {
  className?: string;
}

export default function CreateLexBridgeDownload({ className = '' }: CreateLexBridgeDownloadProps) {
  const { user, hasActiveSubscription } = useAuth();

  // Only show for users with active subscription
  if (!user || !hasActiveSubscription) {
    return null;
  }

  const downloads = [
    {
      platform: 'Windows',
      icon: (
        <svg className="h-8 w-8 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
          <path d="M0 3.449L9.75 2.1v9.451H0m10.949-9.602L24 0v11.4H10.949M0 12.6h9.75v9.451L0 20.699M10.949 12.6H24V24l-13.051-1.351" />
        </svg>
      ),
      filename: 'CreateLex-Bridge-Setup-1.0.0.exe',
      downloadUrl: 'https://github.com/AlexKissiJr/AiWebplatform/releases/download/v1.0.0-bridge/CreateLex-Bridge-Setup-1.0.0.exe',
      size: '435 MB',
      description: 'Windows installer for CreateLex Bridge application'
    },
    {
      platform: 'macOS (Apple Silicon)',
      icon: (
        <svg className="h-8 w-8 text-gray-700" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12.152 6.896c-.948 0-2.415-1.078-3.96-1.04-2.04.027-3.91 1.183-4.961 3.014-2.117 3.675-.546 9.103 1.519 12.09 1.013 1.454 2.208 3.09 3.792 3.039 1.52-.065 2.09-.987 3.935-.987 1.831 0 2.35.987 3.96.948 1.637-.026 2.676-1.48 3.676-2.948 1.156-1.688 1.636-3.325 1.662-3.415-.039-.013-3.182-1.221-3.22-4.857-.026-3.04 2.48-4.494 2.597-4.559-1.429-2.09-3.623-2.324-4.39-2.376-2-.156-3.675 1.09-4.61 1.09zM15.53 3.83c.843-1.012 1.4-2.427 1.245-3.83-1.207.052-2.662.805-3.532 1.818-.78.896-1.454 2.338-1.273 3.714 1.338.104 2.715-.688 3.559-1.701" />
        </svg>
      ),
      filename: 'CreateLex-Bridge-1.0.0-arm64.dmg',
      downloadUrl: 'https://github.com/AlexKissiJr/AiWebplatform/releases/download/v1.0.0-bridge/CreateLex-Bridge-1.0.0-arm64.dmg',
      size: '122 MB',
      description: 'macOS disk image for CreateLex Bridge application (Apple Silicon)'
    },
    {
      platform: 'macOS (Intel)',
      icon: (
        <svg className="h-8 w-8 text-gray-700" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12.152 6.896c-.948 0-2.415-1.078-3.96-1.04-2.04.027-3.91 1.183-4.961 3.014-2.117 3.675-.546 9.103 1.519 12.09 1.013 1.454 2.208 3.09 3.792 3.039 1.52-.065 2.09-.987 3.935-.987 1.831 0 2.35.987 3.96.948 1.637-.026 2.676-1.48 3.676-2.948 1.156-1.688 1.636-3.325 1.662-3.415-.039-.013-3.182-1.221-3.22-4.857-.026-3.04 2.48-4.494 2.597-4.559-1.429-2.09-3.623-2.324-4.39-2.376-2-.156-3.675 1.09-4.61 1.09zM15.53 3.83c.843-1.012 1.4-2.427 1.245-3.83-1.207.052-2.662.805-3.532 1.818-.78.896-1.454 2.338-1.273 3.714 1.338.104 2.715-.688 3.559-1.701" />
        </svg>
      ),
      filename: 'CreateLex-Bridge-1.0.0-x64.dmg',
      downloadUrl: 'https://github.com/AlexKissiJr/AiWebplatform/releases/download/v1.0.0-bridge/CreateLex-Bridge-1.0.0-x64.dmg',
      size: '124 MB',
      description: 'macOS disk image for CreateLex Bridge application (Intel processors)'
    }
  ];

  return (
    <div className={`bg-white shadow rounded-lg ${className}`}>
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">CreateLex Bridge Downloads</h3>
        <p className="mt-1 text-sm text-gray-500">
          Desktop application for seamless AI integration across your development tools
        </p>
      </div>
      
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {downloads.map((download) => (
            <div
              key={download.platform}
              className="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors"
            >
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  {download.icon}
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="text-lg font-medium text-gray-900">
                    {download.platform}
                  </h4>
                  <p className="text-sm text-gray-500 mb-2">
                    {download.description}
                  </p>
                  <p className="text-xs text-gray-400 mb-3">
                    Size: {download.size}
                  </p>
                  <a
                    href={download.downloadUrl}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    Download for {download.platform}
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {/* Info Section */}
        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h4 className="text-sm font-medium text-blue-800">
                What is CreateLex Bridge?
              </h4>
              <p className="mt-1 text-sm text-blue-700">
                CreateLex Bridge is a desktop application that provides seamless integration between AI services and your development environment. 
                It enables direct AI control of applications like Cursor IDE, VS Code, and other development tools through the Model Control Protocol (MCP).
              </p>
              <div className="mt-2">
                <h5 className="text-sm font-medium text-blue-800">Key Features:</h5>
                <ul className="list-disc list-inside text-sm text-blue-700 mt-1">
                  <li>MCP server management and monitoring</li>
                  <li>Cross-platform AI tool integration</li>
                  <li>Secure subscription validation</li>
                  <li>Real-time connection management</li>
                  <li>Development workflow automation</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* System Requirements */}
        <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4 text-xs text-gray-500">
          <div>
            <h5 className="font-medium text-gray-700">Windows Requirements:</h5>
            <ul className="mt-1 space-y-1">
              <li>• Windows 10/11 (64-bit)</li>
              <li>• .NET Framework 4.8+</li>
              <li>• 2GB RAM minimum</li>
            </ul>
          </div>
          <div>
            <h5 className="font-medium text-gray-700">macOS (Apple Silicon):</h5>
            <ul className="mt-1 space-y-1">
              <li>• macOS 12.0+ (Monterey)</li>
              <li>• Apple Silicon (M1/M2/M3)</li>
              <li>• 2GB RAM minimum</li>
            </ul>
          </div>
          <div>
            <h5 className="font-medium text-gray-700">macOS (Intel):</h5>
            <ul className="mt-1 space-y-1">
              <li>• macOS 10.15+ (Catalina)</li>
              <li>• Intel x64 processor</li>
              <li>• 2GB RAM minimum</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
} 