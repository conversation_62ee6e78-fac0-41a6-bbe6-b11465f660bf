require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: SUPABASE_URL and SUPABASE_SERVICE_KEY must be set in .env file');
  process.exit(1);
}

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key (first 10 chars):', supabaseKey.substring(0, 10) + '...');

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createTestUser() {
  try {
    // Create a test user with the same ID as the one in the token
    const userId = '8075c290-0943-4d3e-94a7-dcdf42bda6c6';
    const email = '<EMAIL>';
    const name = '<PERSON>';
    const picture = 'https://lh3.googleusercontent.com/a/ACg8ocIg6OWCzXa_PzMcgcQuOi9DKRzF6lQEaCJhfHGtSdKJLDvNkuE=s96-c';

    console.log(`Creating test user with ID: ${userId}`);

    // First check if the user already exists
    const { data: existingUser, error: fetchError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') {
      console.error('Error fetching user from Supabase:', fetchError);
      process.exit(1);
    }

    if (existingUser) {
      console.log(`User with ID ${userId} already exists:`, existingUser);

      // Update the user
      console.log('Updating user...');
      const { data: updatedUser, error: updateError } = await supabase
        .from('users')
        .update({
          email: email,
          name: name,
          picture: picture,
          subscription_status: 'inactive'
        })
        .eq('id', userId)
        .select();

      if (updateError) {
        console.error('Error updating user in Supabase:', updateError);
        process.exit(1);
      }

      console.log('User updated successfully:', updatedUser);
    } else {
      // Create the user
      console.log('Creating new user...');
      const { data: newUser, error: insertError } = await supabase
        .from('users')
        .insert([{
          id: userId,
          email: email,
          name: name,
          picture: picture,
          subscription_status: 'inactive',
          created_at: new Date().toISOString()
        }])
        .select();

      if (insertError) {
        console.error('Error creating user in Supabase:', insertError);
        process.exit(1);
      }

      console.log('User created successfully:', newUser);
    }

    // Verify the user exists
    const { data: verifyUser, error: verifyError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (verifyError) {
      console.error('Error verifying user in Supabase:', verifyError);
      process.exit(1);
    }

    console.log('User verified:', verifyUser);

    process.exit(0);
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

createTestUser();
