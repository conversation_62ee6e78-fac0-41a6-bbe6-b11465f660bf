'use client';

import { useState, useEffect } from 'react';
import { getSupabaseClient } from '@/lib/supabase-singleton';

export default function UserIdDebug() {
  const [userId, setUserId] = useState<string | null>(null);
  const [userIdSource, setUserIdSource] = useState<string | null>(null);
  const [supabaseUserId, setSupabaseUserId] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Get the current user ID from localStorage
    const currentUserId = localStorage.getItem('ai-chat-user-id');
    const currentUserIdSource = localStorage.getItem('ai-chat-user-id-source');

    setUserId(currentUserId);
    setUserIdSource(currentUserIdSource);

    // Get the user ID from Supabase
    const supabase = getSupabaseClient();
    supabase.auth.getUser().then(({ data, error }) => {
      if (!error && data?.user) {
        setSupabaseUserId(data.user.id);
      }
    });
  }, []);

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-gray-200 text-gray-700 px-3 py-1 rounded-md text-xs hover:bg-gray-300 transition-colors"
      >
        Debug
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-200 rounded-md shadow-md p-4 max-w-xs w-full text-xs">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold">User ID Debug</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          ✕
        </button>
      </div>

      <div className="mb-2">
        <div className="font-semibold">localStorage User ID:</div>
        <div className="break-all">{userId || 'None'}</div>
      </div>

      <div className="mb-2">
        <div className="font-semibold">Source:</div>
        <div>{userIdSource || 'None'}</div>
      </div>

      <div className="mb-4">
        <div className="font-semibold">Supabase Auth User ID:</div>
        <div className="break-all">{supabaseUserId || 'Not authenticated'}</div>
      </div>

      <div className="flex justify-between">
        <a
          href="/reset-user-id"
          className="bg-red-100 text-red-700 px-2 py-1 rounded-md text-xs hover:bg-red-200 transition-colors"
        >
          Reset User ID
        </a>

        <button
          onClick={() => {
            // Refresh the user ID information
            const currentUserId = localStorage.getItem('ai-chat-user-id');
            const currentUserIdSource = localStorage.getItem('ai-chat-user-id-source');

            setUserId(currentUserId);
            setUserIdSource(currentUserIdSource);

            // Get the user ID from Supabase
            const supabase = getSupabaseClient();
            supabase.auth.getUser().then(({ data, error }) => {
              if (!error && data?.user) {
                setSupabaseUserId(data.user.id);
              } else {
                setSupabaseUserId(null);
              }
            });
          }}
          className="bg-blue-100 text-blue-700 px-2 py-1 rounded-md text-xs hover:bg-blue-200 transition-colors"
        >
          Refresh
        </button>
      </div>
    </div>
  );
}
