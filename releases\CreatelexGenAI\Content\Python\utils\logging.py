import unreal
import traceback
from typing import Any

# Production mode flag - set to True for production releases
PRODUCTION_MODE = True

# Debug categories that can be enabled/disabled
DEBUG_HANDSHAKE = False  # Disable handshake logging in production
DEBUG_COMMANDS = False   # Disable command processing details in production


def log_info(message: str, debug_category: str = None) -> None:
    """
    Log an informational message to the Unreal log
    
    Args:
        message: The message to log
        debug_category: Optional debug category for filtering
    """
    # Skip debug messages in production mode
    if PRODUCTION_MODE and debug_category:
        if debug_category == "handshake" and not DEBUG_HANDSHAKE:
            return
        if debug_category == "command" and not DEBUG_COMMANDS:
            return
    
    unreal.log(f"[AI Plugin] {message}")


def log_warning(message: str) -> None:
    """
    Log a warning message to the Unreal log
    
    Args:
        message: The message to log
    """
    unreal.log_warning(f"[AI Plugin] {message}")


def log_error(message: str, include_traceback: bool = False) -> None:
    """
    Log an error message to the Unreal log
    
    Args:
        message: The message to log
        include_traceback: Whether to include the traceback in the log
    """
    error_message = f"[AI Plugin] ERROR: {message}"
    unreal.log_error(error_message)

    if include_traceback:
        tb = traceback.format_exc()
        unreal.log_error(f"[AI Plugin] Traceback:\n{tb}")


def log_command(command_type: str, details: Any = None) -> None:
    """
    Log a command being processed
    
    Args:
        command_type: The type of command being processed
        details: Optional details about the command
    """
    if details:
        unreal.log(f"[AI Plugin] Processing {command_type} command: {details}")
    else:
        unreal.log(f"[AI Plugin] Processing {command_type} command")


def log_result(command_type: str, success: bool, details: Any = None) -> None:
    """
    Log the result of a command
    
    Args:
        command_type: The type of command that was processed
        success: Whether the command was successful
        details: Optional details about the result
    """
    status = "successful" if success else "failed"

    if details:
        unreal.log(f"[AI Plugin] {command_type} command {status}: {details}")
    else:
        unreal.log(f"[AI Plugin] {command_type} command {status}")


def log_production_status(message: str) -> None:
    """
    Log important status messages that should always appear, even in production
    
    Args:
        message: The important status message to log
    """
    unreal.log(f"[CreateLex AI Studio] {message}")


def log_debug(message: str, category: str = "general") -> None:
    """
    Log debug messages that are suppressed in production mode
    
    Args:
        message: The debug message to log
        category: Debug category for filtering
    """
    if not PRODUCTION_MODE:
        unreal.log(f"[AI Plugin Debug] {message}")
    elif category == "handshake" and DEBUG_HANDSHAKE:
        unreal.log(f"[AI Plugin Debug] {message}")
    elif category == "command" and DEBUG_COMMANDS:
        unreal.log(f"[AI Plugin Debug] {message}")