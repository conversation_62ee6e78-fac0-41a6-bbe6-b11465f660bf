'use client';

import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import withSearchParamsProvider from '@/components/utils/withSearchParamsProvider';

// 🚀 Unreal Engine UI Update Functions
async function updateUnrealEngineUI(userEmail: string, isAuthenticated: boolean, hasSubscription: boolean) {
  try {
    const response = await fetch('http://localhost:9878/update-auth', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_email: userEmail,
        is_authenticated: isAuthenticated,
        has_subscription: hasSubscription
      }),
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ Unreal Engine UI updated successfully:', result);
      return true;
    } else {
      console.warn(`⚠️ Unreal Engine UI update failed: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`ℹ️ Unreal Engine UI update skipped (engine not running): ${error}`);
    return false;
  }
}

async function checkUserSubscription(userEmail: string): Promise<boolean> {
  try {
    // Get the current session to get the JWT token
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.access_token) {
      return false;
    }

    // Call your backend API to check subscription
    const response = await fetch('/api/subscription/status', {
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      return data.hasActiveSubscription || false;
    }

    return false;
  } catch (error) {
    console.warn('Failed to check subscription status:', error);
    return false;
  }
}

function AuthCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Handle the OAuth callback
    const handleAuthCallback = async () => {
      // Check if searchParams exists
      if (!searchParams) {
        console.error('Search parameters not available');
        router.push('/login?error=missing_params');
        return;
      }

      // Get the auth code from the URL
      const code = searchParams.get('code');

      if (code) {
        try {
          // Exchange the code for a session
          const { error, data } = await supabase.auth.exchangeCodeForSession(code);

          if (error) {
            console.error('Error exchanging code for session:', error);
            router.push('/login?error=auth_callback_error');
            return;
          }

          // 🚀 NEW: Update Unreal Engine UI after successful authentication
          if (data?.user) {
            try {
              // Check subscription status (you'll need to implement this)
              const hasSubscription = await checkUserSubscription(data.user.email);

              await updateUnrealEngineUI(data.user.email, true, hasSubscription);
              console.log('✅ Unreal Engine UI updated after OAuth callback');
            } catch (uiError) {
              console.warn('⚠️ Failed to update Unreal Engine UI (non-critical):', uiError);
            }
          }

          // Redirect to dashboard on success
          router.push('/dashboard');
        } catch (error) {
          console.error('Auth callback error:', error);
          router.push('/login?error=auth_callback_error');
        }
      } else {
        // No code found, redirect to login
        router.push('/login');
      }
    };

    handleAuthCallback();
  }, [router, searchParams]);

  return (
    <div className="flex justify-center items-center min-h-screen bg-[#f7f7f7]">
      <div className="text-center">
        <div className="spinner mb-4"></div>
        <p>Completing login...</p>
      </div>
    </div>
  );
}

// Export the wrapped component
export default withSearchParamsProvider(AuthCallbackPage);
