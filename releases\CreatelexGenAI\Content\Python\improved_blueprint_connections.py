import unreal
import json
from typing import Dict, Any, List, Optional


def connect_blueprint_nodes_improved(blueprint_path: str, function_name: str, 
                                   source_node_guid: str, source_pin: str,
                                   target_node_guid: str, target_pin: str) -> Dict[str, Any]:
    """
    Connect two Blueprint nodes using the Unreal Python API directly
    
    Args:
        blueprint_path: Path to the Blueprint asset
        function_name: Name of the function containing the nodes
        source_node_guid: GUID of the source node
        source_pin: Name of the output pin on the source node
        target_node_guid: GUID of the target node
        target_pin: Name of the input pin on the target node
        
    Returns:
        Dictionary with success status and connection information
    """
    try:
        # Load the Blueprint asset
        blueprint = unreal.EditorAssetLibrary.load_asset(blueprint_path)
        if not blueprint:
            return {"success": False, "error": f"Failed to load Blueprint at {blueprint_path}"}
            
        if not isinstance(blueprint, unreal.Blueprint):
            return {"success": False, "error": f"Asset at {blueprint_path} is not a Blueprint"}
            
        # Try to use the Blueprint Editor subsystem for more direct access
        try:
            blueprint_editor_subsystem = unreal.get_editor_subsystem(unreal.BlueprintEditorSubsystem)
            
            # Get the Blueprint's function graphs
            function_graphs = blueprint.get_function_graphs()
            blueprint_graph = None
            
            # Find the target function graph
            for graph in function_graphs:
                if hasattr(graph, 'get_fname') and str(graph.get_fname()) == function_name:
                    blueprint_graph = graph
                    break
                elif hasattr(graph, 'get_graph_name') and graph.get_graph_name() == function_name:
                    blueprint_graph = graph
                    break
                    
            if not blueprint_graph:
                # Try alternative method to find the graph
                all_graphs = blueprint.get_all_graphs()
                for graph in all_graphs:
                    try:
                        graph_name = str(graph.get_outer().get_name()) if hasattr(graph, 'get_outer') else str(graph)
                        if function_name in graph_name:
                            blueprint_graph = graph
                            break
                    except:
                        continue
                        
            if not blueprint_graph:
                available_functions = []
                try:
                    for graph in function_graphs:
                        try:
                            name = graph.get_graph_name() if hasattr(graph, 'get_graph_name') else str(graph.get_fname())
                            available_functions.append(name)
                        except:
                            available_functions.append(str(graph))
                except:
                    available_functions = ["Unable to enumerate functions"]
                    
                return {
                    "success": False, 
                    "error": f"Function '{function_name}' not found in Blueprint",
                    "available_functions": available_functions
                }
                
            # Use the improved connection with better error handling
            return {
                "success": False, 
                "error": f"Blueprint graph API not fully compatible with this Unreal version. Falling back to C++ implementation.",
                "blueprint_loaded": True,
                "function_found": blueprint_graph is not None,
                "suggested_action": "Use fallback C++ connection method"
            }
            
        except Exception as api_error:
            return {
                "success": False,
                "error": f"Blueprint API error: {str(api_error)}",
                "fallback_recommended": True
            }
            
    except Exception as e:
        error_msg = f"Error connecting Blueprint nodes: {str(e)}"
        unreal.log_error(error_msg)
        return {"success": False, "error": error_msg}


def connect_blueprint_nodes_bulk_improved(blueprint_path: str, function_name: str, 
                                         connections: List[Dict[str, str]]) -> Dict[str, Any]:
    """
    Connect multiple pairs of Blueprint nodes in bulk
    
    Args:
        blueprint_path: Path to the Blueprint asset
        function_name: Name of the function containing the nodes
        connections: List of connection dictionaries
            
    Returns:
        Dictionary with success status and detailed connection results
    """
    try:
        results = []
        successful_connections = 0
        failed_connections = 0
        
        for i, connection in enumerate(connections):
            try:
                source_node_guid = connection.get("source_node_guid")
                source_pin = connection.get("source_pin") 
                target_node_guid = connection.get("target_node_guid")
                target_pin = connection.get("target_pin")
                
                if not all([source_node_guid, source_pin, target_node_guid, target_pin]):
                    result = {
                        "connection_index": i,
                        "success": False,
                        "error": "Missing required parameters for connection"
                    }
                    results.append(result)
                    failed_connections += 1
                    continue
                    
                # Use our improved single connection function
                result = connect_blueprint_nodes_improved(
                    blueprint_path, function_name,
                    source_node_guid, source_pin,
                    target_node_guid, target_pin
                )
                
                result["connection_index"] = i
                results.append(result)
                
                if result.get("success"):
                    successful_connections += 1
                else:
                    failed_connections += 1
                    
            except Exception as e:
                result = {
                    "connection_index": i,
                    "success": False,
                    "error": f"Exception in connection {i}: {str(e)}"
                }
                results.append(result)
                failed_connections += 1
                
        # For this version, recommend using the C++ fallback
        summary = {
            "success": False,
            "total_connections": len(connections),
            "successful_connections": 0,
            "failed_connections": len(connections),
            "results": results,
            "recommendation": "Use C++ fallback method for this Unreal version",
            "fallback_recommended": True
        }
            
        return summary
        
    except Exception as e:
        error_msg = f"Error in bulk connection: {str(e)}"
        unreal.log_error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "total_connections": len(connections) if connections else 0,
            "successful_connections": 0,
            "failed_connections": len(connections) if connections else 0,
            "fallback_recommended": True
        }


def get_node_pin_info(blueprint_path: str, function_name: str, node_guid: str) -> Dict[str, Any]:
    """
    Get detailed information about a node's pins for debugging connections
    
    Args:
        blueprint_path: Path to the Blueprint asset
        function_name: Name of the function containing the node
        node_guid: GUID of the node to inspect
        
    Returns:
        Dictionary with node pin information
    """
    try:
        # Load the Blueprint asset
        blueprint = unreal.EditorAssetLibrary.load_asset(blueprint_path)
        if not blueprint:
            return {"success": False, "error": f"Failed to load Blueprint at {blueprint_path}"}
            
        # For this version, provide a helpful response that directs to C++ method
        return {
            "success": False,
            "error": "Pin inspection not available in this Unreal version",
            "blueprint_loaded": True,
            "recommendation": "Use C++ utilities for pin inspection",
            "node_guid": node_guid,
            "function_name": function_name
        }
        
    except Exception as e:
        return {"success": False, "error": f"Error getting pin info: {str(e)}"} 