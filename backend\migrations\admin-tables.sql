-- Add is_admin column to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_admin BOOLEAN NOT NULL DEFAULT FALSE;

-- Create usage_logs table
CREATE TABLE IF NOT EXISTS usage_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id TEXT REFERENCES users(id),
  request_id TEXT,
  endpoint TEXT NOT NULL,
  token_count INTEGER NOT NULL,
  model TEXT NOT NULL,
  cost DECIMAL(10, 6) NOT NULL,
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  request_data JSONB,
  response_data JSONB,
  status TEXT,
  ip_address TEXT,
  user_agent TEXT
);

-- Create api_keys table
CREATE TABLE IF NOT EXISTS api_keys (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id TEXT REFERENCES users(id),
  key_name TEXT NOT NULL,
  api_key TEXT NOT NULL UNIQUE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  last_used_at TIMESTAMPTZ,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  rate_limit INTEGER DEFAULT 100,
  permissions JSONB
);

-- Create billing_records table
CREATE TABLE IF NOT EXISTS billing_records (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id TEXT REFERENCES users(id),
  stripe_customer_id TEXT,
  stripe_invoice_id TEXT,
  amount DECIMAL(10, 2) NOT NULL,
  currency TEXT NOT NULL DEFAULT 'usd',
  status TEXT NOT NULL,
  billing_period_start TIMESTAMPTZ,
  billing_period_end TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  metadata JSONB
);

-- Set admin privileges for specified users
UPDATE users SET is_admin = TRUE WHERE email IN ('<EMAIL>', '<EMAIL>');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_usage_logs_user_id ON usage_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_logs_timestamp ON usage_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_usage_logs_model ON usage_logs(model);
CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_billing_records_user_id ON billing_records(user_id);
CREATE INDEX IF NOT EXISTS idx_billing_records_created_at ON billing_records(created_at);

-- Create a view for usage statistics
CREATE OR REPLACE VIEW usage_stats AS
SELECT
  user_id,
  model,
  DATE_TRUNC('day', timestamp) AS day,
  SUM(token_count) AS total_tokens,
  SUM(cost) AS total_cost,
  COUNT(*) AS request_count
FROM usage_logs
GROUP BY user_id, model, DATE_TRUNC('day', timestamp);

-- Create a view for billing statistics
CREATE OR REPLACE VIEW billing_stats AS
SELECT
  user_id,
  DATE_TRUNC('month', created_at) AS month,
  SUM(amount) AS total_amount,
  COUNT(*) AS invoice_count
FROM billing_records
GROUP BY user_id, DATE_TRUNC('month', created_at);

-- Create a function to get user usage for a specific period
CREATE OR REPLACE FUNCTION get_user_usage(user_id_param TEXT, start_date TIMESTAMPTZ, end_date TIMESTAMPTZ)
RETURNS TABLE (
  model TEXT,
  total_tokens BIGINT,
  total_cost DECIMAL,
  request_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    ul.model,
    SUM(ul.token_count) AS total_tokens,
    SUM(ul.cost) AS total_cost,
    COUNT(*) AS request_count
  FROM usage_logs ul
  WHERE ul.user_id = user_id_param
    AND ul.timestamp >= start_date
    AND ul.timestamp <= end_date
  GROUP BY ul.model;
END;
$$ LANGUAGE plpgsql;
