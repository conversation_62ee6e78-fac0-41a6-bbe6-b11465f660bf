#!/usr/bin/env python3
"""
Compile Python source files to protected bytecode
This hides source code while maintaining functionality
"""

import py_compile
import os
import shutil
import sys
from pathlib import Path

def compile_to_pyc(src_file: Path, dest_dir: Path):
    """Compile a Python file to .pyc bytecode"""
    try:
        # Ensure destination directory exists
        dest_dir.mkdir(parents=True, exist_ok=True)
        
        # Compile to bytecode
        dest_file = dest_dir / (src_file.stem + '.pyc')
        py_compile.compile(str(src_file), str(dest_file), doraise=True)
        
        print(f"✓ Compiled {src_file.name} -> {dest_file.name}")
        return True
        
    except Exception as e:
        print(f"✗ Failed to compile {src_file.name}: {e}")
        return False

def create_protected_build():
    """Create a protected build with compiled modules"""
    
    print("Creating protected build for UnrealGenAI MCP Server")
    print("=" * 50)
    
    # Source and destination directories
    src_dir = Path(".")
    protected_dir = Path("protected")
    
    # Clean and create protected directory
    if protected_dir.exists():
        shutil.rmtree(protected_dir)
    protected_dir.mkdir()
    
    # Files to compile and protect
    source_files = [
        "mcp_server.py",
        "cloud_mcp_server.py", 
        "cloud_mcp_wrapper.py",
        "auto_cloud_connector.py",
        "cloud_mcp_bridge.py",
        "cloud_mcp_client.py",
        "mcp_stdio.py"
    ]
    
    compiled_count = 0
    
    # Compile each source file
    for filename in source_files:
        src_file = src_dir / filename
        if src_file.exists():
            if compile_to_pyc(src_file, protected_dir):
                compiled_count += 1
        else:
            print(f"⚠ Source file not found: {filename}")
    
    # Create __init__.py for the protected package
    init_file = protected_dir / "__init__.py"
    init_file.write_text("""# Protected UnrealGenAI MCP Server Package
# Source code compiled to bytecode for protection
""")
    
    # Copy essential non-Python files
    essential_files = [
        "requirements.txt",
        "requirements-cloud.txt"
    ]
    
    for filename in essential_files:
        src_file = src_dir / filename
        if src_file.exists():
            dest_file = protected_dir / filename
            shutil.copy2(src_file, dest_file)
            print(f"✓ Copied {filename}")
    
    print(f"\nProtected build created successfully!")
    print(f"Compiled modules: {compiled_count}")
    print(f"Protected directory: {protected_dir.absolute()}")
    
    # Create build info
    build_info = {
        "build_time": str(os.path.getmtime(__file__)),
        "compiled_modules": compiled_count,
        "source_files": source_files,
        "version": "1.0.0"
    }
    
    import json
    with open(protected_dir / "build_info.json", 'w') as f:
        json.dump(build_info, f, indent=2)
    
    print(f"\n📦 Protected build ready for deployment!")
    print(f"🔒 Source code is now hidden in compiled bytecode")
    print(f"🚀 Use Dockerfile.protected to deploy")

if __name__ == "__main__":
    create_protected_build() 