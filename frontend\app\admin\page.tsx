import { Suspense } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import RecentUsersTable from '@/components/admin/RecentUsersTable';
import TokenUsageChart from '@/components/admin/TokenUsageChart';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import TokenUsageCard from '@/components/admin/TokenUsageCard';
import ProfitAnalysisCard from '@/components/admin/ProfitAnalysisCard';
import HighUsageUsersTable from '@/components/admin/HighUsageUsersTable';
import AdminActions from '@/components/admin/AdminActions';
import ApiKeyManager from '@/components/admin/ApiKeyManager';
import DashboardStats from '@/components/admin/DashboardStats';

export default function AdminDashboard() {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Admin Dashboard</h1>
        <AdminActions />
      </div>

      <Suspense fallback={<div className="h-32 flex items-center justify-center"><LoadingSpinner /></div>}>
        <DashboardStats />
      </Suspense>

      <Tabs defaultValue="users">
        <TabsList>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="usage">Token Usage</TabsTrigger>
          <TabsTrigger value="api-keys">API Keys</TabsTrigger>
          <TabsTrigger value="billing">Billing</TabsTrigger>
        </TabsList>
        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Users</CardTitle>
              <CardDescription>
                Recently registered users on the platform
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<LoadingSpinner />}>
                <RecentUsersTable />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="usage" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Suspense fallback={<Card className="h-32 flex items-center justify-center"><LoadingSpinner /></Card>}>
              <TokenUsageCard />
            </Suspense>
            <Suspense fallback={<Card className="h-32 flex items-center justify-center"><LoadingSpinner /></Card>}>
              <ProfitAnalysisCard />
            </Suspense>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Token Usage</CardTitle>
              <CardDescription>
                Token usage across different models
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<LoadingSpinner />}>
                <TokenUsageChart />
              </Suspense>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>High Usage Users</CardTitle>
              <CardDescription>
                Users with the highest token consumption
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<LoadingSpinner />}>
                <HighUsageUsersTable />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="api-keys" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>API Keys</CardTitle>
              <CardDescription>
                Manage API keys for accessing the CreateLex AI API
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<LoadingSpinner />}>
                <ApiKeyManager />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="billing" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Billing Overview</CardTitle>
              <CardDescription>
                Monthly revenue and subscription statistics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-center py-8 text-gray-500">
                Billing data will be available once the metered billing system is fully implemented.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
