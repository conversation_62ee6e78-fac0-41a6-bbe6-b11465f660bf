#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 Direct MCP Server Test');
console.log('=========================');

const mcpPath = path.join(__dirname, 'src/python-protected/mcp_server_mac');

if (!fs.existsSync(mcpPath)) {
    console.log('❌ MCP server executable not found at:', mcpPath);
    process.exit(1);
}

console.log('✅ MCP server found:', mcpPath);

// Check file info
const stats = fs.statSync(mcpPath);
console.log('📦 Size:', Math.round(stats.size / 1024 / 1024) + 'MB');
console.log('🔒 Permissions:', (stats.mode & parseInt('777', 8)).toString(8));

console.log('\n🚀 Testing MCP server startup...');

const child = spawn(mcpPath, [], {
    stdio: ['pipe', 'pipe', 'pipe']
});

let output = '';
let errorOutput = '';

child.stdout.on('data', (data) => {
    output += data.toString();
    console.log('📤 STDOUT:', data.toString().trim());
});

child.stderr.on('data', (data) => {
    errorOutput += data.toString();
    console.log('📤 STDERR:', data.toString().trim());
});

child.on('close', (code) => {
    console.log('\n📊 Test Results:');
    console.log('================');
    console.log('Exit code:', code);
    
    if (errorOutput.includes('subscription') || errorOutput.includes('Invalid')) {
        console.log('✅ SUCCESS: MCP server starts correctly!');
        console.log('   The subscription validation message means it\'s working.');
        console.log('   This is the expected behavior for the protected version.');
    } else if (errorOutput.includes('ImportError') || errorOutput.includes('ModuleNotFoundError')) {
        console.log('❌ IMPORT ERROR: MCP server has missing dependencies');
        console.log('   Error:', errorOutput.trim());
    } else if (code === null) {
        console.log('⏰ TIMEOUT: MCP server may be waiting for input (this is normal)');
        console.log('   The server is likely working correctly.');
    } else {
        console.log('⚠️  UNEXPECTED: Unknown behavior');
        console.log('   Stdout:', output.trim());
        console.log('   Stderr:', errorOutput.trim());
    }
    
    console.log('\n🎯 Conclusion:');
    if (errorOutput.includes('subscription') || code === null) {
        console.log('✅ MCP server is working correctly!');
        console.log('✅ Ready for Claude Desktop integration!');
    } else {
        console.log('❌ MCP server needs attention');
    }
});

child.on('error', (error) => {
    console.log('❌ Failed to start MCP server:', error.message);
});

// Kill after 3 seconds
setTimeout(() => {
    if (!child.killed) {
        child.kill();
        console.log('\n⏰ Test completed (3 second timeout)');
    }
}, 3000); 