const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose limited IPC methods to the renderer
contextBridge.exposeInMainWorld('electronAPI', {
  // Legacy authentication (for backward compatibility)
  authenticate: (credentials) => ipcRenderer.invoke('authenticate', credentials),
  
  // New OAuth authentication methods
  authenticateOAuth: () => ipcRenderer.invoke('authenticate-oauth'),
  cancelOAuth: () => ipcRenderer.invoke('cancel-oauth'),
  checkAuthStatus: () => ipcRenderer.invoke('check-auth-status'),
  showDashboard: () => ipcRenderer.invoke('show-dashboard'),
  logout: () => ipcRenderer.invoke('logout'),
  
  // MCP server operations
  startMcp: (authData) => ipcRenderer.invoke('start-mcp', authData),
  stopMcp: () => ipcRenderer.invoke('stop-mcp'),
  getMcpStatus: () => ipc<PERSON>ender<PERSON>.invoke('get-mcp-status'),
  checkSubscription: () => ipcRenderer.invoke('check-subscription'),
  
  // MCP update operations
  checkMcpUpdates: () => ipcRenderer.invoke('check-mcp-updates'),
  updateMcpServer: () => ipcRenderer.invoke('update-mcp-server'),
  getMcpVersion: () => ipcRenderer.invoke('get-mcp-version'),
  cleanupMcpUpdates: () => ipcRenderer.invoke('cleanup-mcp-updates'),
  
  // Event listeners for subscription status and updates
  onSubscriptionInvalid: (callback) => {
    ipcRenderer.on('subscription-invalid', (event, data) => callback(data));
  },
  onSubscriptionWarning: (callback) => {
    ipcRenderer.on('subscription-warning', (event, data) => callback(data));
  },
  onMcpUpdateStatus: (callback) => {
    ipcRenderer.on('mcp-update-status', (event, data) => callback(data));
  },
  onMcpUpdateProgress: (callback) => {
    ipcRenderer.on('mcp-update-progress', (event, data) => callback(data));
  },
  
  // Remove event listeners
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  }
}); 