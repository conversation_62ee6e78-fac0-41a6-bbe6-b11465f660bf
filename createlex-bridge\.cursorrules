# CreatelexGenAI Development Workflow Rules

## 🎯 CRITICAL DEVELOPMENT RULE
**MANDATORY**: Every time we make any code changes to the CreatelexGenAI Unreal Engine plugin, we MUST test by running the instant refresh script (`dev_refresh_bypass.bat`) and check Unreal Engine project logs to debug and fix issues.

## 🔄 Development Workflow Process

### 1. Code Change Testing Protocol
- **After ANY plugin code changes**: Run `./automation-scripts/dev_refresh_bypass.bat`
- **Check logs**: `C:\Dev\YourLife\Saved\Logs\YourLife.log`
- **Debug issues**: Fix any errors found in logs before proceeding
- **Verify connection**: Ensure MCP bridge on port 9877 is active

### 2. Automation Scripts Location
- **Primary Script**: `./automation-scripts/dev_refresh_bypass.bat`
- **Process Cleanup**: `./automation-scripts/kill_all_mcp.bat`
- **Bridge Startup**: `./automation-scripts/start_mcp_bypass_simple.bat`
- **Documentation**: `./automation-scripts/README.md`

### 3. Development Environment Setup
- **Project Path**: `C:\Dev\YourLife`
- **Plugin Source**: `C:\Dev\AiWebplatform\CreatelexGenAI`
- **Bridge Path**: `C:\Dev\AiWebplatform\createlex-bridge`
- **UE Engine**: `C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor.exe`

## 🚀 MCP Bridge Bypass Mode
- **Mode**: Development bypass (no authentication)
- **Auto-start**: MCP server launches automatically
- **No manual interaction**: No login or button clicking required
- **Environment**: Development mode with all bypass flags enabled

## 🛡️ Terminal & Process Management
- **Safe cleanup**: Scripts protect themselves and development workflow
- **Conservative targeting**: Only closes OLD MCP/Bridge processes
- **Triple protection**: Current PID + Parent PID + Protected PID system
- **Window handle cleanup**: Direct window closing for stuck terminals

## 📊 Verification Requirements
After running refresh script, verify:
1. ✅ MCP Bridge running on port 9877
2. ✅ Unreal Editor process active
3. ✅ CreatelexGenAI plugin loaded in UE
4. ✅ No errors in UE project logs

## 🔧 Script Behavior Standards
- **Always preserve development workflow windows**
- **Use detailed logging for troubleshooting**
- **Implement multiple cleanup methods for reliability**
- **Provide clear status feedback during execution**
- **Handle edge cases gracefully**

## 📝 Code Modification Guidelines
- **Never modify core bypass logic without testing**
- **Maintain backward compatibility with existing scripts**
- **Update documentation when changing script behavior**
- **Test terminal protection before committing changes**

## 🎯 Development Iteration Cycle
1. **Make code changes** to CreatelexGenAI plugin
2. **Run refresh script** (`dev_refresh_bypass.bat`)
3. **Check UE logs** for errors or issues
4. **Debug and fix** any problems found
5. **Verify MCP connection** and functionality
6. **Repeat cycle** for next changes

## 🔍 Troubleshooting Protocol
- **Script exits early**: Check terminal protection logic
- **Bridge won't start**: Verify bypass environment variables
- **UE compilation fails**: Check plugin paths and dependencies
- **Port conflicts**: Run `kill_all_mcp.bat` to clean up

---

*These rules ensure consistent, reliable development workflow for CreatelexGenAI plugin integration with MCP Bridge architecture.*