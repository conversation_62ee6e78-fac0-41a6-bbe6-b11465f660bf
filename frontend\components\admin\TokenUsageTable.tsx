'use client';

import { useState, useEffect } from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { DatePickerWithRange } from '@/components/ui/date-range-picker';
import { DateRange } from 'react-day-picker';
import { format, subDays } from 'date-fns';
import { fetchWithAuth, getApiUrl } from '@/lib/auth-utils';
import { Skeleton } from '@/components/ui/skeleton';
import { Download, Search } from 'lucide-react';

interface TokenUsageRecord {
  id: string;
  user_id: string;
  model_id: string;
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
  timestamp: string;
  request_type: string;
  subscription_plan: string;
  user_email?: string;
  user_name?: string;
}

export default function TokenUsageTable() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [records, setRecords] = useState<TokenUsageRecord[]>([]);
  const [filteredRecords, setFilteredRecords] = useState<TokenUsageRecord[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [modelFilter, setModelFilter] = useState('all');
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: subDays(new Date(), 7),
    to: new Date(),
  });
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalRecords, setTotalRecords] = useState(0);

  useEffect(() => {
    fetchUsageData();
  }, [dateRange]);

  useEffect(() => {
    filterRecords();
  }, [records, searchQuery, modelFilter]);

  const fetchUsageData = async () => {
    try {
      setLoading(true);
      
      if (!dateRange?.from) return;
      
      const fromDate = dateRange.from;
      const toDate = dateRange.to || new Date();
      
      const apiUrl = getApiUrl();
      const requestUrl = `${apiUrl}/api/admin/token-usage/records?from=${format(fromDate, 'yyyy-MM-dd')}&to=${format(toDate, 'yyyy-MM-dd')}&page=${page}&pageSize=${pageSize}`;
      
      const response = await fetchWithAuth(requestUrl);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch token usage records: ${response.status}`);
      }
      
      const data = await response.json();
      
      setRecords(data.records || []);
      setTotalRecords(data.total || 0);
      
      // If no records returned, use mock data for development
      if (!data.records || data.records.length === 0) {
        const mockData = generateMockData();
        setRecords(mockData);
        setTotalRecords(mockData.length);
      }
    } catch (error) {
      console.error('Error fetching token usage records:', error);
      setError('Failed to load token usage records');
      
      // Use mock data for development
      const mockData = generateMockData();
      setRecords(mockData);
      setTotalRecords(mockData.length);
    } finally {
      setLoading(false);
    }
  };

  const filterRecords = () => {
    let filtered = [...records];
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(record => 
        record.user_email?.toLowerCase().includes(query) ||
        record.user_name?.toLowerCase().includes(query) ||
        record.user_id.toLowerCase().includes(query) ||
        record.model_id.toLowerCase().includes(query)
      );
    }
    
    // Apply model filter
    if (modelFilter !== 'all') {
      filtered = filtered.filter(record => record.model_id === modelFilter);
    }
    
    setFilteredRecords(filtered);
  };

  const generateMockData = (): TokenUsageRecord[] => {
    const models = [
      'claude-3.7-sonnet-20240307',
      'claude-3-opus-20240229',
      'claude-3-sonnet-20240229',
      'claude-3-haiku-20240307'
    ];
    
    const users = [
      { id: 'user1', email: '<EMAIL>', name: 'User One' },
      { id: 'user2', email: '<EMAIL>', name: 'User Two' },
      { id: 'user3', email: '<EMAIL>', name: 'User Three' }
    ];
    
    const requestTypes = ['chat', 'completion', 'embedding'];
    const plans = ['basic', 'pro'];
    
    const mockData: TokenUsageRecord[] = [];
    
    for (let i = 0; i < 20; i++) {
      const user = users[Math.floor(Math.random() * users.length)];
      const promptTokens = Math.floor(Math.random() * 1000) + 100;
      const completionTokens = Math.floor(Math.random() * 2000) + 200;
      
      mockData.push({
        id: `mock-${i}`,
        user_id: user.id,
        user_email: user.email,
        user_name: user.name,
        model_id: models[Math.floor(Math.random() * models.length)],
        prompt_tokens: promptTokens,
        completion_tokens: completionTokens,
        total_tokens: promptTokens + completionTokens,
        timestamp: new Date(Date.now() - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000)).toISOString(),
        request_type: requestTypes[Math.floor(Math.random() * requestTypes.length)],
        subscription_plan: plans[Math.floor(Math.random() * plans.length)]
      });
    }
    
    return mockData;
  };

  const exportToCsv = () => {
    // Create CSV content
    const headers = ['Date', 'User', 'Model', 'Prompt Tokens', 'Completion Tokens', 'Total Tokens', 'Request Type', 'Plan'];
    
    const rows = filteredRecords.map(record => [
      format(new Date(record.timestamp), 'yyyy-MM-dd HH:mm:ss'),
      record.user_email || record.user_id,
      record.model_id,
      record.prompt_tokens,
      record.completion_tokens,
      record.total_tokens,
      record.request_type,
      record.subscription_plan
    ]);
    
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n');
    
    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `token-usage-${format(new Date(), 'yyyy-MM-dd')}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getUniqueModels = () => {
    const models = new Set<string>();
    records.forEach(record => models.add(record.model_id));
    return Array.from(models);
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between">
          <Skeleton className="h-10 w-[200px]" />
          <Skeleton className="h-10 w-[200px]" />
        </div>
        <div className="space-y-2">
          {[...Array(5)].map((_, i) => (
            <Skeleton key={i} className="h-12 w-full" />
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return <div className="text-red-500 p-4">{error}</div>;
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div className="flex flex-1 items-center space-x-2">
          <Search className="h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search by user or model..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
        </div>
        
        <div className="flex flex-wrap gap-2">
          <Select value={modelFilter} onValueChange={setModelFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by model" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Models</SelectItem>
              {getUniqueModels().map(model => (
                <SelectItem key={model} value={model}>{model}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <DatePickerWithRange 
            date={dateRange} 
            setDate={setDateRange} 
          />
          
          <Button variant="outline" onClick={exportToCsv}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>
      
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>User</TableHead>
              <TableHead>Model</TableHead>
              <TableHead className="text-right">Prompt Tokens</TableHead>
              <TableHead className="text-right">Completion Tokens</TableHead>
              <TableHead className="text-right">Total Tokens</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Plan</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredRecords.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-4 text-gray-500">
                  No records found
                </TableCell>
              </TableRow>
            ) : (
              filteredRecords.map((record) => (
                <TableRow key={record.id}>
                  <TableCell>{format(new Date(record.timestamp), 'MMM dd, yyyy HH:mm')}</TableCell>
                  <TableCell>{record.user_email || record.user_id}</TableCell>
                  <TableCell>{record.model_id}</TableCell>
                  <TableCell className="text-right">{record.prompt_tokens.toLocaleString()}</TableCell>
                  <TableCell className="text-right">{record.completion_tokens.toLocaleString()}</TableCell>
                  <TableCell className="text-right font-medium">{record.total_tokens.toLocaleString()}</TableCell>
                  <TableCell>{record.request_type}</TableCell>
                  <TableCell>{record.subscription_plan}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
      
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-500">
          Showing {filteredRecords.length} of {totalRecords} records
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(page - 1)}
            disabled={page === 1}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(page + 1)}
            disabled={page * pageSize >= totalRecords}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
