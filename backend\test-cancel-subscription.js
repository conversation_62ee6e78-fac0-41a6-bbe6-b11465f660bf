// Load environment variables
require('dotenv').config();

const subscriptionService = require('./src/services/subscriptionService');
const authService = require('./src/services/authService');
const emailService = require('./src/services/emailService');

// User ID and subscription ID
const userId = '077f1533-9f81-429c-b1b1-52d9c83f146c';
const subscriptionId = 'sub_1RKCjVJpp8exsOhCkhIK5glG';

async function testCancelSubscription() {
  try {
    console.log(`Testing subscription cancellation for user: ${userId}`);
    
    // Reload users from file
    if (typeof authService.loadUsersFromFile === 'function') {
      authService.loadUsersFromFile();
    }
    
    // Get user details
    const user = await authService.getUserById(userId);
    console.log('User details:', JSON.stringify(user, null, 2));
    
    if (!user) {
      console.error('User not found');
      return;
    }
    
    // If user doesn't have a subscription ID, add it
    if (!user.subscriptionId) {
      console.log(`User doesn't have a subscription ID. Adding it: ${subscriptionId}`);
      
      // Update user with subscription information
      await authService.updateSubscription(userId, 'active', subscriptionId);
      
      // Reload user to verify update
      const updatedUser = await authService.getUserById(userId);
      console.log('Updated user details:', JSON.stringify(updatedUser, null, 2));
    }
    
    // Cancel the subscription in Stripe
    console.log(`Canceling subscription: ${user.subscriptionId || subscriptionId}`);
    const result = await subscriptionService.cancelSubscription(user.subscriptionId || subscriptionId);
    console.log('Cancellation result:', JSON.stringify(result, null, 2));
    
    // Update the user's subscription status in our database
    const updated = await authService.updateSubscription(userId, 'canceled', null);
    console.log('Database update result:', updated);
    
    // Send cancellation notification email
    console.log('Sending cancellation email notification');
    const emailResult = await emailService.sendSubscriptionCancellationEmail({
      user,
      subscriptionId: user.subscriptionId || subscriptionId,
      stripeResult: result
    });
    
    console.log('Email notification result:', emailResult);
    
    console.log('Subscription cancellation test completed successfully');
  } catch (error) {
    console.error('Error during subscription cancellation test:', error);
  }
}

// Run the test
testCancelSubscription().catch(console.error);
