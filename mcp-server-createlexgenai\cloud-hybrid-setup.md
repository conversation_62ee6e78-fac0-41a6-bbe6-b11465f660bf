# Hybrid Cloud Setup Guide

## Architecture Overview
```
Claude Des<PERSON> (Cloud) → MCP Server (Cloud) → Unreal Engine (Local)
```

## Setup Steps

### 1. Deploy MCP Server to Cloud
```bash
# Deploy to DigitalOcean App Platform
doctl apps create --spec cloud-deploy.yml

# Or deploy to Railway
railway login
railway new unreal-mcp-server
railway up
```

### 2. Expose Local Unreal Engine
```bash
# Option A: Use ngrok to expose local Unreal Engine
ngrok tcp 9877

# Option B: Use cloudflared tunnel
cloudflared tunnel --url tcp://localhost:9877
```

### 3. Update MCP Server Environment
```bash
# Set the public URL of your exposed Unreal Engine
export UNREAL_HOST="your-ngrok-url.ngrok.io"
export UNREAL_PORT="12345"  # The port ngrok assigns
```

### 4. Configure Claude Desktop for Cloud MCP
```json
{
  "mcpServers": {
    "unreal-ai-support": {
      "command": "curl",
      "args": [
        "-X", "POST",
        "https://your-mcp-server.ondigitalocean.app/mcp",
        "-H", "Content-Type: application/json",
        "-d", "@-"
      ]
    }
  }
}
```

## Security Considerations

### 1. Authentication
- Add API keys for MCP server access
- Use JWT tokens for session management
- Implement rate limiting

### 2. Network Security
- Use HTTPS/TLS for all communications
- Whitelist IP addresses
- Use VPN for Unreal Engine access

### 3. Firewall Rules
```bash
# Allow only MCP server to access Unreal Engine
ufw allow from YOUR_MCP_SERVER_IP to any port 9877
ufw deny 9877
```

## Cost Estimation

### Cloud Hosting (Monthly)
- **DigitalOcean App Platform**: $5-12/month
- **Railway**: $5-10/month  
- **Google Cloud Run**: $0-5/month (pay per use)
- **AWS Fargate**: $10-20/month

### Tunnel Services
- **ngrok**: $8/month (Pro plan)
- **Cloudflare Tunnel**: Free
- **Tailscale**: Free (up to 3 users)

## Benefits of Cloud Deployment

✅ **Global Access**: Use from anywhere
✅ **Scalability**: Handle multiple users
✅ **Reliability**: 99.9% uptime
✅ **Security**: Professional-grade infrastructure
✅ **Collaboration**: Share with team members

## Limitations

❌ **Latency**: Network delay for Unreal Engine commands
❌ **Complexity**: More moving parts to manage
❌ **Cost**: Monthly hosting fees
❌ **Dependencies**: Requires stable internet connection 