# UnrealGenAI Subscription-Protected MCP Server Configuration

# Subscription & Licensing
SUBSCRIPTION_ENDPOINT=https://your-license-server.com/api/verify
UNREALGENAI_API_KEY=your-api-key-here
SUBSCRIPTION_CHECK_INTERVAL=3600

# MCP Server Configuration
MCP_HOST=0.0.0.0
MCP_PORT=8000

# Database Configuration (if using self-hosted license server)
POSTGRES_USER=unrealgenai_admin
POSTGRES_PASSWORD=secure-db-password-here
JWT_SECRET=your-jwt-secret-key-here

# Security Configuration
ALLOWED_ORIGINS=https://yourdomain.com,http://localhost:3000
CORS_ENABLED=true
RATE_LIMIT_ENABLED=true
MAX_REQUESTS_PER_MINUTE=60

# Logging & Monitoring
LOG_LEVEL=INFO
ENABLE_METRICS=true
HEALTH_CHECK_INTERVAL=30

# Optional: Remote hosting configuration
REMOTE_HOST=your-server.com
REMOTE_PORT=8000
SSL_CERT_PATH=/app/ssl/cert.pem
SSL_KEY_PATH=/app/ssl/key.pem

# Optional: Cloud provider settings
CLOUD_PROVIDER=digitalocean
CLOUD_REGION=nyc3
INSTANCE_SIZE=s-2vcpu-4gb

# Feature flags
ENABLE_OFFLINE_MODE=true
ENABLE_TELEMETRY=false
ENABLE_AUTO_UPDATES=true 