#!/usr/bin/env python3
"""
Local MCP stdio bridge to HTTP server
"""
import json
import sys
import requests

def main():
    for line in sys.stdin:
        try:
            request = json.loads(line.strip())
            
            # Forward to HTTP server
            response = requests.post(
                "http://localhost:8000",
                json=request,
                timeout=30
            )
            
            if response.status_code == 200:
                print(response.text, flush=True)
            else:
                error_response = {
                    "jsonrpc": "2.0",
                    "id": request.get("id", 1),
                    "error": {
                        "code": -32603,
                        "message": f"HTTP {response.status_code}: {response.text}"
                    }
                }
                print(json.dumps(error_response), flush=True)
                
        except Exception as e:
            error_response = {
                "jsonrpc": "2.0",
                "id": 1,
                "error": {
                    "code": -32603,
                    "message": f"Error: {str(e)}"
                }
            }
            print(json.dumps(error_response), flush=True)

if __name__ == "__main__":
    main() 