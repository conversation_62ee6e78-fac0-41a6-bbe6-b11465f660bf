'use client';

import { useEffect, useRef, useState } from 'react';
import { TypeAnimation } from 'react-type-animation';

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: number;
  progress?: string;
  scriptSteps?: string[];
  isCompleted?: boolean;
}

interface ChatMessagesProps {
  messages: Message[];
}

export default function ChatMessages({ messages }: ChatMessagesProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [typingMessage, setTypingMessage] = useState<Message | null>(null);

  // Debug messages state
  useEffect(() => {
    console.log('DEBUG: ChatMessages rendered with messages:', messages);
  }, []);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    console.log('DEBUG: Messages updated, new count:', messages.length);

    // If the last message is from AI, set it as the typing message
    if (messages.length > 0 && messages[messages.length - 1].sender === 'ai') {
      const lastMessage = messages[messages.length - 1];
      console.log('DEBUG: Setting typing message:', lastMessage);
      setTypingMessage(lastMessage);
    } else {
      setTypingMessage(null);
    }
  }, [messages]);

  // Format timestamp
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  console.log('DEBUG: Current typing message:', typingMessage);

  return (
    <div className="chat-messages h-full overflow-y-auto px-4 py-2 rounded-lg">
      {messages.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-full text-center text-gray-400 py-10">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mb-4 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
          </svg>
          <p className="text-lg font-medium mb-2">No messages yet</p>
          <p>Start typing to interact with the Unreal Engine assistant</p>
        </div>
      ) : (
        <>
          {messages.map((message, index) => {
            console.log(`DEBUG: Rendering message ${index}, id: ${message.id}, sender: ${message.sender}`);
            return (
              <div
                key={message.id}
                className={`mb-6 ${message.sender === 'user' ? 'ml-8' : 'mr-8'}`}
              >
                <div className="flex items-start">
                  <div className={`flex-shrink-0 h-8 w-8 rounded-full flex items-center justify-center mr-3 ${message.sender === 'user' ? 'bg-blue-500' : 'bg-gradient-to-br from-purple-500 to-blue-500'}`}>
                    {message.sender === 'user' ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z" />
                        <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z" />
                      </svg>
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="flex justify-between items-center mb-1">
                      <span className="font-medium text-sm">
                        {message.sender === 'user' ? 'You' : 'CreateLex'}
                      </span>
                      <span className="text-xs text-gray-500">
                        {formatTime(message.timestamp)}
                      </span>
                    </div>
                    <div className={`p-3 rounded-lg whitespace-pre-wrap ${message.sender === 'user' ? 'bg-gray-800 text-white' : 'bg-gray-800/50 border border-gray-700 text-white'} ${message.id.startsWith('loading-') ? 'border-blue-500 animate-pulse' : ''} ${message.id.startsWith('progress-') ? 'border-green-500/30' : ''}`}>
                      {message.id.startsWith('loading-') || message.id.startsWith('progress-') ? (
                        <div className="flex items-start">
                          <div className="flex space-x-1 mr-3 mt-1">
                            {message.id.startsWith('loading-') ? (
                              <>
                                <div className="h-2 w-2 bg-blue-500 rounded-full animate-bounce"></div>
                                <div className="h-2 w-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                <div className="h-2 w-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.4s' }}></div>
                              </>
                            ) : (
                              <div className="h-4 w-4 text-green-500">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                </svg>
                              </div>
                            )}
                          </div>
                          <div className="flex-1">
                            <div className={`font-medium mb-1 ${message.id.startsWith('progress-') ? 'text-green-400' : 'text-blue-400'}`}>
                              {message.id.startsWith('loading-script') || message.id.startsWith('progress-script') ?
                                (message.id.startsWith('progress-') ? 'Created in Unreal Engine' : 'Creating in Unreal Engine') :
                                (message.id.startsWith('progress-') ? 'Processed' : 'Processing')}
                            </div>

                            {/* Show script steps if available */}
                            {message.scriptSteps ? (
                              <div className="mb-2">
                                <div className="font-medium text-blue-300 mb-1">Python Script Execution:</div>
                                <div className="text-sm text-gray-300 space-y-1 border-l-2 border-blue-700 pl-3 ml-1">
                                  {message.scriptSteps.map((step, idx) => (
                                    <div key={idx} className="flex items-center">
                                      <div className="w-2 h-2 rounded-full mr-2 bg-blue-400"></div>
                                      <span>{step}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            ) : (
                              <div className="mb-2">{message.text}</div>
                            )}

                            {/* Progress steps - these will be shown based on the processing stage */}
                            <div className={`text-xs space-y-1 border-l-2 pl-2 ml-1 mt-3 ${message.id.startsWith('progress-') ? 'text-gray-300 border-green-700' : 'text-gray-400 border-gray-700'}`}>
                              <div className="flex items-center">
                                <div className={`w-2 h-2 rounded-full mr-2 ${message.progress === 'analyzing' || message.progress === 'generating' || message.progress === 'executing' || message.progress === 'finalizing' || message.progress === 'completed' ? (message.id.startsWith('progress-') ? 'bg-green-400' : 'bg-blue-400') : 'bg-gray-600'}`}></div>
                                <span>Analyzing request</span>
                              </div>

                              <div className="flex items-center">
                                <div className={`w-2 h-2 rounded-full mr-2 ${message.progress === 'generating' || message.progress === 'executing' || message.progress === 'finalizing' || message.progress === 'completed' ? (message.id.startsWith('progress-') ? 'bg-green-400' : 'bg-blue-400') : 'bg-gray-600'}`}></div>
                                <span>{message.id.startsWith('loading-script') || message.id.startsWith('progress-script') ? 'Generating Python script' : 'Preparing response'}</span>
                              </div>

                              <div className="flex items-center">
                                <div className={`w-2 h-2 rounded-full mr-2 ${message.progress === 'executing' || message.progress === 'finalizing' || message.progress === 'completed' ? (message.id.startsWith('progress-') ? 'bg-green-400' : 'bg-blue-400') : 'bg-gray-600'}`}></div>
                                <span>{message.id.startsWith('loading-script') || message.id.startsWith('progress-script') ? 'Executing in Unreal Engine' : 'Finalizing response'}</span>
                              </div>

                              {message.id.startsWith('progress-') && (
                                <div className="flex items-center">
                                  <div className="w-2 h-2 rounded-full mr-2 bg-green-400"></div>
                                  <span>Completed</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ) : message.sender === 'ai' && typingMessage && typingMessage.id === message.id ? (
                        <TypeAnimation
                          sequence={[message.text]}
                          wrapper="span"
                          speed={75}
                          cursor={false}
                        />
                      ) : typeof message.text === 'string' && message.text.startsWith('{') && message.text.endsWith('}') ? (
                        <pre className="bg-gray-900 p-2 rounded overflow-auto text-gray-300 text-sm">
                          {message.text}
                        </pre>
                      ) : (
                        message.text
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </>
      )}
      <div ref={messagesEndRef} />
    </div>
  );
}