# Token Purchase System - Developer Guide

This guide provides practical information for developers working on the token purchase system in the CreateLex AI platform.

## Local Development Setup

### Prerequisites

- Node.js 16+
- npm or yarn
- Stripe CLI (for webhook testing)
- ngrok (optional, for webhook testing)

### Environment Variables

Add these variables to your `.env` file in the backend directory:

```
# Stripe API Keys (Use test keys for development)
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Development helpers
BYPASS_WEBHOOK_SIGNATURE=true  # Only in development
```

### Setting Up Stripe CLI for Local Testing

1. Install the Stripe CLI: https://stripe.com/docs/stripe-cli

2. Login to your Stripe account:
   ```
   stripe login
   ```

3. Forward webhooks to your local server:
   ```
   stripe listen --forward-to http://localhost:5001/api/webhook/stripe
   ```

4. Note the webhook signing secret provided by the CLI and add it to your `.env` file.

## Testing Token Purchases

### Method 1: Using Stripe CLI

1. Start your frontend and backend servers:
   ```
   # Terminal 1
   cd frontend
   npm run dev

   # Terminal 2
   cd backend
   npm run dev

   # Terminal 3
   stripe listen --forward-to http://localhost:5001/api/webhook/stripe
   ```

2. Make a test purchase through the UI.

3. Verify the webhook is received in the Stripe CLI terminal.

4. Check that the token balance is updated in the UI and database.

### Method 2: Using Simulate Webhook

If you can't use the Stripe CLI, you can simulate webhooks:

1. Make a test purchase through the UI.

2. After completing the purchase, the `TokenPurchaseProcessor` component will automatically process the purchase.

3. Alternatively, you can manually simulate a webhook:
   ```
   curl -X POST http://localhost:5001/api/tokens/purchase/simulate-webhook \
     -H "Content-Type: application/json" \
     -H "x-user-id: YOUR_USER_ID" \
     -d '{"userId": "YOUR_USER_ID", "packageId": "small"}'
   ```

4. Check that the token balance is updated in the UI and database.

## Common Development Tasks

### Adding a New Token Package

1. Update the package definitions in `frontend/components/PurchaseTokensModal.js`:
   ```javascript
   const tokenPackages = [
     { id: 'small', name: 'Small', tokens: 100000, price: 5 },
     { id: 'medium', name: 'Medium', tokens: 500000, price: 20 },
     { id: 'large', name: 'Large', tokens: 1000000, price: 35 },
     // Add your new package here
   ];
   ```

2. Update the backend token amount mapping in `backend/src/routes/tokens/purchase/simulate-webhook.js`:
   ```javascript
   let tokenAmount = 100000; // Default to small package
   if (packageId === 'medium') {
     tokenAmount = 500000;
   } else if (packageId === 'large') {
     tokenAmount = 1000000;
   } else if (packageId === 'your-new-package') {
     tokenAmount = YOUR_TOKEN_AMOUNT;
   }
   ```

3. Update the Stripe product and price in the Stripe dashboard.

4. Update the price mapping in `backend/src/routes/tokens/purchase.js`.

### Modifying the Token Purchase Flow

The token purchase flow involves several components:

1. **Frontend Initiation**:
   - `PurchaseTokensModal.js`: Initiates the purchase
   - `api/tokens/purchase/route.ts`: Creates the checkout session

2. **Payment Processing**:
   - Stripe Checkout: Handles payment collection
   - `api/webhook/stripe.js`: Processes the webhook
   - `tokenPurchaseService.js`: Updates the token balance

3. **Post-Purchase Handling**:
   - `TokenPurchaseProcessor.js`: Handles redirect and manual processing
   - `TokenUsage.js`: Updates the UI with new balance

When modifying the flow, ensure all these components are updated consistently.

## Debugging

### Webhook Issues

1. Check the webhook logs in the Stripe dashboard.
2. Verify the webhook signature is correct.
3. Check the server logs for webhook receipt and processing.
4. Use the Stripe CLI to replay webhooks:
   ```
   stripe events resend evt_123
   ```

### Token Balance Issues

1. Check the database directly:
   ```sql
   SELECT * FROM token_balance WHERE user_id = 'user-id';
   SELECT * FROM token_transactions WHERE user_id = 'user-id' ORDER BY created_at DESC;
   ```

2. Verify the transaction was recorded:
   ```sql
   SELECT * FROM token_transactions WHERE transaction_id = 'pi_123';
   ```

3. Force a token balance refresh:
   ```
   curl -X GET "http://localhost:5001/api/tokens/balance?userId=user-id&forceRefresh=true"
   ```

## Production Deployment Checklist

Before deploying to production:

1. **Environment Variables**:
   - Update all Stripe keys to production keys
   - Set `BYPASS_WEBHOOK_SIGNATURE=false` or remove it
   - Verify the webhook secret is correct

2. **Webhook Configuration**:
   - Configure the production webhook endpoint in the Stripe dashboard
   - Enable the necessary events
   - Test the webhook delivery

3. **Testing**:
   - Perform end-to-end testing in a staging environment
   - Verify webhook handling works correctly
   - Test error recovery mechanisms

4. **Monitoring**:
   - Set up logging for webhook events
   - Configure alerts for webhook failures
   - Monitor token balance updates

## Troubleshooting Production Issues

### Token Purchase Not Processed

1. Check the Stripe dashboard for the payment status.
2. Verify the webhook was delivered successfully.
3. Check the server logs for webhook processing errors.
4. Verify the transaction exists in the database.

### Manual Recovery

If a purchase was successful but tokens weren't added:

1. Verify the payment in the Stripe dashboard.
2. Check if the transaction exists in the database.
3. If not, manually add the tokens:
   ```sql
   -- First, check the current balance
   SELECT * FROM token_balance WHERE user_id = 'user-id';
   
   -- Then, add the tokens (adjust values as needed)
   UPDATE token_balance 
   SET balance = balance + 100000, updated_at = NOW() 
   WHERE user_id = 'user-id';
   
   -- Record the transaction
   INSERT INTO token_transactions (
     user_id, amount, transaction_id, created_at
   ) VALUES (
     'user-id', 100000, 'manual-recovery-123', NOW()
   );
   ```

## Architecture Diagram

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  Dashboard  │────▶│  Purchase   │────▶│   Stripe    │
│    Page     │     │   Modal     │     │  Checkout   │
└─────────────┘     └─────────────┘     └─────────────┘
                                               │
                                               ▼
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  Token      │◀────│  Webhook    │◀────│   Stripe    │
│  Balance    │     │  Handler    │     │  Webhook    │
└─────────────┘     └─────────────┘     └─────────────┘
       ▲                                       │
       │                                       │
       │           ┌─────────────┐             │
       └───────────│  Token      │◀────────────┘
                   │  Purchase   │
                   │  Processor  │
                   └─────────────┘
```

## Additional Resources

- [Stripe Webhook Documentation](https://stripe.com/docs/webhooks)
- [Stripe Checkout Documentation](https://stripe.com/docs/payments/checkout)
- [Main Token Purchase System Documentation](./token-purchase-system.md)
