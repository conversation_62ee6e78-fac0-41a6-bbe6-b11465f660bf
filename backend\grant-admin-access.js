require('dotenv').config();
const supabase = require('./src/services/supabaseClient');

async function grantAdminAccess() {
  console.log('🔧 Granting admin access to your user account...');
  
  const YOUR_USER_ID = '5af3ff0f-0ccb-41a2-8dba-3e460aa9bd72';
  
  try {
    // First, check if the user exists
    console.log('1. Checking if user exists...');
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, email, name, is_admin')
      .eq('id', YOUR_USER_ID)
      .single();
      
    if (userError) {
      console.error('❌ Error finding user:', userError);
      return;
    }
    
    if (!user) {
      console.error('❌ User not found with ID:', YOUR_USER_ID);
      return;
    }
    
    console.log('✅ Found user:', {
      id: user.id,
      email: user.email,
      name: user.name,
      is_admin: user.is_admin
    });
    
    // Check if is_admin column exists
    console.log('2. Checking if is_admin column exists...');
    
    // Try to update the user with is_admin flag
    console.log('3. Granting admin privileges...');
    const { data: updateData, error: updateError } = await supabase
      .from('users')
      .update({ is_admin: true })
      .eq('id', YOUR_USER_ID)
      .select();
      
    if (updateError) {
      if (updateError.message && updateError.message.includes('column "is_admin" does not exist')) {
        console.log('⚠️  is_admin column does not exist, creating it...');
        
        // Try to add the column using raw SQL
        const { error: alterError } = await supabase.rpc('exec_sql', {
          sql: 'ALTER TABLE users ADD COLUMN IF NOT EXISTS is_admin BOOLEAN NOT NULL DEFAULT FALSE'
        });
        
        if (alterError) {
          console.error('❌ Error adding is_admin column:', alterError);
          console.log('📝 Manual SQL needed:');
          console.log('   ALTER TABLE users ADD COLUMN IF NOT EXISTS is_admin BOOLEAN NOT NULL DEFAULT FALSE;');
          console.log(`   UPDATE users SET is_admin = TRUE WHERE id = '${YOUR_USER_ID}';`);
          return;
        }
        
        console.log('✅ Added is_admin column');
        
        // Try update again
        const { data: retryData, error: retryError } = await supabase
          .from('users')
          .update({ is_admin: true })
          .eq('id', YOUR_USER_ID)
          .select();
          
        if (retryError) {
          console.error('❌ Error setting admin privileges after adding column:', retryError);
          return;
        }
        
        console.log('✅ Successfully granted admin privileges!');
        console.log('Updated user:', retryData);
      } else {
        console.error('❌ Error setting admin privileges:', updateError);
        return;
      }
    } else {
      console.log('✅ Successfully granted admin privileges!');
      console.log('Updated user:', updateData);
    }
    
    // Verify the update
    console.log('4. Verifying admin access...');
    const { data: verifyUser, error: verifyError } = await supabase
      .from('users')
      .select('id, email, name, is_admin')
      .eq('id', YOUR_USER_ID)
      .single();
      
    if (verifyError) {
      console.error('❌ Error verifying update:', verifyError);
      return;
    }
    
    console.log('✅ Verification complete:', {
      id: verifyUser.id,
      email: verifyUser.email,
      name: verifyUser.name,
      is_admin: verifyUser.is_admin
    });
    
    if (verifyUser.is_admin) {
      console.log('🎉 SUCCESS! You now have admin access!');
      console.log('🔗 You can now access: https://createlex.com/admin/api-keys');
      console.log('🔄 Please refresh your browser and try again.');
    } else {
      console.log('⚠️  Admin flag not set. Manual intervention may be required.');
    }
    
  } catch (error) {
    console.error('💥 Unexpected error:', error);
    console.log('📝 Manual SQL commands to run in Supabase dashboard:');
    console.log('   1. Go to Supabase Dashboard > SQL Editor');
    console.log('   2. Run these commands:');
    console.log('      ALTER TABLE users ADD COLUMN IF NOT EXISTS is_admin BOOLEAN NOT NULL DEFAULT FALSE;');
    console.log(`      UPDATE users SET is_admin = TRUE WHERE id = '${YOUR_USER_ID}';`);
  }
}

// Alternative: Grant admin by email
async function grantAdminByEmail() {
  console.log('🔧 Alternative: Granting admin access by email...');
  
  try {
    // Find user by email pattern (since we don't know the exact email)
    const { data: users, error: findError } = await supabase
      .from('users')
      .select('id, email, name, is_admin')
      .limit(10);
      
    if (findError) {
      console.error('❌ Error finding users:', findError);
      return;
    }
    
    console.log('📋 Found users:');
    users.forEach((user, index) => {
      console.log(`   ${index + 1}. ${user.email} (${user.id}) - Admin: ${user.is_admin}`);
    });
    
    // Try to update all users to find the right one
    console.log('🔍 Looking for your user account...');
    
    // Update the specific user ID we know
    const YOUR_USER_ID = '5af3ff0f-0ccb-41a2-8dba-3e460aa9bd72';
    const { error: updateError } = await supabase
      .from('users')
      .update({ is_admin: true })
      .eq('id', YOUR_USER_ID);
      
    if (updateError) {
      console.error('❌ Error updating user:', updateError);
    } else {
      console.log('✅ Successfully updated user with admin privileges!');
    }
    
  } catch (error) {
    console.error('💥 Error in alternative method:', error);
  }
}

// Run the script
console.log('🚀 Starting admin access grant process...');
console.log('👤 Target User ID: 5af3ff0f-0ccb-41a2-8dba-3e460aa9bd72');
console.log('');

grantAdminAccess()
  .then(() => {
    console.log('');
    console.log('🏁 Admin access grant process completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('💥 Fatal error:', error);
    console.log('');
    console.log('🔄 Trying alternative method...');
    
    grantAdminByEmail()
      .then(() => {
        console.log('🏁 Alternative method completed');
        process.exit(0);
      })
      .catch(altError => {
        console.error('💥 Alternative method failed:', altError);
        process.exit(1);
      });
  });
