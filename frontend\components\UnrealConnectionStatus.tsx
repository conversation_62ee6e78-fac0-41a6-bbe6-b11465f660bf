'use client';

import { useState, useEffect } from 'react';
import { Socket } from 'socket.io-client';

interface UnrealConnectionStatusProps {
  socket: Socket | null;
  isConnected: boolean;
  onTestConnection: () => void;
  isTestingConnection: boolean;
}

export default function UnrealConnectionStatus({
  socket,
  isConnected,
  onTestConnection,
  isTestingConnection
}: UnrealConnectionStatusProps) {
  const [unrealStatus, setUnrealStatus] = useState<'unknown' | 'connected' | 'disconnected'>('unknown');
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  useEffect(() => {
    if (socket) {
      // Listen for Unreal Engine connection status updates
      socket.on('unrealConnectionStatus', (data: { connected: boolean }) => {
        console.log('Received Unreal connection status:', data);
        setUnrealStatus(data.connected ? 'connected' : 'disconnected');
        setLastChecked(new Date());
      });

      return () => {
        socket.off('unrealConnectionStatus');
      };
    }
  }, [socket]);

  // Reset status to unknown if socket disconnects
  useEffect(() => {
    if (!isConnected) {
      setUnrealStatus('unknown');
    }
  }, [isConnected]);

  // Update status when testing connection
  useEffect(() => {
    if (isTestingConnection) {
      setUnrealStatus('unknown');
    }
  }, [isTestingConnection]);

  return (
    <div className="flex items-center space-x-2 text-sm">
      <div className="flex items-center">
        <div 
          className={`w-3 h-3 rounded-full mr-2 ${
            unrealStatus === 'connected' 
              ? 'bg-green-500' 
              : unrealStatus === 'disconnected' 
                ? 'bg-red-500' 
                : 'bg-yellow-500'
          }`}
        />
        <span>
          Unreal Engine: {
            unrealStatus === 'connected' 
              ? 'Connected' 
              : unrealStatus === 'disconnected' 
                ? 'Disconnected' 
                : 'Unknown'
          }
        </span>
      </div>
      
      {lastChecked && (
        <span className="text-xs text-gray-500">
          Last checked: {lastChecked.toLocaleTimeString()}
        </span>
      )}
      
      <button
        onClick={onTestConnection}
        disabled={!isConnected || isTestingConnection}
        className={`px-2 py-1 rounded text-xs ${
          !isConnected || isTestingConnection
            ? 'bg-gray-700 text-gray-400 cursor-not-allowed'
            : 'bg-blue-600 hover:bg-blue-700 text-white'
        }`}
      >
        {isTestingConnection ? 'Testing...' : 'Test Connection'}
      </button>
    </div>
  );
}
