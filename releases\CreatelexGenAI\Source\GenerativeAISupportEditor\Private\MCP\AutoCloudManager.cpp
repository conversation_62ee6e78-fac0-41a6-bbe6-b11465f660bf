#include "MCP/AutoCloudManager.h"
#include "Engine/Engine.h"
#include "HAL/PlatformProcess.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "Interfaces/IPluginManager.h"
#include "Async/Async.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "Misc/FileHelper.h"

DEFINE_LOG_CATEGORY(LogAutoCloudManager);

UAutoCloudManager::UAutoCloudManager()
{
    bIsCloudConnected = false;
    bAutoConnectEnabled = true;
    CloudConnectorProcess = FProcHandle();
}

void UAutoCloudManager::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    
    UE_LOG(LogAutoCloudManager, Log, TEXT("AutoCloudManager initialized"));
    
    // Start auto-connection if enabled
    if (bAutoConnectEnabled)
    {
        // Use background thread with delay to avoid blocking
        AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this]()
        {
            // Delay the connection attempt to ensure everything is loaded
            FPlatformProcess::Sleep(2.0f);
            StartAutoConnection();
        });
    }
}

void UAutoCloudManager::Deinitialize()
{
    StopCloudConnection();
    Super::Deinitialize();
}

void UAutoCloudManager::StartAutoConnection()
{
    if (bIsCloudConnected)
    {
        UE_LOG(LogAutoCloudManager, Warning, TEXT("Cloud connection already active"));
        return;
    }
    
    UE_LOG(LogAutoCloudManager, Log, TEXT("Starting automatic cloud connection..."));
    
    // Run the Python auto-connector asynchronously
    AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this]()
    {
        ExecuteAutoConnector();
    });
}

void UAutoCloudManager::ExecuteAutoConnector()
{
    // Get plugin directory
    TSharedPtr<IPlugin> Plugin = IPluginManager::Get().FindPlugin("GenerativeAISupport");
    if (!Plugin.IsValid())
    {
        UE_LOG(LogAutoCloudManager, Error, TEXT("Could not find GenerativeAISupport plugin"));
        return;
    }
    
    FString PluginDir = Plugin->GetBaseDir();
    FString PythonScriptPath = FPaths::Combine(PluginDir, TEXT("server"), TEXT("auto_cloud_connector.py"));
    
    // Check if Python script exists
    if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*PythonScriptPath))
    {
        UE_LOG(LogAutoCloudManager, Error, TEXT("Auto cloud connector script not found: %s"), *PythonScriptPath);
        return;
    }
    
    // Prepare command
    FString PythonExecutable = TEXT("python");
    FString Arguments = FString::Printf(TEXT("\"%s\""), *PythonScriptPath);
    
    UE_LOG(LogAutoCloudManager, Log, TEXT("Executing: %s %s"), *PythonExecutable, *Arguments);
    
    // Start the process
    CloudConnectorProcess = FPlatformProcess::CreateProc(
        *PythonExecutable,
        *Arguments,
        false,  // bLaunchDetached
        true,   // bLaunchHidden
        true,   // bLaunchReallyHidden
        nullptr, // OutProcessID
        0,      // PriorityModifier
        *FPaths::GetPath(PythonScriptPath), // OptionalWorkingDirectory
        nullptr // PipeWriteChild
    );
    
    if (CloudConnectorProcess.IsValid())
    {
        UE_LOG(LogAutoCloudManager, Log, TEXT("Auto cloud connector process started"));
        
        // Monitor the process
        AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this]()
        {
            MonitorCloudConnection();
        });
    }
    else
    {
        UE_LOG(LogAutoCloudManager, Error, TEXT("Failed to start auto cloud connector process"));
    }
}

void UAutoCloudManager::MonitorCloudConnection()
{
    // Wait for the process to complete or establish connection
    int32 ReturnCode = 0;
    
    while (CloudConnectorProcess.IsValid() && FPlatformProcess::IsProcRunning(CloudConnectorProcess))
    {
        FPlatformProcess::Sleep(1.0f);
        
        // Check if connection is established by reading config file
        if (CheckCloudConnectionStatus())
        {
            break;
        }
    }
    
    // Process completed, check final status
    if (CloudConnectorProcess.IsValid())
    {
        FPlatformProcess::GetProcReturnCode(CloudConnectorProcess, &ReturnCode);
        FPlatformProcess::CloseProc(CloudConnectorProcess);
        CloudConnectorProcess = FProcHandle();
    }
    
    // Update connection status on game thread
    AsyncTask(ENamedThreads::GameThread, [this, ReturnCode]()
    {
        bool bSuccess = CheckCloudConnectionStatus();
        
        if (bSuccess)
        {
            bIsCloudConnected = true;
            UE_LOG(LogAutoCloudManager, Log, TEXT("✅ Cloud connection established successfully!"));
            
            // Broadcast success event
            OnCloudConnectionEstablished.Broadcast(true, TEXT("Cloud connection established"));
        }
        else
        {
            UE_LOG(LogAutoCloudManager, Warning, TEXT("❌ Cloud connection failed (Return Code: %d)"), ReturnCode);
            OnCloudConnectionEstablished.Broadcast(false, TEXT("Failed to establish cloud connection"));
        }
    });
}

bool UAutoCloudManager::CheckCloudConnectionStatus()
{
    // Read the cloud config file to check status
    FString ConfigPath = FPaths::Combine(FPlatformMisc::GetEnvironmentVariable(TEXT("USERPROFILE")), TEXT(".unrealgenai"), TEXT("cloud_config.json"));
    
    if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*ConfigPath))
    {
        return false;
    }
    
    FString ConfigContent;
    if (!FFileHelper::LoadFileToString(ConfigContent, *ConfigPath))
    {
        return false;
    }
    
    // Parse JSON
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(ConfigContent);
    
    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return false;
    }
    
    // Check if tunnel_url exists and is valid
    FString TunnelUrl;
    if (JsonObject->TryGetStringField(TEXT("tunnel_url"), TunnelUrl) && !TunnelUrl.IsEmpty())
    {
        // Store connection info
        CloudTunnelUrl = TunnelUrl;
        JsonObject->TryGetStringField(TEXT("mcp_cloud_url"), CloudMcpUrl);
        JsonObject->TryGetStringField(TEXT("instance_id"), CloudInstanceId);
        
        return true;
    }
    
    return false;
}

void UAutoCloudManager::StopCloudConnection()
{
    if (CloudConnectorProcess.IsValid())
    {
        UE_LOG(LogAutoCloudManager, Log, TEXT("Stopping cloud connection..."));
        
        FPlatformProcess::TerminateProc(CloudConnectorProcess);
        FPlatformProcess::CloseProc(CloudConnectorProcess);
        CloudConnectorProcess = FProcHandle();
    }
    
    bIsCloudConnected = false;
    CloudTunnelUrl.Empty();
    CloudMcpUrl.Empty();
    CloudInstanceId.Empty();
}

FCloudConnectionInfo UAutoCloudManager::GetCloudConnectionInfo() const
{
    FCloudConnectionInfo Info;
    Info.bIsConnected = bIsCloudConnected;
    Info.TunnelUrl = CloudTunnelUrl;
    Info.McpUrl = CloudMcpUrl;
    Info.InstanceId = CloudInstanceId;
    
    return Info;
}

void UAutoCloudManager::SetAutoConnectEnabled(bool bEnabled)
{
    bAutoConnectEnabled = bEnabled;
    
    if (bEnabled && !bIsCloudConnected)
    {
        StartAutoConnection();
    }
    else if (!bEnabled && bIsCloudConnected)
    {
        StopCloudConnection();
    }
} 