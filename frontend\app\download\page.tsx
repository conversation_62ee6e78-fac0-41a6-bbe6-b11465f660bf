'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../contexts/AuthContext';

export default function DownloadPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading, hasActiveSubscription, refreshSubscriptionStatus } = useAuth();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  // Current version information
  const currentVersion = "1.0.1";
  const releaseDate = "December 24, 2025"; // Updated to current date
  const minUnrealVersion = "4.26";
  const recommendedUnrealVersion = "5.1+";

  // Download information
  const zipDownloadUrl = "https://github.com/AlexKissiJr/AiWebplatform/releases/download/v1.0.1/CreatelexGenAI.v1.0.1.zip";

  // Epic Games Store URL (replace with actual URL when available)
  const epicStoreUrl = "https://www.unrealengine.com/marketplace/en-US/product/createlexgenai";

  useEffect(() => {
    const checkAccess = async () => {
      console.log('Download page: Checking access', {
        isAuthenticated,
        isLoading,
        hasActiveSubscription
      });

      // Redirect to login if not authenticated
      if (!isLoading && !isAuthenticated) {
        console.log('Download page: User not authenticated, redirecting to login');
        router.push('/login');
        return;
      }

      // Wait for auth to finish loading
      if (isLoading) {
        console.log('Download page: Still loading auth data, waiting...');
        return;
      }

      // Check if user has an active subscription
      if (!hasActiveSubscription) {
        try {
          console.log('Download page: Refreshing subscription status');
          const subscriptionData = await refreshSubscriptionStatus();
          console.log('Download page: Refreshed subscription status:', subscriptionData);

          if (!subscriptionData.hasActiveSubscription) {
            console.log('Download page: User does not have an active subscription, redirecting to subscription page');
            router.push('/subscription');
            return;
          }
        } catch (error) {
          console.error('Download page: Error refreshing subscription status:', error);
          console.log('Download page: Redirecting to subscription page due to error');
          router.push('/subscription');
          return;
        }
      }

      // User is authenticated and has an active subscription
      console.log('Download page: User is authorized to access downloads');
      setIsAuthorized(true);
      setIsCheckingAuth(false);
    };

    checkAccess();
  }, [isAuthenticated, isLoading, hasActiveSubscription, router, refreshSubscriptionStatus]);

  // Show loading state until fully authorized
  if (isCheckingAuth || !isAuthorized) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
        <p className="text-gray-600">Verifying subscription access...</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-extrabold text-gray-900 sm:text-5xl sm:tracking-tight lg:text-6xl">
            Download CreatelexGenAI
          </h1>
          <p className="mt-5 max-w-xl mx-auto text-xl text-gray-500">
            Professional AI integration for Unreal Engine with advanced MCP support and 25+ AI models
          </p>
        </div>

        {/* Version Info */}
        <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-10">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Current Version Information
            </h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Details about the latest release
            </p>
          </div>
          <div className="border-t border-gray-200">
            <dl>
              <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Version</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{currentVersion}</dd>
              </div>
              <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Release Date</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{releaseDate}</dd>
              </div>
              <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Minimum Unreal Engine Version</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{minUnrealVersion}</dd>
              </div>
              <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Recommended Unreal Engine Version</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{recommendedUnrealVersion}</dd>
              </div>
            </dl>
          </div>
        </div>

        {/* Download Options */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Epic Games Store */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-blue-500 rounded-md p-3">
                  <svg className="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dt className="text-lg font-medium text-gray-900">
                    Epic Games Store
                  </dt>
                  <dd className="mt-2 text-base text-gray-500">
                    Download and install directly through the Epic Games Marketplace (recommended)
                  </dd>
                </div>
              </div>
              <div className="mt-8">
                <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-blue-700">
                        <strong>Coming Soon:</strong> The plugin will be available on the Epic Games Marketplace soon. Please check back later.
                      </p>
                    </div>
                  </div>
                </div>
                <button
                  disabled
                  className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-gray-400 cursor-not-allowed"
                >
                  View on Epic Marketplace
                </button>
              </div>
            </div>
          </div>

          {/* Direct Download */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-green-500 rounded-md p-3">
                  <svg className="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dt className="text-lg font-medium text-gray-900">
                    Direct Download
                  </dt>
                  <dd className="mt-2 text-base text-gray-500">
                    Download the ZIP file directly
                  </dd>
                </div>
              </div>
              <div className="mt-8">
                <a
                  href={zipDownloadUrl}
                  className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                  Download ZIP ({currentVersion})
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Installation Instructions */}
        <div className="mt-10 bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Installation Instructions
            </h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              How to install the CreatelexGenAI plugin in your Unreal Engine project
            </p>
          </div>
          <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
            <h4 className="text-md font-medium text-gray-900 mb-4">Method 1: Epic Games Marketplace (Recommended)</h4>
            <ol className="list-decimal pl-5 space-y-2 text-gray-600">
              <li>Open the Epic Games Launcher</li>
              <li>Navigate to the Marketplace tab</li>
              <li>Search for "CreatelexGenAI"</li>
              <li>Click "Install to Engine"</li>
              <li>Launch your Unreal Engine project</li>
              <li>Go to Edit &gt; Plugins</li>
              <li>Find and enable the "CreatelexGenAI" plugin</li>
              <li>Restart your Unreal Engine project when prompted</li>
            </ol>

            <h4 className="text-md font-medium text-gray-900 mt-6 mb-4">Method 2: Manual Installation</h4>
            <ol className="list-decimal pl-5 space-y-2 text-gray-600">
              <li>Download the ZIP file using the link above</li>
              <li>Extract the ZIP file</li>
              <li>Copy the "CreatelexGenAI" folder to your project's "Plugins" directory
                <ul className="list-disc pl-5 mt-2 text-gray-500 text-sm">
                  <li>If the "Plugins" directory doesn't exist, create it at the root of your project</li>
                  <li>The final path should be: YourProject/Plugins/CreatelexGenAI/</li>
                  <li>Note: The folder is named "CreatelexGenAI" and will appear as "CreatelexGenAI" in the plugin manager</li>
                </ul>
              </li>
              <li>Launch or restart your Unreal Engine project</li>
              <li>If prompted about new plugins, click "Yes" to enable them</li>
              <li>Alternatively, go to Edit &gt; Plugins and manually enable the "CreatelexGenAI" plugin</li>
              <li>Restart your Unreal Engine project when prompted</li>
            </ol>
          </div>
        </div>

        {/* Getting Started */}
        <div className="mt-10 bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Getting Started
            </h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Next steps after installation
            </p>
          </div>
          <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
            <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-6">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-green-700">
                    <strong>Integrated Bridge:</strong> The MCP bridge is now built into the plugin - no separate app download needed!
                  </p>
                </div>
              </div>
            </div>
            <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-blue-700">
                    <strong>Requirement:</strong> Node.js is required to run the integrated bridge functionality. <a href="https://nodejs.org/" target="_blank" rel="noopener noreferrer" className="underline hover:text-blue-800">Download Node.js</a>
                  </p>
                </div>
              </div>
            </div>
            <ol className="list-decimal pl-5 space-y-2 text-gray-600">
              <li>Install Node.js from <a href="https://nodejs.org/" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">nodejs.org</a> if not already installed</li>
              <li>Open your Unreal Engine project with the plugin enabled</li>
              <li>The MCP server will automatically start in the background</li>
              <li>Configure your IDE (Claude Desktop, VSCode, Cursor, or Windsurf) to use the integrated bridge</li>
              <li>Start sending natural language commands to your Unreal Engine project</li>
            </ol>
            <div className="mt-6 bg-gray-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-gray-900 mb-3">Example IDE Configuration (Claude Desktop):</h4>
              <div className="bg-gray-900 text-gray-100 p-3 rounded text-xs overflow-x-auto">
                <pre>
{`{
  "mcpServers": {
    "createlex-unreal": {
      "command": "node",
      "args": ["C:\\\\Dev\\\\[YourProject]\\\\Plugins\\\\CreatelexGenAI\\\\Content\\\\Tools\\\\bridge-native.js"],
      "env": {}
    }
  }
}`}
                </pre>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                Replace <code>[YourProject]</code> with your actual Unreal project folder name.
              </p>
            </div>
            <div className="mt-6 space-y-2">
              <Link
                href="/docs"
                className="inline-block text-blue-600 hover:text-blue-800 mr-4"
              >
                View Complete Setup Guide →
              </Link>
            </div>
          </div>
        </div>

        {/* Resources */}
        <div className="mt-10 grid grid-cols-1 gap-6 lg:grid-cols-3">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg font-medium text-gray-900">Documentation</h3>
              <p className="mt-2 text-sm text-gray-500">
                Comprehensive guides and API reference for the CreatelexGenAI plugin
              </p>
              <div className="mt-4">
                <Link
                  href="/docs"
                  className="text-blue-600 hover:text-blue-800"
                >
                  View Documentation →
                </Link>
              </div>
            </div>
          </div>
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg font-medium text-gray-900">GitHub Repository</h3>
              <p className="mt-2 text-sm text-gray-500">
                Source code, issue tracking, and development resources
              </p>
              <div className="mt-4">
                <a
                  href="https://github.com/AlexKissiJr/AiWebplatform"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800"
                >
                  View on GitHub →
                </a>
              </div>
            </div>
          </div>
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg font-medium text-gray-900">Support</h3>
              <p className="mt-2 text-sm text-gray-500">
                Get help with installation, configuration, and usage
              </p>
              <div className="mt-4">
                <Link
                  href="/support"
                  className="text-blue-600 hover:text-blue-800"
                >
                  Contact Support →
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
