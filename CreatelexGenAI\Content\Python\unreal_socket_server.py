import socket
import json
import unreal
import threading
import time
from typing import Dict, Any, Tuple, List, Optional
import os

# Import handlers
from handlers import basic_commands, actor_commands, blueprint_commands, python_commands
from handlers import ui_commands
from utils import logging as log

# Import blueprint_context_handler with error handling
try:
    from handlers import blueprint_context_handler
    BLUEPRINT_CONTEXT_AVAILABLE = True
except ImportError as e:
    log.log_warning(f"blueprint_context_handler not available: {e}")
    BLUEPRINT_CONTEXT_AVAILABLE = False
    blueprint_context_handler = None

# Global queues and state
command_queue = []
response_dict = {}


# Bridge forwarding removed - all commands now handled locally


def is_authorized_connection(addr):
    """Check if the connection is from an authorized source."""
    client_ip = addr[0]
    
    # Only allow localhost connections
    if client_ip not in ['127.0.0.1', '::1', 'localhost']:
        log.log_warning(f"Rejected connection from unauthorized IP: {client_ip}")
        return False
    
    # Additional check: verify CreateLex Bridge is running
    # You could check for specific process names, ports, etc.
    return True


class CommandDispatcher:
    """
    Dispatches commands to appropriate handlers based on command type
    """
    def __init__(self):
        # Register command handlers
        self.handlers = {
            "handshake": self._handle_handshake,

            # Basic object commands
            "spawn": basic_commands.handle_spawn,
            "create_material": basic_commands.handle_create_material,
            "modify_object": actor_commands.handle_modify_object,

            # Blueprint commands
            "create_blueprint": blueprint_commands.handle_create_blueprint,
            "add_component": blueprint_commands.handle_add_component,
            "add_variable": blueprint_commands.handle_add_variable,
            "add_function": blueprint_commands.handle_add_function,
            "add_node": blueprint_commands.handle_add_node,
            "connect_nodes": blueprint_commands.handle_connect_nodes,
            "compile_blueprint": blueprint_commands.handle_compile_blueprint,
            "spawn_blueprint": blueprint_commands.handle_spawn_blueprint,
            "delete_node": blueprint_commands.handle_delete_node,
            
            # Getters
            "get_node_guid": blueprint_commands.handle_get_node_guid,
            "get_all_nodes": blueprint_commands.handle_get_all_nodes,
            "get_node_suggestions": blueprint_commands.handle_get_node_suggestions,
            
            
            # Bulk commands
            "add_nodes_bulk": blueprint_commands.handle_add_nodes_bulk,
            "connect_nodes_bulk": blueprint_commands.handle_connect_nodes_bulk,
            
            # Python and console
            "execute_python": python_commands.handle_execute_python,
            "execute_unreal_command": python_commands.handle_execute_unreal_command,
            
            # New
            "edit_component_property": actor_commands.handle_edit_component_property,
            "add_component_with_events": actor_commands.handle_add_component_with_events,
            
            # Scene
            "get_all_scene_objects": basic_commands.handle_get_all_scene_objects,
            "create_project_folder": basic_commands.handle_create_project_folder,
            "get_files_in_folder": basic_commands.handle_get_files_in_folder,
            
            # Input
            "add_input_binding": basic_commands.handle_add_input_binding,

            # --- NEW UI COMMANDS ---
            "add_widget_to_user_widget": ui_commands.handle_add_widget_to_user_widget,
            "edit_widget_property": ui_commands.handle_edit_widget_property,
            
            # --- LOCAL BLUEPRINT GENERATION COMMANDS ---
            "get_current_blueprint_context": self._handle_blueprint_context,
            "generate_blueprint_function": blueprint_commands.handle_generate_blueprint_function,
            "generate_context_aware_blueprint_function": blueprint_commands.handle_generate_context_aware_blueprint_function,
            "generate_smart_blueprint_function": blueprint_commands.handle_generate_smart_blueprint_function,
            "test_automatic_node_creation": blueprint_commands.handle_test_automatic_node_creation,
            "analyze_blueprint_graph": self._handle_blueprint_context,
            
            # --- CONTEXT COMMANDS ---
            "get_unreal_context": blueprint_commands.handle_get_unreal_context,
            "get_blueprint_context_detailed": self._handle_blueprint_context,
            "get_blueprint_graph_data": self._handle_blueprint_context,
        }

    def dispatch(self, command: Dict[str, Any]) -> Dict[str, Any]:
        """Dispatch command to appropriate handler"""
        command_type = command.get("type")
        
        # Authentication is handled by the Bridge MCP server, so we just route commands
        if command_type not in self.handlers:
            return {"success": False, "error": f"Unknown command type: {command_type}"}

        try:
            handler = self.handlers[command_type]
            return handler(command)
        except Exception as e:
            log.log_error(f"Error processing command: {str(e)}")
            return {"success": False, "error": str(e)}

    def _handle_handshake(self, command: Dict[str, Any]) -> Dict[str, Any]:
        """Built-in handler for handshake command"""
        message = command.get("message", "")
        # Use debug logging for handshake messages to reduce log spam
        log.log_debug(f"Handshake received: {message}", "handshake")
        
        # Get Unreal Engine version
        engine_version = unreal.SystemLibrary.get_engine_version()
        
        # Add connection and session information
        connection_info = {
            "status": "Connected",
            "engine_version": engine_version,
            "timestamp": time.time(),
            "session_id": f"UE-{int(time.time())}"
        }
        
        return {
            "success": True, 
            "message": f"Received: {message}",
            "connection_info": connection_info
        }

    def _handle_blueprint_context(self, command: Dict[str, Any]) -> Dict[str, Any]:
        """Handle Blueprint context-related commands"""
        command_type = command.get("type")
        
        if not BLUEPRINT_CONTEXT_AVAILABLE or blueprint_context_handler is None:
            log.log_error("blueprint_context_handler is not available")
            return {
                "success": False,
                "error": "Could not import blueprint context handler: No module named 'blueprint_context_handler'",
                "suggestion": "Ensure the blueprint_context_handler.py file is in the handlers directory"
            }
        
        try:
            return blueprint_context_handler.handle_blueprint_context_request(command_type, command)
        except Exception as e:
            log.log_error(f"Error in blueprint context handler: {e}")
            return {
                "success": False,
                "error": str(e)
            }

# Bridge forwarding method removed - all commands now handled locally


# Create global dispatcher instance
dispatcher = CommandDispatcher()


def process_commands(delta_time=None):
    """Process commands on the main thread"""
    if not command_queue:
        return

    command_id, command = command_queue.pop(0)
    # Use debug logging for command processing to reduce log spam
    command_type = command.get("type", "unknown")

    # Special debug logging for add_nodes_bulk
    if command_type == "add_nodes_bulk":
        log.log_info(f"🚨 SOCKET SERVER: Received add_nodes_bulk command!")
        log.log_info(f"🚨 SOCKET SERVER: Command data: {command}")

    log.log_debug(f"Processing {command_type} command on main thread", "command")

    try:
        if command_type == "add_nodes_bulk":
            log.log_info(f"🚨 SOCKET SERVER: About to dispatch add_nodes_bulk command")

        response = dispatcher.dispatch(command)

        if command_type == "add_nodes_bulk":
            log.log_info(f"🚨 SOCKET SERVER: add_nodes_bulk dispatch completed, response: {response}")

        response_dict[command_id] = response
    except Exception as e:
        log.log_error(f"Error processing command: {str(e)}", include_traceback=True)
        response_dict[command_id] = {"success": False, "error": str(e)}


def receive_all_data(conn, buffer_size=4096):
    """
    Receive all data from socket until complete JSON is received
    
    Args:
        conn: Socket connection
        buffer_size: Initial buffer size for receiving data
        
    Returns:
        Decoded complete data
    """
    data = b""
    while True:
        try:
            # Receive chunk of data
            chunk = conn.recv(buffer_size)
            if not chunk:
                break
            
            data += chunk
            
            # Try to parse as JSON to check if we received complete data
            try:
                json.loads(data.decode('utf-8'))
                # If we get here, JSON is valid and complete
                return data.decode('utf-8')
            except json.JSONDecodeError as json_err:
                # Check if the error indicates an unterminated string or incomplete JSON
                if "Unterminated string" in str(json_err) or "Expecting" in str(json_err):
                    # Need more data, continue receiving
                    continue
                else:
                    # JSON is malformed in some other way, not just incomplete
                    log.log_error(f"Malformed JSON received: {str(json_err)}", include_traceback=True)
                    return None
            
        except socket.timeout:
            # Socket timeout, return what we have so far
            log.log_warning("Socket timeout while receiving data")
            return data.decode('utf-8')
        except Exception as e:
            log.log_error(f"Error receiving data: {str(e)}", include_traceback=True)
            return None
    
    return data.decode('utf-8')


def socket_server_thread():
    """Socket server running in a separate thread"""
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    server_socket.bind(('localhost', 9877))
    server_socket.listen(1)
    log.log_production_status("AI Assistant server started on port 9877")

    command_counter = 0

    while True:
        try:
            conn, addr = server_socket.accept()
            
            # Validate connection source
            if not is_authorized_connection(addr):
                conn.close()
                continue
                
            # Set a timeout to prevent hanging
            conn.settimeout(5)  # 5-second timeout
            
            # Receive complete data, handling potential incomplete JSON
            data_str = receive_all_data(conn)
            
            if data_str:
                try:
                    command = json.loads(data_str)
                    # Use debug logging for received commands to reduce log spam
                    command_type = command.get("type", "unknown")
                    log.log_debug(f"Received {command_type} command", "command")

                    # All commands (including handshake) must be processed on the main thread
                    # to avoid "Attempted to access Unreal API from outside the main game thread" errors
                    command_id = command_counter
                    command_counter += 1
                    command_queue.append((command_id, command))

                    # Wait for the response with a timeout
                    timeout = 10  # seconds
                    start_time = time.time()
                    while command_id not in response_dict and time.time() - start_time < timeout:
                        time.sleep(0.1)

                    if command_id in response_dict:
                        response = response_dict.pop(command_id)
                        conn.sendall(json.dumps(response).encode())
                    else:
                        error_response = {"success": False, "error": "Command timed out"}
                        conn.sendall(json.dumps(error_response).encode())
                except json.JSONDecodeError as json_err:
                    log.log_error(f"Error parsing JSON: {str(json_err)}", include_traceback=True)
                    error_response = {"success": False, "error": f"Invalid JSON: {str(json_err)}"}
                    conn.sendall(json.dumps(error_response).encode())
            else:
                # No data or error receiving data
                error_response = {"success": False, "error": "No data received or error parsing data"}
                conn.sendall(json.dumps(error_response).encode())
                
            conn.close()
        except Exception as e:
            log.log_error(f"Error in socket server: {str(e)}", include_traceback=True)
            try:
                # Try to close the connection if it's still open
                conn.close()
            except:
                pass


# Register tick function to process commands on main thread
def register_command_processor():
    """Register the command processor with Unreal's tick system"""
    unreal.register_slate_post_tick_callback(process_commands)
    log.log_debug("Command processor registered", "general")


# Initialize the server
def initialize_server():
    """Initialize and start the socket server"""
    # Start the server thread
    thread = threading.Thread(target=socket_server_thread)
    thread.daemon = True
    thread.start()
    log.log_debug("Socket server thread started", "general")

    # Register the command processor on the main thread
    register_command_processor()

    log.log_production_status("CreateLex AI Studio initialized successfully")
    log.log_debug("Available commands: handshake, spawn, create_material, modify_object, blueprints, python", "general")

# Auto-start the server when this module is imported
initialize_server()

