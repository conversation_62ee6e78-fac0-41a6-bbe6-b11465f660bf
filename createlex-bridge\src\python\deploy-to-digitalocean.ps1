# Deploy Unreal Engine MCP Server to DigitalOcean App Platform
# Usage: .\deploy-to-digitalocean.ps1

$ErrorActionPreference = "Stop"

Write-Host "🚀 Deploying Unreal Engine MCP Server to DigitalOcean..." -ForegroundColor Green

# Check if doctl is installed
if (-not (Get-Command doctl -ErrorAction SilentlyContinue)) {
    Write-Host "❌ doctl CLI not found. Please install it first:" -ForegroundColor Red
    Write-Host "   https://docs.digitalocean.com/reference/doctl/how-to/install/" -ForegroundColor Yellow
    exit 1
}

# Check if user is authenticated
try {
    doctl auth list | Out-Null
} catch {
    Write-Host "❌ Not authenticated with DigitalOcean. Please run:" -ForegroundColor Red
    Write-Host "   doctl auth init" -ForegroundColor Yellow
    exit 1
}

# Commit and push changes to GitHub
Write-Host "📤 Pushing changes to GitHub..." -ForegroundColor Blue
git add .
try {
    git commit -m "feat: Add cloud MCP server deployment configuration"
} catch {
    Write-Host "No changes to commit" -ForegroundColor Yellow
}
git push origin cc_plugin

# Wait a moment for GitHub to process
Write-Host "⏳ Waiting for GitHub to process changes..." -ForegroundColor Blue
Start-Sleep -Seconds 5

# Deploy to DigitalOcean App Platform
Write-Host "🌐 Creating DigitalOcean App..." -ForegroundColor Blue

# Check if app already exists
$APP_NAME = "unreal-mcp-cloud-server"
$existingApps = doctl apps list --format Name --no-header
if ($existingApps -contains $APP_NAME) {
    Write-Host "📱 App already exists. Updating..." -ForegroundColor Yellow
    $APP_ID = (doctl apps list --format ID,Name --no-header | Where-Object { $_ -match $APP_NAME }).Split()[0]
    doctl apps update $APP_ID --spec .do/app.yaml
} else {
    Write-Host "📱 Creating new app..." -ForegroundColor Green
    doctl apps create --spec .do/app.yaml
}

# Get app info
Write-Host "📊 Getting app information..." -ForegroundColor Blue
$APP_ID = (doctl apps list --format ID,Name --no-header | Where-Object { $_ -match $APP_NAME }).Split()[0]

if ($APP_ID) {
    Write-Host "✅ App created/updated successfully!" -ForegroundColor Green
    Write-Host "📱 App ID: $APP_ID" -ForegroundColor Cyan
    Write-Host "🔗 App URL: https://cloud.digitalocean.com/apps/$APP_ID" -ForegroundColor Cyan
    
    # Wait for deployment
    Write-Host "⏳ Waiting for deployment to complete..." -ForegroundColor Blue
    doctl apps get $APP_ID --wait
    
    # Get the live URL
    $LIVE_URL = doctl apps get $APP_ID --format LiveURL --no-header
    Write-Host "🌐 Live URL: $LIVE_URL" -ForegroundColor Cyan
    
    # Test the deployment
    Write-Host "🧪 Testing deployment..." -ForegroundColor Blue
    try {
        $response = Invoke-WebRequest -Uri "$LIVE_URL/health" -Method GET -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ Deployment successful! Server is responding." -ForegroundColor Green
            Write-Host ""
            Write-Host "🎉 Your Unreal Engine MCP Server is now live!" -ForegroundColor Green
            Write-Host "📡 Registration endpoint: $LIVE_URL/register" -ForegroundColor Cyan
            Write-Host "🔧 MCP endpoint: $LIVE_URL/mcp" -ForegroundColor Cyan
            Write-Host "📊 Health check: $LIVE_URL/health" -ForegroundColor Cyan
            Write-Host "📋 Instances list: $LIVE_URL/instances" -ForegroundColor Cyan
            Write-Host ""
            Write-Host "💡 Update your auto_cloud_connector.py with this URL:" -ForegroundColor Yellow
            Write-Host "   mcp_cloud_url = `"$LIVE_URL`"" -ForegroundColor White
        }
    } catch {
        Write-Host "⚠️ Deployment completed but server is not responding yet." -ForegroundColor Yellow
        Write-Host "   This is normal - it may take a few minutes to start." -ForegroundColor Yellow
        Write-Host "   Check status at: https://cloud.digitalocean.com/apps/$APP_ID" -ForegroundColor Cyan
    }
} else {
    Write-Host "❌ Failed to get app information" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🎯 Next steps:" -ForegroundColor Green
Write-Host "1. Update auto_cloud_connector.py with the live URL" -ForegroundColor White
Write-Host "2. Test the connection with a local Unreal Engine instance" -ForegroundColor White
Write-Host "3. Configure Claude Desktop to use the cloud server" -ForegroundColor White
Write-Host ""
Write-Host "💰 Cost: ~$5/month for basic instance" -ForegroundColor Yellow
Write-Host "📈 Auto-scaling enabled for high traffic" -ForegroundColor Yellow 