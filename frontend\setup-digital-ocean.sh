#!/bin/bash

# Exit on error
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}CreateLex AI - Digital Ocean Setup Script${NC}"
echo -e "${YELLOW}This script will help you set up your Digital Ocean droplet for the CreateLex AI frontend.${NC}"
echo

# Check if running as root
if [ "$EUID" -ne 0 ]; then
  echo -e "${RED}Please run this script as root or with sudo.${NC}"
  exit 1
fi

# Update system packages
echo -e "${YELLOW}Updating system packages...${NC}"
apt update && apt upgrade -y

# Install required packages
echo -e "${YELLOW}Installing required packages...${NC}"
apt install -y curl wget git nginx software-properties-common gnupg2 ufw

# Install Node.js
echo -e "${YELLOW}Installing Node.js...${NC}"
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt install -y nodejs

# Verify Node.js and npm installation
echo -e "${YELLOW}Verifying Node.js and npm installation...${NC}"
node -v
npm -v

# Install PM2 globally
echo -e "${YELLOW}Installing PM2 globally...${NC}"
npm install -g pm2

# Install Certbot for SSL
echo -e "${YELLOW}Installing Certbot for SSL...${NC}"
apt install -y certbot python3-certbot-nginx

# Configure firewall
echo -e "${YELLOW}Configuring firewall...${NC}"
ufw allow ssh
ufw allow http
ufw allow https
ufw --force enable

# Create application user
echo -e "${YELLOW}Creating application user...${NC}"
if id "createlex" &>/dev/null; then
  echo -e "${GREEN}User 'createlex' already exists.${NC}"
else
  adduser --disabled-password --gecos "" createlex
  usermod -aG sudo createlex
  echo -e "${GREEN}User 'createlex' created.${NC}"
fi

# Create application directory
echo -e "${YELLOW}Creating application directory...${NC}"
mkdir -p /home/<USER>/app
chown -R createlex:createlex /home/<USER>/app

# Set up Nginx
echo -e "${YELLOW}Setting up Nginx...${NC}"
if [ -f "/etc/nginx/sites-available/createlex" ]; then
  echo -e "${GREEN}Nginx configuration already exists.${NC}"
else
  # Copy Nginx configuration
  cp nginx.conf /etc/nginx/sites-available/createlex
  
  # Create symbolic link
  ln -sf /etc/nginx/sites-available/createlex /etc/nginx/sites-enabled/
  
  # Remove default site
  rm -f /etc/nginx/sites-enabled/default
  
  # Test Nginx configuration
  nginx -t
  
  # Restart Nginx
  systemctl restart nginx
  
  echo -e "${GREEN}Nginx configuration created.${NC}"
fi

# Ask for domain name
echo -e "${YELLOW}Do you want to set up SSL with Let's Encrypt? (y/n)${NC}"
read -r setup_ssl

if [ "$setup_ssl" = "y" ]; then
  echo -e "${YELLOW}Enter your domain name (e.g., createlex.com):${NC}"
  read -r domain_name
  
  # Set up SSL with Let's Encrypt
  echo -e "${YELLOW}Setting up SSL with Let's Encrypt for $domain_name...${NC}"
  certbot --nginx -d "$domain_name" -d "www.$domain_name"
  
  echo -e "${GREEN}SSL certificates installed.${NC}"
fi

# Create deployment instructions
echo -e "${YELLOW}Creating deployment instructions...${NC}"
cat > /home/<USER>/DEPLOYMENT_INSTRUCTIONS.txt << EOL
# CreateLex AI Frontend Deployment Instructions

Your server is now set up and ready for deployment. Follow these steps to deploy the application:

1. Clone the repository:
   git clone https://github.com/AlexKissiJr/AiWebplatform.git /home/<USER>/app

2. Navigate to the frontend directory:
   cd /home/<USER>/app/frontend

3. Checkout the digital branch:
   git checkout digital

4. Create a .env.production file:
   nano .env.production

   Add the following environment variables (replace with your actual values):
   
   # Supabase Configuration
   NEXT_PUBLIC_SUPABASE_URL=https://your-supabase-url.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
   
   # API Configuration
   NEXT_PUBLIC_API_URL=https://api.createlex.com
   
   # Authentication Bypass (set to false in production)
   NEXT_PUBLIC_BYPASS_AUTH=false
   NEXT_PUBLIC_BYPASS_SUBSCRIPTION=false
   
   # Other Configuration
   NEXT_PUBLIC_SITE_URL=https://createlex.com

5. Run the deployment script:
   chmod +x deploy.sh
   ./deploy.sh

6. Set up PM2 to start on boot:
   pm2 startup
   pm2 save

7. Check the application status:
   pm2 status

8. View the application logs:
   pm2 logs createlex-frontend

Your application should now be running at https://your-domain.com
EOL

chown createlex:createlex /home/<USER>/DEPLOYMENT_INSTRUCTIONS.txt

echo -e "${GREEN}Server setup completed successfully!${NC}"
echo -e "${GREEN}Deployment instructions have been saved to /home/<USER>/DEPLOYMENT_INSTRUCTIONS.txt${NC}"
echo -e "${YELLOW}Please follow these instructions to complete the deployment.${NC}"
