#!/usr/bin/env node

const net = require('net');
const path = require('path');

// Universal Bridge Script for AI Coding Assistants
// Compatible with: <PERSON>, Cursor, Windsurf, VS Code, and other MCP-enabled tools
// Connects to the CreateLex Native Bridge App on localhost:9877

const BRIDGE_HOST = 'localhost';
const BRIDGE_PORT = 9877;
const SCRIPT_NAME = 'CreateLex Bridge';

let client = null;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 10;
const RECONNECT_DELAY = 2000;

// Detect which AI assistant is using this bridge
function detectAIAssistant() {
  const processName = process.env.npm_config_user_agent || process.title || 'unknown';
  const parentProcess = process.env.TERM_PROGRAM || process.env.VSCODE_PID ? 'vscode' : 'unknown';
  
  if (process.env.CLAUDE_DESKTOP) return '<PERSON>';
  if (process.env.CURSOR_SESSION_ID || processName.includes('cursor')) return 'Cursor';
  if (process.env.WINDSURF_SESSION || processName.includes('windsurf')) return 'Windsurf';
  if (process.env.VSCODE_PID || parentProcess === 'vscode') return 'VS Code';
  if (processName.includes('claude')) return 'Claude Desktop';
  
  return 'AI Assistant';
}

function log(message, level = 'info') {
  const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
  const aiAssistant = detectAIAssistant();
  const prefix = `[${timestamp}] [${SCRIPT_NAME}] [${aiAssistant}]`;
  
  switch (level) {
    case 'error':
      console.error(`${prefix} ❌ ${message}`);
      break;
    case 'warn':
      console.error(`${prefix} ⚠️  ${message}`);
      break;
    case 'success':
      console.error(`${prefix} ✅ ${message}`);
      break;
    default:
      console.error(`${prefix} ℹ️  ${message}`);
  }
}

function connectToBridge() {
  const aiAssistant = detectAIAssistant();
  log(`Connecting to CreateLex Native Bridge at ${BRIDGE_HOST}:${BRIDGE_PORT}...`);
  
  client = net.createConnection(BRIDGE_PORT, BRIDGE_HOST);

  client.on('connect', () => {
    log(`Connected successfully! ${aiAssistant} can now use Unreal Engine tools`, 'success');
    reconnectAttempts = 0;
    
    // Pipe stdin to bridge server
    process.stdin.pipe(client);
    
    // Pipe bridge server output to stdout
    client.pipe(process.stdout);
  });

  client.on('data', (data) => {
    // Data is already piped to stdout
    // Uncomment for debugging: log(`Received ${data.length} bytes from MCP server`);
  });

  client.on('error', (err) => {
    if (err.code === 'ECONNREFUSED') {
      log('CreateLex Native Bridge app is not running!', 'error');
      log('Please start the CreateLex Bridge app and click "Start MCP Server"', 'warn');
      log('Download from: https://github.com/AlexKissiJr/AiWebplatform/releases', 'info');
      
      if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
        reconnectAttempts++;
        log(`Retrying connection in ${RECONNECT_DELAY/1000}s... (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`, 'warn');
        setTimeout(connectToBridge, RECONNECT_DELAY);
      } else {
        log('Max reconnection attempts reached. Please start the CreateLex Bridge app.', 'error');
        process.exit(1);
      }
    } else {
      log(`Connection error: ${err.message}`, 'error');
      process.exit(1);
    }
  });

  client.on('close', () => {
    log('Connection to CreateLex Native Bridge closed', 'warn');
    
    if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
      reconnectAttempts++;
      log(`Reconnecting in ${RECONNECT_DELAY/1000}s... (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`, 'info');
      setTimeout(connectToBridge, RECONNECT_DELAY);
    } else {
      log('Max reconnection attempts reached. Exiting.', 'error');
      process.exit(1);
    }
  });
}

// Handle process termination gracefully
process.on('SIGINT', () => {
  log('Received interrupt signal, closing connection...', 'info');
  if (client) {
    client.end();
  }
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('Received termination signal, closing connection...', 'info');
  if (client) {
    client.end();
  }
  process.exit(0);
});

process.on('uncaughtException', (err) => {
  log(`Uncaught exception: ${err.message}`, 'error');
  if (client) {
    client.end();
  }
  process.exit(1);
});

// Display startup information
const aiAssistant = detectAIAssistant();
log(`Starting bridge for ${aiAssistant}`, 'info');
log('Connecting to CreateLex Native Bridge with 21+ Unreal Engine tools...', 'info');

// Start the connection
connectToBridge(); 