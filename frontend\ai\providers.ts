import { createOpenAI } from "@ai-sdk/openai";
import { createGroq } from "@ai-sdk/groq";
import { createAnthropic } from "@ai-sdk/anthropic";
import { createXai } from "@ai-sdk/xai";
import { createDeepSeek } from "@ai-sdk/deepseek";

import {
  customProvider,
  wrapLanguageModel,
  extractReasoningMiddleware
} from "ai";

export interface ModelInfo {
  provider: string;
  name: string;
  description: string;
  apiVersion: string;
  capabilities: string[];
}

const middleware = extractReasoningMiddleware({
  tagName: 'think',
});

// Helper to get API keys from environment variables first, then localStorage
const getApiKey = (key: string): string | undefined => {
  // Check for environment variables first
  if (process.env[key]) {
    console.log(`Found ${key} in environment variables`);
    return process.env[key] || undefined;
  }

  // Fall back to localStorage if available
  if (typeof window !== 'undefined') {
    const localStorageKey = window.localStorage.getItem(key);
    if (localStorageKey) {
      console.log(`Found ${key} in localStorage`);
      return localStorageKey;
    }
  }

  // If we're in a server environment and no API key is found, use a fallback for development/testing
  if (typeof window === 'undefined') {
    console.warn(`${key} not found in environment variables or localStorage`);

    // For Anthropic specifically, provide a fallback for testing
    if (key === 'ANTHROPIC_API_KEY') {
      return 'sk-ant-api03-fallback-development-key-not-for-production';
    }
  }

  return undefined;
};

// Create provider instances with API keys from localStorage
const openaiClient = createOpenAI({
  apiKey: getApiKey('OPENAI_API_KEY'),
});

const anthropicClient = createAnthropic({
  apiKey: getApiKey('ANTHROPIC_API_KEY'),
});

const groqClient = createGroq({
  apiKey: getApiKey('GROQ_API_KEY'),
});

const xaiClient = createXai({
  apiKey: getApiKey('XAI_API_KEY'),
});

const deepseekClient = createDeepSeek({
  apiKey: getApiKey('DEEPSEEK_API_KEY') || '***********************************',
});

const languageModels = {
  "gpt-4.1-mini": openaiClient("gpt-4.1-mini"),
  "claude-3-7-sonnet": anthropicClient('claude-3-7-sonnet-20250219'),
  "qwen-qwq": wrapLanguageModel(
    {
      model: groqClient("qwen-qwq-32b"),
      middleware
    }
  ),
  "grok-3-mini": xaiClient("grok-3-mini-latest"),
  "deepseek-chat": wrapLanguageModel(
    {
      model: deepseekClient("deepseek-reasoner"),
      middleware
    }
  ),
  "deepseek-reasoner": wrapLanguageModel(
    {
      model: deepseekClient("deepseek-reasoner"),
      middleware
    }
  ),
};

export const modelDetails: Record<keyof typeof languageModels, ModelInfo> = {
  "gpt-4.1-mini": {
    provider: "OpenAI",
    name: "GPT-4.1 Mini",
    description: "Compact version of OpenAI's GPT-4.1 with good balance of capabilities, including vision.",
    apiVersion: "gpt-4.1-mini",
    capabilities: ["Balance", "Creative", "Vision"]
  },
  "claude-3-7-sonnet": {
    provider: "Anthropic",
    name: "Claude 3.7 Sonnet",
    description: "Latest version of Anthropic's Claude 3.7 Sonnet with strong reasoning and coding capabilities.",
    apiVersion: "claude-3-7-sonnet-20250219",
    capabilities: ["Reasoning", "Efficient", "Agentic"]
  },
  "qwen-qwq": {
    provider: "Groq",
    name: "Qwen QWQ",
    description: "Latest version of Alibaba's Qwen QWQ with strong reasoning and coding capabilities.",
    apiVersion: "qwen-qwq",
    capabilities: ["Reasoning", "Efficient", "Agentic"]
  },
  "grok-3-mini": {
    provider: "XAI",
    name: "Grok 3 Mini",
    description: "Latest version of XAI's Grok 3 Mini with strong reasoning and coding capabilities.",
    apiVersion: "grok-3-mini-latest",
    capabilities: ["Reasoning", "Efficient", "Agentic"]
  },
  "deepseek-chat": {
    provider: "DeepSeek",
    name: "DeepSeek Chat",
    description: "DeepSeek's Reasoning R1 model with step-by-step thinking capabilities.",
    apiVersion: "deepseek-chat",
    capabilities: ["Reasoning", "Step-by-step", "Problem-solving"]
  },
  "deepseek-reasoner": {
    provider: "DeepSeek",
    name: "DeepSeek Reasoning R1",
    description: "DeepSeek's specialized reasoning model with step-by-step thinking capabilities.",
    apiVersion: "deepseek-reasoner",
    capabilities: ["Reasoning", "Step-by-step", "Problem-solving"]
  },
};

// Update API keys when localStorage changes (for runtime updates)
if (typeof window !== 'undefined') {
  window.addEventListener('storage', (event) => {
    // Reload the page if any API key changed to refresh the providers
    if (event.key?.includes('API_KEY')) {
      window.location.reload();
    }
  });
}

export const model = customProvider({
  languageModels,
});

export type modelID = keyof typeof languageModels;

export const MODELS = Object.keys(languageModels);

export const defaultModel: modelID = "claude-3-7-sonnet"; // Using Claude as default since DeepSeek API key is invalid
