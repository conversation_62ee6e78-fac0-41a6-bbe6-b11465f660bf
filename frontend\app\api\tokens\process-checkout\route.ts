import { NextRequest, NextResponse } from 'next/server';
import { apiRequest } from '../../../../lib/api-client';

/**
 * Process a checkout session manually
 * This is used as a fallback mechanism for local development
 * when Stripe webhooks can't reach the local server
 */
export async function POST(req: NextRequest) {
  try {
    console.log('[API] Process checkout endpoint called');

    // Parse the request body
    const body = await req.json();

    // Validate required fields
    if (!body.userId || !body.sessionId) {
      return NextResponse.json(
        {
          error: 'Missing required fields: userId and sessionId are required'
        },
        { status: 400 }
      );
    }

    console.log(`[API] Processing checkout for user ${body.userId}, session ${body.sessionId}`);

    // Forward the request to the backend
    try {
      const backendResponse = await apiRequest('/api/tokens/purchase/process-checkout', {
        method: 'POST',
        headers: {
          'x-user-id': body.userId,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      }, undefined, body.userId);

      console.log('[API] Backend response received:', backendResponse);

      // Return the backend response
      return NextResponse.json(backendResponse);
    } catch (backendError: any) {
      console.error('[API] Error from backend:', backendError.message);

      // If the backend is unavailable, try to use the simulate-webhook endpoint
      try {
        console.log('[API] Backend unavailable, trying simulate-webhook endpoint');

        // Determine the package ID based on the session ID
        // This is a fallback mechanism, so we'll just use 'small' as the default
        const packageId = 'small';

        const simulateResponse = await apiRequest('/api/tokens/purchase/simulate-webhook', {
          method: 'POST',
          headers: {
            'x-user-id': body.userId,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            userId: body.userId,
            packageId: packageId
          })
        }, undefined, body.userId);

        console.log('[API] Simulate webhook response received:', simulateResponse);

        // Return the simulate webhook response
        return NextResponse.json({
          ...simulateResponse,
          note: 'Used simulate-webhook as fallback'
        });
      } catch (simulateError: any) {
        console.error('[API] Error from simulate-webhook:', simulateError.message);

        // Return a detailed error response
        return NextResponse.json(
          {
            success: false,
            error: 'All fallback mechanisms failed',
            details: simulateError.message
          },
          { status: 500 }
        );
      }
    }
  } catch (error: any) {
    console.error('[API] Process checkout error:', error.message);

    // Return an error response
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error.message
      },
      { status: 500 }
    );
  }
}
