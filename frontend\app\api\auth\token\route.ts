import { NextRequest, NextResponse } from 'next/server';

// Completely bypass Supabase during build time
const isBuildTime = process.env.NODE_ENV === 'production' && typeof window === 'undefined';
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY || '';

export async function GET(request: NextRequest) {
  try {
    // Get the session cookie from the request
    const cookieStore = request.cookies;
    const supabaseSessionCookie =
      cookieStore.get('sb-access-token')?.value ||
      cookieStore.get('sb:token')?.value ||
      cookieStore.get('supabase-auth-token')?.value;

    // Check for auth header
    const authHeader = request.headers.get('authorization');
    const tokenFromHeader = authHeader ? authHeader.replace('Bearer ', '') : null;

    // Use token from header or cookie
    const token = tokenFromHeader || supabaseSessionCookie;

    if (!token) {
      console.log('No authentication token found in cookies or headers');
      // Return a 200 response with a null token instead of a 401 error
      // This helps prevent console errors in the browser
      return NextResponse.json({
        token: null,
        authenticated: false
      }, { status: 200 });
    }

    // During build time, skip Supabase client creation
    if (isBuildTime) {
      console.log('[API] Build-time environment detected, skipping Supabase client creation');
    } else {
      try {
        // Dynamic import to prevent build-time evaluation
        const { createClient } = require('@supabase/supabase-js');
        const supabase = createClient(supabaseUrl, supabaseServiceKey);
        console.log('[Supabase] Client initialized successfully');
      } catch (error) {
        console.error('[Supabase] Error initializing client:', error);
      }
    }

    // Return the token
    return NextResponse.json({
      token: token,
      authenticated: true
    }, { status: 200 });
  } catch (error) {
    console.error('Error getting token:', error);
    // Return a 200 response with a null token instead of a 500 error
    return NextResponse.json({
      token: null,
      authenticated: false,
      error: 'Internal server error'
    }, { status: 200 });
  }
}
