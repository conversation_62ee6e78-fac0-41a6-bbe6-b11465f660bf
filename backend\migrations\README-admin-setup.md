# Admin Dashboard Setup

This document provides instructions for setting up the admin dashboard tables in Supabase.

## Step 1: Execute SQL in Supabase Dashboard

1. Log in to the [Supabase Dashboard](https://app.supabase.com/)
2. Select your project
3. Go to the SQL Editor
4. Create a new query
5. Copy and paste the contents of `create-admin-tables.sql` into the editor
6. Run the query

## Step 2: Verify Setup

After running the SQL, you can verify the setup by running the following JavaScript code:

```javascript
// Check admin users
const { data: adminUsers, error: adminError } = await supabase
  .from('users')
  .select('id, email, name, is_admin')
  .eq('is_admin', true);

console.log('Admin users:', adminUsers);

// Check if tables were created
const tables = ['usage_logs', 'api_keys', 'billing_records'];
for (const table of tables) {
  const { data, error } = await supabase
    .from(table)
    .select('count');
    
  if (error) {
    console.log(`Error checking table ${table}:`, error);
  } else {
    console.log(`Table ${table} exists and has ${data[0].count} records`);
  }
}
```

## Step 3: Create Admin Dashboard Pages

After setting up the database tables, you'll need to create the admin dashboard pages in the frontend.

1. Create an admin layout component
2. Create admin dashboard pages for:
   - User management
   - Usage monitoring
   - Billing management
   - API key management
3. Implement access control to restrict access to admin users only

## Step 4: Implement Token Usage Tracking

To track token usage:

1. Create middleware to log API requests
2. Implement token counting for each request
3. Set up Stripe metered billing integration
4. Implement rate limiting based on user subscription
