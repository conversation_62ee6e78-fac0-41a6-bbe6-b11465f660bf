'use client';

import React, { ReactNode, useEffect } from 'react';
import { ThemeProvider } from "@/components/theme-provider";
import { SidebarProvider } from "@/components/ui/sidebar";
import { Toaster } from "sonner";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useLocalStorage } from "@/lib/hooks/use-local-storage";
import { STORAGE_KEYS } from "@/lib/constants";
import { MCPProvider } from "@/lib/context/mcp-context";
import { AuthProvider } from '@/contexts/AuthContext';
import { SupabaseAuthProvider } from '@/contexts/SupabaseAuthContext';
import { SubscriptionProvider } from '@/contexts/SubscriptionContext';
import { SocketProvider } from '@/contexts/SocketContext';
import { usePathname } from "next/navigation";

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: true,
    },
  },
});

export function Providers({ children }: { children: ReactNode }) {
  const [sidebarOpen, setSidebarOpen] = useLocalStorage<boolean>(
    STORAGE_KEYS.SIDEBAR_STATE,
    true
  );
  const pathname = usePathname();
  const isChatPage = pathname?.startsWith('/chat');

  // Dummy function for socket message handling at the provider level
  const handleMessageReceived = (message: string) => {
    console.log('Message received at provider level:', message);
  };

  // Initialize Supabase client on component mount
  useEffect(() => {
    // Import getSupabaseClient dynamically to avoid SSR issues
    import('@/lib/supabase-singleton').then(({ getSupabaseClient }) => {
      // Initialize the client by calling the function
      getSupabaseClient();
    });
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem={true}
        disableTransitionOnChange
        themes={["light", "dark", "sunset", "black"]}
      >
        <SupabaseAuthProvider>
          <AuthProvider>
            <SubscriptionProvider>
              <SocketProvider onMessageReceived={handleMessageReceived}>
                <MCPProvider>
                  {isChatPage ? (
                    <SidebarProvider defaultOpen={sidebarOpen} open={sidebarOpen} onOpenChange={setSidebarOpen}>
                      {children}
                    </SidebarProvider>
                  ) : (
                    children
                  )}
                  <Toaster position="top-center" richColors />
                </MCPProvider>
              </SocketProvider>
            </SubscriptionProvider>
          </AuthProvider>
        </SupabaseAuthProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
}
