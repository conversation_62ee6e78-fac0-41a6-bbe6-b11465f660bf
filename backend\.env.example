# Server Configuration
PORT=5001
FRONTEND_URL=https://createlex.com

# MCP Server Connection
MCP_SERVER_URL=ws://mcp_server:8765

# Authentication
GOOGLE_CLIENT_ID=your-google-client-id
JWT_SECRET=your-jwt-secret-here
JWT_EXPIRES_IN=7d

# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_KEY=your-service-key

# Subscription/Billing
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret
STRIPE_PRICE_ID=your-stripe-price-id

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>
SYSTEM_NOTIFICATION_EMAIL=<EMAIL>

# Rate Limiting Configuration
RATE_LIMIT_GLOBAL=500      # Global rate limit (requests per 5 minutes)
RATE_LIMIT_API=200         # API rate limit (requests per 15 minutes)
RATE_LIMIT_AUTH=30         # Auth rate limit (requests per hour)
RATE_LIMIT_CHAT=20         # Chat rate limit (requests per minute)
RATE_LIMIT_SOCKET=20       # Socket rate limit (messages per minute)
RATE_LIMIT_ADMIN=300       # Admin rate limit (requests per 15 minutes)

# AI Model API Keys
PS_OPENAIAPIKEY=your-openai-api-key
PS_DEEPSEEKAPIKEY=your-deepseek-api-key
PS_ANTHROPICAPIKEY=your-anthropic-api-key
PS_GOOGLEAPIKEY=your-google-api-key