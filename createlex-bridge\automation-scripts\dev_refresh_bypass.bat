@echo off
setlocal

REM =============================================
REM   DEVELOPMENT REFRESH - MCP BRIDGE BYPASS MODE
REM   UE Plugin + Bypass MCP Bridge (No Login/Clicking)
REM =============================================

title Development Refresh - Bypass Mode
cls

echo ========================================
echo   DEVELOPMENT REFRESH - BYPASS MODE
echo ========================================
echo.
echo This will refresh:
echo - Unreal Engine plugin
echo - MCP Bridge in bypass mode (no login, auto MCP start)
echo - All development processes
echo.

REM Configuration
set PROJECT_DIR=C:\Dev\YourLife
set PLUGIN_DIR=%PROJECT_DIR%\Plugins
set SOURCE_PLUGIN=C:\Dev\AiWebplatform\CreatelexGenAI
set TARGET_PLUGIN=%PLUGIN_DIR%\CreatelexGenAI
set BRIDGE_DIR=C:\Dev\AiWebplatform\createlex-bridge
set UE_EDITOR="C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor.exe"
set UBT="C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.exe"
set PROJECT_FILE=%PROJECT_DIR%\YourLife.uproject

echo ========================================
echo   PHASE 1: STOPPING ALL SERVICES
echo ========================================

REM 1. Kill ALL MCP and Bridge processes thoroughly
echo ^> Stopping ALL MCP and Bridge processes...
call "%~dp0kill_all_mcp.bat"

REM 2. Stop Unreal Editor and Build Tools
echo ^> Stopping Unreal Editor and build tools...
taskkill /F /T /IM UnrealEditor.exe >nul 2>&1
taskkill /F /T /IM UnrealBuildTool.exe >nul 2>&1
taskkill /F /T /IM devenv.exe >nul 2>&1
taskkill /F /T /IM MSBuild.exe >nul 2>&1
wmic process where "name='UnrealEditor.exe'" delete >nul 2>&1

echo ^> Waiting for all processes to terminate...
timeout /t 3 /nobreak >nul

echo ========================================
echo   PHASE 2: PLUGIN REFRESH
echo ========================================

REM 3. Clean build artifacts
echo ^> Cleaning build files...
for %%d in (Binaries Intermediate .vs) do (
    if exist "%PROJECT_DIR%\%%d" (
        echo ^>   Removing %%d...
        rmdir /s /q "%PROJECT_DIR%\%%d" >nul 2>&1
    )
)

REM 4. Plugin management
echo ^> Refreshing CreatelexGenAI plugin...
if exist "%TARGET_PLUGIN%" (
    echo ^>   Removing old plugin...
    rmdir /s /q "%TARGET_PLUGIN%" >nul 2>&1
)

if not exist "%PLUGIN_DIR%" mkdir "%PLUGIN_DIR%" >nul 2>&1

echo ^>   Installing fresh plugin...
robocopy "%SOURCE_PLUGIN%" "%TARGET_PLUGIN%" /E /NFL /NDL /NJH /NJS /R:0 /W:0 >nul

REM 5. Configure project
echo ^> Configuring project file...
(
echo {
echo   "FileVersion": 3,
echo   "EngineAssociation": "5.6",
echo   "Category": "",
echo   "Description": "",
echo   "Plugins": [
echo     {
echo       "Name": "CreatelexGenAI",
echo       "Enabled": true
echo     }
echo   ]
echo }
) > "%PROJECT_FILE%"

REM 6. Pre-build
echo ^> Pre-building project...
%UBT% -projectfiles -project="%PROJECT_FILE%" -game -rocket -progress >nul 2>&1
%UBT% YourLifeEditor Win64 Development -Project="%PROJECT_FILE%" -WaitMutex >nul 2>&1

echo ========================================
echo   PHASE 3: STARTING SERVICES - BYPASS MODE
echo ========================================

REM 7. Start MCP Bridge in Bypass Mode
echo ^> Starting MCP Bridge in bypass mode...
cd /d "%BRIDGE_DIR%"

echo ^> Setting bypass environment variables...
set NODE_ENV=development
set DEV_MODE=true
set BYPASS_SUBSCRIPTION=true
set BYPASS_LOGIN=true
set AUTO_START_MCP=true
set SKIP_AUTH=true
set CREATELEX_BASE_URL=http://localhost:3000
set API_BASE_URL=http://localhost:5001/api

echo ^> Running: npm run start:bypass
start "MCP-Bridge-Bypass-Window" cmd /k "title MCP-Bridge-Bypass-Active && npm run start:bypass"

echo ^> Waiting for bridge to start...
timeout /t 15 /nobreak >nul

REM 8. Start Unreal Editor
echo ^> Starting Unreal Editor...
start "" %UE_EDITOR% "%PROJECT_FILE%" -log -unattended -skipcompile -nosplash

echo ========================================
echo   PHASE 4: VERIFICATION
echo ========================================

echo ^> Waiting for services to start...
timeout /t 10 /nobreak >nul

REM Check MCP Bridge
echo ^> Checking MCP Bridge (port 9877)...
netstat -an | find ":9877" >nul
if %errorlevel% equ 0 (
    echo ^>   ✅ MCP Bridge Server is running
) else (
    echo ^>   ⚠️  MCP Bridge Server may still be starting
)

REM Check if UE process exists
tasklist /FI "IMAGENAME eq UnrealEditor.exe" 2>NUL | find /I /N "UnrealEditor.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ^>   ✅ Unreal Editor is running
) else (
    echo ^>   ⚠️  Unreal Editor may still be starting
)

echo.
echo ========================================
echo   DEVELOPMENT REFRESH COMPLETE! (BYPASS MODE)
echo ========================================
echo.
echo Status Summary:
echo - Plugin: REFRESHED
echo - Build: PRE-COMPILED  
echo - MCP Bridge: BYPASS MODE (no login, auto MCP start)
echo - Unreal Editor: LAUNCHED
echo.
echo Benefits of Bypass Mode:
echo - No OAuth authentication required
echo - No manual button clicking
echo - Automatic MCP server startup
echo - Faster development iteration
echo - Full bridge functionality maintained
echo.
echo Development environment is ready!
echo.
echo Next Steps:
echo 1. Check UE Editor logs: %PROJECT_DIR%\Saved\Logs\YourLife.log
echo 2. Verify plugin loaded in UE Editor
echo 3. Test MCP connection on port 9877
echo 4. Bridge dashboard should be visible
echo ========================================

timeout /t 5 /nobreak >nul
exit /b 0