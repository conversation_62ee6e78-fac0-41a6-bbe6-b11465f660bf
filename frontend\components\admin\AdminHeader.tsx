'use client';

import { useState, useEffect } from 'react';
import { Bell, Search, User } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { signOutAndClearStorage } from '@/lib/supabase';
import { useRouter } from 'next/navigation';

export default function AdminHeader() {
  const { user } = useAuth();
  const router = useRouter();
  const [userName, setUserName] = useState('Admin User');
  const [userEmail, setUserEmail] = useState('');

  useEffect(() => {
    if (user) {
      setUserName(user.name || 'Admin User');
      setUserEmail(user.email || '');
    }
  }, [user]);

  const handleSignOut = async () => {
    await signOutAndClearStorage();
    router.push('/login');
  };

  return (
    <header className="h-14 border-b bg-white flex items-center px-4 justify-between">
      <div className="flex items-center gap-2 w-72">
        <Search className="text-gray-400" size={18} />
        <Input
          placeholder="Search..."
          className="border-none shadow-none focus-visible:ring-0 h-9"
        />
      </div>

      <div className="flex items-center gap-4">
        <Button variant="ghost" size="icon" className="rounded-full">
          <Bell size={18} />
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="flex items-center gap-2 rounded-full">
              <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground">
                <User size={16} />
              </div>
              <span className="font-medium">{userName}</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>
              <div className="flex flex-col">
                <span>{userName}</span>
                <span className="text-xs text-gray-500">{userEmail}</span>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => router.push('/admin/settings')}>
              Settings
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleSignOut}>
              Sign out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
}
