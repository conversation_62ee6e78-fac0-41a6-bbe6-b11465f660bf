import { NextRequest, NextResponse } from 'next/server';
import { adminApiRequest } from '@/lib/admin-api';
import { getCurrentUser } from '@/lib/supabase';

export async function DELETE(
  req: NextRequest,
  { params }: { params: { keyId: string } }
) {
  try {
    const keyId = params.keyId;

    if (!keyId) {
      return NextResponse.json(
        { error: 'API key ID is required' },
        { status: 400 }
      );
    }

    // Forward the request to the backend using our helper function
    const response = await adminApiRequest(`/api/admin/api-keys/${keyId}`, {
      method: 'DELETE'
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response from backend:', errorText);

      return NextResponse.json(
        { error: `Failed to revoke API key: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in API keys route:', error);

    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}

export async function GET(
  req: NextRequest,
  { params }: { params: { keyId: string } }
) {
  try {
    const keyId = params.keyId;

    if (!keyId) {
      return NextResponse.json(
        { error: 'API key ID is required' },
        { status: 400 }
      );
    }

    // Check if we're requesting usage statistics
    const url = new URL(req.url);
    const usage = url.searchParams.get('usage') === 'true';

    // Forward the request to the backend using our helper function
    let backendUrl = `/api/admin/api-keys/${keyId}`;

    if (usage) {
      backendUrl += '/usage';
    }

    const response = await adminApiRequest(backendUrl, {
      method: 'GET'
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response from backend:', errorText);

      return NextResponse.json(
        { error: `Failed to get API key: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in API keys route:', error);

    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}
