#!/usr/bin/env python3
"""
Build script for creating TEST protected MCP server distribution
Same protection as production but WITHOUT subscription validation
"""

import os
import shutil
import subprocess
import sys
from pathlib import Path

def create_test_protected_version():
    """Create protected version WITHOUT subscription validation"""
    print("🔒 Creating TEST protected version (no subscription validation)...")
    
    # Read the original stdio server
    with open('mcp_server_stdio.py', 'r', encoding='utf-8') as f:
        original_source = f.read()
    
    # Create test version - remove subscription validation but keep structure
    test_protected_source = f'''#!/usr/bin/env python3
"""
UnrealGenAI MCP Server - TEST VERSION
Protected but no subscription validation for testing
"""

{original_source}
'''
    
    # Write test protected version
    with open('mcp_server_test.py', 'w', encoding='utf-8') as f:
        f.write(test_protected_source)
    
    print("✅ Test protected version created: mcp_server_test.py")

def obfuscate_test_version():
    """Obfuscate the test version"""
    print("🔒 Obfuscating test version...")
    
    try:
        # Create obfuscated directory
        os.makedirs('test_dist/obfuscated', exist_ok=True)
        
        # Obfuscate the test server
        result = subprocess.run([
            'pyarmor', 'gen', 
            '--output', 'test_dist/obfuscated',
            '--recursive',
            'mcp_server_test.py'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Test version obfuscated successfully")
        else:
            print(f"❌ Obfuscation failed: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("❌ PyArmor not found. Installing...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyarmor'], check=True)
        return obfuscate_test_version()
    
    return True

def build_test_executable():
    """Build standalone executable from obfuscated test version"""
    print("📦 Building test executable...")
    
    try:
        # Create spec file for test version
        spec_content = '''
# -*- mode: python ; coding: utf-8 -*-
import sys
sys.setrecursionlimit(5000)

a = Analysis(
    ['test_dist/obfuscated/mcp_server_test.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('test_dist/obfuscated/pyarmor_runtime_000000', 'pyarmor_runtime_000000'),
    ],
    hiddenimports=[
        'fastmcp',
        'requests',
        'json',
        'os',
        'sys',
        'time',
        'pathlib'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='unrealgenai_mcp_server_test',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
        
        with open('test_server.spec', 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        # Build with PyInstaller
        result = subprocess.run([
            'pyinstaller', 
            '--clean', 
            '--noconfirm',
            'test_server.spec'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            # Move the executable to test_dist
            if os.path.exists('dist/unrealgenai_mcp_server_test.exe'):
                shutil.move('dist/unrealgenai_mcp_server_test.exe', 'test_dist/')
                print("✅ Test executable built successfully")
                return True
        else:
            print(f"❌ Build failed: {result.stderr}")
            
    except FileNotFoundError:
        print("❌ PyInstaller not found. Installing...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)
        return build_test_executable()
    
    return False

def create_test_installer():
    """Create installer for test version"""
    installer_script = '''#!/usr/bin/env python3
"""
UnrealGenAI MCP Server - TEST Installation
"""

import os
import shutil
import json
from pathlib import Path

def install_test_mcp():
    print("🧪 Installing UnrealGenAI MCP Server - TEST VERSION...")
    
    # Create installation directory
    install_dir = Path.home() / ".unrealgenai" / "mcp_server_test"
    install_dir.mkdir(parents=True, exist_ok=True)
    
    # Copy test executable
    shutil.copy2("unrealgenai_mcp_server_test.exe", install_dir)
    
    # Create Claude Desktop config
    config = {
        "mcpServers": {
            "unreal-ai-support-test": {
                "command": str(install_dir / "unrealgenai_mcp_server_test.exe"),
                "args": []
            }
        }
    }
    
    config_file = install_dir / "claude_config_test.json"
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print("✅ TEST Installation complete!")
    print(f"📁 Installed to: {install_dir}")
    print(f"📋 Config file: {config_file}")
    print("🔄 Copy config to Claude Desktop and restart")
    
    # Show config content
    print("\\n📋 Claude Desktop Configuration:")
    print("-" * 40)
    print(json.dumps(config, indent=2))
    print("-" * 40)

if __name__ == "__main__":
    install_test_mcp()
'''
    
    with open('test_dist/install_test.py', 'w', encoding='utf-8') as f:
        f.write(installer_script)
    
    print("✅ Test installer created")

def main():
    print("🧪 Building UnrealGenAI MCP Server - TEST VERSION")
    print("=" * 50)
    
    # Clean previous builds
    if os.path.exists('test_dist'):
        shutil.rmtree('test_dist')
    if os.path.exists('mcp_server_test.py'):
        os.remove('mcp_server_test.py')
    
    # Create test_dist directory
    os.makedirs('test_dist', exist_ok=True)
    
    # Build steps
    steps = [
        ("Creating test protected version", create_test_protected_version),
        ("Obfuscating test version", obfuscate_test_version),
        ("Building test executable", build_test_executable),
        ("Creating test installer", create_test_installer)
    ]
    
    for step_name, step_func in steps:
        print(f"\\n{step_name}...")
        if not step_func():
            print(f"❌ Failed: {step_name}")
            return False
    
    print("\\n" + "=" * 50)
    print("🎉 TEST BUILD COMPLETE!")
    print("📂 Files created in test_dist/:")
    print("   • unrealgenai_mcp_server_test.exe (Protected executable)")
    print("   • install_test.py (Test installer)")
    print("   • obfuscated/ (Obfuscated source)")
    print("\\n🧪 This version has NO subscription validation")
    print("🚀 Run: python test_dist/install_test.py")
    
    return True

if __name__ == "__main__":
    main() 