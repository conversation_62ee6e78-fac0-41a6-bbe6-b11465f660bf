-- Create API requests table
CREATE TABLE IF NOT EXISTS api_requests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  api_key_id UUID NOT NULL REFERENCES api_keys(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  endpoint <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  method VARCHAR(10) NOT NULL,
  status_code INTEGER NOT NULL,
  response_time INTEGER NOT NULL, -- in milliseconds
  request_size INTEGER NOT NULL, -- in bytes
  response_size INTEGER NOT NULL, -- in bytes
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create index on api_key_id for faster lookups
CREATE INDEX IF NOT EXISTS api_requests_api_key_id_idx ON api_requests(api_key_id);

-- Create index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS api_requests_user_id_idx ON api_requests(user_id);

-- Create index on created_at for faster lookups
CREATE INDEX IF NOT EXISTS api_requests_created_at_idx ON api_requests(created_at);

-- Create RLS policies for API requests table
ALTER TABLE api_requests ENABLE ROW LEVEL SECURITY;

-- Only allow users to see their own API requests
CREATE POLICY api_requests_select_policy ON api_requests
  FOR SELECT
  USING (auth.uid() = user_id);

-- Only allow users to insert their own API requests
CREATE POLICY api_requests_insert_policy ON api_requests
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Create function to get API requests by endpoint
CREATE OR REPLACE FUNCTION get_api_requests_by_endpoint(key_id UUID)
RETURNS TABLE (
  endpoint VARCHAR(255),
  count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    ar.endpoint,
    COUNT(*)::BIGINT AS count
  FROM api_requests ar
  WHERE ar.api_key_id = key_id
  AND ar.created_at >= NOW() - INTERVAL '24 hours'
  GROUP BY ar.endpoint
  ORDER BY count DESC;
END;
$$ LANGUAGE plpgsql;

-- Create function to get API requests by day
CREATE OR REPLACE FUNCTION get_api_requests_by_day(key_id UUID, days INTEGER DEFAULT 7)
RETURNS TABLE (
  date DATE,
  count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    DATE(ar.created_at) AS date,
    COUNT(*)::BIGINT AS count
  FROM api_requests ar
  WHERE ar.api_key_id = key_id
  AND ar.created_at >= NOW() - (days || ' days')::INTERVAL
  GROUP BY date
  ORDER BY date;
END;
$$ LANGUAGE plpgsql;
