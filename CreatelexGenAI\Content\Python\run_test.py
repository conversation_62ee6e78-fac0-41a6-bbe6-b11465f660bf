#!/usr/bin/env python3
"""
Simple test runner to verify level blueprint functionality
"""

import sys
import os

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def run_level_blueprint_test():
    """Run the level blueprint test"""
    try:
        import test_level_blueprint
        return test_level_blueprint.test_level_blueprint_detection()
    except Exception as e:
        print(f"Error running test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_level_blueprint_test()
    print(f"\nTest result: {'PASSED' if success else 'FAILED'}")