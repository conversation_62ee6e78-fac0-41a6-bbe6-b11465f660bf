import hashlib
import hmac
import time
import json
import requests
from datetime import datetime, timedelta
import os

class SubscriptionValidator:
    def __init__(self, api_endpoint="https://your-api.com/validate", secret_key=None):
        self.api_endpoint = api_endpoint
        self.secret_key = secret_key or os.environ.get("SUBSCRIPTION_SECRET", "default-secret")
        self.cache_file = os.path.expanduser("~/.unrealgenai/subscription_cache.json")
        self.cache_duration = 24 * 60 * 60  # 24 hours in seconds
        
    def generate_machine_id(self):
        """Generate unique machine identifier"""
        import platform
        import uuid
        
        # Combine multiple machine identifiers
        machine_info = f"{platform.node()}-{platform.machine()}-{platform.processor()}"
        try:
            mac_address = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                                  for elements in range(0,2*6,2)][::-1])
            machine_info += f"-{mac_address}"
        except:
            pass
            
        return hashlib.sha256(machine_info.encode()).hexdigest()[:16]
    
    def create_signature(self, data):
        """Create HMAC signature for data"""
        message = json.dumps(data, sort_keys=True)
        return hmac.new(
            self.secret_key.encode(),
            message.encode(),
            hashlib.sha256
        ).hexdigest()
    
    def load_cached_validation(self):
        """Load cached validation result"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r') as f:
                    cache_data = json.load(f)
                    
                # Check if cache is still valid
                cached_time = cache_data.get('timestamp', 0)
                if time.time() - cached_time < self.cache_duration:
                    return cache_data.get('valid', False)
        except:
            pass
        return None
    
    def save_cached_validation(self, is_valid):
        """Save validation result to cache"""
        try:
            os.makedirs(os.path.dirname(self.cache_file), exist_ok=True)
            cache_data = {
                'valid': is_valid,
                'timestamp': time.time()
            }
            with open(self.cache_file, 'w') as f:
                json.dump(cache_data, f)
        except:
            pass
    
    def validate_subscription(self, license_key=None, user_email=None):
        """Validate subscription with server"""
        # First check cache
        cached_result = self.load_cached_validation()
        if cached_result is not None:
            return cached_result
        
        try:
            machine_id = self.generate_machine_id()
            
            # Prepare validation data
            validation_data = {
                'license_key': license_key or os.environ.get("LICENSE_KEY"),
                'user_email': user_email or os.environ.get("USER_EMAIL"),
                'machine_id': machine_id,
                'timestamp': int(time.time()),
                'product': 'unrealgenai-mcp'
            }
            
            # Create signature
            signature = self.create_signature(validation_data)
            validation_data['signature'] = signature
            
            # Make API request
            response = requests.post(
                self.api_endpoint,
                json=validation_data,
                timeout=10,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                is_valid = result.get('valid', False)
                
                # Cache the result
                self.save_cached_validation(is_valid)
                return is_valid
            else:
                # Server error - check if we have a recent cache
                return self.handle_validation_error()
                
        except requests.RequestException:
            # Network error - check if we have a recent cache
            return self.handle_validation_error()
        except Exception as e:
            print(f"Validation error: {e}")
            return False
    
    def handle_validation_error(self):
        """Handle validation errors with grace period"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r') as f:
                    cache_data = json.load(f)
                    
                # Allow 7 days grace period for network issues
                cached_time = cache_data.get('timestamp', 0)
                grace_period = 7 * 24 * 60 * 60  # 7 days
                
                if time.time() - cached_time < grace_period:
                    return cache_data.get('valid', False)
        except:
            pass
        return False
    
    def get_subscription_info(self):
        """Get detailed subscription information"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r') as f:
                    cache_data = json.load(f)
                    
                cached_time = cache_data.get('timestamp', 0)
                is_valid = cache_data.get('valid', False)
                
                return {
                    'valid': is_valid,
                    'last_checked': datetime.fromtimestamp(cached_time).isoformat(),
                    'expires_in_hours': max(0, (self.cache_duration - (time.time() - cached_time)) / 3600)
                }
        except:
            pass
        
        return {
            'valid': False,
            'last_checked': 'Never',
            'expires_in_hours': 0
        }

# Usage example:
def check_subscription():
    """Main subscription check function"""
    # Development / testing bypasses
    dev_mode = os.environ.get('DEV_MODE')
    node_env = os.environ.get('NODE_ENV')
    if dev_mode == 'true' or node_env == 'development':
        print("Development mode detected - bypassing subscription validation")
        return True

    # Explicit bypass via environment variable
    if os.environ.get('BYPASS_SUBSCRIPTION') == 'true':
        print("BYPASS_SUBSCRIPTION=true - bypassing subscription validation")
        return True

    # Bridge (Electron app) may validate the subscription already and set this flag
    if os.environ.get('BRIDGE_SUBSCRIPTION_VALIDATED') == 'true':
        print("Bridge has already validated subscription - bypassing local validation")
        return True

    validator = SubscriptionValidator()
    
    # Try to validate
    is_valid = validator.validate_subscription()
    
    if not is_valid:
        print("❌ Invalid or expired subscription!")
        print("Please contact support or renew your license.")
        return False
    
    print("✅ Subscription validated successfully!")
    return True

# Integration with MCP server
def require_subscription(func):
    """Decorator to require valid subscription for MCP tools"""
    def wrapper(*args, **kwargs):
        if not check_subscription():
            return "❌ This tool requires a valid subscription. Please contact support."
        return func(*args, **kwargs)
    return wrapper 