import { NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/supabase';
import { supabase } from '@/lib/supabase';

export async function GET() {
  try {
    // Get the current user
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({ 
        data: { isAdmin: false }, 
        error: 'Not authenticated' 
      }, { status: 401 });
    }
    
    // Check if user is admin by email
    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    if (adminEmails.includes(user.email || '')) {
      return NextResponse.json({ 
        data: { isAdmin: true }, 
        error: null 
      });
    }
    
    // Check if user has is_admin flag in Supabase
    const { data, error } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single();
      
    if (!error && data && data.is_admin === true) {
      return NextResponse.json({ 
        data: { isAdmin: true }, 
        error: null 
      });
    }
    
    // If we get here, user is not an admin
    return NextResponse.json({ 
      data: { isAdmin: false }, 
      error: null 
    });
  } catch (error) {
    console.error('Error checking admin status:', error);
    return NextResponse.json({ 
      data: { isAdmin: false }, 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}
