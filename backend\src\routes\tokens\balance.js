const express = require('express');
const router = express.Router();
const { supabase, isSupabaseInitialized, getInitializationError, testSupabaseConnection } = require('../../services/supabaseService');

/**
 * @route GET /api/tokens/balance
 * @description Get token balance for a user
 * @access Private
 */
router.get('/', async (req, res) => {
  // Generate a unique request ID for tracking
  const requestId = `balance-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;

  try {
    // Get user ID from request user or query parameter
    let userId = req.user?.id;

    // If no user ID in req.user, check for userId in query parameters
    if (!userId && req.query.userId) {
      userId = req.query.userId;
      console.log(`[${requestId}] Using userId from query parameter: ${userId}`);
    }

    // If no user ID in req.user or query, check for x-user-id header
    if (!userId && req.headers['x-user-id']) {
      userId = req.headers['x-user-id'];
      console.log(`[${requestId}] Using userId from x-user-id header: ${userId}`);
    }

    // If still no user ID, return error
    if (!userId) {
      console.error(`[${requestId}] No user ID provided`);
      return res.status(400).json({
        error: 'No user ID provided',
        requestId
      });
    }

    // Check if forceRefresh is set
    const forceRefresh = req.query.forceRefresh === 'true';
    const nocache = req.query.nocache || Date.now();

    console.log(`[${requestId}] Fetching token balance for user: ${userId} (forceRefresh: ${forceRefresh}, nocache: ${nocache})`);

    // Check if Supabase is initialized
    if (!isSupabaseInitialized()) {
      const error = getInitializationError();
      console.error(`[${requestId}] Supabase client not initialized:`, error?.message || 'Unknown error');
      return res.status(500).json({
        error: 'Database connection not available',
        details: error?.message || 'Unknown error',
        requestId
      });
    }

    // Test Supabase connection if forceRefresh is true
    if (forceRefresh) {
      try {
        console.log(`[${requestId}] Testing Supabase connection before fetching balance`);
        await testSupabaseConnection();
        console.log(`[${requestId}] Supabase connection test successful`);
      } catch (connectionError) {
        console.error(`[${requestId}] Supabase connection test failed:`, connectionError);
        return res.status(500).json({
          error: 'Database connection test failed',
          details: connectionError.message,
          requestId
        });
      }
    }

    // Get token balance from Supabase
    console.log(`[${requestId}] Querying token_balance table for user ${userId}`);

    let query = supabase
      .from('token_balance')
      .select('*')
      .eq('user_id', userId);

    // If forceRefresh is true, use .single() without caching
    if (forceRefresh) {
      console.log(`[${requestId}] Force refresh requested, bypassing cache`);
      query = query.single({ cache: 'no-store' });
    } else {
      query = query.single();
    }

    let { data, error } = await query;

    if (error) {
      console.error(`[${requestId}] Error fetching token balance:`, error);

      // If the error is that no rows were returned, create a new balance record
      if (error.code === 'PGRST116') {
        console.log(`[${requestId}] No token balance found for user ${userId}, creating new record with 0 tokens`);

        try {
          const { data: newBalance, error: createError } = await supabase
            .from('token_balance')
            .insert([
              {
                user_id: userId,
                balance: 0, // Start with 0 tokens - tokens should only be added through purchases
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              }
            ])
            .select()
            .single();

          if (createError) {
            console.error(`[${requestId}] Error creating token balance:`, createError);
            return res.status(500).json({
              error: 'Failed to create token balance',
              details: createError.message,
              requestId
            });
          }

          console.log(`[${requestId}] Created new token balance record: ${JSON.stringify(newBalance)}`);

          // Record this creation in the transactions table for tracking
          try {
            const { data: transactionData, error: transactionError } = await supabase
              .from('token_transactions')
              .insert({
                user_id: userId,
                amount: 0,
                type: 'initial_balance',
                reference_id: requestId,
                previous_balance: 0,
                new_balance: 0,
                operation_id: requestId
              })
              .select()
              .single();

            if (transactionError) {
              console.error(`[${requestId}] Warning: Failed to record initial balance transaction:`, transactionError);
              // Continue anyway since the balance was created successfully
            } else {
              console.log(`[${requestId}] Recorded initial balance transaction: ${JSON.stringify(transactionData)}`);
            }
          } catch (transactionError) {
            console.error(`[${requestId}] Warning: Error recording initial balance transaction:`, transactionError);
            // Continue anyway since the balance was created successfully
          }

          return res.json({
            ...newBalance,
            requestId
          });
        } catch (createError) {
          console.error(`[${requestId}] Unexpected error creating token balance:`, createError);
          return res.status(500).json({
            error: 'Failed to create token balance',
            details: createError.message,
            requestId
          });
        }
      }

      return res.status(500).json({
        error: 'Failed to fetch token balance',
        details: error.message,
        requestId
      });
    }

    // If no data was returned, create a new balance record
    if (!data) {
      console.log(`[${requestId}] No token balance data found for user ${userId}, creating new record with 0 tokens`);

      try {
        const { data: newBalance, error: createError } = await supabase
          .from('token_balance')
          .insert([
            {
              user_id: userId,
              balance: 0, // Start with 0 tokens - tokens should only be added through purchases
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
          ])
          .select()
          .single();

        if (createError) {
          console.error(`[${requestId}] Error creating token balance:`, createError);
          return res.status(500).json({
            error: 'Failed to create token balance',
            details: createError.message,
            requestId
          });
        }

        console.log(`[${requestId}] Created new token balance record: ${JSON.stringify(newBalance)}`);

        // Record this creation in the transactions table for tracking
        try {
          const { data: transactionData, error: transactionError } = await supabase
            .from('token_transactions')
            .insert({
              user_id: userId,
              amount: 0,
              type: 'initial_balance',
              reference_id: requestId,
              previous_balance: 0,
              new_balance: 0,
              operation_id: requestId
            })
            .select()
            .single();

          if (transactionError) {
            console.error(`[${requestId}] Warning: Failed to record initial balance transaction:`, transactionError);
            // Continue anyway since the balance was created successfully
          } else {
            console.log(`[${requestId}] Recorded initial balance transaction: ${JSON.stringify(transactionData)}`);
          }
        } catch (transactionError) {
          console.error(`[${requestId}] Warning: Error recording initial balance transaction:`, transactionError);
          // Continue anyway since the balance was created successfully
        }

        return res.json({
          ...newBalance,
          requestId
        });
      } catch (createError) {
        console.error(`[${requestId}] Unexpected error creating token balance:`, createError);
        return res.status(500).json({
          error: 'Failed to create token balance',
          details: createError.message,
          requestId
        });
      }
    }

    console.log(`[${requestId}] Found existing token balance record: ID=${data.id}, balance=${data.balance}`);

    // Check for recent transactions that might not be reflected in the balance
    if (forceRefresh) {
      try {
        console.log(`[${requestId}] Checking for recent transactions that might not be reflected in the balance`);
        const { data: recentTransactions, error: transactionError } = await supabase
          .from('token_transactions')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', { ascending: false })
          .limit(5);

        if (transactionError) {
          console.error(`[${requestId}] Error fetching recent transactions:`, transactionError);
        } else if (recentTransactions && recentTransactions.length > 0) {
          console.log(`[${requestId}] Found ${recentTransactions.length} recent transactions`);

          // Check if the most recent transaction has a newer balance
          const latestTransaction = recentTransactions[0];
          console.log(`[${requestId}] Latest transaction: ID=${latestTransaction.id}, new_balance=${latestTransaction.new_balance}, created_at=${latestTransaction.created_at}`);

          // If the transaction is newer than the balance record's updated_at
          const transactionDate = new Date(latestTransaction.created_at);
          const balanceUpdateDate = new Date(data.updated_at);

          if (transactionDate > balanceUpdateDate && latestTransaction.new_balance !== data.balance) {
            console.log(`[${requestId}] Transaction is newer than balance record and has different balance`);
            console.log(`[${requestId}] Transaction date: ${transactionDate}, Balance update date: ${balanceUpdateDate}`);
            console.log(`[${requestId}] Updating balance from ${data.balance} to ${latestTransaction.new_balance}`);

            // Update the balance record
            try {
              const { data: updatedBalance, error: updateError } = await supabase
                .from('token_balance')
                .update({
                  balance: latestTransaction.new_balance,
                  updated_at: new Date().toISOString()
                })
                .eq('id', data.id)
                .select()
                .single();

              if (updateError) {
                console.error(`[${requestId}] Error updating balance from transaction:`, updateError);
              } else if (updatedBalance) {
                console.log(`[${requestId}] Successfully updated balance from transaction: ${updatedBalance.balance}`);
                data = updatedBalance; // Use the updated balance
              }
            } catch (updateError) {
              console.error(`[${requestId}] Unexpected error updating balance from transaction:`, updateError);
            }
          }
        }
      } catch (transactionError) {
        console.error(`[${requestId}] Unexpected error checking recent transactions:`, transactionError);
      }
    }

    // We're completely removing the automatic token addition
    // The Stripe webhook should be the only mechanism adding tokens for purchases

    // Log the current state for debugging
    const purchaseParam = req.query.purchase;
    console.log(`[${requestId}] Purchase parameter: ${purchaseParam}`);
    console.log(`[${requestId}] Current balance: ${data.balance}`);
    console.log(`[${requestId}] Data ID: ${data.id}`);
    console.log(`[${requestId}] Last updated: ${data.updated_at}`);

    // If a purchase parameter is detected, log it but don't add tokens
    if (purchaseParam === 'true' || purchaseParam === '1') {
      console.log(`[${requestId}] Purchase parameter detected, but not adding tokens automatically.`);
      console.log(`[${requestId}] Tokens should be added by the Stripe webhook only.`);
    }

    return res.json({
      ...data,
      requestId
    });
  } catch (error) {
    console.error(`[${requestId}] Unexpected error in token balance route:`, error);
    return res.status(500).json({
      error: 'Server error',
      details: error.message,
      requestId
    });
  }
});

module.exports = router;
