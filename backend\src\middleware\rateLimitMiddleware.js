const rateLimit = require('express-rate-limit');

/**
 * Rate limiting middleware factory
 * Creates different rate limiters based on configuration
 */
const createRateLimiter = (options = {}) => {
  // Default options
  const defaultOptions = {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per windowMs
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    message: 'Too many requests from this IP, please try again later',
    skipSuccessfulRequests: false, // Count all requests
    keyGenerator: (req) => {
      // Use IP address as default key
      return req.ip;
    }
  };

  // Merge default options with provided options
  const mergedOptions = { ...defaultOptions, ...options };

  // Create and return the rate limiter
  return rateLimit(mergedOptions);
};

/**
 * Global rate limiter for all requests
 * This provides basic protection against DoS attacks
 */
const globalLimiter = createRateLimiter({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: process.env.RATE_LIMIT_GLOBAL || 500, // 500 requests per 5 minutes by default
  message: 'Too many requests from this IP, please try again after a few minutes',
});

/**
 * API rate limiter for authenticated endpoints
 * More generous limits for authenticated users
 */
const apiLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: process.env.RATE_LIMIT_API || 200, // 200 requests per 15 minutes by default
  message: 'Too many API requests, please try again after a few minutes',
  // Skip rate limiting for admin users
  skip: (req) => {
    // Check if user is authenticated and is an admin
    return req.user && req.user.is_admin === true;
  },
  keyGenerator: (req) => {
    // Use user ID as key if authenticated, otherwise use IP
    return req.user ? `user_${req.user.id}` : req.ip;
  }
});

/**
 * Auth rate limiter for login/signup endpoints
 * Stricter limits to prevent brute force attacks
 */
const authLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: process.env.RATE_LIMIT_AUTH || 30, // 30 requests per hour by default
  message: 'Too many authentication attempts, please try again after an hour',
});

/**
 * Chat message rate limiter
 * Specific limits for chat message endpoints
 */
const chatLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: process.env.RATE_LIMIT_CHAT || 20, // 20 messages per minute by default
  message: 'You are sending messages too quickly. Please slow down.',
  // Skip rate limiting for admin users
  skip: (req) => {
    // Check if user is authenticated and is an admin
    return req.user && req.user.is_admin === true;
  },
  keyGenerator: (req) => {
    // Use user ID as key if authenticated, otherwise use IP
    return req.user ? `user_${req.user.id}` : req.ip;
  }
});

/**
 * Admin rate limiter
 * More generous limits for admin endpoints
 */
const adminLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: process.env.RATE_LIMIT_ADMIN || 300, // 300 requests per 15 minutes by default
  message: 'Too many admin API requests, please try again after a few minutes',
  // Only apply to non-admin users who somehow access admin routes
  skip: (req) => {
    // Skip rate limiting for actual admins
    return req.user && req.user.is_admin === true;
  }
});

/**
 * Socket rate limiter
 * Tracks message rates for socket connections
 */
const socketRateLimiter = {
  // Store rate limit data for each user/socket
  userLimits: new Map(),

  // Check if a user has exceeded their rate limit
  checkLimit: function(userId, socketId) {
    const key = userId || socketId;
    const now = Date.now();
    const windowMs = 60 * 1000; // 1 minute window
    const maxMessages = process.env.RATE_LIMIT_SOCKET || 20; // 20 messages per minute by default

    // Get or create user's rate limit data
    if (!this.userLimits.has(key)) {
      this.userLimits.set(key, {
        count: 0,
        resetTime: now + windowMs,
        blocked: false
      });
    }

    const userData = this.userLimits.get(key);

    // Reset count if window has expired
    if (now > userData.resetTime) {
      userData.count = 0;
      userData.resetTime = now + windowMs;
      userData.blocked = false;
    }

    // Check if user is blocked
    if (userData.blocked) {
      return {
        limited: true,
        message: 'You are sending messages too quickly. Please wait before sending more messages.',
        remainingMs: userData.resetTime - now
      };
    }

    // Increment count and check limit
    userData.count++;

    // If limit exceeded, block user for the remainder of the window
    if (userData.count > maxMessages) {
      userData.blocked = true;
      return {
        limited: true,
        message: 'You are sending messages too quickly. Please wait before sending more messages.',
        remainingMs: userData.resetTime - now
      };
    }

    return {
      limited: false,
      count: userData.count,
      limit: maxMessages,
      remainingMs: userData.resetTime - now
    };
  },

  // Reset rate limit for a user
  resetLimit: function(userId, socketId) {
    const key = userId || socketId;
    if (this.userLimits.has(key)) {
      this.userLimits.delete(key);
    }
  }
};

module.exports = {
  globalLimiter,
  apiLimiter,
  authLimiter,
  chatLimiter,
  adminLimiter,
  socketRateLimiter,
  createRateLimiter
};
