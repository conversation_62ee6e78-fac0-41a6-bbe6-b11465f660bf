'use client';

import React, { useState } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import { fetchWithAuth } from '../../../lib/authUtils';
import { toast } from 'react-hot-toast';

export default function TokenFallbackTestPage() {
  const { user, token } = useAuth();
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const runTest = async () => {
    if (!user || !token) {
      setError('You must be logged in to run this test');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
      const response = await fetchWithAuth(
        `${apiUrl}/api/test/token-fallback`,
        token,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({}),
        }
      );

      if (response.ok) {
        const data = await response.json();
        setResult(data);
        toast.success('Test completed successfully');
      } else {
        const errorData = await response.json();
        setError(`Error: ${errorData.error || 'Unknown error'}`);
        toast.error('Test failed');
      }
    } catch (err) {
      setError(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
      toast.error('Test failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-4">Token Fallback Test</h1>
      <p className="mb-4">
        This test will simulate using up your daily token limit and then verify that additional tokens are used for subsequent requests.
      </p>

      <div className="mb-6">
        <button
          onClick={runTest}
          disabled={loading}
          className={`px-4 py-2 rounded-md ${
            loading
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {loading ? 'Running Test...' : 'Run Test'}
        </button>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-100 border border-red-300 rounded-md text-red-700">
          {error}
        </div>
      )}

      {result && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Test Results</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="p-4 bg-gray-100 rounded-md">
              <h3 className="font-medium mb-2">Initial State</h3>
              <p><span className="font-medium">Daily Usage:</span> {result.initialState.dailyUsage.used.toLocaleString()} / {result.initialState.dailyUsage.limit.toLocaleString()} tokens ({result.initialState.dailyUsage.percentage.toFixed(2)}%)</p>
              <p><span className="font-medium">Daily Limit Exceeded:</span> {result.initialState.dailyUsage.exceeded ? '✓ Yes' : '✗ No'}</p>
              <p><span className="font-medium">Monthly Usage:</span> {result.initialState.monthlyUsage.used.toLocaleString()} / {result.initialState.monthlyUsage.limit.toLocaleString()} tokens ({result.initialState.monthlyUsage.percentage.toFixed(2)}%)</p>
              <p><span className="font-medium">Additional Token Balance:</span> {result.initialState.tokenBalance.toLocaleString()} tokens</p>
            </div>

            <div className="p-4 bg-gray-100 rounded-md">
              <h3 className="font-medium mb-2">After Setting Daily Limit</h3>
              <p><span className="font-medium">Daily Usage:</span> {result.afterSetLimit.dailyUsage.used.toLocaleString()} / {result.afterSetLimit.dailyUsage.limit.toLocaleString()} tokens ({result.afterSetLimit.dailyUsage.percentage.toFixed(2)}%)</p>
              <p><span className="font-medium">Daily Limit Exceeded:</span> {result.afterSetLimit.dailyUsage.exceeded ? '✓ Yes' : '✗ No'}</p>
              <p><span className="font-medium">Tokens Added:</span> {result.action.tokensAdded.toLocaleString()} tokens</p>
              <p><span className="font-medium">Test Request Size:</span> {result.action.tokensUsed.toLocaleString()} tokens</p>
            </div>

            <div className="p-4 bg-gray-100 rounded-md">
              <h3 className="font-medium mb-2">Final State</h3>
              <p><span className="font-medium">Daily Usage:</span> {result.updatedState.dailyUsage.used.toLocaleString()} / {result.updatedState.dailyUsage.limit.toLocaleString()} tokens ({result.updatedState.dailyUsage.percentage.toFixed(2)}%)</p>
              <p><span className="font-medium">Monthly Usage:</span> {result.updatedState.monthlyUsage.used.toLocaleString()} / {result.updatedState.monthlyUsage.limit.toLocaleString()} tokens ({result.updatedState.monthlyUsage.percentage.toFixed(2)}%)</p>
              <p><span className="font-medium">Additional Token Balance:</span> {result.updatedState.tokenBalance.toLocaleString()} tokens</p>
              <p className="mt-2 text-blue-600 font-medium">Tokens Used From Balance: {result.updatedState.tokenBalanceChange.toLocaleString()} tokens</p>
            </div>
          </div>

          <div className="p-4 bg-green-100 border border-green-300 rounded-md">
            <h3 className="font-medium mb-2">Test Summary</h3>
            <div className="mb-2">
              <p className="font-medium">Test Steps:</p>
              <ol className="list-decimal ml-5 mt-1">
                <li>Checked your current token usage and additional token balance</li>
                <li>Set your daily usage to the maximum limit ({result.action.setDailyUsageToLimit.toLocaleString()} tokens)</li>
                <li>Made a test request for {result.action.tokensUsed.toLocaleString()} tokens</li>
                <li>Verified if tokens were deducted from your additional balance</li>
              </ol>
            </div>

            <div className="mt-3 p-3 bg-white rounded-md">
              <p className="font-medium">
                {result.afterSetLimit.dailyUsage.exceeded && result.updatedState.tokenBalanceChange > 0
                  ? `✅ Success! The test confirmed that when your daily limit is exceeded, the system automatically uses your additional token balance.`
                  : result.afterSetLimit.dailyUsage.exceeded
                    ? '❌ Daily limit was exceeded, but no tokens were deducted from your additional balance. The test may have failed.'
                    : '❌ Daily limit was not successfully set to maximum. The test could not verify the fallback mechanism.'}
              </p>

              {result.updatedState.tokenBalanceChange > 0 && (
                <p className="mt-2">
                  <span className="font-medium">{result.updatedState.tokenBalanceChange.toLocaleString()} tokens</span> were deducted from your additional token balance.
                </p>
              )}

              {result.useTokensResult && result.useTokensResult.success && (
                <p className="mt-2 text-green-600">
                  Token usage transaction was successful.
                </p>
              )}

              {result.useTokensResult && !result.useTokensResult.success && (
                <p className="mt-2 text-red-600">
                  Token usage transaction failed: {result.useTokensResult.error}
                </p>
              )}
            </div>

            <p className="mt-3">
              <span className="font-medium">Operation ID:</span> {result.operationId}
            </p>
          </div>

          <div className="mt-4">
            <h3 className="font-medium mb-2">Raw Response</h3>
            <pre className="p-4 bg-gray-800 text-white rounded-md overflow-auto max-h-96">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
}
