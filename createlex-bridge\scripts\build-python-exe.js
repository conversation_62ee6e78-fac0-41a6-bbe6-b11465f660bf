const fs = require('fs-extra');
const path = require('path');
const { execSync } = require('child_process');

async function buildPythonExe() {
  const pythonDir = path.join(__dirname, '..', 'src', 'python');
  const distDir = path.join(__dirname, '..', 'src', 'python-dist');
  
  console.log('🔄 Building Python MCP server as executable...');
  
  // Clean dist directory
  await fs.remove(distDir);
  await fs.ensureDir(distDir);
  
  // Create a spec file for PyInstaller
  const specContent = `
# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['${path.join(pythonDir, 'mcp_server_protected.py').replace(/\\/g, '\\\\')}'],
    pathex=['${pythonDir.replace(/\\/g, '\\\\')}'],
    binaries=[],
    datas=[],
    hiddenimports=['subscription_validator', 'mcp_server_stdio', 'fastmcp'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='mcp_server',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
`;
  
  const specFile = path.join(distDir, 'mcp_server.spec');
  await fs.writeFile(specFile, specContent);
  
  try {
    // Install PyInstaller if not already installed
    console.log('Installing PyInstaller...');
    execSync('pip install pyinstaller', { stdio: 'inherit' });
    
    // Build the executable
    console.log('Building executable...');
    execSync(`pyinstaller --onefile --distpath "${distDir}" "${specFile}"`, {
      cwd: pythonDir,
      stdio: 'inherit'
    });
    
    console.log('✅ Python MCP server built as executable');
  } catch (error) {
    console.error('❌ Failed to build Python executable:', error);
    process.exit(1);
  }
}

buildPythonExe(); 