// CreateLex Bridge Bypass Starter
// Automatically starts MCP server without login or button clicking

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 CreateLex Bridge - Bypass Mode');
console.log('📍 Auto-starting MCP server without authentication');
console.log('🔗 Development Mode: BYPASS_LOGIN=true, AUTO_START_MCP=true');
console.log('');

// Set environment variables for bypass mode
process.env.NODE_ENV = 'development';
process.env.DEV_MODE = 'true';
process.env.BYPASS_SUBSCRIPTION = 'true';
process.env.BYPASS_LOGIN = 'true';
process.env.AUTO_START_MCP = 'true';
process.env.SKIP_AUTH = 'true';
process.env.CREATELEX_BASE_URL = 'http://localhost:3000';
process.env.API_BASE_URL = 'http://localhost:5001/api';

console.log('=== CreateLex Bridge Bypass Starting ===');
console.log('Environment variables:');
console.log('  NODE_ENV:', process.env.NODE_ENV);
console.log('  DEV_MODE:', process.env.DEV_MODE);
console.log('  BYPASS_SUBSCRIPTION:', process.env.BYPASS_SUBSCRIPTION);
console.log('  BYPASS_LOGIN:', process.env.BYPASS_LOGIN);
console.log('  AUTO_START_MCP:', process.env.AUTO_START_MCP);
console.log('  SKIP_AUTH:', process.env.SKIP_AUTH);
console.log('');

// Check if main.js exists
const mainPath = path.join(__dirname, 'main.js');
if (!fs.existsSync(mainPath)) {
    console.error('❌ ERROR: main.js not found!');
    console.error('   Expected at:', mainPath);
    process.exit(1);
}

// Start the Electron app with bypass flags
console.log('🔧 Starting Electron app in bypass mode...');

// Try multiple electron paths
const electronPaths = [
    path.join(__dirname, 'node_modules', '.bin', 'electron.cmd'),
    path.join(__dirname, 'node_modules', '.bin', 'electron'),
    path.join(__dirname, 'node_modules', 'electron', 'dist', 'electron.exe'),
    'electron.cmd',
    'electron'
];

let electronPath = null;
for (const ePath of electronPaths) {
    if (fs.existsSync(ePath)) {
        electronPath = ePath;
        console.log(`📍 Found Electron at: ${electronPath}`);
        break;
    }
}

if (!electronPath) {
    console.error('❌ ERROR: Electron not found!');
    console.error('   Tried paths:');
    electronPaths.forEach(p => console.error(`     ${p}`));
    console.error('   Run: npm install electron');
    process.exit(1);
}

// Spawn the Electron process
const electronProcess = spawn(electronPath, ['.'], {
    stdio: 'inherit',
    shell: true,  // Use shell to handle .cmd files on Windows
    env: {
        ...process.env,
        // Additional bypass flags
        FORCE_BYPASS: 'true',
        DEVELOPMENT_MODE: 'true',
        AUTO_LOGIN: 'true'
    }
});

// Handle process events
electronProcess.on('error', (error) => {
    console.error('❌ Failed to start Electron:', error);
    process.exit(1);
});

electronProcess.on('close', (code) => {
    console.log(`✅ CreateLex Bridge exited with code ${code}`);
    process.exit(code);
});

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n🛑 Received SIGINT, terminating...');
    electronProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Received SIGTERM, terminating...');
    electronProcess.kill('SIGTERM');
});

console.log('🎯 Bridge should start with automatic MCP server activation!');
console.log('📱 No login required, no button clicking needed');
console.log('🔧 Perfect for development and testing');
console.log('');