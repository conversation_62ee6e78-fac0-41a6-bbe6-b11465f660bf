"use client";

import Chat from "@/components/chat";
import { getUserId } from "@/lib/user-id";
import { useQueryClient } from "@tanstack/react-query";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useAuth } from '@/contexts/AuthContext';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';

export default function ChatPage() {
  const params = useParams();
  const chatId = params?.id as string;
  const queryClient = useQueryClient();
  const userId = getUserId();
  const router = useRouter();
  const { isAuthenticated, isLoading, hasActiveSubscription, refreshSubscriptionStatus } = useAuth();
  const { user: supabaseUser, isLoading: supabaseLoading } = useSupabaseAuth();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  // Check authentication and subscription
  useEffect(() => {
    const checkAccess = async () => {
      console.log('Chat ID page: Checking access', {
        isAuthenticated,
        isLoading,
        hasActiveSubscription,
        supabaseUser,
        supabaseLoading
      });

      // Wait for auth to finish loading
      if (isLoading || supabaseLoading) {
        return;
      }

      // Check if user is authenticated
      if (!isAuthenticated || !supabaseUser) {
        console.log('Chat ID page: User not authenticated, redirecting to login');
        router.push('/login');
        return;
      }

      // Refresh subscription status to ensure it's up to date
      try {
        const subscriptionData = await refreshSubscriptionStatus();
        console.log('Chat ID page: Refreshed subscription status:', subscriptionData);
      } catch (error) {
        console.error('Chat ID page: Error refreshing subscription status:', error);
      }

      // Check if user has an active subscription
      if (!hasActiveSubscription) {
        console.log('Chat ID page: User does not have an active subscription, redirecting to access-denied');
        router.push('/access-denied');
        return;
      }

      // User is authenticated and has an active subscription
      console.log('Chat ID page: User is authorized to access chat');
      setIsAuthorized(true);
      setIsCheckingAuth(false);
    };

    checkAccess();
  }, [isAuthenticated, isLoading, hasActiveSubscription, supabaseUser, supabaseLoading, router, refreshSubscriptionStatus]);

  // Prefetch chat data
  useEffect(() => {
    async function prefetchChat() {
      if (!chatId || !userId || !isAuthorized) return;

      // Check if data already exists in cache
      const existingData = queryClient.getQueryData(['chat', chatId, userId]);
      if (existingData) return;

      console.log('Chat ID page: Prefetching chat data', { chatId, userId });

      // Prefetch the data
      await queryClient.prefetchQuery({
        queryKey: ['chat', chatId, userId] as const,
        queryFn: async () => {
          try {
            const response = await fetch(`/api/chats/${chatId}`, {
              headers: {
                'x-user-id': userId
              }
            });

            if (!response.ok) {
              throw new Error('Failed to load chat');
            }

            return response.json();
          } catch (error) {
            console.error('Error prefetching chat:', error);
            return null;
          }
        },
        staleTime: 1000 * 60 * 5, // 5 minutes
      });
    }

    prefetchChat();
  }, [chatId, userId, queryClient, isAuthorized]);

  if (isCheckingAuth) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="spinner"></div>
      </div>
    );
  }

  if (!isAuthorized) {
    return null; // Will be redirected by the useEffect
  }

  return <Chat />;
}