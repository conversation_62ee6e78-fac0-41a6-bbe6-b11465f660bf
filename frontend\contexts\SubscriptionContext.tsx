import React, { createContext, useContext, useState, ReactNode } from 'react';
import { useAuth } from '../lib/context/auth-context';

interface SubscriptionContextType {
  isLoading: boolean;
  createCheckoutSession: () => Promise<string | null>;
}

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
};

interface SubscriptionProviderProps {
  children: ReactNode;
}

export const SubscriptionProvider: React.FC<SubscriptionProviderProps> = ({ children }) => {
  const [isLoading, setIsLoading] = useState(false);
  const { token } = useAuth();

  // Create a checkout session for subscription
  const createCheckoutSession = async (): Promise<string | null> => {
    if (!token) {
      console.error('Authentication required');
      return null;
    }

    try {
      setIsLoading(true);
      
      // Get the current URL for success and cancel URLs
      const origin = window.location.origin;
      const successUrl = `${origin}/subscription/success`;
      const cancelUrl = `${origin}/subscription/cancel`;
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/subscription/create-checkout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ successUrl, cancelUrl })
      });
      
      if (!response.ok) {
        throw new Error('Failed to create checkout session');
      }
      
      const data = await response.json();
      return data.url;
    } catch (error) {
      console.error('Error creating checkout session:', error);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const value = {
    isLoading,
    createCheckoutSession
  };

  return <SubscriptionContext.Provider value={value}>{children}</SubscriptionContext.Provider>;
};
