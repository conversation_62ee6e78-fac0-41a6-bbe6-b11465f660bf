import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../contexts/AuthContext';

/**
 * Component to manually process token purchases when Stripe webhooks can't reach the local server
 * This is used as a fallback mechanism for local development
 */
const TokenPurchaseProcessor = ({ purchase, packageId, userId, sessionId }) => {
  const router = useRouter();
  const { user } = useAuth();
  const [processing, setProcessing] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);
  const [tokens, setTokens] = useState(0);

  // Process the checkout session when the component mounts
  useEffect(() => {
    // Only process if this is a successful purchase
    if (purchase === 'success' && packageId && userId) {
      // Store the checkout session ID in localStorage if it's in the URL
      if (sessionId) {
        localStorage.setItem('lastCheckoutSessionId', sessionId);
      }

      // Process the checkout after a short delay to ensure the webhook has time to process
      setTimeout(() => {
        processCheckout();
      }, 2000);
    }
  }, [purchase, packageId, userId, sessionId]);

  // Function to process the checkout session
  const processCheckout = async () => {
    if (processing || success) return;

    setProcessing(true);
    setError(null);

    try {
      console.log('Processing checkout session manually');

      // Get the session ID from the URL or localStorage
      const checkoutSessionId = sessionId || localStorage.getItem('lastCheckoutSessionId');

      if (!checkoutSessionId) {
        throw new Error('No checkout session ID found');
      }

      console.log(`Using checkout session ID: ${checkoutSessionId}`);
      console.log(`User ID: ${userId || user?.id}`);

      // First try to use the simulate-webhook endpoint which is more reliable for local development
      try {
        console.log('Trying to use simulate-webhook endpoint first');
        const simulateResponse = await fetch('/api/tokens/simulate-webhook', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId: userId || user?.id,
            packageId: packageId || 'small'
          })
        });

        if (simulateResponse.ok) {
          const simulateData = await simulateResponse.json();
          console.log('Simulate webhook successful:', simulateData);

          // Set success state
          setSuccess(true);
          setTokens(simulateData.result?.transaction?.amount || 100000);

          // Store purchase success in sessionStorage
          sessionStorage.setItem('tokenPurchaseSuccess', 'true');

          // Clear the checkout session ID from localStorage
          localStorage.removeItem('lastCheckoutSessionId');

          // Dispatch event to refresh token balance
          window.dispatchEvent(new CustomEvent('refreshTokenBalance', {
            detail: {
              force: true,
              isPurchase: true
            }
          }));

          // Redirect to dashboard after a delay
          setTimeout(() => {
            router.push('/dashboard');
          }, 3000);

          return;
        } else {
          console.log('Simulate webhook failed, falling back to process-checkout');
        }
      } catch (simulateError) {
        console.error('Error with simulate-webhook, falling back to process-checkout:', simulateError);
      }

      // Fall back to the process-checkout endpoint
      console.log('Calling process-checkout endpoint');
      const response = await fetch('/api/tokens/process-checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: userId || user?.id,
          sessionId: checkoutSessionId
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to process checkout');
      }

      console.log('Checkout processed successfully:', data);

      // Set success state
      setSuccess(true);
      setTokens(data.tokens || 0);

      // Store purchase success in sessionStorage
      sessionStorage.setItem('tokenPurchaseSuccess', 'true');

      // Clear the checkout session ID from localStorage
      localStorage.removeItem('lastCheckoutSessionId');

      // Dispatch event to refresh token balance
      window.dispatchEvent(new CustomEvent('refreshTokenBalance', {
        detail: {
          force: true,
          isPurchase: true
        }
      }));

      // Redirect to dashboard after a delay
      setTimeout(() => {
        router.push('/dashboard');
      }, 3000);
    } catch (error) {
      console.error('Error processing checkout:', error);
      setError(error.message);

      // Even if there's an error, we'll still set the purchase success flag
      // This is because the webhook might have processed the purchase successfully
      // even if our manual process failed
      sessionStorage.setItem('tokenPurchaseSuccess', 'true');
    } finally {
      setProcessing(false);
    }
  };

  // If not a successful purchase, don't render anything
  if (purchase !== 'success') {
    return null;
  }

  return (
    <div className="token-purchase-processor">
      <div className="card">
        <div className="card-body">
          <h3 className="card-title">Processing Your Purchase</h3>

          {processing && (
            <div className="text-center my-4">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
              <p className="mt-2">Processing your token purchase...</p>
            </div>
          )}

          {error && (
            <div className="alert alert-danger" role="alert">
              <p><strong>Error:</strong> {error}</p>
              <p>Don't worry! Your purchase may still be processing. Please check your token balance in a few minutes.</p>
            </div>
          )}

          {success && (
            <div className="alert alert-success" role="alert">
              <h4 className="alert-heading">Purchase Successful!</h4>
              <p>You have successfully purchased {tokens.toLocaleString()} tokens.</p>
              <p>Redirecting to dashboard...</p>
            </div>
          )}

          {!processing && !success && (
            <button
              className="btn btn-primary"
              onClick={processCheckout}
              disabled={processing}
            >
              Process Purchase
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default TokenPurchaseProcessor;
