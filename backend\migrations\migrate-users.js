require('dotenv').config();
const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: SUPABASE_URL and SUPABASE_SERVICE_KEY must be set in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Load users from file
async function migrateUsers() {
  try {
    const usersFile = path.join(__dirname, '../data/users.json');

    if (!fs.existsSync(usersFile)) {
      console.error(`Error: Users file not found at ${usersFile}`);
      process.exit(1);
    }

    const userData = JSON.parse(fs.readFileSync(usersFile, 'utf8'));

    if (!userData || Object.keys(userData).length === 0) {
      console.log('No users found in file. Nothing to migrate.');
      process.exit(0);
    }

    console.log(`Found ${Object.keys(userData).length} users to migrate`);

    // Convert users to Supabase format
    const supabaseUsers = Object.values(userData).map(user => ({
      id: user.id,
      email: user.email,
      name: user.name,
      picture: user.picture,
      subscription_status: user.subscriptionStatus || 'inactive',
      stripe_customer_id: user.stripeCustomerId,
      subscription_id: user.subscriptionId,
      created_at: user.createdAt || new Date().toISOString(),
      subscription_updated_at: user.subscriptionUpdatedAt,
      last_checkout_session_id: user.lastCheckoutSessionId
    }));

    // Insert users into Supabase
    try {
      console.log('Attempting to insert users into Supabase...');
      console.log('First user data:', JSON.stringify(supabaseUsers[0], null, 2));

      const { data, error } = await supabase
        .from('users')
        .upsert(supabaseUsers);

      if (error) {
        console.error('Error migrating users:', error);
        console.error('Error details:', JSON.stringify(error, null, 2));
        process.exit(1);
      } else {
        console.log(`Successfully migrated ${supabaseUsers.length} users to Supabase`);

        // Create backup of users file
        const backupFile = path.join(__dirname, '../data/users.json.bak');
        fs.copyFileSync(usersFile, backupFile);
        console.log(`Created backup of users file at ${backupFile}`);

        process.exit(0);
      }
    } catch (insertError) {
      console.error('Exception during user insertion:', insertError);
      console.error('Stack trace:', insertError.stack);
      process.exit(1);
    }
  } catch (error) {
    console.error('Unexpected error during migration:', error);
    process.exit(1);
  }
}

// Run the migration
migrateUsers();
