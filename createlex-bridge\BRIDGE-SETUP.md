# CreateLex Universal Bridge Setup

This guide shows how to configure the `bridge-native.js` script with different AI coding assistants to access 21+ Unreal Engine tools.

## Prerequisites

1. **Install CreateLex Bridge App** - Download from [createlex.com/download](https://createlex.com/download) and run the native bridge application
2. **Start MCP Server** - Click "Start MCP Server" in the bridge app dashboard
3. **Node.js** - Ensure Node.js is installed on your system

## Configuration by AI Assistant

> **📁 Standard Path**: The bridge script is located at `[ProjectName]/Plugins/UnrealGenAISupport_with_server/Content/Tools/bridge-native.js` in your Unreal Engine project. Replace `[ProjectName]` with your actual project path (e.g., `C:/Dev/MyGame`).

### 1. <PERSON> Desktop

Add to your `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "createlex-unreal": {
      "command": "node",
      "args": ["[ProjectName]/Plugins/UnrealGenAISupport_with_server/Content/Tools/bridge-native.js"]
    }
  }
}
```

> **Note**: Replace `[ProjectName]` with the full path to your Unreal Engine project directory.

**Location:**
- Windows: `%APPDATA%\Claude\claude_desktop_config.json`
- macOS: `~/Library/Application Support/Claude/claude_desktop_config.json`

### 2. Cursor

Add to your Cursor settings (`settings.json`):

```json
{
  "mcp.servers": {
    "createlex-unreal": {
      "command": "node",
      "args": ["[ProjectName]/Plugins/UnrealGenAISupport_with_server/Content/Tools/bridge-native.js"]
    }
  }
}
```

**Access:** Cursor → Settings → Extensions → MCP

### 3. Windsurf

Add to your Windsurf configuration:

```json
{
  "mcp": {
    "servers": {
      "createlex-unreal": {
        "command": "node",
        "args": ["[ProjectName]/Plugins/UnrealGenAISupport_with_server/Content/Tools/bridge-native.js"]
      }
    }
  }
}
```

### 4. VS Code (with MCP Extension)

If using an MCP extension in VS Code:

```json
{
  "mcp.servers": [
    {
      "name": "createlex-unreal",
      "command": "node",
      "args": ["[ProjectName]/Plugins/UnrealGenAISupport_with_server/Content/Tools/bridge-native.js"]
    }
  ]
}
```

### 5. Generic MCP Client

For any MCP-compatible tool:

```json
{
  "servers": {
    "createlex-unreal": {
      "command": "node",
      "args": ["[ProjectName]/Plugins/UnrealGenAISupport_with_server/Content/Tools/bridge-native.js"]
    }
  }
}
```

## Available Tools

Once configured, your AI assistant will have access to these Unreal Engine tools:

### Project Management
- `create_unreal_project` - Create new Unreal projects
- `open_unreal_project` - Open existing projects
- `get_project_info` - Get project details

### Asset Management
- `import_asset` - Import 3D models, textures, audio
- `create_material` - Create and configure materials
- `create_blueprint` - Create Blueprint classes

### Level Design
- `create_level` - Create new levels/maps
- `place_actor` - Place actors in the level
- `set_actor_transform` - Position, rotate, scale actors

### Animation & Rigging
- `import_skeletal_mesh` - Import rigged characters
- `create_animation_blueprint` - Create animation logic
- `setup_character_controller` - Configure character movement

### Lighting & Rendering
- `setup_lighting` - Configure scene lighting
- `create_post_process_volume` - Add visual effects
- `configure_render_settings` - Optimize rendering

### Gameplay Programming
- `create_game_mode` - Set up game rules
- `create_player_controller` - Handle player input
- `setup_ui_widget` - Create user interfaces

### And many more...

## Troubleshooting

### Connection Issues

If you see connection errors:

1. **Check Bridge App** - Ensure CreateLex Bridge app is running
2. **Start MCP Server** - Click "Start MCP Server" in the dashboard
3. **Port Conflicts** - Make sure port 9877 is available
4. **Firewall** - Allow the bridge app through Windows Firewall

### Path Issues

Update the path in your configuration to match your installation:

```bash
# Find your bridge script location
where node
# Then use the full path to bridge-native.js
```

### Logs

The bridge script provides detailed logging:

```
[12:34:56] [CreateLex Bridge] [Claude Desktop] ✅ Connected successfully! Claude Desktop can now use Unreal Engine tools
```

## Support

- **Support Center**: [createlex.com/support](https://createlex.com/support)
- **Documentation**: [createlex.com/docs](https://createlex.com/docs)
- **Contact Us**: [<EMAIL>](mailto:<EMAIL>)
- **Live Chat**: Available on [createlex.com](https://createlex.com)

## License

This bridge script is part of the CreateLex platform and requires an active subscription to use with Unreal Engine tools. Visit [createlex.com/pricing](https://createlex.com/pricing) for subscription options. 