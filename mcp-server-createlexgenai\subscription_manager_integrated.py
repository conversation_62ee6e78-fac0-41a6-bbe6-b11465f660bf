#!/usr/bin/env python3
"""
UnrealGenAI Integrated Subscription-Protected MCP Server
Uses your existing backend API endpoints for subscription verification
"""

import sys
import os
import json
import time
import requests
import importlib.util
from pathlib import Path
from typing import Tuple, Dict, Any, Optional
import threading
import signal
import jwt
from datetime import datetime, timedelta

class IntegratedSubscriptionManager:
    def __init__(self):
        # Use your existing backend API endpoints
        self.api_base_url = os.getenv('API_BASE_URL', 'http://localhost:5001')
        self.subscription_check_endpoint = f'{self.api_base_url}/api/subscription/check'
        self.subscription_status_endpoint = f'{self.api_base_url}/api/subscription/status'
        self.subscription_premium_endpoint = f'{self.api_base_url}/api/subscription/isPremium'
        
        # JWT token for authentication (provided by your existing auth system)
        self.jwt_token_file = Path('/app/config/auth_token.jwt')
        self.subscription_cache_file = Path('/app/config/subscription_cache.json')
        
        # Configuration
        self.check_interval = int(os.getenv('SUBSCRIPTION_CHECK_INTERVAL', '3600'))  # 1 hour
        self.running = True
        self.always_subscribed = os.getenv('ALWAYS_SUBSCRIBED', 'false').lower() == 'true'
        
        # Cache for performance
        self._auth_headers = None
        self._last_token_load = 0
        
    def load_auth_token(self) -> Optional[str]:
        """Load JWT token from secure location"""
        try:
            if self.jwt_token_file.exists():
                # Check if we need to reload the token (cache for 5 minutes)
                current_time = time.time()
                if self._auth_headers and (current_time - self._last_token_load) < 300:
                    return self._auth_headers.get('Authorization', '').replace('Bearer ', '')
                
                token = self.jwt_token_file.read_text().strip()
                
                # Validate JWT token structure
                try:
                    decoded = jwt.decode(token, options={"verify_signature": False})
                    print(f"JWT token loaded for user: {decoded.get('id', 'unknown')}", file=sys.stderr)
                    
                    # Cache the headers
                    self._auth_headers = {
                        'Authorization': f'Bearer {token}',
                        'Content-Type': 'application/json',
                        'User-Agent': 'UnrealGenAI-Docker/1.0'
                    }
                    self._last_token_load = current_time
                    
                    return token
                except jwt.InvalidTokenError as e:
                    print(f"Invalid JWT token format: {e}", file=sys.stderr)
                    return None
                    
            else:
                print("ERROR: No auth token found. Mount auth_token.jwt to /app/config/", file=sys.stderr)
                return None
                
        except Exception as e:
            print(f"ERROR: Failed to load auth token: {e}", file=sys.stderr)
            return None
    
    def verify_subscription_with_backend(self, token: str) -> Tuple[bool, Dict[str, Any]]:
        """Verify subscription using your existing backend API"""
        try:
            # Development override
            if self.always_subscribed:
                print("Development mode: ALWAYS_SUBSCRIBED is true", file=sys.stderr)
                return True, {
                    'hasActiveSubscription': True,
                    'source': 'development_override',
                    'userId': 'dev-user',
                    'plan': 'pro'
                }
            
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json',
                'User-Agent': 'UnrealGenAI-Docker/1.0'
            }
            
            # Try the main subscription check endpoint first
            response = requests.get(
                self.subscription_check_endpoint,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"Subscription check response: {data}", file=sys.stderr)
                
                return data.get('hasActiveSubscription', False), data
            
            # Fallback to subscription status endpoint
            elif response.status_code == 401:
                print("Authentication failed, token may be expired", file=sys.stderr)
                return False, {'error': 'authentication_failed'}
            
            else:
                print(f"Subscription check failed: HTTP {response.status_code}", file=sys.stderr)
                
                # Try the status endpoint as fallback
                status_response = requests.get(
                    self.subscription_status_endpoint,
                    headers=headers,
                    timeout=30
                )
                
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    return status_data.get('hasActiveSubscription', False), status_data
                
                return False, {'error': f'http_error_{response.status_code}'}
                
        except requests.RequestException as e:
            print(f"Network error during subscription verification: {e}", file=sys.stderr)
            return False, {'error': 'network_error'}
        except Exception as e:
            print(f"Error verifying subscription: {e}", file=sys.stderr)
            return False, {'error': 'unknown_error'}
    
    def get_premium_status(self, token: str) -> Dict[str, Any]:
        """Get premium subscription details from your backend"""
        try:
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json',
                'User-Agent': 'UnrealGenAI-Docker/1.0'
            }
            
            response = requests.get(
                self.subscription_premium_endpoint,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Premium status check failed: HTTP {response.status_code}", file=sys.stderr)
                return {}
                
        except Exception as e:
            print(f"Error getting premium status: {e}", file=sys.stderr)
            return {}
    
    def cache_subscription_status(self, valid: bool, data: Dict[str, Any]):
        """Cache subscription status for offline verification"""
        try:
            cache_data = {
                'valid': valid,
                'cached_at': time.time(),
                'expires_at': time.time() + 3600,  # Cache for 1 hour
                'user_info': {
                    'id': data.get('userId', 'unknown'),
                    'email': data.get('userEmail', 'unknown')
                },
                'subscription_data': data,
                'plan': data.get('plan', 'basic'),
                'limits': {
                    'daily_usage': data.get('dailyUsage', {}),
                    'monthly_usage': data.get('monthlyUsage', {})
                }
            }
            
            self.subscription_cache_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.subscription_cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2)
                
            print(f"Subscription status cached for user: {cache_data['user_info']['id']}", file=sys.stderr)
                
        except Exception as e:
            print(f"Warning: Failed to cache subscription status: {e}", file=sys.stderr)
    
    def load_cached_subscription(self) -> Tuple[bool, Dict[str, Any]]:
        """Load cached subscription status for offline mode"""
        try:
            if not self.subscription_cache_file.exists():
                return False, {}
            
            with open(self.subscription_cache_file, 'r') as f:
                data = json.load(f)
            
            # Check if cache is still valid
            if time.time() > data.get('expires_at', 0):
                print("Cached subscription expired", file=sys.stderr)
                return False, {}
            
            print(f"Using cached subscription for user: {data.get('user_info', {}).get('id', 'unknown')}", file=sys.stderr)
            return data.get('valid', False), data.get('subscription_data', {})
            
        except Exception as e:
            print(f"Error loading cached subscription: {e}", file=sys.stderr)
            return False, {}
    
    def check_subscription_status(self) -> Tuple[bool, str, Dict[str, Any]]:
        """Main subscription verification logic using your existing backend"""
        token = self.load_auth_token()
        if not token:
            return False, "No authentication token found", {}
        
        # Try backend verification first
        valid, data = self.verify_subscription_with_backend(token)
        if valid:
            self.cache_subscription_status(valid, data)
            user_id = data.get('userId', 'Unknown')
            plan = data.get('plan', 'basic')
            return True, f"Active subscription verified for user {user_id} (plan: {plan})", data
        
        # If backend verification failed due to network, try cached subscription
        if data.get('error') in ['network_error', 'timeout']:
            print("Backend verification failed, checking cached subscription...", file=sys.stderr)
            valid, cached_data = self.load_cached_subscription()
            if valid:
                user_id = cached_data.get('userId', 'Unknown')
                return True, f"Cached subscription valid for user {user_id}", cached_data
        
        return False, "No valid subscription found", data
    
    def load_protected_mcp_server(self):
        """Load the protected MCP server module"""
        try:
            # Import the protected server module
            protected_dir = Path('/app/protected')
            sys.path.insert(0, str(protected_dir))
            
            # Try to import compiled modules first (most secure)
            for module_file in ['mcp_server.pyc', 'mcp_server_protected.pyc']:
                module_path = protected_dir / module_file
                if module_path.exists():
                    spec = importlib.util.spec_from_file_location("protected_mcp_server", str(module_path))
                    if spec and spec.loader:
                        module = importlib.util.module_from_spec(spec)
                        sys.modules["protected_mcp_server"] = module
                        spec.loader.exec_module(module)
                        print(f"Loaded protected module: {module_file}", file=sys.stderr)
                        return module
            
            # Fallback to source files (less secure but functional)
            for module_file in ['mcp_server.py', 'server.py']:
                module_path = protected_dir / module_file
                if module_path.exists():
                    spec = importlib.util.spec_from_file_location("protected_mcp_server", str(module_path))
                    if spec and spec.loader:
                        module = importlib.util.module_from_spec(spec)
                        sys.modules["protected_mcp_server"] = module
                        spec.loader.exec_module(module)
                        print(f"Loaded source module: {module_file}", file=sys.stderr)
                        return module
            
            raise ImportError("No protected MCP server module found")
            
        except Exception as e:
            print(f"ERROR: Failed to load protected MCP server: {e}", file=sys.stderr)
            return None
    
    def start_periodic_subscription_check(self):
        """Start background thread for periodic subscription verification"""
        def check_periodically():
            while self.running:
                time.sleep(self.check_interval)
                if not self.running:
                    break
                
                print("Performing periodic subscription check...", file=sys.stderr)
                valid, message, data = self.check_subscription_status()
                if not valid:
                    print(f"CRITICAL: Subscription check failed: {message}", file=sys.stderr)
                    
                    # If it's a network error, give it another try
                    if data.get('error') == 'network_error':
                        print("Network error detected, will retry on next check", file=sys.stderr)
                    else:
                        print("Shutting down MCP server due to invalid subscription...", file=sys.stderr)
                        os._exit(1)
                else:
                    print(f"Subscription check passed: {message}", file=sys.stderr)
        
        thread = threading.Thread(target=check_periodically, daemon=True)
        thread.start()
        return thread
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        print(f"Received signal {signum}, shutting down...", file=sys.stderr)
        self.running = False
        sys.exit(0)
    
    def create_health_endpoint(self, subscription_data: Dict[str, Any]):
        """Create a simple health check endpoint"""
        try:
            import http.server
            import socketserver
            import urllib.parse
            
            class HealthHandler(http.server.BaseHTTPRequestHandler):
                def do_GET(self):
                    if self.path == '/health':
                        self.send_response(200)
                        self.send_header('Content-type', 'application/json')
                        self.end_headers()
                        
                        health_data = {
                            'status': 'healthy',
                            'subscription_active': subscription_data.get('hasActiveSubscription', False),
                            'user_id': subscription_data.get('userId', 'unknown'),
                            'plan': subscription_data.get('plan', 'basic'),
                            'timestamp': datetime.now().isoformat()
                        }
                        
                        self.wfile.write(json.dumps(health_data).encode())
                    else:
                        self.send_response(404)
                        self.end_headers()
                
                def log_message(self, format, *args):
                    # Suppress HTTP server logs
                    pass
            
            port = int(os.getenv('HEALTH_PORT', '8001'))
            with socketserver.TCPServer(("", port), HealthHandler) as httpd:
                print(f"Health check server running on port {port}", file=sys.stderr)
                httpd.serve_forever()
                
        except Exception as e:
            print(f"Failed to start health check server: {e}", file=sys.stderr)
    
    def main(self):
        """Main entry point"""
        # Set up signal handlers
        signal.signal(signal.SIGTERM, self.signal_handler)
        signal.signal(signal.SIGINT, self.signal_handler)
        
        print("UnrealGenAI Integrated Subscription-Protected MCP Server", file=sys.stderr)
        print("Using existing backend API endpoints for subscription verification", file=sys.stderr)
        print("=" * 60, file=sys.stderr)
        
        # Initial subscription check
        valid, message, subscription_data = self.check_subscription_status()
        print(f"Subscription Status: {message}", file=sys.stderr)
        
        if not valid:
            print("ERROR: MCP Server cannot start without valid subscription", file=sys.stderr)
            print("", file=sys.stderr)
            print("To use this MCP server:", file=sys.stderr)
            print("1. Ensure you have a valid authentication token from your dashboard", file=sys.stderr)
            print("2. Mount your auth_token.jwt file to /app/config/auth_token.jwt", file=sys.stderr)
            print("3. Verify your backend API is accessible", file=sys.stderr)
            print("4. Check your subscription status in the dashboard", file=sys.stderr)
            print("5. Restart the container", file=sys.stderr)
            sys.exit(1)
        
        print("SUCCESS: Subscription verified, loading protected MCP server...", file=sys.stderr)
        
        # Start periodic subscription checks
        self.start_periodic_subscription_check()
        
        # Start health check server in background
        health_thread = threading.Thread(
            target=self.create_health_endpoint, 
            args=(subscription_data,), 
            daemon=True
        )
        health_thread.start()
        
        # Load and start the protected MCP server
        server_module = self.load_protected_mcp_server()
        if not server_module:
            print("ERROR: Failed to load protected MCP server module", file=sys.stderr)
            sys.exit(1)
        
        print("SUCCESS: Protected MCP server loaded, starting service...", file=sys.stderr)
        print(f"Health check available at: http://localhost:{os.getenv('HEALTH_PORT', '8001')}/health", file=sys.stderr)
        
        # Pass subscription data to the server
        if hasattr(server_module, 'start_server'):
            server_module.start_server(subscription_data)
        elif hasattr(server_module, 'main'):
            server_module.main()
        else:
            print("ERROR: Protected MCP server module missing entry point", file=sys.stderr)
            sys.exit(1)

if __name__ == "__main__":
    manager = IntegratedSubscriptionManager()
    manager.main() 