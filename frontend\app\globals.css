@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Unreal Engine 5 Light Theme Inspired */
  --background: #F5F5F5; /* Light background */
  --foreground: #333333; /* Dark text */
  --card: #FFFFFF; /* White card background */
  --card-foreground: #333333; /* Dark text */
  --popover: #FFFFFF; /* White popover background */
  --popover-foreground: #333333; /* Dark text */
  --primary: #2573CE; /* UE5 darker blue */
  --primary-foreground: #FFFFFF; /* White text on primary */
  --secondary: #4CC3FF; /* UE5 light blue */
  --secondary-foreground: #000000; /* Black text on secondary */
  --muted: #E5E5E5; /* Light muted background */
  --muted-foreground: #666666; /* Muted text */
  --accent: #4CC3FF; /* UE5 light blue accent */
  --accent-foreground: #000000; /* Black text on accent */
  --destructive: #E74C3C; /* Red for destructive actions */
  --destructive-foreground: #FFFFFF; /* White text on destructive */
  --border: #E0E0E0; /* Light border */
  --input: #F0F0F0; /* Light input background */
  --ring: #2573CE; /* UE5 darker blue for focus rings */
  --chart-1: #2573CE; /* UE5 darker blue */
  --chart-2: #4CC3FF; /* UE5 light blue */
  --chart-3: #3498DB; /* Medium blue */
  --chart-4: #1ABC9C; /* Teal */
  --chart-5: #F1C40F; /* Yellow */
  --sidebar: #F0F0F0; /* Light sidebar background */
  --sidebar-foreground: #333333; /* Dark text */
  --sidebar-primary: #2573CE; /* UE5 darker blue */
  --sidebar-primary-foreground: #FFFFFF; /* White text on primary */
  --sidebar-accent: #4CC3FF; /* UE5 light blue */
  --sidebar-accent-foreground: #000000; /* Black text on accent */
  --sidebar-border: #E0E0E0; /* Light border */
  --sidebar-ring: #2573CE; /* UE5 darker blue for focus rings */
  --font-sans: Montserrat, sans-serif;
  --font-serif: Merriweather, serif;
  --font-mono: Ubuntu Mono, monospace;
  --radius: 0.625rem;
  --radius-sm: 0.375rem;
  --radius-md: 0.625rem;
  --radius-lg: 0.875rem;
  --radius-xl: 1.125rem;
  --radius-2xl: 1.5rem;
  --radius-button: 0.875rem;
  --shadow-2xs: 0px 6px 12px -3px hsl(0 0% 0% / 0.04);
  --shadow-xs: 0px 6px 12px -3px hsl(0 0% 0% / 0.04);
  --shadow-sm: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 1px 2px -4px hsl(0 0% 0% / 0.09);
  --shadow: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 1px 2px -4px hsl(0 0% 0% / 0.09);
  --shadow-md: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 2px 4px -4px hsl(0 0% 0% / 0.09);
  --shadow-lg: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 4px 6px -4px hsl(0 0% 0% / 0.09);
  --shadow-xl: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 8px 10px -4px hsl(0 0% 0% / 0.09);
  --shadow-2xl: 0px 6px 12px -3px hsl(0 0% 0% / 0.22);
}

.dark {
  /* Unreal Engine 5 Editor Colors */
  --background: #1B1B1B; /* UE5 dark background */
  --foreground: #CCCCCC; /* UE5 light text */
  --card: #2A2A2A; /* UE5 panel background */
  --card-foreground: #CCCCCC; /* UE5 light text */
  --popover: #2A2A2A; /* UE5 panel background */
  --popover-foreground: #CCCCCC; /* UE5 light text */
  --primary: #4CC3FF; /* UE5 light blue accent */
  --primary-foreground: #000000; /* Black text on light blue */
  --secondary: #2573CE; /* UE5 darker blue */
  --secondary-foreground: #FFFFFF; /* White text on darker blue */
  --muted: #333333; /* UE5 muted background */
  --muted-foreground: #999999; /* UE5 muted text */
  --accent: #4CC3FF; /* UE5 light blue accent */
  --accent-foreground: #000000; /* Black text on accent */
  --destructive: #E74C3C; /* Red for destructive actions */
  --destructive-foreground: #FFFFFF; /* White text on destructive */
  --border: #333333; /* UE5 border color */
  --input: #333333; /* UE5 input background */
  --ring: #4CC3FF; /* UE5 light blue for focus rings */
  --chart-1: #4CC3FF; /* UE5 light blue */
  --chart-2: #2573CE; /* UE5 darker blue */
  --chart-3: #3498DB; /* Medium blue */
  --chart-4: #1ABC9C; /* Teal */
  --chart-5: #F1C40F; /* Yellow */
  --sidebar: #1B1B1B; /* UE5 dark background */
  --sidebar-foreground: #CCCCCC; /* UE5 light text */
  --sidebar-primary: #4CC3FF; /* UE5 light blue accent */
  --sidebar-primary-foreground: #000000; /* Black text on light blue */
  --sidebar-accent: #2573CE; /* UE5 darker blue */
  --sidebar-accent-foreground: #FFFFFF; /* White text on darker blue */
  --sidebar-border: #333333; /* UE5 border color */
  --sidebar-ring: #4CC3FF; /* UE5 light blue for focus rings */
  --font-sans: Montserrat, sans-serif;
  --font-serif: Merriweather, serif;
  --font-mono: Ubuntu Mono, monospace;
  --radius: 0.625rem;
  --radius-sm: 0.375rem;
  --radius-md: 0.625rem;
  --radius-lg: 0.875rem;
  --radius-xl: 1.125rem;
  --radius-2xl: 1.5rem;
  --radius-button: 0.875rem;
  --shadow-2xs: 0px 6px 12px -3px hsl(0 0% 0% / 0.04);
  --shadow-xs: 0px 6px 12px -3px hsl(0 0% 0% / 0.04);
  --shadow-sm: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 1px 2px -4px hsl(0 0% 0% / 0.09);
  --shadow: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 1px 2px -4px hsl(0 0% 0% / 0.09);
  --shadow-md: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 2px 4px -4px hsl(0 0% 0% / 0.09);
  --shadow-lg: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 4px 6px -4px hsl(0 0% 0% / 0.09);
  --shadow-xl: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 8px 10px -4px hsl(0 0% 0% / 0.09);
  --shadow-2xl: 0px 6px 12px -3px hsl(0 0% 0% / 0.22);
}

.sunset {
  /* Unreal Engine 5 Blueprint Editor Theme */
  --background: #252525; /* UE5 blueprint background */
  --foreground: #CCCCCC; /* UE5 light text */
  --card: #303030; /* UE5 blueprint panel background */
  --card-foreground: #CCCCCC; /* UE5 light text */
  --popover: #303030; /* UE5 blueprint panel background */
  --popover-foreground: #CCCCCC; /* UE5 light text */
  --primary: #3BA9FF; /* UE5 blueprint blue */
  --primary-foreground: #000000; /* Black text on blueprint blue */
  --secondary: #1E5D8C; /* UE5 blueprint darker blue */
  --secondary-foreground: #FFFFFF; /* White text on darker blue */
  --muted: #353535; /* UE5 blueprint muted background */
  --muted-foreground: #999999; /* UE5 blueprint muted text */
  --accent: #00A2FF; /* UE5 blueprint accent */
  --accent-foreground: #000000; /* Black text on accent */
  --destructive: #E74C3C; /* Red for destructive actions */
  --destructive-foreground: #FFFFFF; /* White text on destructive */
  --border: #404040; /* UE5 blueprint border color */
  --input: #353535; /* UE5 blueprint input background */
  --ring: #3BA9FF; /* UE5 blueprint blue for focus rings */
  --chart-1: #3BA9FF; /* UE5 blueprint blue */
  --chart-2: #00A2FF; /* UE5 blueprint accent */
  --chart-3: #1E5D8C; /* UE5 blueprint darker blue */
  --chart-4: #00FFFF; /* Cyan for execution pins */
  --chart-5: #FFA500; /* Orange for flow control */
  --sidebar: #252525; /* UE5 blueprint background */
  --sidebar-foreground: #CCCCCC; /* UE5 light text */
  --sidebar-primary: #3BA9FF; /* UE5 blueprint blue */
  --sidebar-primary-foreground: #000000; /* Black text on blueprint blue */
  --sidebar-accent: #00A2FF; /* UE5 blueprint accent */
  --sidebar-accent-foreground: #000000; /* Black text on accent */
  --sidebar-border: #404040; /* UE5 blueprint border color */
  --sidebar-ring: #3BA9FF; /* UE5 blueprint blue for focus rings */
  --font-sans: Montserrat, sans-serif;
  --font-serif: Merriweather, serif;
  --font-mono: Ubuntu Mono, monospace;
  --radius: 0.625rem;
  --radius-sm: 0.375rem;
  --radius-md: 0.625rem;
  --radius-lg: 0.875rem;
  --radius-xl: 1.125rem;
  --radius-2xl: 1.5rem;
  --radius-button: 0.875rem;
  --shadow-2xs: 0px 6px 12px -3px hsl(0 0% 0% / 0.04);
  --shadow-xs: 0px 6px 12px -3px hsl(0 0% 0% / 0.04);
  --shadow-sm: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 1px 2px -4px hsl(0 0% 0% / 0.09);
  --shadow: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 1px 2px -4px hsl(0 0% 0% / 0.09);
  --shadow-md: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 2px 4px -4px hsl(0 0% 0% / 0.09);
  --shadow-lg: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 4px 6px -4px hsl(0 0% 0% / 0.09);
  --shadow-xl: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 8px 10px -4px hsl(0 0% 0% / 0.09);
  --shadow-2xl: 0px 6px 12px -3px hsl(0 0% 0% / 0.22);
}

.black {
  /* Unreal Engine 5 Editor Dark Variant */
  --background: #121212; /* UE5 darker background */
  --foreground: #CCCCCC; /* UE5 light text */
  --card: #1A1A1A; /* UE5 darker panel background */
  --card-foreground: #CCCCCC; /* UE5 light text */
  --popover: #1A1A1A; /* UE5 darker panel background */
  --popover-foreground: #CCCCCC; /* UE5 light text */
  --primary: #4CC3FF; /* UE5 light blue accent */
  --primary-foreground: #000000; /* Black text on light blue */
  --secondary: #2573CE; /* UE5 darker blue */
  --secondary-foreground: #FFFFFF; /* White text on darker blue */
  --muted: #222222; /* UE5 darker muted background */
  --muted-foreground: #888888; /* UE5 darker muted text */
  --accent: #4CC3FF; /* UE5 light blue accent */
  --accent-foreground: #000000; /* Black text on accent */
  --destructive: #E74C3C; /* Red for destructive actions */
  --destructive-foreground: #FFFFFF; /* White text on destructive */
  --border: #222222; /* UE5 darker border color */
  --input: #222222; /* UE5 darker input background */
  --ring: #4CC3FF; /* UE5 light blue for focus rings */
  --chart-1: #4CC3FF; /* UE5 light blue */
  --chart-2: #2573CE; /* UE5 darker blue */
  --chart-3: #3498DB; /* Medium blue */
  --chart-4: #1ABC9C; /* Teal */
  --chart-5: #F1C40F; /* Yellow */
  --sidebar: #121212; /* UE5 darker background */
  --sidebar-foreground: #CCCCCC; /* UE5 light text */
  --sidebar-primary: #4CC3FF; /* UE5 light blue accent */
  --sidebar-primary-foreground: #000000; /* Black text on light blue */
  --sidebar-accent: #2573CE; /* UE5 darker blue */
  --sidebar-accent-foreground: #FFFFFF; /* White text on darker blue */
  --sidebar-border: #222222; /* UE5 darker border color */
  --sidebar-ring: #4CC3FF; /* UE5 light blue for focus rings */
  --font-sans: Montserrat, sans-serif;
  --font-serif: Merriweather, serif;
  --font-mono: Ubuntu Mono, monospace;
  --radius: 0.625rem;
  --radius-sm: 0.375rem;
  --radius-md: 0.625rem;
  --radius-lg: 0.875rem;
  --radius-xl: 1.125rem;
  --radius-2xl: 1.5rem;
  --radius-button: 0.875rem;
  --shadow-2xs: 0px 6px 12px -3px hsl(0 0% 0% / 0.04);
  --shadow-xs: 0px 6px 12px -3px hsl(0 0% 0% / 0.04);
  --shadow-sm: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 1px 2px -4px hsl(0 0% 0% / 0.09);
  --shadow: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 1px 2px -4px hsl(0 0% 0% / 0.09);
  --shadow-md: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 2px 4px -4px hsl(0 0% 0% / 0.09);
  --shadow-lg: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 4px 6px -4px hsl(0 0% 0% / 0.09);
  --shadow-xl: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 8px 10px -4px hsl(0 0% 0% / 0.09);
  --shadow-2xl: 0px 6px 12px -3px hsl(0 0% 0% / 0.22);
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
  button,
  .button,
  [type="button"],
  [type="reset"],
  [type="submit"] {
    @apply rounded-button;
  }
}

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    /* Use Firefox-specific scrollbar hiding when supported */
    scrollbar-width: none;
  }

  /* Ensure dropdown menus are visible */
  [data-radix-popper-content-wrapper] {
    z-index: 9999 !important;
  }
}

/* Landing Page Styles */
.landing-page {
  --ue-blue: #2196f3;
  --ue-dark-blue: #0d47a1;
  --ue-light-blue: #29b6f6;
  --ue-accent: #00b0ff;
  --ue-dark: #0d1117;
  --ue-darker: #090c10;
  --ue-gray: #1f2937;
  --ue-light-gray: #374151;
  --ue-text: #e0e0e0;
  --ue-text-secondary: #9ca3af;
  color-scheme: dark;
}

/* Diagonal section dividers */
.diagonal-top {
  position: relative;
}

.diagonal-top::before {
  content: '';
  position: absolute;
  top: -50px;
  left: 0;
  width: 100%;
  height: 100px;
  background: inherit;
  clip-path: polygon(0 0, 100% 100%, 100% 100%, 0% 100%);
  z-index: 1;
}

.diagonal-bottom {
  position: relative;
}

.diagonal-bottom::after {
  content: '';
  position: absolute;
  bottom: -50px;
  left: 0;
  width: 100%;
  height: 100px;
  background: inherit;
  clip-path: polygon(0 0, 100% 0, 100% 0, 0 100%);
  z-index: 1;
}

/* Animation for scroll indicator */
@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.animate-bounce {
  animation: bounce 1s infinite;
}

/* Spinner animation */
.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #3b82f6;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(to right, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}