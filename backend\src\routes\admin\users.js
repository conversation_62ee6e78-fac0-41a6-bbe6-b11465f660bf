const express = require('express');
const router = express.Router();
const { authenticateJWT } = require('../../middleware/auth');
const supabase = require('../../services/supabaseClient');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const tokenUsageService = require('../../services/tokenUsageService');

// Middleware to check if user is an admin
const isAdmin = (req, res, next) => {
  const adminEmails = ['<EMAIL>', '<EMAIL>'];

  // Check if user has is_admin flag set
  if (req.user && req.user.is_admin === true) {
    console.log('[Admin] User is admin by flag:', req.user.email);
    return next();
  }

  // Check if user email is in admin list
  if (req.user && req.user.email && adminEmails.includes(req.user.email.toLowerCase())) {
    console.log('[Admin] User is admin by email:', req.user.email);
    return next();
  }

  // For development, check environment variable
  if (process.env.NODE_ENV === 'development' && process.env.BYPASS_ADMIN_CHECK === 'true') {
    console.log('[Admin] Bypassing admin check in development mode');
    return next();
  }

  console.log('[Admin] Access denied for user:', req.user?.email || 'unknown');
  return res.status(403).json({ error: 'Admin access required' });
};

/**
 * Get all users
 * GET /api/admin/users
 */
router.get('/', authenticateJWT, isAdmin, async (req, res) => {
  try {
    console.log('[Admin] Getting all users');

    const { page = 1, limit = 10, search = '', filter = '' } = req.query;
    const offset = (page - 1) * limit;

    let query = supabase
      .from('users')
      .select('*', { count: 'exact' });

    // Apply search filter if provided
    if (search) {
      query = query.or(`email.ilike.%${search}%,name.ilike.%${search}%`);
    }

    // Apply subscription filter if provided
    if (filter === 'active') {
      query = query.eq('subscription_status', 'active');
    } else if (filter === 'inactive') {
      query = query.eq('subscription_status', 'inactive');
    } else if (filter === 'canceled') {
      query = query.eq('subscription_status', 'canceled');
    } else if (filter === 'admin') {
      query = query.eq('is_admin', true);
    }

    // Apply pagination
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) {
      console.error('[Admin] Error getting users:', error);
      return res.status(500).json({ error: 'Failed to get users' });
    }

    res.json({
      users: data,
      total: count,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(count / limit)
    });
  } catch (error) {
    console.error('[Admin] Error getting users:', error);
    res.status(500).json({ error: 'Failed to get users' });
  }
});

/**
 * Get user details
 * GET /api/admin/users/:userId
 */
router.get('/:userId', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    console.log(`[Admin] Getting user details for ${userId}`);

    // Get user from Supabase
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('[Admin] Error getting user:', userError);
      return res.status(404).json({ error: 'User not found' });
    }

    // Get token usage data
    const { data: tokenUsage, error: tokenUsageError } = await supabase
      .from('token_usage')
      .select('*')
      .eq('user_id', userId)
      .order('timestamp', { ascending: false })
      .limit(10);

    if (tokenUsageError) {
      console.error('[Admin] Error getting token usage:', tokenUsageError);
    }

    // Get token balance
    const { data: tokenBalance, error: tokenBalanceError } = await supabase
      .from('token_balance')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (tokenBalanceError && tokenBalanceError.code !== 'PGRST116') {
      console.error('[Admin] Error getting token balance:', tokenBalanceError);
    }

    // Get daily and monthly token usage
    const dailyUsage = await tokenUsageService.getDailyTokenUsage(userId);
    const monthlyUsage = await tokenUsageService.getMonthlyTokenUsage(userId);

    // Get subscription details from Stripe if available
    let subscriptionDetails = null;
    if (user.stripe_customer_id && user.subscription_id) {
      try {
        const subscription = await stripe.subscriptions.retrieve(user.subscription_id);
        subscriptionDetails = {
          status: subscription.status,
          current_period_end: subscription.current_period_end,
          cancel_at_period_end: subscription.cancel_at_period_end,
          plan: {
            id: subscription.items.data[0].plan.id,
            name: subscription.items.data[0].plan.nickname,
            amount: subscription.items.data[0].plan.amount,
            currency: subscription.items.data[0].plan.currency,
            interval: subscription.items.data[0].plan.interval
          }
        };
      } catch (error) {
        console.error('[Admin] Error getting subscription details:', error);
      }
    }

    // Combine all data
    const userData = {
      ...user,
      token_balance: tokenBalance?.balance || 0,
      daily_token_usage: dailyUsage.totalTokens,
      monthly_token_usage: monthlyUsage.totalTokens,
      daily_token_limit: user.subscription_plan === 'pro' ? 100000 : 50000,
      monthly_token_limit: user.subscription_plan === 'pro' ? 2000000 : 1000000,
      subscription_details: subscriptionDetails,
      recent_token_usage: tokenUsage || []
    };

    res.json(userData);
  } catch (error) {
    console.error('[Admin] Error getting user details:', error);
    res.status(500).json({ error: 'Failed to get user details' });
  }
});

/**
 * Update user
 * PATCH /api/admin/users/:userId
 */
router.patch('/:userId', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const updates = req.body;

    console.log(`[Admin] Updating user ${userId}:`, updates);

    // Validate updates
    const allowedFields = ['name', 'email', 'is_admin', 'token_balance'];
    const filteredUpdates = {};

    for (const field of allowedFields) {
      if (updates[field] !== undefined) {
        filteredUpdates[field] = updates[field];
      }
    }

    if (Object.keys(filteredUpdates).length === 0) {
      return res.status(400).json({ error: 'No valid fields to update' });
    }

    // Update user in Supabase
    const { data, error } = await supabase
      .from('users')
      .update(filteredUpdates)
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      console.error('[Admin] Error updating user:', error);
      return res.status(500).json({ error: 'Failed to update user' });
    }

    // If token balance was updated, update the token_balance table
    if (updates.token_balance !== undefined) {
      const { data: tokenBalance, error: tokenBalanceError } = await supabase
        .from('token_balance')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (tokenBalanceError && tokenBalanceError.code !== 'PGRST116') {
        console.error('[Admin] Error getting token balance:', tokenBalanceError);
      }

      if (tokenBalance) {
        // Update existing token balance
        const { error: updateError } = await supabase
          .from('token_balance')
          .update({ balance: updates.token_balance })
          .eq('user_id', userId);

        if (updateError) {
          console.error('[Admin] Error updating token balance:', updateError);
        }
      } else {
        // Create new token balance record
        const { error: insertError } = await supabase
          .from('token_balance')
          .insert({ user_id: userId, balance: updates.token_balance });

        if (insertError) {
          console.error('[Admin] Error creating token balance:', insertError);
        }
      }
    }

    res.json(data);
  } catch (error) {
    console.error('[Admin] Error updating user:', error);
    res.status(500).json({ error: 'Failed to update user' });
  }
});

/**
 * Delete user
 * DELETE /api/admin/users/:userId
 */
router.delete('/:userId', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { userId } = req.params;

    console.log(`[Admin] Deleting user ${userId}`);

    // Get user from Supabase
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('[Admin] Error getting user:', userError);
      return res.status(404).json({ error: 'User not found' });
    }

    // Cancel subscription in Stripe if exists
    if (user.subscription_id) {
      try {
        await stripe.subscriptions.del(user.subscription_id);
        console.log(`[Admin] Canceled subscription ${user.subscription_id}`);
      } catch (error) {
        console.error('[Admin] Error canceling subscription:', error);
      }
    }

    // Delete user from Supabase Auth
    const { error: authError } = await supabase.auth.admin.deleteUser(userId);

    if (authError) {
      console.error('[Admin] Error deleting user from auth:', authError);
      return res.status(500).json({ error: 'Failed to delete user from auth' });
    }

    // User data in the database will be deleted by RLS cascade

    res.json({ success: true, message: 'User deleted successfully' });
  } catch (error) {
    console.error('[Admin] Error deleting user:', error);
    res.status(500).json({ error: 'Failed to delete user' });
  }
});

/**
 * Update user subscription
 * PATCH /api/admin/users/:userId/subscription
 */
router.patch('/:userId/subscription', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { status } = req.body;

    console.log(`[Admin] Updating subscription status for user ${userId} to ${status}`);

    if (!['active', 'inactive', 'canceled'].includes(status)) {
      return res.status(400).json({ error: 'Invalid subscription status' });
    }

    // Get user from Supabase
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('[Admin] Error getting user:', userError);
      return res.status(404).json({ error: 'User not found' });
    }

    // Update subscription in Stripe if exists
    if (user.subscription_id) {
      try {
        if (status === 'canceled') {
          await stripe.subscriptions.del(user.subscription_id);
          console.log(`[Admin] Canceled subscription ${user.subscription_id}`);
        } else if (status === 'active' && user.subscription_status !== 'active') {
          // Reactivate subscription if it was canceled
          await stripe.subscriptions.update(user.subscription_id, {
            cancel_at_period_end: false
          });
          console.log(`[Admin] Reactivated subscription ${user.subscription_id}`);
        }
      } catch (error) {
        console.error('[Admin] Error updating subscription in Stripe:', error);
      }
    }

    // Update user in Supabase
    const { data, error } = await supabase
      .from('users')
      .update({ subscription_status: status })
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      console.error('[Admin] Error updating user subscription:', error);
      return res.status(500).json({ error: 'Failed to update user subscription' });
    }

    res.json(data);
  } catch (error) {
    console.error('[Admin] Error updating user subscription:', error);
    res.status(500).json({ error: 'Failed to update user subscription' });
  }
});

/**
 * Reset user token usage
 * POST /api/admin/users/:userId/reset-token-usage
 */
router.post('/:userId/reset-token-usage', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { userId } = req.params;

    console.log(`[Admin] Resetting token usage for user ${userId}`);

    // Reset daily and monthly token usage in the database
    await tokenUsageService.resetUserTokenUsage(userId);

    res.json({ success: true, message: 'Token usage reset successfully' });
  } catch (error) {
    console.error('[Admin] Error resetting token usage:', error);
    res.status(500).json({ error: 'Failed to reset token usage' });
  }
});

/**
 * Get user token usage
 * GET /api/admin/users/:userId/token-usage
 */
router.get('/:userId/token-usage', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { from, to } = req.query;

    console.log(`[Admin] Getting token usage for user ${userId}`);

    if (!from || !to) {
      return res.status(400).json({ error: 'Missing required parameters: from, to' });
    }

    // Parse dates
    const fromDate = new Date(from);
    const toDate = new Date(to);

    if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
      return res.status(400).json({ error: 'Invalid date format. Use YYYY-MM-DD' });
    }

    // Query token usage data
    const { data, error } = await supabase
      .from('token_usage')
      .select('prompt_tokens, completion_tokens, total_tokens, timestamp')
      .eq('user_id', userId)
      .gte('timestamp', fromDate.toISOString())
      .lte('timestamp', toDate.toISOString())
      .order('timestamp', { ascending: true });

    if (error) {
      console.error('[Admin] Error querying token usage:', error);
      return res.status(500).json({ error: 'Failed to query token usage data' });
    }

    // Group data by day
    const dailyUsage = {};

    data.forEach(record => {
      const date = new Date(record.timestamp);
      const dateString = date.toISOString().split('T')[0]; // YYYY-MM-DD

      if (!dailyUsage[dateString]) {
        dailyUsage[dateString] = {
          date: dateString,
          promptTokens: 0,
          completionTokens: 0,
          totalTokens: 0
        };
      }

      dailyUsage[dateString].promptTokens += record.prompt_tokens;
      dailyUsage[dateString].completionTokens += record.completion_tokens;
      dailyUsage[dateString].totalTokens += record.total_tokens;
    });

    // Convert to array and sort by date
    const dailyUsageArray = Object.values(dailyUsage).sort((a, b) => {
      return new Date(a.date) - new Date(b.date);
    });

    return res.json({
      dailyUsage: dailyUsageArray,
      totalDays: dailyUsageArray.length
    });
  } catch (error) {
    console.error('[Admin] Error getting user token usage:', error);
    res.status(500).json({ error: 'Failed to get user token usage' });
  }
});

/**
 * Get user invoices
 * GET /api/admin/users/:userId/invoices
 */
router.get('/:userId/invoices', authenticateJWT, isAdmin, async (req, res) => {
  try {
    const { userId } = req.params;

    console.log(`[Admin] Getting invoices for user ${userId}`);

    // Get user from Supabase
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('[Admin] Error getting user:', userError);
      return res.status(404).json({ error: 'User not found' });
    }

    // If user has no Stripe customer ID, return empty array
    if (!user.stripe_customer_id) {
      return res.json({ invoices: [] });
    }

    // Get invoices from Stripe
    const invoices = await stripe.invoices.list({
      customer: user.stripe_customer_id,
      limit: 10
    });

    // Format invoices
    const formattedInvoices = invoices.data.map(invoice => ({
      id: invoice.id,
      invoice_id: invoice.number,
      amount: invoice.amount_due,
      currency: invoice.currency,
      status: invoice.status,
      created_at: new Date(invoice.created * 1000).toISOString(),
      paid_at: invoice.status === 'paid' ? new Date(invoice.status_transitions.paid_at * 1000).toISOString() : null,
      invoice_url: invoice.hosted_invoice_url,
      receipt_url: invoice.receipt_number ? `https://dashboard.stripe.com/receipts/${invoice.receipt_number}` : null,
      description: invoice.description || (invoice.subscription ? 'Subscription' : 'One-time charge')
    }));

    res.json({ invoices: formattedInvoices });
  } catch (error) {
    console.error('[Admin] Error getting user invoices:', error);
    res.status(500).json({ error: 'Failed to get user invoices' });
  }
});

module.exports = router;
