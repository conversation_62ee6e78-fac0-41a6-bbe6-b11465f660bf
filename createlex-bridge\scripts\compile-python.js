const fs = require('fs-extra');
const path = require('path');
const { execSync } = require('child_process');

async function compilePython() {
  const pythonDir = path.join(__dirname, '..', 'src', 'python');
  const compiledDir = path.join(__dirname, '..', 'src', 'python-compiled');
  
  console.log('🔄 Compiling Python files to bytecode...');
  
  // Clean and create compiled directory
  await fs.remove(compiledDir);
  await fs.ensureDir(compiledDir);
  
  // Copy all files first
  await fs.copy(pythonDir, compiledDir);
  
  // Compile all .py files to .pyc
  try {
    execSync(`python -m compileall -b "${compiledDir}"`, { stdio: 'inherit' });
    
    // Remove .py source files, keep only .pyc
    const files = await fs.readdir(compiledDir);
    for (const file of files) {
      if (file.endsWith('.py') && !file.startsWith('__')) {
        await fs.remove(path.join(compiledDir, file));
      }
    }
    
    console.log('✅ Python files compiled to bytecode');
  } catch (error) {
    console.error('❌ Failed to compile Python files:', error);
    process.exit(1);
  }
}

compilePython(); 