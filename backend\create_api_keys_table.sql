-- Create API Keys table for CreateLex Admin Dashboard
-- This table stores API keys for users with rate limiting and management features

-- Create the api_keys table
CREATE TABLE IF NOT EXISTS api_keys (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL,
  key_name TEXT NOT NULL,
  api_key TEXT NOT NULL UNIQUE,
  rate_limit INTEGER DEFAULT 100,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_api_key ON api_keys(api_key);
CREATE INDEX IF NOT EXISTS idx_api_keys_is_active ON api_keys(is_active);
CREATE INDEX IF NOT EXISTS idx_api_keys_created_at ON api_keys(created_at);

-- Enable Row Level Security
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Service role can manage all api_keys" ON api_keys;
DROP POLICY IF EXISTS "Users can view their own api_keys" ON api_keys;

-- Create RLS policies
-- Policy for service role (backend) - full access
CREATE POLICY "Service role can manage all api_keys" ON api_keys
  FOR ALL USING (auth.role() = 'service_role');

-- Policy for authenticated users - can only see their own keys
CREATE POLICY "Users can view their own api_keys" ON api_keys
  FOR SELECT USING (auth.uid() = user_id);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_api_keys_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
DROP TRIGGER IF EXISTS update_api_keys_updated_at_trigger ON api_keys;
CREATE TRIGGER update_api_keys_updated_at_trigger
  BEFORE UPDATE ON api_keys
  FOR EACH ROW
  EXECUTE FUNCTION update_api_keys_updated_at();

-- Insert some sample data for testing (optional)
-- Uncomment the lines below if you want sample data

/*
INSERT INTO api_keys (user_id, key_name, api_key, rate_limit, is_active) VALUES
  ('00000000-0000-0000-0000-000000000001', 'Test API Key 1', 'test_key_1234567890abcdef', 100, true),
  ('00000000-0000-0000-0000-000000000002', 'Test API Key 2', 'test_key_abcdef1234567890', 200, true),
  ('00000000-0000-0000-0000-000000000003', 'Inactive Key', 'test_key_inactive123456', 50, false)
ON CONFLICT (api_key) DO NOTHING;
*/

-- Verify the table was created successfully
SELECT 
  table_name, 
  column_name, 
  data_type, 
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'api_keys' 
ORDER BY ordinal_position;
