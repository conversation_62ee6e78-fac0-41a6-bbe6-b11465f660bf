'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { updateUserId } from "@/lib/user-id";
import { useRouter } from "next/navigation";

export default function CreateTestChatPage() {
  const [userId, setUserId] = useState('077f1533-9f81-429c-b1b1-52d9c83f146c'); // Pre-filled with the Supabase UUID
  const [currentUserId, setCurrentUserId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  // Get the current user ID from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedUserId = localStorage.getItem('ai-chat-user-id');
      setCurrentUserId(storedUserId || '');
    }
  }, []);

  const handleCreateTestChat = async () => {
    if (!userId.trim()) {
      toast.error("User ID cannot be empty");
      return;
    }

    setIsLoading(true);

    try {
      // Call the create-test-chat API
      const response = await fetch('/api/create-test-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: userId.trim() }),
      });

      if (!response.ok) {
        toast.error(`Failed to create test chat: ${response.statusText}`);
        setIsLoading(false);
        return;
      }

      const data = await response.json();

      if (data.success) {
        // Update the user ID in localStorage
        updateUserId(userId.trim(), 'supabase');
        
        toast.success("Test chat created successfully");
        
        // Redirect to the chat page after a short delay
        setTimeout(() => {
          router.push('/chat');
          // Reload the page to ensure all components use the new user ID
          window.location.reload();
        }, 1500);
      } else {
        toast.error("Failed to create test chat");
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Error creating test chat:', error);
      toast.error("An error occurred while creating the test chat");
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Create Test Chat</CardTitle>
          <CardDescription>
            Create a test chat with the Supabase UUID to ensure consistent chat history
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="currentUserId">Current User ID</Label>
            <Input
              id="currentUserId"
              value={currentUserId}
              readOnly
              className="font-mono text-xs"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="userId">Supabase UUID</Label>
            <Input
              id="userId"
              value={userId}
              onChange={(e) => setUserId(e.target.value)}
              placeholder="Enter the Supabase UUID"
              className="font-mono text-xs"
            />
            <p className="text-xs text-muted-foreground">
              This will create a test chat with the Supabase UUID
            </p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => router.push('/chat')} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleCreateTestChat} disabled={isLoading}>
            {isLoading ? "Creating..." : "Create Test Chat"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
