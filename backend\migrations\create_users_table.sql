-- Drop existing table if it exists
DROP TABLE IF EXISTS public.users;

-- Create users table
CREATE TABLE IF NOT EXISTS public.users (
  id TEXT PRIMARY KEY,
  email TEXT NOT NULL,
  name TEXT,
  picture TEXT,
  subscription_status TEXT DEFAULT 'inactive',
  stripe_customer_id TEXT,
  subscription_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  subscription_updated_at TIMESTAMP WITH TIME ZONE,
  last_checkout_session_id TEXT
);

-- Add indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users (email);
CREATE INDEX IF NOT EXISTS idx_users_stripe_customer_id ON public.users (stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_users_subscription_id ON public.users (subscription_id);

-- Disable RLS for now to allow easier testing
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;

-- Create policies
-- Allow authenticated users to read their own data
CREATE POLICY "Users can view their own data"
  ON public.users
  FOR SELECT
  USING (auth.uid()::text = id);

-- Allow service role to read all users
CREATE POLICY "Service role can read all users"
  ON public.users
  FOR SELECT
  USING (auth.role()::text = 'service_role');

-- Allow service role to insert/update users
CREATE POLICY "Service role can insert users"
  ON public.users
  FOR INSERT
  WITH CHECK (true);  -- Allow any role to insert users

CREATE POLICY "Service role can update users"
  ON public.users
  FOR UPDATE
  USING (auth.role()::text = 'service_role');

-- Allow authenticated users to update their own data
CREATE POLICY "Users can update their own data"
  ON public.users
  FOR UPDATE
  USING (auth.uid()::text = id);
