-- Create chats table if it doesn't exist
CREATE TABLE IF NOT EXISTS chats (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  title TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create messages table if it doesn't exist
CREATE TABLE IF NOT EXISTS messages (
  id TEXT PRIMARY KEY,
  chat_id TEXT NOT NULL REFERENCES chats(id) ON DELETE CASCADE,
  role TEXT NOT NULL,
  content JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Add index for faster queries
  CONSTRAINT fk_chat
    FOREIGN KEY (chat_id)
    REFERENCES chats(id)
    ON DELETE CASCADE
);

-- Create index on chat_id for faster queries
CREATE INDEX IF NOT EXISTS idx_messages_chat_id ON messages(chat_id);

-- Check if content column is J<PERSON>N<PERSON>, if not, alter it
DO $$
DECLARE
  content_type text;
BEGIN
  SELECT data_type
  FROM information_schema.columns
  WHERE table_name = 'messages'
  AND column_name = 'content'
  INTO content_type;
  
  IF content_type IS NOT NULL AND content_type != 'jsonb' THEN
    -- Alter the column type to JSONB
    EXECUTE 'ALTER TABLE messages ALTER COLUMN content TYPE JSONB USING content::jsonb';
    RAISE NOTICE 'Altered content column from % to JSONB', content_type;
  END IF;
END $$;
