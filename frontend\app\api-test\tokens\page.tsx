'use client';

import { useState, useEffect } from 'react';
import { useSupabaseAuth } from '../../../contexts/SupabaseAuthContext';
import Link from 'next/link';

export default function TokensTestPage() {
  const { user, session } = useSupabaseAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [tokenBalance, setTokenBalance] = useState<any>(null);
  const [tokenPackages, setTokenPackages] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const checkTokenBalance = async () => {
    if (!user) {
      setError('You must be logged in to check token balance');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      // Get the API URL from environment variables
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'https://api.createlex.com';
      
      // Call the token balance endpoint
      const response = await fetch(`${backendUrl}/api/tokens/balance`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session?.access_token || ''}`,
          'x-user-id': user.id,
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setTokenBalance(data);
      } else {
        setError(`API returned status: ${response.status}`);
        try {
          const errorData = await response.json();
          setTokenBalance(errorData);
        } catch (e) {
          // If we can't parse the error as JSON, just show the status
        }
      }
    } catch (error: any) {
      setError(error.message || 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchTokenPackages = async () => {
    try {
      // Get the API URL from environment variables
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'https://api.createlex.com';
      
      // Call the token packages endpoint
      const response = await fetch(`${backendUrl}/api/tokens/packages`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setTokenPackages(data);
      } else {
        console.error('Failed to fetch token packages:', response.status);
      }
    } catch (error) {
      console.error('Error fetching token packages:', error);
    }
  };

  useEffect(() => {
    fetchTokenPackages();
    
    if (user) {
      checkTokenBalance();
    }
  }, [user]);

  return (
    <div className="container mx-auto p-8">
      <div className="mb-4">
        <Link href="/api-test" className="text-blue-500 hover:underline">
          ← Back to API Test
        </Link>
      </div>
      
      <h1 className="text-3xl font-bold mb-6">Token Balance Test</h1>
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Authentication Status</h2>
        <div className={`p-4 rounded ${user ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
          {user ? 'You are authenticated! ✅' : 'You are not authenticated! ❌'}
        </div>
      </div>
      
      {!user && (
        <div className="mb-6">
          <p className="text-red-600">
            You need to be logged in to check token balance.
          </p>
          <Link 
            href="/api-test/auth" 
            className="mt-2 inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Go to Authentication Test
          </Link>
        </div>
      )}
      
      {user && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Actions</h2>
          <button
            onClick={checkTokenBalance}
            disabled={isLoading}
            className={`px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {isLoading ? 'Checking...' : 'Check Token Balance'}
          </button>
        </div>
      )}
      
      {error && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Error</h2>
          <div className="p-4 bg-red-100 text-red-800 rounded">
            {error}
          </div>
        </div>
      )}
      
      {tokenBalance && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Token Balance</h2>
          <pre className="p-4 bg-gray-100 rounded overflow-auto">
            {JSON.stringify(tokenBalance, null, 2)}
          </pre>
          
          <div className="mt-4 p-4 rounded bg-blue-100">
            <h3 className="font-semibold text-blue-800">Token Balance Summary</h3>
            <ul className="mt-2 list-disc list-inside text-blue-800">
              <li>
                Available Tokens: <span className="font-semibold">{tokenBalance.availableTokens || 0}</span>
              </li>
              <li>
                Used Today: <span className="font-semibold">{tokenBalance.usedToday || 0}</span>
              </li>
              <li>
                Used This Month: <span className="font-semibold">{tokenBalance.usedThisMonth || 0}</span>
              </li>
            </ul>
          </div>
        </div>
      )}
      
      {tokenPackages && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Available Token Packages</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {tokenPackages.map((pkg: any) => (
              <div key={pkg.id} className="border rounded p-4 bg-white shadow-sm">
                <h3 className="font-bold text-lg">{pkg.name}</h3>
                <p className="text-gray-600">{pkg.description}</p>
                <p className="mt-2">
                  <span className="font-semibold text-green-600">${pkg.price}</span>
                  <span className="text-gray-500"> for </span>
                  <span className="font-semibold">{pkg.tokens.toLocaleString()}</span>
                  <span className="text-gray-500"> tokens</span>
                </p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
