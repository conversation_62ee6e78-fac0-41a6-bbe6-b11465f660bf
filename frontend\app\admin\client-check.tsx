'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../contexts/AuthContext';
import { useSupabaseAuth } from '../../contexts/SupabaseAuthContext';
import LoadingSpinner from '../../components/ui/LoadingSpinner';

export default function AdminClientCheck({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuth();
  const { user: supabaseUser, isLoading: supabaseLoading } = useSupabaseAuth();
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null);

  useEffect(() => {
    // Check if the user is an admin
    const checkAdminStatus = async () => {
      // Skip check if still loading
      if (isLoading || supabaseLoading) {
        return;
      }

      // If not authenticated, redirect to login
      if (!isAuthenticated || !supabaseUser) {
        console.log('Not authenticated, redirecting to login');
        router.push('/login');
        return;
      }

      // Check if user email is one of the admin emails
      const adminEmails = ['<EMAIL>', '<EMAIL>'];
      const isAdminByEmail = adminEmails.includes(supabaseUser.email || '');

      if (isAdminByEmail) {
        console.log('User is admin by email');
        setIsAdmin(true);
        return;
      }

      // Check if user has is_admin flag in Supabase
      try {
        const { data, error } = await fetch('/api/check-admin', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }).then(res => res.json());

        if (!error && data && data.isAdmin) {
          console.log('User is admin by flag');
          setIsAdmin(true);
          return;
        }
      } catch (error) {
        console.error('Error checking admin status:', error);
      }

      // If we get here, user is not an admin
      console.log('User is not an admin, redirecting to dashboard');
      setIsAdmin(false);
      router.push('/dashboard');
    };

    checkAdminStatus();
  }, [isAuthenticated, isLoading, supabaseUser, supabaseLoading, router]);

  // Show loading spinner while checking admin status
  if (isAdmin === null) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  // If user is admin, render children
  return isAdmin ? <>{children}</> : null;
}
