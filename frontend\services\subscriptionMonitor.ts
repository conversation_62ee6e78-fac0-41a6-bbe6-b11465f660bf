import { fetchWithAuth } from '../lib/authUtils';

interface SubscriptionStatus {
  hasActiveSubscription: boolean;
  error?: string;
}

interface SubscriptionMonitorCallbacks {
  onSubscriptionExpired?: (data: SubscriptionStatus) => void;
  onSubscriptionWarning?: (error: string) => void;
  onSubscriptionValid?: () => void;
}

class SubscriptionMonitor {
  private intervalId: NodeJS.Timeout | null = null;
  private checkFrequency: number = 5 * 60 * 1000; // 5 minutes
  private callbacks: SubscriptionMonitorCallbacks = {};
  private isRunning: boolean = false;
  private lastValidCheck: number = 0;
  private gracePeriod: number = 7 * 24 * 60 * 60 * 1000; // 7 days

  constructor(callbacks: SubscriptionMonitorCallbacks = {}) {
    this.callbacks = callbacks;
    
    // Use shorter interval in development
    if (process.env.NODE_ENV === 'development') {
      this.checkFrequency = 30 * 1000; // 30 seconds in dev
    }
  }

  start() {
    if (this.isRunning) {
      console.log('🔄 Subscription monitor already running');
      return;
    }

    console.log(`🔄 Starting subscription monitor (checking every ${this.checkFrequency / 1000} seconds)`);
    this.isRunning = true;

    // Initial check
    this.checkSubscription();

    // Set up periodic checks
    this.intervalId = setInterval(() => {
      console.log('⏰ Periodic subscription check...');
      this.checkSubscription();
    }, this.checkFrequency);
  }

  stop() {
    if (!this.isRunning) {
      return;
    }

    console.log('🛑 Stopping subscription monitor');
    this.isRunning = false;

    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  private async checkSubscription(providedToken?: string) {
    try {
      console.log('🔍 Checking subscription status...');
      
      // Use provided token or try to get from localStorage
      let token = providedToken;
      
      if (!token) {
        // Try to get token from various sources
        try {
          // Check if we're in a browser environment
          if (typeof window !== 'undefined') {
            // Try to get token from Supabase auth session (correct project-specific key)
            const supabaseSession = localStorage.getItem('sb-ujiakzkncbxisdatygpo-auth-token');
            if (supabaseSession) {
              try {
                const session = JSON.parse(supabaseSession);
                if (session.access_token) {
                  token = session.access_token;
                  console.log('🔑 Retrieved token from localStorage for periodic check');
                }
              } catch (parseError) {
                console.warn('Failed to parse Supabase session:', parseError);
              }
            } else {
              console.warn('No Supabase session found in localStorage');
            }
          }
        } catch (e) {
          console.warn('Token retrieval error:', e);
        }
      }
      
      // Call the backend API to check subscription (same as dashboard)
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
      const response = await fetchWithAuth(
        `${apiUrl}/api/subscription/status`,
        token
      );

      if (!response.ok) {
        throw new Error(`Subscription check failed: ${response.status}`);
      }

      const data: SubscriptionStatus = await response.json();
      
      if (!data.hasActiveSubscription) {
        console.error('❌ Subscription validation failed:', data.error || 'No active subscription');
        
        // Stop MCP server automatically
        await this.stopMCPServer();
        
        // Notify callbacks
        if (this.callbacks.onSubscriptionExpired) {
          this.callbacks.onSubscriptionExpired(data);
        }
        
        return false;
      } else {
        console.log('✅ Subscription validated successfully');
        this.lastValidCheck = Date.now();
        
        if (this.callbacks.onSubscriptionValid) {
          this.callbacks.onSubscriptionValid();
        }
        
        return true;
      }
    } catch (error) {
      console.error('❌ Error checking subscription:', error);
      
      // Check grace period for network errors
      const now = Date.now();
      const timeSinceLastValid = now - this.lastValidCheck;
      
      if (timeSinceLastValid > this.gracePeriod) {
        console.error('🚨 Grace period expired, stopping MCP server');
        await this.stopMCPServer();
        
        if (this.callbacks.onSubscriptionExpired) {
          this.callbacks.onSubscriptionExpired({
            hasActiveSubscription: false,
            error: 'Grace period expired due to network issues'
          });
        }
      } else {
        // Still within grace period
        if (this.callbacks.onSubscriptionWarning) {
          this.callbacks.onSubscriptionWarning(
            `Unable to verify subscription: ${error}. Grace period: ${Math.ceil((this.gracePeriod - timeSinceLastValid) / (24 * 60 * 60 * 1000))} days remaining.`
          );
        }
      }
      
      return false;
    }
  }

  private async stopMCPServer() {
    try {
      console.log('🛑 Automatically stopping MCP server due to subscription issue...');
      
      // Try to stop via HTTP API (for web interface)
      try {
        const response = await fetch('http://localhost:9878/enable-native-button', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ enabled: false }),
        });
        
        if (response.ok) {
          console.log('✅ MCP server stopped via HTTP API');
          return;
        }
      } catch (httpError) {
        console.warn('⚠️ HTTP API stop failed:', httpError);
      }
      
      // Try to stop via bridge API (if available)
      // @ts-ignore
      const bridgeApi = typeof window !== 'undefined' ? (window as any).bridgeApi : undefined;
      
      if (bridgeApi?.stopMCP) {
        try {
          const result = await bridgeApi.stopMCP();
          if (result?.success) {
            console.log('✅ MCP server stopped via bridge API');
            return;
          }
        } catch (bridgeError) {
          console.warn('⚠️ Bridge API stop failed:', bridgeError);
        }
      }
      
      console.warn('⚠️ Could not stop MCP server automatically');
    } catch (error) {
      console.error('❌ Error stopping MCP server:', error);
    }
  }

  // Manual subscription check with token
  async manualCheck(token?: string): Promise<boolean> {
    return await this.checkSubscription(token);
  }

  // Update callbacks
  updateCallbacks(callbacks: SubscriptionMonitorCallbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  // Get status
  getStatus() {
    return {
      isRunning: this.isRunning,
      checkFrequency: this.checkFrequency,
      lastValidCheck: this.lastValidCheck,
      gracePeriodRemaining: this.lastValidCheck ? Math.max(0, this.gracePeriod - (Date.now() - this.lastValidCheck)) : 0
    };
  }
}

export default SubscriptionMonitor; 