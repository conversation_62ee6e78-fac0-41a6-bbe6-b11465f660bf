const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function testDeviceInsert() {
  console.log('🧪 Testing device seat insertion...');
  
  // Initialize Supabase client
  const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
  );
  
  // Test data
  const testDevice = {
    user_id: 'mock-user-id',
    device_id: 'test-device-123',
    device_name: 'Test MacBook Pro',
    platform: 'darwin',
    ip_address: '*************',
    is_active: true,
    last_active: new Date().toISOString(),
    created_at: new Date().toISOString()
  };
  
  try {
    console.log('📝 Inserting test device:', testDevice);
    
    // Insert device seat
    const { data, error } = await supabase
      .from('device_seats')
      .insert([testDevice])
      .select();
    
    if (error) {
      console.error('❌ Insert error:', error);
      return;
    }
    
    console.log('✅ Device inserted successfully:', data);
    
    // Now test fetching devices
    console.log('\n🔍 Fetching devices for user:', testDevice.user_id);
    
    const { data: devices, error: fetchError } = await supabase
      .from('device_seats')
      .select('*')
      .eq('user_id', testDevice.user_id);
    
    if (fetchError) {
      console.error('❌ Fetch error:', fetchError);
      return;
    }
    
    console.log('📱 Found devices:', devices);
    
    // Test the API endpoint
    console.log('\n🌐 Testing API endpoint...');
    
    const response = await fetch('http://localhost:5001/api/device/seats', {
      headers: {
        'x-user-id': testDevice.user_id,
        'Authorization': 'Bearer mock-token'
      }
    });
    
    if (response.ok) {
      const apiData = await response.json();
      console.log('✅ API response:', apiData);
    } else {
      const errorText = await response.text();
      console.error('❌ API error:', response.status, errorText);
    }
    
  } catch (err) {
    console.error('❌ Test failed:', err);
  }
}

testDeviceInsert(); 