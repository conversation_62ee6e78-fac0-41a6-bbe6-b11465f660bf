# MCP Server Automatic Shutdown

This document describes the automatic MCP server shutdown functionality implemented in CreateLex Bridge.

## Overview

The CreateLex Bridge now automatically stops the MCP server in various scenarios to ensure proper resource cleanup and prevent orphaned processes. This improves system stability and user experience.

## Automatic Shutdown Scenarios

### 1. User Logout
**Trigger**: When a user clicks the "Logout" button in the dashboard
**Behavior**: 
- MCP server stops immediately
- User is redirected to login page
- Authentication token is cleared
- Tray tooltip updates to show "Not Authenticated"

**Implementation**: Modified the `logout` IPC handler in `main.js`

### 2. App Window Closed
**Trigger**: When the user closes the main application window
**Behavior**: 
- MCP server stops immediately
- Window reference is cleared
- App may continue running in tray (platform dependent)

**Implementation**: Added `mcpServer.stop()` to window `closed` event handlers

### 3. App Quit
**Trigger**: When the application is terminated
**Behavior**: 
- MCP server stops before app termination
- All resources are cleaned up properly

**Implementation**: Added `before-quit` event handler

### 4. Tray Menu Quit
**Trigger**: When user selects "Quit" from the system tray menu
**Behavior**: 
- MCP server stops immediately
- App quits completely

**Implementation**: Modified tray menu quit handler

### 5. Process Termination Signals
**Trigger**: When the process receives SIGINT or SIGTERM signals
**Behavior**: 
- MCP server stops gracefully
- App terminates cleanly

**Implementation**: Added signal handlers for SIGINT and SIGTERM

### 6. All Windows Closed (Platform Specific)

#### macOS
**Trigger**: When all windows are closed on macOS
**Behavior**: 
- MCP server stops to save resources
- App remains running in tray/dock
- MCP server can be restarted when window is reopened

#### Windows/Linux
**Trigger**: When all windows are closed
**Behavior**: 
- MCP server stops
- App quits completely

## Code Changes

### Main Process (`main.js`)

1. **Window Event Handlers**:
   ```javascript
   mainWindow.on('closed', () => {
     console.log('Main window closed - stopping MCP server...');
     if (mcpServer) {
       mcpServer.stop('Main window closed');
     }
     mainWindow = null;
   });
   ```

2. **App Event Handlers**:
   ```javascript
   app.on('before-quit', () => {
     console.log('App is about to quit - stopping MCP server...');
     if (mcpServer) {
       mcpServer.stop('App quit');
     }
   });
   ```

3. **Process Signal Handlers**:
   ```javascript
   process.on('SIGINT', () => {
     console.log('Received SIGINT - stopping MCP server and exiting...');
     if (mcpServer) {
       mcpServer.stop('SIGINT received');
     }
     app.quit();
   });
   ```

4. **Logout Handler**:
   ```javascript
   ipcMain.handle('logout', async (event) => {
     // Stop MCP server when user logs out
     if (mcpServer) {
       console.log('User logging out - stopping MCP server...');
       mcpServer.stop('User logout');
     }
     // ... rest of logout logic
   });
   ```

### Frontend (`dashboard/index.html`)

Updated logout confirmation to inform users about MCP server shutdown:
```javascript
if (confirm('Are you sure you want to logout? This will automatically stop the MCP server.')) {
  // ... logout logic
}
```

## Benefits

1. **Resource Management**: Prevents orphaned MCP server processes
2. **System Stability**: Ensures clean shutdown of all components
3. **User Experience**: Clear feedback about server state changes
4. **Security**: Automatic cleanup when authentication is revoked
5. **Platform Compatibility**: Handles different OS behaviors appropriately

## Testing

A test script is available at `test-mcp-shutdown.js` to verify the shutdown functionality:

```bash
node test-mcp-shutdown.js
```

## Logging

All shutdown events are logged with descriptive reasons:
- `User logout`
- `Main window closed`
- `Dashboard window closed`
- `App quit`
- `Quit from tray`
- `All windows closed`
- `SIGINT received`
- `SIGTERM received`

## Backward Compatibility

This change is fully backward compatible. Existing functionality remains unchanged, with additional automatic cleanup ensuring better resource management.

## Future Considerations

- Consider adding a grace period for accidental window closures
- Implement MCP server auto-restart when user returns to authenticated state
- Add user preferences for shutdown behavior customization 