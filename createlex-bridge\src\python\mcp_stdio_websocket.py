#!/usr/bin/env python3
"""
stdio-to-websocket bridge for MCP server - allows <PERSON> to communicate with WebSocket MCP server
"""

import json
import sys
import asyncio
import websockets
import logging

# Configure logging to stderr
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s', stream=sys.stderr)
logger = logging.getLogger(__name__)

# WebSocket server endpoint
WEBSOCKET_URL = "ws://localhost:8000"

async def send_mcp_request_websocket(message):
    """Send a request to the WebSocket MCP server"""
    try:
        logger.debug(f"Connecting to WebSocket server at {WEBSOCKET_URL}")
        
        async with websockets.connect(WEBSOCKET_URL) as websocket:
            logger.debug(f"Sending message: {json.dumps(message)}")
            await websocket.send(json.dumps(message))
            
            logger.debug("Waiting for response...")
            response = await websocket.recv()
            logger.debug(f"Received response: {response}")
            
            return json.loads(response)
            
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        return {
            "jsonrpc": "2.0",
            "id": message.get("id", 1),
            "error": {
                "code": -32603,
                "message": f"WebSocket error: {str(e)}"
            }
        }

async def handle_stdin():
    """Handle stdin input and process MCP requests"""
    logger.debug("Starting stdio-to-websocket bridge...")
    
    # Read from stdin line by line
    loop = asyncio.get_event_loop()
    reader = asyncio.StreamReader()
    protocol = asyncio.StreamReaderProtocol(reader)
    await loop.connect_read_pipe(lambda: protocol, sys.stdin)
    
    while True:
        try:
            line = await reader.readline()
            if not line:
                break
                
            line = line.decode().strip()
            if not line:
                continue
                
            logger.debug(f"Received stdin: {line}")
            
            # Parse JSON request
            request = json.loads(line)
            method = request.get("method")
            request_id = request.get("id", 1)
            
            logger.debug(f"Processing request: {method}")
            
            # Handle notifications (no response expected)
            if method in ["initialized", "notifications/initialized"]:
                # Send to WebSocket server but don't send response to Claude
                await send_mcp_request_websocket(request)
                continue
            
            # Send request to WebSocket server and get response
            response = await send_mcp_request_websocket(request)
            
            # Send response back to Claude Desktop
            print(json.dumps(response), flush=True)
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            error_response = {
                "jsonrpc": "2.0",
                "id": 1,
                "error": {
                    "code": -32700,
                    "message": f"Parse error: {str(e)}"
                }
            }
            print(json.dumps(error_response), flush=True)
        except Exception as e:
            logger.error(f"General error: {e}")
            error_response = {
                "jsonrpc": "2.0",
                "id": 1,
                "error": {
                    "code": -32603,
                    "message": f"Internal error: {str(e)}"
                }
            }
            print(json.dumps(error_response), flush=True)

def main():
    """Main entry point"""
    try:
        asyncio.run(handle_stdin())
    except KeyboardInterrupt:
        logger.info("Shutting down...")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 