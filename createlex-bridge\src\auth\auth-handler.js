const axios = require('axios');
const os = require('os');
const crypto = require('crypto');
const { TokenManager } = require('./token-manager');
const { OAuthFlow } = require('./oauth-flow');

class AuthHandler {
  constructor() {
    this.tokenManager = new TokenManager();
    this.oauthFlow = new OAuthFlow();
    const isDev = process.env.NODE_ENV !== 'production';
    
    // Try multiple API endpoints for better compatibility
    this.apiUrls = [
      process.env.API_BASE_URL,
      'https://createlex.com/api',
      'https://api.createlex.com/api'
    ].filter(Boolean); // Remove null/undefined values
    
    this.baseURL = this.apiUrls[0];
    console.log(`Auth<PERSON>andler initialized with primary baseURL: ${this.baseURL}`);
    console.log(`AuthHandler fallback URLs: ${this.apiUrls.slice(1)}`);
    
    // Generate device ID on initialization
    this.deviceId = this.generateDeviceId();
    this.deviceInfo = this.getDeviceInfo();
  }

  /**
   * Generate a unique device ID based on hardware characteristics
   */
  generateDeviceId() {
    const cpus = os.cpus();
    const networkInterfaces = os.networkInterfaces();
    const hostname = os.hostname();
    const platform = os.platform();
    const arch = os.arch();
    
    // Combine various hardware characteristics
    const hardwareString = JSON.stringify({
      hostname,
      platform,
      arch,
      cpuModel: cpus[0]?.model || 'unknown',
      cpuCount: cpus.length,
      // Get MAC addresses (filter out internal interfaces)
      macAddresses: Object.values(networkInterfaces)
        .flat()
        .filter(iface => !iface.internal && iface.mac && iface.mac !== '00:00:00:00:00:00')
        .map(iface => iface.mac)
        .sort()
    });
    
    // Generate a consistent hash
    return crypto.createHash('sha256').update(hardwareString).digest('hex');
  }

  /**
   * Get device information
   */
  getDeviceInfo() {
    const platform = os.platform();
    const arch = os.arch();
    const hostname = os.hostname();
    
    // Generate user-friendly device name based on platform
    let deviceName = hostname;
    let platformName = `${platform}-${arch}`;
    
    // Create more descriptive device names
    if (platform === 'win32') {
      deviceName = this.getWindowsDeviceName(hostname);
      platformName = `Windows-${arch}`;
    } else if (platform === 'darwin') {
      deviceName = this.getMacDeviceName(hostname);
      platformName = `macOS-${arch}`;
    } else if (platform === 'linux') {
      deviceName = this.getLinuxDeviceName(hostname);
      platformName = `Linux-${arch}`;
    }
    
    return {
      deviceId: this.deviceId,
      deviceName: deviceName,
      platform: platformName,
      osVersion: os.release()
    };
  }

  /**
   * Generate Windows-specific device name
   */
  getWindowsDeviceName(hostname) {
    // Try to get more descriptive Windows info
    const cpus = os.cpus();
    const totalMemoryGB = Math.round(os.totalmem() / (1024 * 1024 * 1024));
    
    // Check if hostname suggests it's actually a Windows PC
    const cleanHostname = hostname.toLowerCase();
    
    // If hostname contains mac-related terms but we're on Windows, it's probably misconfigured
    if (cleanHostname.includes('macbook') || cleanHostname.includes('imac') || cleanHostname.includes('mac-')) {
      // Generate a Windows-appropriate name
      const cpuBrand = cpus[0]?.model.includes('Intel') ? 'Intel' : 
                     cpus[0]?.model.includes('AMD') ? 'AMD' : 'Windows';
      return `${cpuBrand} Windows PC (${totalMemoryGB}GB)`;
    }
    
    // Use hostname if it seems appropriate, otherwise generate a generic name
    if (cleanHostname.includes('desktop') || cleanHostname.includes('pc') || cleanHostname.includes('win')) {
      return hostname;
    }
    
    // Fallback to generic Windows device name
    return `Windows PC (${hostname})`;
  }

  /**
   * Generate macOS-specific device name
   */
  getMacDeviceName(hostname) {
    const cpus = os.cpus();
    
    // Check for Apple Silicon vs Intel
    const isAppleSilicon = cpus[0]?.model.includes('Apple') || os.arch() === 'arm64';
    
    if (isAppleSilicon) {
      // Try to determine if it's MacBook or other Mac
      if (hostname.toLowerCase().includes('macbook')) {
        return hostname.includes('MacBook') ? hostname : `MacBook (${hostname})`;
      }
      return `Apple Silicon Mac (${hostname})`;
    } else {
      // Intel Mac
      if (hostname.toLowerCase().includes('macbook')) {
        return hostname.includes('MacBook') ? hostname : `Intel MacBook (${hostname})`;
      } else if (hostname.toLowerCase().includes('imac')) {
        return hostname.includes('iMac') ? hostname : `Intel iMac (${hostname})`;
      }
      return `Intel Mac (${hostname})`;
    }
  }

  /**
   * Generate Linux-specific device name
   */
  getLinuxDeviceName(hostname) {
    const cpus = os.cpus();
    const totalMemoryGB = Math.round(os.totalmem() / (1024 * 1024 * 1024));
    
    // Try to get distribution info (basic detection)
    let distro = 'Linux';
    if (cpus[0]?.model.includes('Intel')) {
      distro = 'Intel Linux';
    } else if (cpus[0]?.model.includes('AMD')) {
      distro = 'AMD Linux';
    }
    
    return `${distro} (${hostname}, ${totalMemoryGB}GB)`;
  }

  /**
   * Authenticate using OAuth flow (preferred method)
   */
  async authenticateOAuth() {
    try {
      console.log('Starting OAuth authentication flow...');
      const result = await this.oauthFlow.authenticate();
      
      if (result.success && result.token) {
        // Store the token for future use
        await this.tokenManager.storeToken(result.token);
        
        // Always validate subscription status after authentication
        // Don't trust the frontend callback data
        console.log('OAuth authentication successful, validating subscription...');
        const subscriptionStatus = await this.getSubscriptionStatus();
        
        console.log('OAuth authentication complete:', {
          userId: result.userId,
          email: result.email,
          hasActiveSubscription: subscriptionStatus.hasActiveSubscription
        });
        
        return {
          success: true,
          token: result.token,
          userData: {
            ...result.userData,
            subscription: {
              hasActiveSubscription: subscriptionStatus.hasActiveSubscription,
              status: subscriptionStatus.hasActiveSubscription ? 'active' : 'inactive'
            }
          },
          authMethod: 'oauth'
        };
      }
      
      return { success: false, error: 'OAuth authentication failed' };
    } catch (error) {
      console.error('OAuth authentication error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Cancel any ongoing OAuth authentication
   */
  cancelOAuth() {
    this.oauthFlow.cancel();
  }

  async makeApiRequest(endpoint, options = {}) {
    let lastError = null;
    
    for (const baseUrl of this.apiUrls) {
      try {
        console.log(`Trying API endpoint: ${baseUrl}${endpoint}`);
        const response = await axios({
          url: `${baseUrl}${endpoint}`,
          timeout: 10000,
          ...options
        });
        console.log(`Success with endpoint: ${baseUrl}${endpoint}`);
        return response;
      } catch (error) {
        console.log(`Failed with endpoint ${baseUrl}${endpoint}:`, error.response?.status || error.message);
        lastError = error;
        continue;
      }
    }
    
    // If all endpoints failed, throw the last error
    throw lastError;
  }

  /**
   * Legacy authentication with email/password (fallback method)
   */
  async authenticate({ email, password }) {
    try {
      const response = await this.makeApiRequest('/auth/login', {
        method: 'POST',
        data: { email, password }
      });
      
      if (response.data && response.data.token) {
        await this.tokenManager.storeToken(response.data.token);
        return { 
          success: true, 
          token: response.data.token,
          authMethod: 'credentials'
        };
      }
      return { success: false, error: 'Invalid server response' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Check if user is already authenticated (has valid stored token)
   */
  async isAuthenticated() {
    try {
      const token = await this.tokenManager.getToken();
      if (!token) {
        return { authenticated: false, reason: 'no_token' };
      }

      // Validate token format (should be a JWT with 3 parts)
      const parts = token.split('.');
      if (parts.length !== 3) {
        console.warn('Invalid token format (not a valid JWT)');
        return { authenticated: false, reason: 'invalid_token_format' };
      }

      // Check if any part is empty
      if (parts.some(part => !part)) {
        console.warn('Invalid token: contains empty parts');
        return { authenticated: false, reason: 'invalid_token_structure' };
      }

      // Try to get subscription status to verify token is still valid
      const subscriptionStatus = await this.getSubscriptionStatus();
      
      if (subscriptionStatus.error && subscriptionStatus.error.includes('token')) {
        return { authenticated: false, reason: 'token_expired' };
      }

      return { 
        authenticated: true, 
        hasSubscription: subscriptionStatus.hasActiveSubscription || false 
      };

    } catch (error) {
      console.error('Error checking authentication status:', error);
      return { authenticated: false, reason: 'check_failed', error: error.message };
    }
  }

  async getSubscriptionStatus() {
    const token = await this.tokenManager.getToken();
    if (!token) return { hasSubscription: false, error: 'No authentication token' };

    // Validate token format (should be a JWT with 3 parts)
    const parts = token.split('.');
    if (parts.length !== 3) {
      console.warn('Invalid token format (not a valid JWT)');
      return { hasSubscription: false, error: 'Invalid token format' };
    }

    // Check if any part is empty
    if (parts.some(part => !part)) {
      console.warn('Invalid token: contains empty parts');
      return { hasSubscription: false, error: 'Invalid token structure' };
    }

    try {
      console.log('AuthHandler: Making subscription status request with token length:', token.length);
      
      const response = await this.makeApiRequest('/subscription/status', {
        method: 'GET',
        headers: { 
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('AuthHandler: Subscription API response:', response.status, response.data);
      
      // After getting subscription status, check device seat
      if (response.data.hasActiveSubscription) {
        const seatStatus = await this.checkDeviceSeat(response.data.userId, response.data.plan);
        response.data.seatStatus = seatStatus;
        
        // If seat check fails, override subscription status
        if (!seatStatus.canUse) {
          response.data.hasActiveSubscription = false;
          response.data.seatError = seatStatus.error;
        }
      }
      
      return response.data;
    } catch (error) {
      console.error('AuthHandler: Subscription API error:', error.response?.status, error.response?.data || error.message);
      return { hasSubscription: false, error: error.response?.data?.message || error.message };
    }
  }

  /**
   * Check and register device seat
   */
  async checkDeviceSeat(userId, subscriptionPlan) {
    const token = await this.tokenManager.getToken();
    if (!token) return { canUse: false, error: 'No authentication token' };

    try {
      console.log('Checking device seat for user:', userId);
      console.log('Device info:', this.deviceInfo);
      
      const response = await this.makeApiRequest('/device/check-seat', {
        method: 'POST',
        headers: { 
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        data: {
          deviceInfo: this.deviceInfo,
          subscriptionPlan
        }
      });
      
      console.log('Device seat check response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Device seat check error:', error.response?.data || error.message);
      return { 
        canUse: false, 
        error: error.response?.data?.message || 'Failed to verify device seat' 
      };
    }
  }

  /**
   * Clear stored authentication token
   */
  async logout() {
    try {
      await this.tokenManager.clearToken();
      console.log('Successfully logged out - token cleared');
      return { success: true };
    } catch (error) {
      console.error('Error during logout:', error);
      return { success: false, error: error.message };
    }
  }
}

module.exports = { AuthHandler }; 