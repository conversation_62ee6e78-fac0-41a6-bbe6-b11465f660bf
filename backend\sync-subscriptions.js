// Load environment variables
require('dotenv').config();

const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase URL or service key is missing. Please check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function syncSubscriptions() {
  try {
    console.log('Starting subscription sync...');
    
    // Get all users from Supabase
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('*');
    
    if (usersError) {
      console.error('Error fetching users from Supabase:', usersError);
      process.exit(1);
    }
    
    console.log(`Found ${users.length} users in Supabase`);
    
    // Process each user
    for (const user of users) {
      console.log(`Processing user: ${user.id} (${user.email})`);
      
      // If user has a Stripe customer ID, check their subscriptions
      if (user.stripe_customer_id) {
        console.log(`User has Stripe customer ID: ${user.stripe_customer_id}`);
        
        try {
          // Get all subscriptions for this customer
          const subscriptions = await stripe.subscriptions.list({
            customer: user.stripe_customer_id,
            status: 'all',
            limit: 100
          });
          
          console.log(`Found ${subscriptions.data.length} subscriptions for customer ${user.stripe_customer_id}`);
          
          // Find the most recent active subscription
          const activeSubscription = subscriptions.data.find(sub => sub.status === 'active');
          
          if (activeSubscription) {
            console.log(`Found active subscription: ${activeSubscription.id}`);
            
            // Update user with subscription information
            const { error: updateError } = await supabase
              .from('users')
              .update({
                subscription_id: activeSubscription.id,
                subscription_status: 'active',
                subscription_updated_at: new Date().toISOString()
              })
              .eq('id', user.id);
            
            if (updateError) {
              console.error(`Error updating user ${user.id}:`, updateError);
            } else {
              console.log(`Updated user ${user.id} with active subscription ${activeSubscription.id}`);
            }
          } else {
            // Check if there's a canceled subscription
            const canceledSubscription = subscriptions.data.find(sub => sub.status === 'canceled');
            
            if (canceledSubscription) {
              console.log(`Found canceled subscription: ${canceledSubscription.id}`);
              
              // Update user with canceled subscription information
              const { error: updateError } = await supabase
                .from('users')
                .update({
                  subscription_id: null,
                  subscription_status: 'canceled',
                  subscription_updated_at: new Date().toISOString()
                })
                .eq('id', user.id);
              
              if (updateError) {
                console.error(`Error updating user ${user.id}:`, updateError);
              } else {
                console.log(`Updated user ${user.id} with canceled subscription status`);
              }
            } else if (user.subscription_status === 'active') {
              // User has no active subscription in Stripe but is marked as active in our database
              console.log(`User ${user.id} is marked as active but has no active subscription in Stripe`);
              
              // Update user to inactive
              const { error: updateError } = await supabase
                .from('users')
                .update({
                  subscription_id: null,
                  subscription_status: 'inactive',
                  subscription_updated_at: new Date().toISOString()
                })
                .eq('id', user.id);
              
              if (updateError) {
                console.error(`Error updating user ${user.id}:`, updateError);
              } else {
                console.log(`Updated user ${user.id} to inactive status`);
              }
            }
          }
        } catch (error) {
          console.error(`Error processing subscriptions for user ${user.id}:`, error);
        }
      } else if (user.email) {
        // If user doesn't have a Stripe customer ID but has an email, try to find them in Stripe
        console.log(`User doesn't have Stripe customer ID, searching by email: ${user.email}`);
        
        try {
          // Search for customer by email
          const customers = await stripe.customers.list({
            email: user.email,
            limit: 1
          });
          
          if (customers.data.length > 0) {
            const customer = customers.data[0];
            console.log(`Found Stripe customer for email ${user.email}: ${customer.id}`);
            
            // Update user with Stripe customer ID
            const { error: updateError } = await supabase
              .from('users')
              .update({
                stripe_customer_id: customer.id
              })
              .eq('id', user.id);
            
            if (updateError) {
              console.error(`Error updating user ${user.id} with Stripe customer ID:`, updateError);
            } else {
              console.log(`Updated user ${user.id} with Stripe customer ID ${customer.id}`);
              
              // Now check for subscriptions
              const subscriptions = await stripe.subscriptions.list({
                customer: customer.id,
                status: 'all',
                limit: 100
              });
              
              console.log(`Found ${subscriptions.data.length} subscriptions for customer ${customer.id}`);
              
              // Find the most recent active subscription
              const activeSubscription = subscriptions.data.find(sub => sub.status === 'active');
              
              if (activeSubscription) {
                console.log(`Found active subscription: ${activeSubscription.id}`);
                
                // Update user with subscription information
                const { error: subUpdateError } = await supabase
                  .from('users')
                  .update({
                    subscription_id: activeSubscription.id,
                    subscription_status: 'active',
                    subscription_updated_at: new Date().toISOString()
                  })
                  .eq('id', user.id);
                
                if (subUpdateError) {
                  console.error(`Error updating user ${user.id} with subscription:`, subUpdateError);
                } else {
                  console.log(`Updated user ${user.id} with active subscription ${activeSubscription.id}`);
                }
              }
            }
          } else {
            console.log(`No Stripe customer found for email ${user.email}`);
          }
        } catch (error) {
          console.error(`Error searching for customer by email ${user.email}:`, error);
        }
      }
    }
    
    console.log('Subscription sync completed successfully');
  } catch (error) {
    console.error('Error during subscription sync:', error);
    process.exit(1);
  }
}

// Run the sync
syncSubscriptions().catch(console.error);
