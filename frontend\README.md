<a href="https://createlex.com">
  <h1 align="center">CreateLex AiChat</h1>
</a>

<p align="center">
  An open-source AI chatbot app powered by Model Context Protocol (MCP), built with Next.js and the AI SDK by Vercel.
</p>

<p align="center">
  <a href="#features"><strong>Features</strong></a> •
  <a href="#mcp-server-configuration"><strong>MCP Configuration</strong></a> •
  <a href="#production-deployment"><strong>Production</strong></a> •
  <a href="#license"><strong>License</strong></a>
</p>
<br/>

## Features

- Streaming text responses powered by the [AI SDK by Vercel](https://sdk.vercel.ai/docs), allowing multiple AI providers to be used interchangeably with just a few lines of code.
- Full integration with [Model Context Protocol (MCP)](https://modelcontextprotocol.io) servers to expand available tools and capabilities.
- Multiple MCP transport types (SSE and stdio) for connecting to various tool providers.
- Built-in tool integration for extending AI capabilities.
- Reasoning model support.
- [shadcn/ui](https://ui.shadcn.com/) components for a modern, responsive UI powered by [Tailwind CSS](https://tailwindcss.com).
- Built with the latest [Next.js](https://nextjs.org) App Router.

## MCP Server Configuration

This application supports connecting to Model Context Protocol (MCP) servers to access their tools. You can add and manage MCP servers through the settings icon in the chat interface.

### Adding an MCP Server

1. Click the settings icon (⚙️) next to the model selector in the chat interface.
2. Enter a name for your MCP server.
3. Select the transport type:
   - **SSE (Server-Sent Events)**: For HTTP-based remote servers
   - **stdio (Standard I/O)**: For local servers running on the same machine

#### SSE Configuration

If you select SSE transport:
1. Enter the server URL (e.g., `https://mcp.example.com/token/sse`)
2. Click "Add Server"

#### stdio Configuration

If you select stdio transport:
1. Enter the command to execute (e.g., `npx`)
2. Enter the command arguments (e.g., `-y @modelcontextprotocol/server-google-maps`)
   - You can enter space-separated arguments or paste a JSON array
3. Click "Add Server"

4. Click "Use" to activate the server for the current chat session.

### Available MCP Servers

You can use any MCP-compatible server with this application. Here are some examples:

- [Composio](https://composio.dev/mcp) - Provides search, code interpreter, and other tools
- [Zapier MCP](https://zapier.com/mcp) - Provides access to Zapier tools
- Any MCP server using stdio transport with npx and python3

## Production Deployment

When deploying this application to production, follow these steps to ensure proper authentication and subscription enforcement:

### Environment Configuration

1. Create a `.env.production` file based on the `.env.production.template` provided.
2. Set all required environment variables with your production values.
3. Ensure the following variables are set correctly for production:
   ```
   NEXT_PUBLIC_BYPASS_AUTH=false
   NEXT_PUBLIC_BYPASS_SUBSCRIPTION=false
   NODE_ENV=production
   ```

### Authentication Setup

The application uses Supabase for authentication. Make sure your Supabase project is properly configured with:
- Email/password authentication
- Social login providers (if needed)
- Proper security settings and redirects

### Subscription Verification

The application checks for active subscriptions through the backend API. Ensure your backend API:
- Is properly secured
- Correctly verifies subscription status
- Returns the expected response format

### Digital Ocean Deployment (Without Docker)

We've provided a comprehensive guide for deploying to Digital Ocean without Docker. Please refer to the [Digital Ocean Setup Guide](./DIGITAL_OCEAN_SETUP.md) for detailed instructions.

#### Quick Deployment

For a quick deployment to Digital Ocean, you can use the provided deploy script:

```bash
npm run deploy
```

This script will:
1. Install dependencies
2. Build the application
3. Set up PM2 for process management
4. Start the application

### Testing Production Behavior

For development, authentication and subscription checks are bypassed by default in the code.

To test production authentication and subscription behavior:
1. Open `middleware.ts` and change `const bypassAuth = true;` to `const bypassAuth = false;`
2. Also change `const bypassSubscription = true;` to `const bypassSubscription = false;`
3. Make the same changes in `contexts/AuthContext.tsx`
4. This will force the application to perform actual authentication and subscription checks

For easier development, keep these values set to `true`.

## License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.