# CreateLex Bridge Seat Management System

## Overview

The CreateLex Bridge application implements a robust seat management system to ensure fair usage and prevent abuse. Each subscription includes a limited number of concurrent device seats, allowing users to install and use the application on multiple devices while preventing excessive usage.

## How It Works

### 1. Device Identification

Each device is uniquely identified using a combination of hardware characteristics:
- Hostname
- Platform (OS type and architecture)
- CPU model and count
- MAC addresses of network interfaces

This information is hashed to create a unique device ID that remains consistent across sessions.

### 2. Seat Limits by Subscription Tier

- **Free**: 1 device
- **Basic**: 2 devices
- **Premium**: 2 devices  
- **Pro**: 5 devices
- **Enterprise**: 10 devices

### 3. Device Registration Process

When a user launches the CreateLex Bridge:

1. **Device ID Generation**: The application generates a unique device ID based on hardware characteristics
2. **Authentication**: User authenticates via OAuth
3. **Seat Check**: The application checks if the device can be registered:
   - If the device is already registered, it updates the last active timestamp
   - If it's a new device and seats are available, it registers the device
   - If the seat limit is reached, the user must deactivate another device first

### 4. Activity Tracking

- **Heartbeat System**: The bridge sends a heartbeat every 10 minutes to keep the device marked as active
- **Automatic Deactivation**: Devices inactive for 30 days are automatically deactivated
- **Manual Deactivation**: Users can manually deactivate devices through the dashboard

## Implementation Details

### Database Schema

```sql
CREATE TABLE device_seats (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id),
    device_id VARCHAR(255) NOT NULL,
    device_name VARCHAR(255),
    platform VARCHAR(50),
    last_active TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE,
    ip_address INET,
    is_active BOOLEAN DEFAULT true,
    UNIQUE(user_id, device_id)
);
```

### API Endpoints

#### POST /api/device/check-seat
Checks and registers a device seat for the authenticated user.

**Request Body:**
```json
{
  "deviceInfo": {
    "deviceId": "sha256-hash",
    "deviceName": "Johns-MacBook",
    "platform": "darwin-arm64",
    "osVersion": "23.1.0"
  }
}
```

**Response:**
```json
{
  "canUse": true,
  "deviceId": "sha256-hash",
  "seatCount": 2,
  "seatLimit": 2,
  "message": "Device registered successfully"
}
```

#### GET /api/device/seats
Gets all device seats for the authenticated user.

**Response:**
```json
{
  "devices": [
    {
      "id": "123",
      "device_id": "sha256-hash",
      "device_name": "Johns-MacBook",
      "platform": "darwin-arm64",
      "last_active": "2024-01-15T10:30:00Z",
      "is_active": true
    }
  ],
  "seatLimit": 2,
  "activeCount": 1
}
```

#### DELETE /api/device/seats/:deviceId
Deactivates a specific device seat.

#### POST /api/device/heartbeat
Updates device activity timestamp.

**Request Body:**
```json
{
  "deviceId": "sha256-hash"
}
```

## User Experience

### When Seat Limit is Reached

If a user tries to use the application on a new device when their seat limit is reached:

1. The bridge will show an error message
2. The user will be directed to the dashboard to manage their devices
3. They can deactivate an old/unused device
4. After deactivation, they can use the application on the new device

### Dashboard Device Management

The dashboard provides a comprehensive view of:
- Current device usage (X of Y seats used)
- List of all registered devices with:
  - Device name and platform
  - Last active timestamp
  - IP address
  - Ability to deactivate devices
- Clear indication of which device is currently being used

## Security Considerations

1. **Device ID Tampering**: The device ID is generated from hardware characteristics that are difficult to spoof
2. **Token Security**: All API calls require valid authentication tokens
3. **IP Tracking**: IP addresses are logged for security auditing
4. **Rate Limiting**: Heartbeat endpoints are rate-limited to prevent abuse

## Benefits

1. **Fair Usage**: Prevents users from sharing accounts or using the service on unlimited devices
2. **Security**: Helps identify and prevent unauthorized access
3. **Analytics**: Provides insights into how users interact with the application
4. **Flexibility**: Users can manage their own devices without support intervention

## Future Enhancements

1. **Device Naming**: Allow users to set custom names for their devices
2. **Notifications**: Email alerts when new devices are registered
3. **Grace Period**: Allow temporary access on new devices before enforcing limits
4. **Device Groups**: Support for team/organization device management 