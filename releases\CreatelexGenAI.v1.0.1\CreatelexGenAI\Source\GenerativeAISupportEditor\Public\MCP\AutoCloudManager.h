#pragma once

#include "CoreMinimal.h"
#include "Subsystems/EngineSubsystem.h"
#include "HAL/PlatformProcess.h"
#include "AutoCloudManager.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAutoCloudManager, Log, All);

USTRUCT(BlueprintType)
struct FCloudConnectionInfo
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly)
    bool bIsConnected = false;

    UPROPERTY(BlueprintReadOnly)
    FString TunnelUrl;

    UPROPERTY(BlueprintReadOnly)
    FString McpUrl;

    UPROPERTY(BlueprintReadOnly)
    FString InstanceId;
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCloudConnectionEstablished, bool, bSuccess, const FString&, Message);

/**
 * Automatic Cloud Manager for Unreal Engine MCP Plugin
 * Handles automatic cloud connectivity without user intervention
 */
UCLASS(BlueprintType)
class GENERATIVEAISUPPORTEDITOR_API UAutoCloudManager : public UEngineSubsystem
{
    GENERATED_BODY()

public:
    UAutoCloudManager();

    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;

    /**
     * Start automatic cloud connection
     */
    UFUNCTION(BlueprintCallable, Category = "Auto Cloud")
    void StartAutoConnection();

    /**
     * Stop cloud connection
     */
    UFUNCTION(BlueprintCallable, Category = "Auto Cloud")
    void StopCloudConnection();

    /**
     * Get current cloud connection information
     */
    UFUNCTION(BlueprintCallable, Category = "Auto Cloud")
    FCloudConnectionInfo GetCloudConnectionInfo() const;

    /**
     * Check if cloud connection is active
     */
    UFUNCTION(BlueprintCallable, Category = "Auto Cloud")
    bool IsCloudConnected() const { return bIsCloudConnected; }

    /**
     * Enable or disable automatic cloud connection
     */
    UFUNCTION(BlueprintCallable, Category = "Auto Cloud")
    void SetAutoConnectEnabled(bool bEnabled);

    /**
     * Event fired when cloud connection is established or fails
     */
    UPROPERTY(BlueprintAssignable)
    FOnCloudConnectionEstablished OnCloudConnectionEstablished;

private:
    /**
     * Execute the Python auto-connector script
     */
    void ExecuteAutoConnector();

    /**
     * Monitor the cloud connection process
     */
    void MonitorCloudConnection();

    /**
     * Check cloud connection status by reading config file
     */
    bool CheckCloudConnectionStatus();

private:
    /** Whether cloud connection is currently active */
    UPROPERTY()
    bool bIsCloudConnected;

    /** Whether auto-connect is enabled */
    UPROPERTY()
    bool bAutoConnectEnabled;

    /** Process handle for the cloud connector */
    FProcHandle CloudConnectorProcess;

    /** Current tunnel URL */
    UPROPERTY()
    FString CloudTunnelUrl;

    /** Current MCP cloud URL */
    UPROPERTY()
    FString CloudMcpUrl;

    /** Current instance ID */
    UPROPERTY()
    FString CloudInstanceId;
}; 