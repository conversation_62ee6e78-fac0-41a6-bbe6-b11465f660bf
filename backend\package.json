{"name": "ai-webplatform-backend", "version": "0.1.0", "private": true, "main": "src/index.js", "scripts": {"prestart": "npm install", "start": "cross-env NODE_ENV=production node src/index.js", "start:production": "node start-production.js", "start:native": "node src/index.js", "dev": "cross-env NODE_ENV=development nodemon src/index.js", "dev:port": "cross-env PORT=5001 NODE_ENV=development nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1", "migrate-users": "node migrations/migrate-users.js", "migrate-admin-tables": "node migrations/run-admin-tables.js"}, "dependencies": {"@supabase/supabase-js": "^2.49.4", "axios": "^1.8.4", "body-parser": "^1.20.2", "cors": "^2.8.5", "cross-env": "^7.0.3", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "google-auth-library": "^9.6.3", "jsonwebtoken": "^9.0.2", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "pg": "^8.15.6", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "stripe": "^14.17.0", "ws": "^8.18.1"}, "devDependencies": {"nodemon": "^3.1.0"}}