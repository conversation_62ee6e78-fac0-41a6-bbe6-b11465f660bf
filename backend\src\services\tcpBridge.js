const net = require('net');

class TcpBridge {
  constructor() {
    this.connected = false;
    this.socket = null;
    this.pendingCommands = [];
    this.callbacks = new Map();
    this.reconnectInterval = null;
    this.reconnectDelay = 10000; // 10 seconds
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5; // Maximum number of reconnect attempts
    this.host = process.env.UNREAL_ENGINE_HOST || '127.0.0.1';
    this.port = process.env.UNREAL_ENGINE_PORT || 9877;
    this.apiKey = process.env.UNREAL_API_KEY || 'your_default_api_key'; // Default API key for authentication
    this.connecting = false; // Flag to prevent multiple simultaneous connection attempts
    this.keepConnectionOpen = true; // Flag to keep the connection open after sending commands
    this.dataBuffer = ''; // Buffer for incoming data

    // Automatically connect when created
    this.connect();
  }

  connect() {
    // Prevent multiple simultaneous connection attempts
    if (this.connecting) {
      console.log('[Tcp<PERSON>ridge] Already attempting to connect, skipping');
      return;
    }

    this.connecting = true;
    this.reconnectAttempts++;

    console.log(`[TcpBridge] Connecting to Unreal Engine at ${this.host}:${this.port} (attempt ${this.reconnectAttempts})`);

    // Create a new TCP socket
    this.socket = new net.Socket();

    // Set up event handlers
    this.socket.on('connect', () => {
      console.log(`[TcpBridge] Connected to Unreal Engine at ${this.host}:${this.port}`);
      this.connected = true;
      this.connecting = false;
      this.reconnectAttempts = 0; // Reset reconnect attempts on successful connection

      // Clear any reconnect interval
      if (this.reconnectInterval) {
        clearInterval(this.reconnectInterval);
        this.reconnectInterval = null;
      }

      // Process any pending commands with a short delay to ensure connection is ready
      setTimeout(() => {
        this.processPendingCommands();
      }, 500);
    });

    this.socket.on('data', (data) => {
      try {
        // Add the new data to our buffer
        this.dataBuffer += data.toString();

        // Process complete JSON objects in the buffer
        let jsonEndIndex;
        while ((jsonEndIndex = this.findJsonEnd()) !== -1) {
          // Extract a complete JSON object
          const jsonStr = this.dataBuffer.substring(0, jsonEndIndex + 1);
          this.dataBuffer = this.dataBuffer.substring(jsonEndIndex + 1);

          try {
            const response = JSON.parse(jsonStr);
            console.log(`[TcpBridge] Received response: ${JSON.stringify(response)}`);

            // Check if we have a callback for this response
            if (response.id && this.callbacks.has(response.id)) {
              const callback = this.callbacks.get(response.id);
              callback(response);
              this.callbacks.delete(response.id);
            }
          } catch (jsonError) {
            console.error(`[TcpBridge] Error parsing JSON response: ${jsonError.message}`);
          }
        }
      } catch (error) {
        console.error(`[TcpBridge] Error processing data: ${error.message}`);
      }
    });

    this.socket.on('error', (error) => {
      console.error(`[TcpBridge] Socket error: ${error.message}`);
      this.connecting = false;

      // Handle connection refused errors
      if (error.code === 'ECONNREFUSED') {
        console.log('[TcpBridge] Connection refused, Unreal Engine socket server may not be running');

        // If we've reached the maximum number of reconnect attempts, stop trying
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          console.log(`[TcpBridge] Maximum reconnect attempts (${this.maxReconnectAttempts}) reached, giving up`);

          // Clear any reconnect interval
          if (this.reconnectInterval) {
            clearInterval(this.reconnectInterval);
            this.reconnectInterval = null;
          }

          // Fail any pending commands
          this.failPendingCommands('Connection to Unreal Engine failed after multiple attempts');
          return;
        }
      }
    });

    this.socket.on('close', () => {
      console.log('[TcpBridge] Disconnected from Unreal Engine');
      this.connected = false;
      this.connecting = false;

      // Only attempt to reconnect if we want to keep the connection open
      if (this.keepConnectionOpen) {
        // Schedule reconnection if we haven't reached the maximum number of attempts
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          if (!this.reconnectInterval) {
            console.log(`[TcpBridge] Scheduling reconnection in ${this.reconnectDelay / 1000} seconds`);
            this.reconnectInterval = setInterval(() => {
              this.connect();
            }, this.reconnectDelay);
          }
        } else {
          console.log(`[TcpBridge] Maximum reconnect attempts (${this.maxReconnectAttempts}) reached, giving up`);

          // Clear any reconnect interval
          if (this.reconnectInterval) {
            clearInterval(this.reconnectInterval);
            this.reconnectInterval = null;
          }

          // Fail any pending commands
          this.failPendingCommands('Connection to Unreal Engine failed after multiple attempts');
        }
      } else {
        console.log('[TcpBridge] Connection closed intentionally, not reconnecting');
      }
    });

    // Connect to the Unreal Engine socket server
    this.socket.connect(this.port, this.host);

    // Set a connection timeout
    setTimeout(() => {
      if (this.connecting) {
        console.log('[TcpBridge] Connection attempt timed out');
        this.connecting = false;
        if (this.socket) {
          this.socket.destroy();
        }
      }
    }, 5000); // 5 second timeout
  }

  processPendingCommands() {
    if (this.pendingCommands.length > 0) {
      console.log(`[TcpBridge] Processing ${this.pendingCommands.length} pending commands`);

      // Make a copy of the pending commands before processing
      const commandsToProcess = [...this.pendingCommands];

      // Clear the pending commands
      this.pendingCommands = [];

      // Process each pending command
      commandsToProcess.forEach(({ command, callback }) => {
        console.log('[TcpBridge] Processing pending command:', JSON.stringify(command));

        // Send the command directly to avoid double-formatting
        try {
          // Generate a unique ID for this command
          const id = Date.now().toString();

          // Store the callback for when we get a response
          if (callback) {
            this.callbacks.set(id, callback);

            // Set a timeout for the response
            setTimeout(() => {
              if (this.callbacks.has(id)) {
                console.log(`[TcpBridge] Response timeout for command with ID ${id}`);
                const timeoutCallback = this.callbacks.get(id);
                this.callbacks.delete(id);
                timeoutCallback({
                  success: false,
                  error: 'Response timeout',
                  timestamp: new Date().toISOString()
                });
              }
            }, 30000); // 30 second timeout
          }

          // Make sure the connection is still open
          if (this.socket && this.socket.writable) {
            // Format the command properly for the Unreal Engine MCP socket server
            let formattedCommand;

            if (command.command === 'sequence') {
              // Handle sequence command by processing each step individually
              console.log('[TcpBridge] Processing sequence command with steps:', command.steps.length);

              // Process each step in the sequence
              this.processSequence(command.steps, callback);
              return; // Skip the rest of the processing
            } else if (command.command === 'spawn_object') {
              // Map spawn_object to spawn for Unreal Engine MCP
              formattedCommand = {
                id,
                type: 'spawn', // Use 'spawn' instead of 'spawn_object'
                actor_class: command.params.actor_class,
                location: command.params.location,
                rotation: command.params.rotation || [0, 0, 0],
                scale: command.params.scale || [1, 1, 1],
                actor_label: command.params.actor_label || `${command.params.actor_class}_${Date.now()}`,
                api_key: this.apiKey // Add API key for authentication
              };

              // If a color is specified, add it to the command
              if (command.params.color) {
                formattedCommand.color = command.params.color;
                console.log('[TcpBridge] Added color to spawn command:', JSON.stringify(command.params.color));
              }
            } else if (command.command === 'execute_python') {
              // Handle Python script execution
              formattedCommand = {
                id,
                type: 'execute_python',
                script: command.params.script,
                api_key: this.apiKey // Add API key for authentication
              };
            } else if (command.command === 'create_material') {
              formattedCommand = {
                id,
                type: 'create_material',
                material_name: command.params.material_name,
                color: command.params.color,
                api_key: this.apiKey // Add API key for authentication
              };
            } else if (command.command === 'set_object_material') {
              formattedCommand = {
                id,
                type: 'modify_object', // Using correct type expected by MCP
                actor_name: command.params.actor_name,
                property_type: 'material',
                value: command.params.material_path, // The material path
                connect_to_base_color: true, // Connect the material to the base color
                api_key: this.apiKey // Add API key for authentication
              };
            } else if (command.command === 'modify_object') {
              formattedCommand = {
                id,
                type: 'modify_object',
                actor_name: command.params.actor_name,
                property_type: command.params.property_type,
                value: command.params.value,
                api_key: this.apiKey // Add API key for authentication
              };
            } else {
              // Default case - just add API key
              formattedCommand = { ...command, api_key: this.apiKey };
            }

            console.log('[TcpBridge] Sending formatted pending command:', JSON.stringify(formattedCommand));
            this.socket.write(JSON.stringify(formattedCommand) + '\n'); // Add newline for proper parsing
          } else {
            console.error('[TcpBridge] Socket not writable, command not sent');
            if (callback) {
              const err = {
                success: false,
                error: 'Socket connection lost',
                timestamp: new Date().toISOString()
              };
              this.callbacks.delete(id);
              callback(err);
            }
          }
        } catch (error) {
          console.error(`[TcpBridge] Error sending command: ${error.message}`);
          if (callback) {
            callback({
              success: false,
              error: error.message,
              timestamp: new Date().toISOString(),
              stack: error.stack
            });
          }
        }
      });
    }
  }

  sendCommand(command, callback) {
    if (!this.connected) {
      console.log('[TcpBridge] Not connected, queueing command:', JSON.stringify(command));

      // Store the command object directly
      const queuedCommand = {
        command: { ...command }, // Create a deep copy of the command
        callback
      };
      this.pendingCommands.push(queuedCommand);

      // Attempt to connect if not already connecting
      if (!this.connecting && this.reconnectAttempts < this.maxReconnectAttempts) {
        console.log('[TcpBridge] Attempting to connect to process command');
        this.connect();
      }
      return;
    }

    try {
      // Generate a unique ID for this command
      const id = Date.now().toString();

      // Format the command for the Unreal Engine MCP socket server
      // Based on UnrealGenAISupport plugin's expected format
      let formattedCommand;

      // Add timestamp to all commands
      const timestamp = new Date().toISOString();

      if (command.command === 'test_unreal_connection') {
        // Special case for test_unreal_connection
        formattedCommand = {
          id,
          type: 'handshake',
          message: 'Testing connection from CreateLex AI Assistant',
          api_key: this.apiKey
        };
      } else if (command.command === 'spawn_object') {
        // Map spawn_object to spawn for Unreal Engine MCP
        formattedCommand = {
          id,
          type: 'spawn', // Use 'spawn' instead of 'spawn_object'
          actor_class: command.params.actor_class,
          location: command.params.location,
          rotation: command.params.rotation || [0, 0, 0],
          scale: command.params.scale || [1, 1, 1],
          actor_label: command.params.actor_label || `${command.params.actor_class}_${Date.now()}`,
          api_key: this.apiKey // Add API key for authentication
        };

        // If a color is specified, add it to the command
        if (command.params.color) {
          formattedCommand.color = command.params.color;
          console.log('[TcpBridge] Added color to spawn command:', JSON.stringify(command.params.color));
        }
      } else if (command.command === 'execute_python') {
        // Handle Python script execution
        formattedCommand = {
          id,
          type: 'execute_python',
          script: command.params.script,
          api_key: this.apiKey // Add API key for authentication
        };
      } else if (command.command === 'create_material') {
        formattedCommand = {
          id,
          type: 'create_material',
          material_name: command.params.material_name,
          color: command.params.color,
          api_key: this.apiKey // Add API key for authentication
        };
      } else if (command.command === 'set_object_material') {
        formattedCommand = {
          id,
          type: 'modify_object', // Using correct type expected by MCP
          actor_name: command.params.actor_name,
          property_type: 'material',
          value: command.params.material_path, // The material path
          connect_to_base_color: true, // Connect the material to the base color
          api_key: this.apiKey // Add API key for authentication
        };
      } else if (command.command === 'sequence') {
        // Handle sequence of commands
        console.log('[TcpBridge] Processing sequence of commands');
        this.processSequence(command.steps, callback);
        return;
      } else {
        // Default case - pass through the command as type and include params
        formattedCommand = {
          id,
          type: command.command,
          ...command.params,
          api_key: this.apiKey // Add API key for authentication
        };
      }

      // Store the callback for when we get a response
      if (callback) {
        this.callbacks.set(id, callback);

        // Set a timeout for the response
        setTimeout(() => {
          if (this.callbacks.has(id)) {
            console.log(`[TcpBridge] Response timeout for command with ID ${id}`);
            const timeoutCallback = this.callbacks.get(id);
            this.callbacks.delete(id);
            timeoutCallback({
              success: false,
              error: 'Response timeout',
              timestamp: new Date().toISOString()
            });
          }
        }, 30000); // 30 second timeout
      }

      // Send the message
      const jsonString = JSON.stringify(formattedCommand);
      console.log(`[TcpBridge] Sending formatted command to Unreal Engine: ${jsonString}`);

      // Make sure the connection is still open
      if (this.socket && this.socket.writable) {
        this.socket.write(jsonString + '\n'); // Add newline for proper parsing
      } else {
        console.error('[TcpBridge] Socket not writable, command not sent');
        if (callback) {
          const err = {
            success: false,
            error: 'Socket connection lost',
            timestamp: new Date().toISOString()
          };
          this.callbacks.delete(id);
          callback(err);
        }
      }
    } catch (error) {
      console.error(`[TcpBridge] Error sending command: ${error.message}`);
      if (callback) {
        callback({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
          stack: error.stack
        });
      }
    }
  }

  processSequence(steps, callback) {
    console.log(`[TcpBridge] Processing sequence with ${steps.length} steps:`, JSON.stringify(steps));

    // Process a sequence of commands one after the other
    const results = [];
    let currentStep = 0;

    const processNextStep = () => {
      if (currentStep >= steps.length) {
        // All steps completed
        console.log('[TcpBridge] All sequence steps completed successfully');
        if (callback) {
          callback({ success: true, results });
        }
        return;
      }

      const step = steps[currentStep];
      console.log(`[TcpBridge] Executing step ${currentStep + 1}/${steps.length}: ${step.command}`, JSON.stringify(step));

      // Format the step for TcpBridge sendCommand
      const formattedStep = {
        command: step.command,
        params: step.params
      };

      // Create a fresh TcpBridge for each step to avoid connection issues
      const newTcpBridge = new TcpBridge();

      // Add a small delay before sending the command to ensure the connection is ready
      setTimeout(() => {
        console.log(`[TcpBridge] Sending step ${currentStep + 1} command after delay`);

        // Special handling for spawn commands
        if (step.command === 'spawn_object' && currentStep === 0) {
          // For the first step (spawn), we'll ignore timeouts
          newTcpBridge.sendCommand(formattedStep, (result) => {
            // If it's a timeout, assume success for spawn commands
            if (result.error === 'Response timeout') {
              console.log(`[TcpBridge] Ignoring timeout for spawn command in sequence, assuming success`);
              results.push({ success: true, message: 'Created object successfully' });
              currentStep++;
              setTimeout(() => {
                processNextStep();
              }, 1000);
              return;
            }

            // Normal processing for non-timeout results
            console.log(`[TcpBridge] Step ${currentStep + 1} result:`, JSON.stringify(result));
            results.push(result);

            if (result.error) {
              console.error(`[TcpBridge] Error in step ${currentStep + 1}:`, result.error);
              if (callback) {
                callback({ success: false, error: `Error in step ${currentStep + 1}: ${result.error}`, results });
              }
              return;
            }

            console.log(`[TcpBridge] Step ${currentStep + 1} completed successfully`);
            currentStep++;

            // Add delay between steps to prevent connection issues
            setTimeout(() => {
              processNextStep();
            }, 1000); // 1 second delay between steps
          });
        } else {
          // Normal processing for non-spawn commands
          // Send the step command
          newTcpBridge.sendCommand(formattedStep, (result) => {
          console.log(`[TcpBridge] Step ${currentStep + 1} result:`, JSON.stringify(result));

          results.push(result);

          if (result.error) {
            console.error(`[TcpBridge] Error in step ${currentStep + 1}:`, result.error);
            if (callback) {
              callback({ success: false, error: `Error in step ${currentStep + 1}: ${result.error}`, results });
            }
            return;
          }

          console.log(`[TcpBridge] Step ${currentStep + 1} completed successfully`);
          currentStep++;

          // Add delay between steps to prevent connection issues
          setTimeout(() => {
            processNextStep();
          }, 1000); // 1 second delay between steps
        });
        }
      }, 500); // 500ms delay before sending the command
    };

    // Start processing the sequence
    processNextStep();
  }

  testConnection(callback) {
    this.sendCommand({ command: 'test_unreal_connection' }, callback);
  }

  // Method to explicitly close the connection
  closeConnection() {
    console.log('[TcpBridge] Explicitly closing connection');
    this.keepConnectionOpen = false;

    // Clear any reconnect interval
    if (this.reconnectInterval) {
      clearInterval(this.reconnectInterval);
      this.reconnectInterval = null;
    }

    // Close the socket if it exists
    if (this.socket) {
      this.socket.end();
      this.socket.destroy();
      this.socket = null;
    }

    this.connected = false;
    this.connecting = false;
  }

  failPendingCommands(errorMessage) {
    if (this.pendingCommands.length > 0) {
      console.log(`[TcpBridge] Failing ${this.pendingCommands.length} pending commands with error: ${errorMessage}`);

      // Fail each pending command
      this.pendingCommands.forEach(({ callback }) => {
        if (callback) {
          callback({ success: false, error: errorMessage });
        }
      });

      // Clear the pending commands
      this.pendingCommands = [];
    }

    // Also fail any callbacks that are still waiting for responses
    if (this.callbacks.size > 0) {
      console.log(`[TcpBridge] Failing ${this.callbacks.size} waiting callbacks with error: ${errorMessage}`);

      // Fail each callback
      this.callbacks.forEach((callback, id) => {
        callback({ success: false, error: errorMessage });
      });

      // Clear the callbacks
      this.callbacks.clear();
    }
  }

  // Helper method to find the end of a complete JSON object in the buffer
  findJsonEnd() {
    // Simple implementation - look for closing brace
    // This assumes each JSON object ends with a closing brace
    // A more robust implementation would track opening and closing braces
    let braceCount = 0;
    let inString = false;
    let escapeNext = false;

    for (let i = 0; i < this.dataBuffer.length; i++) {
      const char = this.dataBuffer[i];

      if (escapeNext) {
        escapeNext = false;
        continue;
      }

      if (char === '\\') {
        escapeNext = true;
        continue;
      }

      if (char === '"' && !escapeNext) {
        inString = !inString;
        continue;
      }

      if (!inString) {
        if (char === '{') {
          braceCount++;
        } else if (char === '}') {
          braceCount--;
          if (braceCount === 0) {
            return i; // Found the end of a complete JSON object
          }
        }
      }
    }

    return -1; // No complete JSON object found
  }
}

module.exports = TcpBridge;
