const express = require('express');
const router = express.Router();
const { authenticateJWT } = require('../../middleware/auth');
const subscriptionService = require('../../services/subscriptionService');
const authService = require('../../services/authService');

// Check if user has premium subscription
router.get('/', authenticateJWT, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get the user's subscription details
    const user = await authService.getUserById(userId);

    if (!user || !user.subscriptionId) {
      return res.json({
        isPremium: false,
        tier: 'free',
        status: 'inactive'
      });
    }

    // Check if the user has an active subscription
    if (user.subscriptionStatus !== 'active') {
      return res.json({
        isPremium: false,
        tier: 'free',
        status: user.subscriptionStatus || 'inactive'
      });
    }

    // Get the subscription details from Stripe
    const subscriptionDetails = await subscriptionService.getSubscriptionDetails(user.subscriptionId);

    // Check if the subscription is for the Pro plan
    const isPro = subscriptionDetails &&
                 subscriptionDetails.items &&
                 subscriptionDetails.items.data &&
                 subscriptionDetails.items.data.length > 0 &&
                 subscriptionDetails.items.data[0].price &&
                 subscriptionDetails.items.data[0].price.id === process.env.STRIPE_PRICE_ID_PRO;

    // Check if the subscription is for the Basic plan
    const isBasic = subscriptionDetails &&
                   subscriptionDetails.items &&
                   subscriptionDetails.items.data &&
                   subscriptionDetails.items.data.length > 0 &&
                   subscriptionDetails.items.data[0].price &&
                   subscriptionDetails.items.data[0].price.id === process.env.STRIPE_PRICE_ID_BASIC;

    // Determine the tier
    let tier = 'free';
    if (isPro) {
      tier = 'pro';
    } else if (isBasic) {
      tier = 'basic';
    }

    return res.json({
      isPremium: isPro || isBasic, // Any active subscription is considered premium
      tier: tier,
      status: 'active',
      subscriptionId: user.subscriptionId
    });
  } catch (error) {
    console.error('Error checking premium status:', error);
    return res.status(500).json({ error: 'Failed to check premium status' });
  }
});

module.exports = router;
