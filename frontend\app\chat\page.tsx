'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Chat from "@/components/chat";
import { useAuth } from '@/contexts/AuthContext';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';

export default function ChatPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading, hasActiveSubscription, refreshSubscriptionStatus } = useAuth();
  const { user: supabaseUser, isLoading: supabaseLoading } = useSupabaseAuth();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  // Debug: Log environment variables and user info
  useEffect(() => {
    console.log('Chat page: Environment variables and user info', {
      NEXT_PUBLIC_BYPASS_AUTH: process.env.NEXT_PUBLIC_BYPASS_AUTH,
      NEXT_PUBLIC_BYPASS_SUBSCRIPTION: process.env.NEXT_PUBLIC_BYPASS_SUBSCRIPTION,
      NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
      userId: supabaseUser?.id,
      email: supabaseUser?.email,
      hasActiveSubscription
    });
  }, [supabaseUser, hasActiveSubscription]);

  // Periodically check subscription status
  useEffect(() => {
    // Only run this effect if the user is authenticated and not loading
    if (isAuthenticated && !isLoading && !supabaseLoading) {
      // Set up an interval to check subscription status every minute
      const intervalId = setInterval(async () => {
        try {
          console.log('Chat page: Performing periodic subscription check');
          const subscriptionData = await refreshSubscriptionStatus();

          // If subscription is no longer active, redirect to dashboard
          if (!subscriptionData.hasActiveSubscription) {
            console.log('Chat page: Subscription no longer active, redirecting to dashboard');
            router.push('/dashboard');
          }
        } catch (error) {
          console.error('Chat page: Error checking subscription status:', error);
        }
      }, 60000); // Check every minute

      // Clean up the interval when the component unmounts
      return () => clearInterval(intervalId);
    }
  }, [isAuthenticated, isLoading, supabaseLoading, refreshSubscriptionStatus, router]);

  useEffect(() => {
    const checkAccess = async () => {
      console.log('Chat page: Checking access', {
        isAuthenticated,
        isLoading,
        hasActiveSubscription,
        supabaseUser: supabaseUser ? { id: supabaseUser.id, email: supabaseUser.email } : null,
        supabaseLoading
      });

      // Immediately redirect if we can determine the user is not authenticated
      // This happens before auth is fully loaded if we know the user is definitely not logged in
      if (!isLoading && !supabaseLoading && (!isAuthenticated || !supabaseUser)) {
        console.log('Chat page: User not authenticated, redirecting to login');
        router.push('/login');
        return;
      }

      // Wait for auth to finish loading
      if (isLoading || supabaseLoading) {
        console.log('Chat page: Still loading auth data, waiting...');
        return;
      }

      // At this point, auth is loaded and user is authenticated
      // Check if user has an active subscription
      if (!hasActiveSubscription) {
        // Try to refresh subscription status once
        try {
          const userId = supabaseUser?.id || 'unknown';
          console.log('Chat page: Refreshing subscription status for user', userId);
          const subscriptionData = await refreshSubscriptionStatus();
          console.log('Chat page: Refreshed subscription status for user', userId, ':', subscriptionData);

          // Always enforce subscription check regardless of environment
          if (!subscriptionData.hasActiveSubscription) {
            console.log('Chat page: User', userId, 'does not have an active subscription, redirecting to dashboard');
            router.push('/dashboard');
            return;
          }
        } catch (error) {
          const userId = supabaseUser?.id || 'unknown';
          console.error('Chat page: Error refreshing subscription status for user', userId, ':', error);

          // Always redirect on error to be safe
          console.log('Chat page: Redirecting to dashboard due to subscription check error');
          router.push('/dashboard');
          return;
        }
      }

      // User is authenticated and has an active subscription
      console.log('Chat page: User is authorized to access chat');
      setIsAuthorized(true);
      setIsCheckingAuth(false);
    };

    checkAccess();
  }, [isAuthenticated, isLoading, hasActiveSubscription, supabaseUser, supabaseLoading, router, refreshSubscriptionStatus]);

  // Always show loading state until fully authorized
  // This prevents the chat UI from flashing before redirect
  if (isCheckingAuth || !isAuthorized) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-gray-50">
        <div className="spinner mb-4"></div>
        <p className="text-gray-600 mb-4">Checking access...</p>

        {/* Debug information - only visible in development */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-8 p-4 bg-gray-100 rounded-md max-w-md text-xs text-left">
            <h3 className="font-bold mb-2">Debug Info:</h3>
            <p>isAuthenticated: {isAuthenticated ? 'true' : 'false'}</p>
            <p>isLoading: {isLoading ? 'true' : 'false'}</p>
            <p>supabaseLoading: {supabaseLoading ? 'true' : 'false'}</p>
            <p>hasActiveSubscription: {hasActiveSubscription ? 'true' : 'false'}</p>
            <p>isCheckingAuth: {isCheckingAuth ? 'true' : 'false'}</p>
            <p>isAuthorized: {isAuthorized ? 'true' : 'false'}</p>
            <p>User ID: {supabaseUser?.id || 'none'}</p>
            <p>Email: {supabaseUser?.email || 'none'}</p>

            <div className="mt-2 border-t pt-2">
              <h4 className="font-bold">Session Storage:</h4>
              {typeof window !== 'undefined' && supabaseUser?.id && (
                <div>
                  <p>Current Key: {sessionStorage.getItem(`current_subscription_key_${supabaseUser.id}`) || 'none'}</p>
                  <p>Status: {
                    (() => {
                      const key = sessionStorage.getItem(`current_subscription_key_${supabaseUser.id}`);
                      return key ? sessionStorage.getItem(key) || 'none' : 'none';
                    })()
                  }</p>
                </div>
              )}
            </div>

            <div className="flex space-x-2 mt-4">
              <button
                onClick={() => {
                  refreshSubscriptionStatus();
                  console.log('Manual refresh triggered');
                }}
                className="px-2 py-1 bg-blue-500 text-white rounded text-xs"
              >
                Force Refresh
              </button>
              <button
                onClick={() => {
                  setIsAuthorized(true);
                  setIsCheckingAuth(false);
                  console.log('Manual authorization override');
                }}
                className="px-2 py-1 bg-green-500 text-white rounded text-xs"
              >
                Force Access
              </button>
              <button
                onClick={() => {
                  if (supabaseUser?.id) {
                    // Clear all subscription status entries
                    for (let i = 0; i < sessionStorage.length; i++) {
                      const key = sessionStorage.key(i);
                      if (key && key.startsWith(`subscription_status_${supabaseUser.id}_`)) {
                        sessionStorage.removeItem(key);
                      }
                    }
                    sessionStorage.removeItem(`current_subscription_key_${supabaseUser.id}`);
                    console.log('Cleared all subscription status entries');
                  }
                }}
                className="px-2 py-1 bg-red-500 text-white rounded text-xs"
              >
                Clear Storage
              </button>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Only render the chat component when fully authorized
  return <Chat />;
}
