const express = require('express');
const router = express.Router();
const aiService = require('../services/aiService');
const { authenticateJWT } = require('../middleware/auth');

// Get all available models
router.get('/models', authenticateJWT, (req, res) => {
  try {
    const models = aiService.getAvailableModels();
    const currentModel = aiService.getCurrentModel();

    res.json({
      models,
      currentModel
    });
  } catch (error) {
    console.error('Error getting models:', error);
    res.status(500).json({ error: 'Failed to get models' });
  }
});

// Get configuration for a specific model
router.get('/models/:modelId/config', authenticateJWT, (req, res) => {
  try {
    const { modelId } = req.params;
    const config = aiService.getModelConfig(modelId);

    if (!config) {
      return res.status(404).json({ error: 'Model not found' });
    }

    // Don't send the full API key, just an indication if it's set
    const hasApiKey = !!config.apiKey;

    res.json({
      modelId,
      apiKey: hasApiKey ? '********' : '',
      endpoint: config.endpoint
    });
  } catch (error) {
    console.error('Error getting model config:', error);
    res.status(500).json({ error: 'Failed to get model configuration' });
  }
});

// Update configuration for a specific model
router.post('/models/:modelId/config', authenticateJWT, (req, res) => {
  try {
    const { modelId } = req.params;
    const { apiKey, endpoint } = req.body;

    if (!apiKey && !endpoint) {
      return res.status(400).json({ error: 'API key or endpoint is required' });
    }

    const success = aiService.updateModelConfig(modelId, { apiKey, endpoint });

    if (!success) {
      return res.status(404).json({ error: 'Model not found' });
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Error updating model config:', error);
    res.status(500).json({ error: 'Failed to update model configuration' });
  }
});

// Set the current model
router.post('/models/current', authenticateJWT, async (req, res) => {
  try {
    const { modelId } = req.body;

    if (!modelId) {
      return res.status(400).json({ error: 'Model ID is required' });
    }

    // Get user's subscription tier
    const userTier = req.user.subscriptionTier || 'standard';

    // Only allow model changes for premium tier users
    if (userTier !== 'premium' && modelId !== 'gemini-flash') {
      return res.status(403).json({
        error: 'Model selection is only available for premium tier subscribers',
        currentModel: 'gemini-flash'
      });
    }

    const success = aiService.setCurrentModel(modelId, userTier);

    if (!success) {
      return res.status(404).json({ error: 'Model not found' });
    }

    // For standard users, always return gemini-flash as the current model
    const currentModel = userTier === 'premium' ? modelId : 'gemini-flash';

    res.json({ success: true, currentModel, userTier });
  } catch (error) {
    console.error('Error setting current model:', error);
    res.status(500).json({ error: 'Failed to set current model' });
  }
});

module.exports = router;
