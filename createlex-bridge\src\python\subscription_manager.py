#!/usr/bin/env python3
"""
UnrealGenAI Subscription-Protected MCP Server
Docker-containerized subscription control system
"""

import sys
import os
import json
import time
import requests
import hashlib
import importlib.util
from pathlib import Path
from typing import Tuple, Dict, Any
import threading
import signal

class SubscriptionManager:
    def __init__(self):
        self.subscription_endpoint = os.getenv('SUBSCRIPTION_ENDPOINT', 'https://your-auth-server.com/api/verify')
        self.license_key_file = Path('/app/config/license.key')
        self.subscription_cache_file = Path('/app/config/subscription_cache.json')
        self.api_key = os.getenv('UNREALGENAI_API_KEY')
        self.check_interval = int(os.getenv('SUBSCRIPTION_CHECK_INTERVAL', '3600'))  # 1 hour
        self.running = True
        
    def load_license_key(self) -> str:
        """Load license key from secure location"""
        try:
            if self.license_key_file.exists():
                return self.license_key_file.read_text().strip()
            else:
                print("ERROR: No license key found. Mount license.key to /app/config/", file=sys.stderr)
                return None
        except Exception as e:
            print(f"ERROR: Failed to load license key: {e}", file=sys.stderr)
            return None
    
    def verify_subscription_remote(self, license_key: str) -> Tuple[bool, Dict[str, Any]]:
        """Verify subscription with remote server"""
        try:
            payload = {
                'license_key': license_key,
                'product': 'UnrealGenAISupport',
                'version': '1.0.0',
                'container_id': os.getenv('HOSTNAME', 'unknown')
            }
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json',
                'User-Agent': 'UnrealGenAI-Docker/1.0'
            }
            
            response = requests.post(
                self.subscription_endpoint,
                json=payload,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('valid', False), data
            else:
                print(f"Subscription verification failed: HTTP {response.status_code}", file=sys.stderr)
                return False, {}
                
        except requests.RequestException as e:
            print(f"Network error during subscription verification: {e}", file=sys.stderr)
            return False, {}
        except Exception as e:
            print(f"Error verifying subscription: {e}", file=sys.stderr)
            return False, {}
    
    def cache_subscription_status(self, valid: bool, data: Dict[str, Any]):
        """Cache subscription status for offline verification"""
        try:
            cache_data = {
                'valid': valid,
                'cached_at': time.time(),
                'expires_at': data.get('expires_at', time.time() + 3600),
                'user_info': data.get('user_info', {}),
                'features': data.get('features', []),
                'limits': data.get('limits', {})
            }
            
            self.subscription_cache_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.subscription_cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2)
                
        except Exception as e:
            print(f"Warning: Failed to cache subscription status: {e}", file=sys.stderr)
    
    def load_cached_subscription(self) -> Tuple[bool, Dict[str, Any]]:
        """Load cached subscription status for offline mode"""
        try:
            if not self.subscription_cache_file.exists():
                return False, {}
            
            with open(self.subscription_cache_file, 'r') as f:
                data = json.load(f)
            
            # Check if cache is still valid
            if time.time() > data.get('expires_at', 0):
                print("Cached subscription expired", file=sys.stderr)
                return False, {}
            
            return data.get('valid', False), data
            
        except Exception as e:
            print(f"Error loading cached subscription: {e}", file=sys.stderr)
            return False, {}
    
    def check_subscription_status(self) -> Tuple[bool, str, Dict[str, Any]]:
        """Main subscription verification logic"""
        
        # Check for development bypass
        if os.getenv('ALWAYS_SUBSCRIBED', '').lower() in ['true', '1', 'yes']:
            print("DEVELOPMENT MODE: ALWAYS_SUBSCRIBED bypass enabled", file=sys.stderr)
            return True, "Development mode - subscription bypassed", {
                'user_info': {'email': 'developer@localhost'},
                'features': ['all'],
                'limits': {}
            }
        
        license_key = self.load_license_key()
        if not license_key:
            return False, "No license key found", {}
        
        # Try remote verification first
        valid, data = self.verify_subscription_remote(license_key)
        if valid:
            self.cache_subscription_status(valid, data)
            user_email = data.get('user_info', {}).get('email', 'Unknown')
            return True, f"Active subscription verified for {user_email}", data
        
        # Fallback to cached subscription for offline mode
        print("Remote verification failed, checking cached subscription...", file=sys.stderr)
        valid, data = self.load_cached_subscription()
        if valid:
            user_email = data.get('user_info', {}).get('email', 'Unknown')
            return True, f"Cached subscription valid for {user_email}", data
        
        return False, "No valid subscription found", {}
    
    def load_protected_mcp_server(self):
        """Load the protected MCP server module"""
        try:
            # Import the protected server module
            protected_dir = Path('/app/protected')
            sys.path.insert(0, str(protected_dir))
            
            # Try to import compiled modules first
            for module_file in ['mcp_server.pyc', 'mcp_server_protected.pyc']:
                module_path = protected_dir / module_file
                if module_path.exists():
                    spec = importlib.util.spec_from_file_location("protected_mcp_server", str(module_path))
                    if spec and spec.loader:
                        module = importlib.util.module_from_spec(spec)
                        sys.modules["protected_mcp_server"] = module
                        spec.loader.exec_module(module)
                        return module
            
            # Fallback to source files (less secure)
            for module_file in ['mcp_server.py', 'server.py']:
                module_path = protected_dir / module_file
                if module_path.exists():
                    spec = importlib.util.spec_from_file_location("protected_mcp_server", str(module_path))
                    if spec and spec.loader:
                        module = importlib.util.module_from_spec(spec)
                        sys.modules["protected_mcp_server"] = module
                        spec.loader.exec_module(module)
                        return module
            
            raise ImportError("No protected MCP server module found")
            
        except Exception as e:
            print(f"ERROR: Failed to load protected MCP server: {e}", file=sys.stderr)
            return None
    
    def start_periodic_subscription_check(self):
        """Start background thread for periodic subscription verification"""
        def check_periodically():
            while self.running:
                time.sleep(self.check_interval)
                if not self.running:
                    break
                
                print("Performing periodic subscription check...", file=sys.stderr)
                valid, message, _ = self.check_subscription_status()
                if not valid:
                    print(f"CRITICAL: Subscription check failed: {message}", file=sys.stderr)
                    print("Shutting down MCP server due to invalid subscription...", file=sys.stderr)
                    os._exit(1)
                else:
                    print(f"Subscription check passed: {message}", file=sys.stderr)
        
        thread = threading.Thread(target=check_periodically, daemon=True)
        thread.start()
        return thread
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        print(f"Received signal {signum}, shutting down...", file=sys.stderr)
        self.running = False
        sys.exit(0)
    
    def main(self):
        """Main entry point"""
        # Set up signal handlers
        signal.signal(signal.SIGTERM, self.signal_handler)
        signal.signal(signal.SIGINT, self.signal_handler)
        
        print("UnrealGenAI Subscription-Protected MCP Server", file=sys.stderr)
        print("=" * 50, file=sys.stderr)
        
        # Initial subscription check
        valid, message, subscription_data = self.check_subscription_status()
        print(f"Subscription Status: {message}", file=sys.stderr)
        
        if not valid:
            print("ERROR: MCP Server cannot start without valid subscription", file=sys.stderr)
            print("", file=sys.stderr)
            print("To use this MCP server:", file=sys.stderr)
            print("1. Ensure you have a valid UnrealGenAI license", file=sys.stderr)
            print("2. Mount your license.key file to /app/config/license.key", file=sys.stderr)
            print("3. Verify network connectivity to license server", file=sys.stderr)
            print("4. Restart the container", file=sys.stderr)
            sys.exit(1)
        
        print("SUCCESS: Subscription verified, loading protected MCP server...", file=sys.stderr)
        
        # Start periodic subscription checks
        self.start_periodic_subscription_check()
        
        # Load and start the protected MCP server
        server_module = self.load_protected_mcp_server()
        if not server_module:
            print("ERROR: Failed to load protected MCP server module", file=sys.stderr)
            sys.exit(1)
        
        print("SUCCESS: Protected MCP server loaded, starting service...", file=sys.stderr)
        
        # Pass subscription data to the server
        if hasattr(server_module, 'start_server'):
            server_module.start_server(subscription_data)
        elif hasattr(server_module, 'main'):
            server_module.main()
        else:
            print("ERROR: Protected MCP server module missing entry point", file=sys.stderr)
            sys.exit(1)

if __name__ == "__main__":
    manager = SubscriptionManager()
    manager.main() 