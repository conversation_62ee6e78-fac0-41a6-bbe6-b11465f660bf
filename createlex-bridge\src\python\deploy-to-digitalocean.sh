#!/bin/bash

# Deploy Unreal Engine MCP Server to DigitalOcean App Platform
# Usage: ./deploy-to-digitalocean.sh

set -e

echo "🚀 Deploying Unreal Engine MCP Server to DigitalOcean..."

# Check if doctl is installed
if ! command -v doctl &> /dev/null; then
    echo "❌ doctl CLI not found. Please install it first:"
    echo "   https://docs.digitalocean.com/reference/doctl/how-to/install/"
    exit 1
fi

# Check if user is authenticated
if ! doctl auth list &> /dev/null; then
    echo "❌ Not authenticated with DigitalOcean. Please run:"
    echo "   doctl auth init"
    exit 1
fi

# Commit and push changes to GitHub
echo "📤 Pushing changes to GitHub..."
git add .
git commit -m "feat: Add cloud MCP server deployment configuration" || echo "No changes to commit"
git push origin cc_plugin

# Wait a moment for GitHub to process
echo "⏳ Waiting for GitHub to process changes..."
sleep 5

# Deploy to DigitalOcean App Platform
echo "🌐 Creating DigitalOcean App..."

# Check if app already exists
APP_NAME="unreal-mcp-cloud-server"
if doctl apps list --format Name --no-header | grep -q "^${APP_NAME}$"; then
    echo "📱 App already exists. Updating..."
    APP_ID=$(doctl apps list --format ID,Name --no-header | grep "${APP_NAME}" | awk '{print $1}')
    doctl apps update $APP_ID --spec .do/app.yaml
else
    echo "📱 Creating new app..."
    doctl apps create --spec .do/app.yaml
fi

# Get app info
echo "📊 Getting app information..."
APP_ID=$(doctl apps list --format ID,Name --no-header | grep "${APP_NAME}" | awk '{print $1}')

if [ -n "$APP_ID" ]; then
    echo "✅ App created/updated successfully!"
    echo "📱 App ID: $APP_ID"
    echo "🔗 App URL: https://cloud.digitalocean.com/apps/$APP_ID"
    
    # Wait for deployment
    echo "⏳ Waiting for deployment to complete..."
    doctl apps get $APP_ID --wait
    
    # Get the live URL
    LIVE_URL=$(doctl apps get $APP_ID --format LiveURL --no-header)
    echo "🌐 Live URL: $LIVE_URL"
    
    # Test the deployment
    echo "🧪 Testing deployment..."
    if curl -f "$LIVE_URL/health" > /dev/null 2>&1; then
        echo "✅ Deployment successful! Server is responding."
        echo ""
        echo "🎉 Your Unreal Engine MCP Server is now live!"
        echo "📡 Registration endpoint: $LIVE_URL/register"
        echo "🔧 MCP endpoint: $LIVE_URL/mcp"
        echo "📊 Health check: $LIVE_URL/health"
        echo "📋 Instances list: $LIVE_URL/instances"
        echo ""
        echo "💡 Update your auto_cloud_connector.py with this URL:"
        echo "   mcp_cloud_url = \"$LIVE_URL\""
    else
        echo "⚠️ Deployment completed but server is not responding yet."
        echo "   This is normal - it may take a few minutes to start."
        echo "   Check status at: https://cloud.digitalocean.com/apps/$APP_ID"
    fi
else
    echo "❌ Failed to get app information"
    exit 1
fi

echo ""
echo "🎯 Next steps:"
echo "1. Update auto_cloud_connector.py with the live URL"
echo "2. Test the connection with a local Unreal Engine instance"
echo "3. Configure Claude Desktop to use the cloud server"
echo ""
echo "💰 Cost: ~$5/month for basic instance"
echo "📈 Auto-scaling enabled for high traffic" 