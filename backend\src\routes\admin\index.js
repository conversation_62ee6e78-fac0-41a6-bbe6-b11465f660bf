const express = require('express');
const router = express.Router();
const { authenticateJWT } = require('../../middleware/auth');
const tokenUsageRoutes = require('./tokenUsage');
const apiKeysRoutes = require('./apiKeys');
const usersRoutes = require('./users');
const dashboardStatsRoutes = require('./dashboardStats');

// Import settings routes or create it if it doesn't exist
let settingsRoutes;
try {
  settingsRoutes = require('./settings');
} catch (error) {
  console.log('Settings routes not found, creating empty router');
  settingsRoutes = express.Router();
}

// Middleware to check if user is an admin
const isAdmin = (req, res, next) => {
  const adminEmails = ['<EMAIL>', '<EMAIL>'];

  // Check if user has is_admin flag set
  if (req.user && req.user.is_admin === true) {
    console.log('[Admin] User is admin by flag:', req.user.email);
    return next();
  }

  // Check if user email is in admin list
  if (req.user && req.user.email && adminEmails.includes(req.user.email.toLowerCase())) {
    console.log('[Admin] User is admin by email:', req.user.email);
    return next();
  }

  // Check environment variable for bypass (works in both dev and production)
  if (process.env.BYPASS_ADMIN_CHECK === 'true') {
    console.log('[Admin] Bypassing admin check via environment variable');
    return next();
  }

  console.log('[Admin] Access denied for user:', req.user?.email || 'unknown');
  return res.status(403).json({ error: 'Admin access required' });
};

// Apply admin check middleware to all routes
router.use(authenticateJWT, isAdmin);

// Use token usage routes
router.use('/token-usage', tokenUsageRoutes);

// Use API keys routes
router.use('/api-keys', apiKeysRoutes);

// Use users routes
router.use('/users', usersRoutes);

// Use settings routes
router.use('/settings', settingsRoutes);

// Use dashboard stats routes
router.use('/dashboard-stats', dashboardStatsRoutes);

module.exports = router;
