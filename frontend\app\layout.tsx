import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { Providers } from "./providers";
import "./globals.css";
import <PERSON>ript from "next/script";
import AuthBridge from "./auth-bridge";
import ForceUserId from "./force-user-id";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as SonnerToaster } from "sonner";
import { Suspense } from "react";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  metadataBase: new URL("https://createlex.com"),
  title: "CreateLex AI - Unreal Engine AI Toolkit",
  description: "Professional AI tools for Unreal Engine: Natural language commands, Blueprint generation, C++ assistance, and AI-powered scene creation. Transform your game development workflow with CreateLex AI plugin.",
  keywords: [
    "Unreal Engine AI",
    "UE5 AI tools",
    "Blueprint AI",
    "C++ assistant",
    "Game development AI",
    "Unreal Engine plugin",
    "AI scene generation",
    "Natural language programming",
    "CreateLex AI",
    "Unreal Engine automation"
  ],
  authors: [{ name: "CreateLex" }],
  creator: "<PERSON><PERSON><PERSON><PERSON>",
  publisher: "<PERSON>reateLex",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: "website",
    siteName: "CreateLex AI - Unreal Engine AI Toolkit",
    title: "CreateLex AI - Professional AI Tools for Unreal Engine",
    description: "Transform your Unreal Engine workflow with AI-powered tools: natural language commands, Blueprint generation, C++ assistance, and intelligent scene creation.",
    url: "https://createlex.com",
    images: [
      {
        url: "https://createlex.com/opengraph-image.png",
        width: 1200,
        height: 630,
        alt: "CreateLex AI - Unreal Engine AI Toolkit",
      },
    ],
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    site: "@CreateLexAI",
    creator: "@CreateLexAI",
    title: "CreateLex AI - Unreal Engine AI Toolkit",
    description: "Professional AI tools for Unreal Engine: Natural language commands, Blueprint generation, C++ assistance, and AI-powered scene creation. Try CreateLex AI plugin today!",
    images: [
      {
        url: "https://createlex.com/twitter-image.png",
        alt: "CreateLex AI - Unreal Engine AI Toolkit",
      },
    ],
  },
  alternates: {
    canonical: "https://createlex.com",
  },
  category: "Technology",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.className}`}>
        <Providers>
          {/* AuthBridge component to handle authentication between main app and MCP-Chat */}
          <Suspense fallback={<div>Loading authentication...</div>}>
            <AuthBridge />
          </Suspense>

          {/* Force the user ID to be the Supabase user ID */}
          <Suspense fallback={<div>Loading user ID...</div>}>
            <ForceUserId />
          </Suspense>

          {/* Wrap all children in a Suspense boundary to handle useSearchParams() */}
          <Suspense fallback={<div>Loading content...</div>}>
            {children}
          </Suspense>

          <Toaster />
          <SonnerToaster position="top-right" />
        </Providers>
        <Script defer src="https://cloud.umami.is/script.js" data-website-id="1373896a-fb20-4c9d-b718-c723a2471ae5" />
      </body>
    </html>
  );
}
