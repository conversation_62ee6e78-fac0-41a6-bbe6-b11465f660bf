/**
 * Test script to verify connection to Unreal Engine
 * 
 * This script tests the connection to the Unreal Engine socket server
 * using the TcpBridge class.
 * 
 * Usage:
 * node test-unreal-connection.js
 */

// Load environment variables
require('dotenv').config();

// Import the TcpBridge class
const TcpBridge = require('./services/tcpBridge');

console.log('Starting Unreal Engine connection test...');
console.log(`Host: ${process.env.UNREAL_ENGINE_HOST || '127.0.0.1'}`);
console.log(`Port: ${process.env.UNREAL_ENGINE_PORT || 9877}`);
console.log(`API Key: ${process.env.UNREAL_API_KEY || 'web_auth'}`);

// Create a new TcpBridge instance
const tcpBridge = new TcpBridge();

// Test the connection
console.log('Testing connection to Unreal Engine...');
tcpBridge.testConnection((result) => {
  console.log('Connection test result:', JSON.stringify(result, null, 2));
  
  if (result.success) {
    console.log('✅ Connection to Unreal Engine successful!');
  } else {
    console.log('❌ Connection to Unreal Engine failed:', result.error);
    console.log('Make sure the Unreal Engine plugin is running and the socket server is started.');
    console.log('To start the socket server in Unreal Engine:');
    console.log('1. Open your Unreal Engine project');
    console.log('2. Go to Tools -> Run Python Script');
    console.log('3. Select the Plugins/GenerativeAISupport/Content/Python/unreal_socket_server.py file');
  }
  
  // Exit after the test
  setTimeout(() => {
    process.exit(0);
  }, 1000);
});
