const express = require('express');
const router = express.Router();
const { authenticateJWTWithFallback } = require('../../middleware/auth');
const subscriptionService = require('../../services/subscriptionService');
const tokenUsageService = require('../../services/tokenUsageService');

// Check if user has an active subscription
router.get('/', authenticateJWTWithFallback, async (req, res) => {
  try {
    const userId = req.user.id;
    console.log(`[Subscription Check] Checking subscription for user: ${userId}`);

    // For development purposes, always return true if ALWAYS_SUBSCRIBED is set
    if (process.env.ALWAYS_SUBSCRIBED === 'true') {
      console.log(`[Subscription Check] ALWAYS_SUBSCRIBED is true, returning active subscription for user: ${userId}`);

      // Get token usage information
      const usageStatus = await tokenUsageService.checkUsageLimits(userId);
      console.log(`[Subscription Check] Token usage status:`, usageStatus);

      return res.json({
        hasActiveSubscription: true,
        userId,
        source: 'development_override',
        dailyUsage: usageStatus.dailyUsage || { used: 0, limit: 50000, percentage: 0 },
        monthlyUsage: usageStatus.monthlyUsage || { used: 0, limit: 1000000, percentage: 0 },
        plan: usageStatus.plan || 'basic'
      });
    }

    // Check if the user has an active subscription
    const hasActiveSubscription = await subscriptionService.checkSubscription(userId);
    console.log(`[Subscription Check] User ${userId} subscription status: ${hasActiveSubscription ? 'active' : 'inactive'}`);

    // Get token usage information
    const usageStatus = await tokenUsageService.checkUsageLimits(userId);

    // Always use real data from the database
    console.log(`[Subscription Check] Using real data for user: ${userId}`);

    return res.json({
      hasActiveSubscription,
      userId,
      source: 'subscription_service',
      dailyUsage: usageStatus.dailyUsage || { used: 0, limit: 50000, percentage: 0 },
      monthlyUsage: usageStatus.monthlyUsage || { used: 0, limit: 1000000, percentage: 0 },
      plan: usageStatus.plan || 'basic'
    });
  } catch (error) {
    console.error('[Subscription Check] Error checking subscription status:', error);
    return res.status(500).json({
      error: 'Failed to check subscription status',
      message: error.message
    });
  }
});

module.exports = router;
