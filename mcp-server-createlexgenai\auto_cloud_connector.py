#!/usr/bin/env python3
"""
Automated Cloud Connector for Unreal Engine MCP Plugin
Automatically establishes cloud connectivity without user intervention
"""

import asyncio
import json
import os
import subprocess
import sys
import time
import requests
import socket
from pathlib import Path
import tempfile
import zipfile
import platform

class AutoCloudConnector:
    def __init__(self, unreal_port=9877, mcp_cloud_url=None):
        self.unreal_port = unreal_port
        self.mcp_cloud_url = mcp_cloud_url or "https://unreal-mcp.ondigitalocean.app"
        self.tunnel_process = None
        self.tunnel_url = None
        self.config_file = Path.home() / ".unrealgenai" / "cloud_config.json"
        self.tunnel_binary = None
        
    async def start_auto_connection(self):
        """Start automated cloud connection process"""
        print("[AutoCloud] Starting automated cloud connection...")
        
        # 1. Download and setup tunnel binary
        await self.setup_tunnel_binary()
        
        # 2. Start tunnel to expose local Unreal Engine
        await self.start_tunnel()
        
        # 3. Register with cloud MCP server
        await self.register_with_cloud()
        
        # 4. Update local configuration
        await self.update_local_config()
        
        print(f"[AutoCloud] ✅ Cloud connection established!")
        print(f"[AutoCloud] 🌐 Your Unreal Engine is accessible at: {self.tunnel_url}")
        print(f"[AutoCloud] 🔗 MCP Server: {self.mcp_cloud_url}")
        
        return {
            "success": True,
            "tunnel_url": self.tunnel_url,
            "mcp_url": self.mcp_cloud_url,
            "local_port": self.unreal_port
        }
    
    async def setup_tunnel_binary(self):
        """Download and setup tunnel binary automatically"""
        print("[AutoCloud] Setting up tunnel binary...")
        
        # Create config directory
        self.config_file.parent.mkdir(exist_ok=True)
        
        # Determine platform and download appropriate binary
        system = platform.system().lower()
        arch = platform.machine().lower()
        
        if system == "windows":
            binary_name = "cloudflared.exe"
            download_url = "https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-windows-amd64.exe"
        elif system == "darwin":  # macOS
            binary_name = "cloudflared"
            download_url = "https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-darwin-amd64.tgz"
        else:  # Linux
            binary_name = "cloudflared"
            download_url = "https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64"
        
        # Download to plugin directory
        binary_path = self.config_file.parent / binary_name
        
        if not binary_path.exists():
            print(f"[AutoCloud] Downloading tunnel binary from {download_url}")
            response = requests.get(download_url, stream=True)
            response.raise_for_status()
            
            if download_url.endswith('.tgz'):
                # Handle macOS tar.gz file
                with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
                    for chunk in response.iter_content(chunk_size=8192):
                        tmp_file.write(chunk)
                    tmp_file.flush()
                    
                    # Extract tar.gz
                    import tarfile
                    with tarfile.open(tmp_file.name, 'r:gz') as tar:
                        tar.extractall(self.config_file.parent)
                    
                    os.unlink(tmp_file.name)
            else:
                # Direct binary download
                with open(binary_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
            
            # Make executable on Unix systems
            if system != "windows":
                os.chmod(binary_path, 0o755)
            
            print(f"[AutoCloud] ✅ Tunnel binary downloaded to {binary_path}")
        
        self.tunnel_binary = str(binary_path)
    
    async def start_tunnel(self):
        """Start cloudflared tunnel to expose local Unreal Engine"""
        print(f"[AutoCloud] Starting tunnel for port {self.unreal_port}...")
        
        # Start cloudflared tunnel
        cmd = [
            self.tunnel_binary,
            "tunnel",
            "--url", f"tcp://localhost:{self.unreal_port}",
            "--logfile", str(self.config_file.parent / "tunnel.log")
        ]
        
        self.tunnel_process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait for tunnel to establish and get URL
        max_wait = 30  # seconds
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            if self.tunnel_process.poll() is not None:
                # Process died
                stdout, stderr = self.tunnel_process.communicate()
                raise Exception(f"Tunnel process failed: {stderr}")
            
            # Check log file for tunnel URL
            log_file = self.config_file.parent / "tunnel.log"
            if log_file.exists():
                with open(log_file, 'r') as f:
                    log_content = f.read()
                    
                # Look for tunnel URL in logs
                for line in log_content.split('\n'):
                    if 'https://' in line and 'trycloudflare.com' in line:
                        # Extract URL
                        import re
                        url_match = re.search(r'https://[a-zA-Z0-9-]+\.trycloudflare\.com', line)
                        if url_match:
                            self.tunnel_url = url_match.group(0)
                            print(f"[AutoCloud] ✅ Tunnel established: {self.tunnel_url}")
                            return
            
            await asyncio.sleep(1)
        
        raise Exception("Failed to establish tunnel within timeout period")
    
    async def register_with_cloud(self):
        """Register this Unreal Engine instance with the cloud MCP server"""
        print("[AutoCloud] Registering with cloud MCP server...")
        
        registration_data = {
            "tunnel_url": self.tunnel_url,
            "local_port": self.unreal_port,
            "instance_id": self.get_instance_id(),
            "capabilities": [
                "spawn_actor", "create_material", "execute_python",
                "create_blueprint", "modify_object", "get_scene_objects"
            ]
        }
        
        try:
            response = requests.post(
                f"{self.mcp_cloud_url}/register",
                json=registration_data,
                timeout=10
            )
            response.raise_for_status()
            
            result = response.json()
            print(f"[AutoCloud] ✅ Registered with cloud server: {result.get('instance_id')}")
            
        except Exception as e:
            print(f"[AutoCloud] ⚠️ Registration failed (continuing anyway): {e}")
    
    async def update_local_config(self):
        """Update local configuration files"""
        config = {
            "tunnel_url": self.tunnel_url,
            "mcp_cloud_url": self.mcp_cloud_url,
            "local_port": self.unreal_port,
            "instance_id": self.get_instance_id(),
            "last_connected": time.time()
        }
        
        with open(self.config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"[AutoCloud] ✅ Configuration saved to {self.config_file}")
    
    def get_instance_id(self):
        """Generate unique instance ID for this Unreal Engine instance"""
        import hashlib
        import uuid
        
        # Use MAC address + hostname for consistent ID
        mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                       for elements in range(0,2*6,2)][::-1])
        hostname = socket.gethostname()
        
        instance_string = f"{mac}-{hostname}-{self.unreal_port}"
        return hashlib.md5(instance_string.encode()).hexdigest()[:16]
    
    async def stop_connection(self):
        """Stop the tunnel and clean up"""
        print("[AutoCloud] Stopping cloud connection...")
        
        if self.tunnel_process:
            self.tunnel_process.terminate()
            try:
                self.tunnel_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.tunnel_process.kill()
            
            print("[AutoCloud] ✅ Tunnel stopped")
    
    def is_connected(self):
        """Check if cloud connection is active"""
        return (self.tunnel_process is not None and 
                self.tunnel_process.poll() is None and 
                self.tunnel_url is not None)

# Plugin Integration Functions
async def auto_start_cloud_connection(unreal_port=9877):
    """Main function to be called by Unreal Engine plugin"""
    connector = AutoCloudConnector(unreal_port)
    return await connector.start_auto_connection()

def get_cloud_status():
    """Get current cloud connection status"""
    config_file = Path.home() / ".unrealgenai" / "cloud_config.json"
    
    if not config_file.exists():
        return {"connected": False, "message": "No cloud configuration found"}
    
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        # Check if connection is recent (within last hour)
        last_connected = config.get('last_connected', 0)
        if time.time() - last_connected > 3600:  # 1 hour
            return {"connected": False, "message": "Connection expired"}
        
        return {
            "connected": True,
            "tunnel_url": config.get('tunnel_url'),
            "mcp_url": config.get('mcp_cloud_url'),
            "instance_id": config.get('instance_id')
        }
    
    except Exception as e:
        return {"connected": False, "message": f"Error reading config: {e}"}

if __name__ == "__main__":
    # Test the auto connector
    async def test():
        connector = AutoCloudConnector()
        try:
            result = await connector.start_auto_connection()
            print("Test successful:", result)
            
            # Keep running for a bit
            await asyncio.sleep(10)
            
        finally:
            await connector.stop_connection()
    
    asyncio.run(test()) 