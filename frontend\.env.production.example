# Server Configuration
NODE_ENV=production

# MCP Server Connection
NEXT_PUBLIC_MCP_SERVER_URL=wss://YOUR_DOMAIN.com/mcp
NEXT_PUBLIC_UNREAL_ENGINE_HOST=mcp_server
NEXT_PUBLIC_UNREAL_ENGINE_PORT=9877
NEXT_PUBLIC_UNREAL_API_KEY=your_production_api_key

# Authentication
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_KEY=your_supabase_service_key

# JWT Configuration
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d

# Development Mode
USE_MOCK_USER=false
USE_SUPABASE=true
NEXT_PUBLIC_BYPASS_AUTH=false
NEXT_PUBLIC_BYPASS_SUBSCRIPTION=false
NEXT_PUBLIC_API_URL=https://YOUR_DOMAIN.com/api

# Subscription/Billing
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
STRIPE_PRICE_ID=your_stripe_price_id

# AI Model API Keys
XAI_API_KEY=your_xai_api_key
OPENAI_API_KEY=your_openai_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key
GOOGLE_API_KEY=your_google_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
DATABASE_URL=your_database_url
