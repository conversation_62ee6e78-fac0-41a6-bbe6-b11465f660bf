import { NextResponse } from 'next/server';

/**
 * This API route handles the Stripe success redirect
 * It redirects to the dashboard with a success query parameter
 */
export async function GET(request: Request) {
  try {
    // Get the URL parameters
    const url = new URL(request.url);
    const sessionId = url.searchParams.get('session_id');

    // Redirect to the dashboard with the subscription success parameter
    return NextResponse.redirect(`${url.origin}/dashboard?subscription=success`);
  } catch (error) {
    console.error('Error in subscription success redirect:', error);
    
    // Redirect to the dashboard with an error parameter
    return NextResponse.redirect(`${new URL(request.url).origin}/dashboard?subscription=error`);
  }
}
