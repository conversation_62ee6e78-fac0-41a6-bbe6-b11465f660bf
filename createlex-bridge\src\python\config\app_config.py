#!/usr/bin/env python3
"""
Application Configuration for UnrealGenAI MCP Server
"""

import os
from typing import Dict, Any

class AppConfig:
    """Application configuration management"""
    
    def __init__(self):
        self.load_config()
    
    def load_config(self):
        """Load configuration from environment variables"""
        # Server configuration
        self.SERVER_HOST = os.getenv('SERVER_HOST', '0.0.0.0')
        self.SERVER_PORT = int(os.getenv('SERVER_PORT', '8000'))
        self.HEALTH_PORT = int(os.getenv('HEALTH_PORT', '8001'))
        
        # Security configuration
        self.JWT_SECRET = os.getenv('JWT_SECRET', 'your-secret-key')
        self.ENCRYPTION_KEY = os.getenv('ENCRYPTION_KEY', 'your-encryption-key')
        
        # Backend API configuration
        self.BACKEND_URL = os.getenv('BACKEND_URL', 'http://localhost:3001')
        
        # Development settings
        self.DEBUG = os.getenv('DEBUG', 'false').lower() == 'true'
        self.ALWAYS_SUBSCRIBED = os.getenv('ALWAYS_SUBSCRIBED', 'false').lower() == 'true'
        
        # Subscription settings
        self.SUBSCRIPTION_CHECK_INTERVAL = int(os.getenv('SUBSCRIPTION_CHECK_INTERVAL', '300'))
        self.OFFLINE_MODE_ENABLED = os.getenv('OFFLINE_MODE_ENABLED', 'true').lower() == 'true'
        self.CACHE_DURATION = int(os.getenv('CACHE_DURATION', '3600'))
        
        # Logging configuration
        self.LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
        self.LOG_FILE = os.getenv('LOG_FILE', '/app/logs/app.log')
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            'server': {
                'host': self.SERVER_HOST,
                'port': self.SERVER_PORT,
                'health_port': self.HEALTH_PORT
            },
            'security': {
                'jwt_secret': '***' if self.JWT_SECRET else None,
                'encryption_key': '***' if self.ENCRYPTION_KEY else None
            },
            'backend': {
                'url': self.BACKEND_URL
            },
            'development': {
                'debug': self.DEBUG,
                'always_subscribed': self.ALWAYS_SUBSCRIBED
            },
            'subscription': {
                'check_interval': self.SUBSCRIPTION_CHECK_INTERVAL,
                'offline_mode': self.OFFLINE_MODE_ENABLED,
                'cache_duration': self.CACHE_DURATION
            },
            'logging': {
                'level': self.LOG_LEVEL,
                'file': self.LOG_FILE
            }
        }
    
    def validate(self) -> bool:
        """Validate configuration"""
        required_vars = ['JWT_SECRET', 'BACKEND_URL']
        missing_vars = [var for var in required_vars if not getattr(self, var)]
        
        if missing_vars:
            print(f"Missing required configuration: {missing_vars}")
            return False
        
        return True

# Global configuration instance
config = AppConfig() 