import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const url = request.nextUrl.clone();

  // Handle Stripe success redirect
  if ((pathname === '/api/subscription/success' ||
       pathname === '/api/api/subscription/success' ||
       pathname === '/subscription/success') &&
      url.searchParams.has('session_id')) {

    console.log('Middleware: Handling Stripe success redirect from', pathname);

    // Redirect to the dashboard with the subscription success parameter
    url.pathname = '/dashboard';
    url.searchParams.set('subscription', 'success');

    return NextResponse.redirect(url);
  }

  // Skip middleware for API routes and static assets
  if (pathname.startsWith('/api/') ||
      pathname.includes('/_next/') ||
      pathname.includes('/static/')) {
    return NextResponse.next();
  }

  // Only protect the chat routes
  if (pathname.startsWith('/chat')) {
    console.log('Middleware: Protecting chat route:', pathname);

    // Get all cookies for debugging
    const cookieStore = request.cookies;
    console.log('Middleware: Available cookies:', cookieStore.getAll().map(c => c.name));

    // Check for Supabase session cookie in various formats
    // First, try to parse the sb-access-token cookie which might contain the session
    let token = null;

    // Use environment variable to control authentication bypass
    const bypassAuth = process.env.NEXT_PUBLIC_BYPASS_AUTH === 'true';

    console.log('Middleware: Authentication bypass set to:', bypassAuth);

    // Bypass auth check if configured
    if (bypassAuth) {
      console.log('Middleware: Bypassing auth check');
      return NextResponse.next();
    }

    // Try to get token from cookies in various formats
    // 1. Check sb-access-token (most common format)
    const sbAccessTokenCookie = cookieStore.get('sb-access-token');
    if (sbAccessTokenCookie?.value) {
      try {
        // The cookie might be JSON with the session info
        const parsedCookie = JSON.parse(sbAccessTokenCookie.value);
        if (parsedCookie?.access_token) {
          token = parsedCookie.access_token;
          console.log('Middleware: Found access_token in parsed sb-access-token cookie');
        }
      } catch (e) {
        // If it's not JSON, use the value directly
        token = sbAccessTokenCookie.value;
        console.log('Middleware: Using sb-access-token cookie value directly');
      }
    }

    // 2. Check other Supabase cookie formats
    if (!token) {
      // Try sb:token format
      const sbToken = cookieStore.get('sb:token')?.value;
      if (sbToken) {
        token = sbToken;
        console.log('Middleware: Using sb:token cookie');
      }

      // Try supabase-auth-token format
      if (!token) {
        const sbAuthToken = cookieStore.get('supabase-auth-token')?.value;
        if (sbAuthToken) {
          try {
            // This might be a JSON string
            const parsed = JSON.parse(sbAuthToken);
            if (parsed?.access_token) {
              token = parsed.access_token;
              console.log('Middleware: Using access_token from supabase-auth-token cookie');
            }
          } catch (e) {
            // If not JSON, use directly
            token = sbAuthToken;
            console.log('Middleware: Using supabase-auth-token cookie directly');
          }
        }
      }

      // Try mcp-auth-state format (custom cookie)
      if (!token) {
        const mcpAuthState = cookieStore.get('mcp-auth-state')?.value;
        if (mcpAuthState) {
          try {
            const parsed = JSON.parse(mcpAuthState);
            if (parsed?.token) {
              token = parsed.token;
              console.log('Middleware: Using token from mcp-auth-state cookie');
            }
          } catch (e) {
            // Ignore parsing errors
          }
        }
      }
    }

    // 3. Check for auth header
    if (!token) {
      const authHeader = request.headers.get('authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        token = authHeader.replace('Bearer ', '');
        console.log('Middleware: Using token from Authorization header');
      }
    }

    // Log all token sources for debugging
    console.log('Middleware: Token sources:', {
      sbAccessToken: !!sbAccessTokenCookie?.value,
      sbToken: !!cookieStore.get('sb:token')?.value,
      sbAuthToken: !!cookieStore.get('supabase-auth-token')?.value,
      sbRefreshToken: !!cookieStore.get('sb-refresh-token')?.value,
      mcpAuthState: !!cookieStore.get('mcp-auth-state')?.value,
      authHeader: !!request.headers.get('authorization'),
      tokenFound: !!token
    });

    if (!token) {
      console.log('Middleware: No authentication token found in cookies or headers');
      // Redirect to login page
      return NextResponse.redirect(new URL('/login', request.url));
    }

    try {
      // Initialize Supabase client
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
      const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

      if (!supabaseUrl || !supabaseAnonKey) {
        console.error('Middleware: Missing Supabase configuration');
        return NextResponse.redirect(new URL('/login', request.url));
      }

      console.log('Middleware: Creating Supabase client with URL:', supabaseUrl);
      const supabase = createClient(supabaseUrl, supabaseAnonKey, {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      });

      // Verify the token with Supabase
      console.log('Middleware: Verifying token with Supabase');
      const { data, error } = await supabase.auth.getUser(token);

      if (error) {
        console.error('Middleware: Invalid token:', error.message);
        // Redirect to login page
        return NextResponse.redirect(new URL('/login', request.url));
      }

      if (!data.user) {
        console.error('Middleware: No user found for token');
        return NextResponse.redirect(new URL('/login', request.url));
      }

      console.log('Middleware: User authenticated:', data.user.id);

      // User is authenticated, now check subscription
      const userId = data.user.id;

      // Use environment variable to control subscription bypass
      const bypassSubscription = process.env.NEXT_PUBLIC_BYPASS_SUBSCRIPTION === 'true';

      console.log('Middleware: Subscription bypass set to:', bypassSubscription);

      // Bypass subscription check if configured
      if (bypassSubscription) {
        console.log('Middleware: Bypassing subscription check');
        return NextResponse.next();
      }

      // Call the backend to check subscription status
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
      console.log('Middleware: Checking subscription status at:', `${apiUrl}/api/subscription/check`);

      try {
        // Make the API call to check subscription status
        console.log('Middleware: Making subscription check API call');
        const subscriptionResponse = await fetch(`${apiUrl}/api/subscription/check`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        // Log the response status
        console.log('Middleware: Subscription check API response status:', subscriptionResponse.status);

        if (!subscriptionResponse.ok) {
          console.error('Middleware: Failed to check subscription status:', subscriptionResponse.status);

          // For development purposes, allow access if the subscription check fails
          // This prevents blocking users if the backend is down
          console.log('Middleware: Allowing access despite subscription check error');
          return NextResponse.next();
        }

        // Parse the response
        const subscriptionData = await subscriptionResponse.json();
        console.log('Middleware: Subscription check result:', subscriptionData);

        // Check if the user has an active subscription
        if (!subscriptionData.hasActiveSubscription) {
          console.log('Middleware: User does not have an active subscription');
          // Redirect to dashboard page instead of access-denied
          return NextResponse.redirect(new URL('/dashboard', request.url));
        }
      } catch (error) {
        console.error('Middleware: Error checking subscription:', error);
        // For development purposes, allow access if the subscription check fails
        // This prevents blocking users if the backend is down
        console.log('Middleware: Allowing access despite subscription check error');
        return NextResponse.next();
      }

      console.log('Middleware: User has active subscription, allowing access');
      // User is authenticated and has an active subscription, allow access
      return NextResponse.next();
    } catch (error) {
      console.error('Middleware: Error in middleware:', error);
      // Redirect to login page on error
      return NextResponse.redirect(new URL('/login', request.url));
    }
  }

  // For all other routes, allow access
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - login, signup, access-denied (auth pages)
     * - dashboard (dashboard page)
     */
    '/chat',
    '/chat/:path*',
    // Add Stripe success redirect paths
    '/api/subscription/success',
    '/api/api/subscription/success',
    '/subscription/success',
  ],
};
