// Simple test script
console.log('Starting simple test...');

// Import TcpBridge
const TcpBridge = require('./src/services/tcpBridge');
console.log('TcpBridge imported successfully');

// Create a new TcpBridge instance
const tcpBridge = new TcpBridge();
console.log('TcpBridge instance created');

// Send a handshake command
setTimeout(() => {
  console.log('Sending handshake command...');
  
  tcpBridge.sendCommand({ 
    command: 'test_unreal_connection' 
  }, (result) => {
    console.log('Handshake result:', result);
    process.exit(0);
  });
}, 2000);

// Keep the process alive
console.log('Waiting for response...');
