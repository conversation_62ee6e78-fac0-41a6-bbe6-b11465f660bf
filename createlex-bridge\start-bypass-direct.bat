@echo off
setlocal

REM =============================================
REM   START BRIDGE BYPASS - DIRECT ELECTRON
REM =============================================

title Bridge Bypass Direct

echo Starting CreateLex Bridge in Bypass Mode...

REM Set all bypass environment variables
set NODE_ENV=development
set DEV_MODE=true
set BYPASS_SUBSCRIPTION=true
set BYPASS_LOGIN=true
set AUTO_START_MCP=true
set SKIP_AUTH=true
set CREATELEX_BASE_URL=http://localhost:3000
set API_BASE_URL=http://localhost:5001/api
set FORCE_BYPASS=true
set DEVELOPMENT_MODE=true

echo Environment variables set for bypass mode:
echo   BYPASS_LOGIN: %BYPASS_LOGIN%
echo   AUTO_START_MCP: %AUTO_START_MCP%
echo   DEV_MODE: %DEV_MODE%

echo.
echo Starting Electron directly...
node_modules\.bin\electron.cmd .

echo.
echo Bridge process ended.
pause