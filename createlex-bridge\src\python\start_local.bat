@echo off
REM Quick start script for local development and testing on Windows
REM This script starts the MCP server locally without Docker

echo 🚀 Starting Unreal Engine MCP Server locally...

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed. Please install Python 3.8+ first.
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "venv" (
    echo 📦 Creating virtual environment...
    python -m venv venv
)

REM Activate virtual environment
echo 🔧 Activating virtual environment...
call venv\Scripts\activate.bat

REM Install dependencies
echo 📥 Installing dependencies...
pip install -r requirements.txt

REM Set default environment variables if not set
if not defined UNREAL_HOST set UNREAL_HOST=localhost
if not defined UNREAL_PORT set UNREAL_PORT=9877

echo ⚙️  Configuration:
echo    Unreal Engine: %UNREAL_HOST%:%UNREAL_PORT%
echo    MCP Server: localhost:8000
echo.

REM Run tests first
echo 🧪 Running tests...
python test_server.py

if errorlevel 1 (
    echo ❌ Tests failed. Please fix the issues before starting the server.
    pause
    exit /b 1
)

echo.
echo ✅ Tests passed! Starting MCP server...
echo 🌐 Server will be available at: http://localhost:8000
echo 🛑 Press Ctrl+C to stop the server
echo.

REM Start the server
python mcp_server.py

pause 