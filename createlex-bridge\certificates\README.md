# Certificate Management for CreateLex Bridge

## Overview
This directory contains the Developer ID Application certificate and private key needed to sign CreateLex Bridge for distribution.

## Files
- `createlex-developer-id.p12` - Password-protected certificate and private key bundle
- `install-certificate.sh` - Script to install certificate on new Macs

## Security Notice
⚠️ **IMPORTANT**: The `.p12` file contains your private signing key. 
- Keep the password secure and separate from this repository
- Never commit the password to version control
- Only share with trusted team members who need to build releases

## Installing on New Mac

### Method 1: Using the Install Script
```bash
cd createlex-bridge/certificates
chmod +x install-certificate.sh
./install-certificate.sh
```

### Method 2: Manual Installation
1. Double-click `createlex-developer-id.p12`
2. Enter the password when prompted
3. Choose "login" keychain
4. Verify installation:
   ```bash
   security find-identity -v -p codesigning | grep "CREATELEX"
   ```

## Building Signed App
After installing the certificate:
```bash
cd createlex-bridge
npm run build-mac-protected
```

## Troubleshooting

### Certificate Not Found
If you get "no identity found" errors:
1. Verify certificate is installed: `security find-identity -v -p codesigning`
2. Check Team ID matches in package.json: `UWWHD8G966`
3. Ensure you have the private key (not just the certificate)

### Permission Denied
If you get permission errors:
1. Open Keychain Access
2. Find "Developer ID Application: CREATELEX LLC"
3. Right-click → Get Info → Access Control
4. Add `/usr/bin/codesign` to allowed applications

### Wrong Certificate Type
Make sure you're using:
- ✅ **Developer ID Application** (for outside App Store)
- ❌ Not "Apple Development" (for development only)
- ❌ Not "Apple Distribution" (for App Store only)

## Password Storage
Store the certificate password securely:
- Use a password manager
- Set as environment variable: `export CERT_PASSWORD="your-password"`
- Never commit to git or share in plain text

## Regenerating Certificate
If you need to create a new certificate:
1. Revoke old certificate in Apple Developer portal
2. Create new Certificate Signing Request (CSR)
3. Generate new Developer ID Application certificate
4. Export new .p12 file
5. Update this documentation 