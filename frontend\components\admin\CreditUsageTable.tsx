'use client';

import { useState, useEffect } from 'react';
import { format, subDays } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { fetchWithAuth } from '@/lib/authUtils';
import { useAuth } from '@/contexts/AuthContext';
import { CalendarIcon, Download, ChevronLeft, ChevronRight } from 'lucide-react';

interface CreditUsageRecord {
  id: string;
  user_id: string;
  model_id: string;
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
  request_type: string;
  subscription_plan: string;
  timestamp: string;
  email?: string;
}

export default function CreditUsageTable() {
  const [records, setRecords] = useState<CreditUsageRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [fromDate, setFromDate] = useState<Date>(subDays(new Date(), 7));
  const [toDate, setToDate] = useState<Date>(new Date());
  const [page, setPage] = useState(1);
  const [pageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const { token } = useAuth();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
        const requestUrl = `${apiUrl}/api/admin/token-usage/records?from=${format(fromDate, 'yyyy-MM-dd')}&to=${format(toDate, 'yyyy-MM-dd')}&page=${page}&pageSize=${pageSize}`;

        const response = await fetchWithAuth(requestUrl, token);

        if (!response.ok) {
          throw new Error(`Failed to fetch credit usage records: ${response.status}`);
        }

        const data = await response.json();
        setRecords(data.records || []);
        setTotalPages(Math.ceil((data.total || 0) / pageSize));
      } catch (error) {
        console.error('Error fetching credit usage records:', error);
        setError('Failed to load credit usage records');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [fromDate, toDate, page, pageSize, token]);

  const exportToCsv = () => {
    // Create CSV content
    const headers = ['User', 'Model', 'Prompt Credits', 'Completion Credits', 'Total Credits', 'Type', 'Plan', 'Timestamp'];
    const rows = records.map(record => [
      record.email || record.user_id,
      record.model_id,
      record.prompt_tokens,
      record.completion_tokens,
      record.total_tokens,
      record.request_type,
      record.subscription_plan,
      record.timestamp
    ]);

    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n');

    // Create a download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `credit-usage-${format(new Date(), 'yyyy-MM-dd')}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="flex flex-col sm:flex-row gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-full sm:w-auto">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {format(fromDate, 'MMM dd, yyyy')}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={fromDate}
                onSelect={(date) => date && setFromDate(date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
          <span className="hidden sm:flex items-center">to</span>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-full sm:w-auto">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {format(toDate, 'MMM dd, yyyy')}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={toDate}
                onSelect={(date) => date && setToDate(date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
        <Button variant="outline" onClick={exportToCsv}>
          <Download className="mr-2 h-4 w-4" />
          Export CSV
        </Button>
      </div>

      {loading ? (
        <div className="flex justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : error ? (
        <div className="text-red-500 p-4">{error}</div>
      ) : (
        <>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Model</TableHead>
                  <TableHead className="text-right">Prompt</TableHead>
                  <TableHead className="text-right">Completion</TableHead>
                  <TableHead className="text-right">Total</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Plan</TableHead>
                  <TableHead>Timestamp</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {records.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                      No records found for the selected date range
                    </TableCell>
                  </TableRow>
                ) : (
                  records.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell className="font-medium">{record.email || record.user_id.substring(0, 8)}</TableCell>
                      <TableCell>{record.model_id}</TableCell>
                      <TableCell className="text-right">{record.prompt_tokens.toLocaleString()}</TableCell>
                      <TableCell className="text-right">{record.completion_tokens.toLocaleString()}</TableCell>
                      <TableCell className="text-right font-medium">{record.total_tokens.toLocaleString()}</TableCell>
                      <TableCell>{record.request_type}</TableCell>
                      <TableCell>{record.subscription_plan}</TableCell>
                      <TableCell>{format(new Date(record.timestamp), 'MMM dd, HH:mm')}</TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              Page {page} of {totalPages}
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(p => Math.max(1, p - 1))}
                disabled={page === 1}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                disabled={page === totalPages}
              >
                Next
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
