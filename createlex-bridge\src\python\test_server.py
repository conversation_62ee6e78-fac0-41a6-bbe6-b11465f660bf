#!/usr/bin/env python3
"""
Test script for the MCP Server
This script tests the server functionality without requiring Unreal Engine to be running.
"""

import json
import sys
import time
import subprocess
import requests
from pathlib import Path

def test_syntax():
    """Test that the server code compiles without syntax errors."""
    print("🔍 Testing Python syntax...")
    try:
        import py_compile
        py_compile.compile('mcp_server.py', doraise=True)
        print("✅ Syntax check passed")
        return True
    except py_compile.PyCompileError as e:
        print(f"❌ Syntax error: {e}")
        return False

def test_imports():
    """Test that all required imports are available."""
    print("📦 Testing imports...")
    try:
        import socket
        import json
        import os
        import atexit
        from pathlib import Path
        print("✅ Standard library imports OK")
        
        try:
            from mcp.server.fastmcp import FastMCP
            print("✅ FastMCP import OK")
        except ImportError as e:
            print(f"⚠️  FastMCP not available: {e}")
            print("   Install with: pip install fastmcp>=1.0.0")
            return False
            
        try:
            from dotenv import load_dotenv
            print("✅ python-dotenv import OK")
        except ImportError:
            print("⚠️  python-dotenv not available (optional)")
            
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_docker_build():
    """Test that Docker image builds successfully."""
    print("🐳 Testing Docker build...")
    try:
        result = subprocess.run(
            ["docker", "build", "-t", "unreal-mcp-server-test", "."],
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )
        
        if result.returncode == 0:
            print("✅ Docker build successful")
            return True
        else:
            print(f"❌ Docker build failed: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ Docker build timed out")
        return False
    except FileNotFoundError:
        print("⚠️  Docker not found - skipping Docker test")
        return True
    except Exception as e:
        print(f"❌ Docker build error: {e}")
        return False

def test_server_startup():
    """Test that the server can start up (without full functionality)."""
    print("🚀 Testing server startup...")
    try:
        # This would normally start the server, but we'll just test the import
        import mcp_server
        print("✅ Server module loads successfully")
        return True
    except Exception as e:
        print(f"❌ Server startup test failed: {e}")
        return False

def test_environment_config():
    """Test environment variable configuration."""
    print("⚙️  Testing environment configuration...")
    
    # Test default values
    import os
    original_host = os.environ.get("UNREAL_HOST")
    original_port = os.environ.get("UNREAL_PORT")
    
    try:
        # Clear environment variables
        if "UNREAL_HOST" in os.environ:
            del os.environ["UNREAL_HOST"]
        if "UNREAL_PORT" in os.environ:
            del os.environ["UNREAL_PORT"]
            
        # Test defaults
        host = os.environ.get("UNREAL_HOST", "localhost")
        port = int(os.environ.get("UNREAL_PORT", "9877"))
        
        if host == "localhost" and port == 9877:
            print("✅ Default configuration OK")
        else:
            print(f"❌ Default configuration failed: {host}:{port}")
            return False
            
        # Test custom values
        os.environ["UNREAL_HOST"] = "test-host"
        os.environ["UNREAL_PORT"] = "1234"
        
        host = os.environ.get("UNREAL_HOST", "localhost")
        port = int(os.environ.get("UNREAL_PORT", "9877"))
        
        if host == "test-host" and port == 1234:
            print("✅ Custom configuration OK")
            return True
        else:
            print(f"❌ Custom configuration failed: {host}:{port}")
            return False
            
    finally:
        # Restore original values
        if original_host is not None:
            os.environ["UNREAL_HOST"] = original_host
        elif "UNREAL_HOST" in os.environ:
            del os.environ["UNREAL_HOST"]
            
        if original_port is not None:
            os.environ["UNREAL_PORT"] = original_port
        elif "UNREAL_PORT" in os.environ:
            del os.environ["UNREAL_PORT"]

def test_file_structure():
    """Test that all required files are present."""
    print("📁 Testing file structure...")
    
    required_files = [
        "mcp_server.py",
        "requirements.txt",
        "Dockerfile",
        "docker-compose.yml",
        "README.md",
        "env.example",
        "deploy.sh"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing files: {', '.join(missing_files)}")
        return False
    else:
        print("✅ All required files present")
        return True

def main():
    """Run all tests."""
    print("🧪 Running MCP Server Tests")
    print("=" * 50)
    
    tests = [
        test_file_structure,
        test_syntax,
        test_imports,
        test_environment_config,
        test_server_startup,
        test_docker_build,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # Empty line between tests
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The server is ready for deployment.")
        return 0
    else:
        print("⚠️  Some tests failed. Please fix the issues before deploying.")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 