Write-Host "Starting CreateLex Bridge in Development Mode (localhost)..." -ForegroundColor Cyan
Write-Host ""

# Set environment variables for localhost development
$env:NODE_ENV = "development"
$env:CREATELEX_BASE_URL = "http://localhost:3000"
$env:API_BASE_URL = "http://localhost:5001/api"
$env:DEV_MODE = "true"

Write-Host "Configuration:" -ForegroundColor Yellow
Write-Host "- Frontend URL: $($env:CREATELEX_BASE_URL)" -ForegroundColor Green
Write-Host "- Backend API: $($env:API_BASE_URL)" -ForegroundColor Green
Write-Host "- Development Mode: $($env:DEV_MODE)" -ForegroundColor Green
Write-Host ""

Write-Host "Starting bridge..." -ForegroundColor Cyan
npm start 