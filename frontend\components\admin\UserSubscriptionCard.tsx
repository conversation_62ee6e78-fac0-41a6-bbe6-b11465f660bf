'use client';

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { CreditCard, Calendar } from 'lucide-react';
import { format } from 'date-fns';

interface UserSubscriptionCardProps {
  user: any;
  onSubscriptionChange: (status: string) => void;
}

export default function UserSubscriptionCard({ user, onSubscriptionChange }: UserSubscriptionCardProps) {
  if (!user) return null;
  
  const getSubscriptionBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'canceled':
        return <Badge variant="outline" className="text-orange-500 border-orange-500">Canceled</Badge>;
      case 'inactive':
        return <Badge variant="outline" className="text-gray-500">Inactive</Badge>;
      default:
        return <Badge variant="outline" className="text-gray-500">None</Badge>;
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-sm font-medium">Subscription</CardTitle>
        <CardDescription>
          Current subscription status and details
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Status</span>
            {getSubscriptionBadge(user.subscription_status || 'inactive')}
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Plan</span>
            <Badge variant="outline">
              {user.subscription_plan ? user.subscription_plan.toUpperCase() : 'None'}
            </Badge>
          </div>
          
          {user.subscription_id && (
            <div className="flex items-center text-sm text-gray-500 mt-1">
              <CreditCard className="h-4 w-4 mr-1" />
              <span>ID: {user.subscription_id}</span>
            </div>
          )}
          
          {user.subscription_start_date && (
            <div className="flex items-center text-sm text-gray-500">
              <Calendar className="h-4 w-4 mr-1" />
              <span>Started: {format(new Date(user.subscription_start_date), 'MMM dd, yyyy')}</span>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex flex-col space-y-2">
        <div className="grid grid-cols-2 gap-2 w-full">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => onSubscriptionChange('active')}
            disabled={user.subscription_status === 'active'}
          >
            Activate
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => onSubscriptionChange('inactive')}
            disabled={user.subscription_status === 'inactive'}
          >
            Deactivate
          </Button>
        </div>
        <Button 
          variant="outline" 
          size="sm" 
          className="w-full"
          onClick={() => onSubscriptionChange('canceled')}
          disabled={user.subscription_status === 'canceled'}
        >
          Mark as Canceled
        </Button>
      </CardFooter>
    </Card>
  );
}
