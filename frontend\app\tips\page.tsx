'use client';

import React, { useState, useEffect } from 'react';
import Navbar from '../../components/landing/Navbar';
import Footer from '../../components/landing/Footer';
import { useAuth } from '../../contexts/AuthContext';

const TipsPage: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const [isScrolled, setIsScrolled] = useState(false);

  // Update navbar style on scroll
  useEffect(() => {
    const handleScroll = () => setIsScrolled(window.scrollY > 50);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900 text-white">
      <Navbar isScrolled={isScrolled} isAuthenticated={isAuthenticated} />

      <main className="pt-24 pb-16">
        <div className="container mx-auto px-4 md:px-8 max-w-4xl">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="inline-block mb-4 px-4 py-1 border border-blue-400 rounded-full bg-blue-900/30">
              <span className="text-blue-300 font-medium flex items-center gap-2">
                <span role="img" aria-label="Graduation Cap">🎓</span>
                Tips
              </span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              How To Get <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-300">Consistent Results</span>
            </h1>
          </div>

          {/* Content */}
          <div className="space-y-6 text-gray-300">
            <p>
              From time to time the assistant will respond with a note that it "couldn't generate a Blueprint function from your request."  In most cases this happens because:
            </p>
            <ul className="list-disc list-inside space-y-2">
              <li>Your prompt describes something that would actually require several functions or a more complex system.</li>
              <li>The feature you're asking for isn't possible with Blueprint nodes alone (for example, it needs C++ or an external plugin).</li>
              <li>The wording of the prompt sounds like normal chat rather than a build-this-function command.</li>
              <li>Very occasionally, the model just misfires—try sending the same request again.</li>
            </ul>

            <h2 className="text-2xl font-semibold text-white mt-8">How to prompt</h2>
            <ul className="list-disc list-inside space-y-2">
              <li>
                Start with <code className="text-blue-400">Generate a function that …</code>. That single phrase switches the model into
                "blueprint-builder" mode.
              </li>
              <li>
                Describe inputs and outputs up front (e.g. "takes Health and Damage, returns NewHealth") rather than describing every pin connection.
              </li>
              <li>
                Keep the explanation high-level—let the AI decide the exact sequence of nodes. Over-specifying each step can confuse the generator.
              </li>
              <li>
                If you get the "can't generate" notice, try simplifying the request first; add extra constraints only after you see a working draft.
              </li>
            </ul>

            <p className="mt-6">
              When a function is produced but doesn't work as expected, please drop us a note with the prompt and a short description of what went wrong.  Real-world feedback is the fastest way for us to improve the generator! 🙏
            </p>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default TipsPage; 