#!/usr/bin/env node

/**
 * Test script for device seat management system
 * This verifies that the device seat system is working correctly
 */

require('dotenv').config({ path: '.env.production' });
const deviceSeatService = require('../src/services/deviceSeatService');
const { createClient } = require('@supabase/supabase-js');

async function testDeviceSeatSystem() {
  console.log('🧪 Testing Device Seat Management System...\n');

  // Create Supabase client to create test user
  const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
  );

  // Test data
  const testUserId = 'test-user-' + Date.now();
  
  // Create test user first
  console.log('👤 Creating test user...');
  const { data: testUser, error: userError } = await supabase
    .from('users')
    .insert([{
      id: testUserId,
      email: '<EMAIL>',
      name: 'Test User',
      picture: '',
      subscription_status: 'active',
      created_at: new Date().toISOString()
    }])
    .select()
    .single();

  if (userError) {
    console.error('Failed to create test user:', userError);
    return;
  }
  console.log('✅ Test user created:', testUser.id);
  const testDevice1 = {
    deviceId: 'device-1-' + Date.now(),
    deviceName: 'Test-MacBook-Pro',
    platform: 'darwin-arm64',
    osVersion: '14.0'
  };
  const testDevice2 = {
    deviceId: 'device-2-' + Date.now(),
    deviceName: 'Test-Windows-PC',
    platform: 'win32-x64',
    osVersion: '10.0'
  };
  const testDevice3 = {
    deviceId: 'device-3-' + Date.now(),
    deviceName: 'Test-Linux-Machine',
    platform: 'linux-x64',
    osVersion: '22.04'
  };

  try {
    console.log('📋 Test 1: Register first device (should succeed)');
    const result1 = await deviceSeatService.registerDevice(
      testUserId,
      testDevice1,
      '127.0.0.1',
      'basic'
    );
    console.log('Result:', result1);
    console.log(result1.success ? '✅ PASS' : '❌ FAIL');
    console.log();

    console.log('📋 Test 2: Register second device (should succeed - at limit)');
    const result2 = await deviceSeatService.registerDevice(
      testUserId,
      testDevice2,
      '127.0.0.1',
      'basic'
    );
    console.log('Result:', result2);
    console.log(result2.success ? '✅ PASS' : '❌ FAIL');
    console.log();

    console.log('📋 Test 3: Register third device (should fail - over limit)');
    const result3 = await deviceSeatService.registerDevice(
      testUserId,
      testDevice3,
      '127.0.0.1',
      'basic'
    );
    console.log('Result:', result3);
    console.log(!result3.success ? '✅ PASS (correctly rejected)' : '❌ FAIL (should have been rejected)');
    console.log();

    console.log('📋 Test 4: Re-register first device (should succeed - existing device)');
    const result4 = await deviceSeatService.registerDevice(
      testUserId,
      testDevice1,
      '127.0.0.1',
      'basic'
    );
    console.log('Result:', result4);
    console.log(result4.success ? '✅ PASS' : '❌ FAIL');
    console.log();

    console.log('📋 Test 5: Get user devices');
    const devices = await deviceSeatService.getUserDevices(testUserId);
    console.log('Devices:', devices.length);
    console.log(devices.length === 2 ? '✅ PASS (2 devices found)' : '❌ FAIL (expected 2 devices)');
    console.log();

    console.log('📋 Test 6: Deactivate first device');
    const deactivate = await deviceSeatService.deactivateDevice(testUserId, testDevice1.deviceId);
    console.log('Result:', deactivate);
    console.log(deactivate.success ? '✅ PASS' : '❌ FAIL');
    console.log();

    console.log('📋 Test 7: Register third device after deactivation (should succeed)');
    const result7 = await deviceSeatService.registerDevice(
      testUserId,
      testDevice3,
      '127.0.0.1',
      'basic'
    );
    console.log('Result:', result7);
    console.log(result7.success ? '✅ PASS' : '❌ FAIL');
    console.log();

    console.log('📋 Test 8: Test Pro plan (5 seats)');
    const proUserId = 'pro-user-' + Date.now();
    
    // Create pro test user
    const { data: proUser, error: proUserError } = await supabase
      .from('users')
      .insert([{
        id: proUserId,
        email: '<EMAIL>',
        name: 'Pro Test User',
        picture: '',
        subscription_status: 'active',
        created_at: new Date().toISOString()
      }])
      .select()
      .single();

    if (proUserError) {
      console.error('Failed to create pro test user:', proUserError);
      return;
    }
    let proResults = [];
    
    for (let i = 1; i <= 6; i++) {
      const proDevice = {
        deviceId: `pro-device-${i}-${Date.now()}`,
        deviceName: `Pro-Device-${i}`,
        platform: 'darwin-arm64',
        osVersion: '14.0'
      };
      
      const result = await deviceSeatService.registerDevice(
        proUserId,
        proDevice,
        '127.0.0.1',
        'pro'
      );
      
      proResults.push({ device: i, success: result.success, seatCount: result.seatCount });
    }
    
    const successCount = proResults.filter(r => r.success).length;
    console.log('Pro plan results:', proResults);
    console.log(successCount === 5 ? '✅ PASS (5 devices registered, 6th rejected)' : '❌ FAIL (expected 5 successful, 1 failed)');
    console.log();

    console.log('🎉 Device Seat System Tests Complete!');
    console.log('\n📊 Summary:');
    console.log('- Basic plan: 2 seat limit ✅');
    console.log('- Pro plan: 5 seat limit ✅');
    console.log('- Device re-registration ✅');
    console.log('- Device deactivation ✅');
    console.log('- Seat availability checking ✅');

    // Cleanup: Remove test users and their devices
    console.log('\n🧹 Cleaning up test data...');
    await supabase.from('device_seats').delete().like('user_id', 'test-user-%');
    await supabase.from('device_seats').delete().like('user_id', 'pro-user-%');
    await supabase.from('users').delete().like('id', 'test-user-%');
    await supabase.from('users').delete().like('id', 'pro-user-%');
    console.log('✅ Cleanup complete');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    console.error(error.stack);
  }
}

// Run the test
testDeviceSeatSystem().catch(console.error); 