'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { Session, User, createClient } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import { updateUserId } from '@/lib/user-id';

// Fallback Supabase client in case the main one fails
const getFallbackSupabaseClient = () => {
  const supabaseUrl = 'https://ujiakzkncbxisdatygpo.supabase.co';
  const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.eBXnLrlnRUAS5sEQe5Nra89mTmaSy8FidGGEWAL-c-A';

  try {
    return createClient(supabaseUrl, supabaseAnonKey);
  } catch (error) {
    console.error('Error creating fallback Supabase client:', error);
    return null;
  }
};

type SupabaseAuthContextType = {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  signIn: (provider: 'google' | 'github') => Promise<void>;
  signOut: () => Promise<void>;
};

const SupabaseAuthContext = createContext<SupabaseAuthContextType>({
  user: null,
  session: null,
  isLoading: true,
  signIn: async () => {},
  signOut: async () => {},
});

export const useSupabaseAuth = () => useContext(SupabaseAuthContext);

export const SupabaseAuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if we're in development mode and want to use a mock user
    const useMockUser = process.env.NEXT_PUBLIC_USE_MOCK_USER === 'true';

    if (useMockUser) {
      // For development purposes, create a mock user
      // Create a more complete mock user that matches the User type
      const mockUser: User = {
        id: 'dev-user-123',
        email: '<EMAIL>',
        app_metadata: {},
        user_metadata: {
          full_name: 'Development User',
          avatar_url: 'https://via.placeholder.com/150'
        },
        aud: 'authenticated',
        created_at: new Date().toISOString(),
        role: '',
        updated_at: new Date().toISOString()
      };

      // Set mock user and session
      setUser(mockUser);
      setSession({ user: mockUser } as Session);

      // Store the mock user ID in localStorage for consistent user identification
      updateUserId(mockUser.id, 'supabase');

      setIsLoading(false);

      console.log('Development mode: Using mock user');
      return () => {};
    } else {
      // Use the actual Supabase auth with fallback
      const getInitialSession = async () => {
        try {
          // Try with the main client first
          let sessionResult;
          try {
            sessionResult = await supabase.auth.getSession();
          } catch (error) {
            console.error('Error with main Supabase client, trying fallback:', error);
            const fallbackClient = getFallbackSupabaseClient();
            if (fallbackClient) {
              sessionResult = await fallbackClient.auth.getSession();
            } else {
              throw new Error('Both main and fallback Supabase clients failed');
            }
          }

          const session = sessionResult?.data?.session;
          console.log('Initial session:', session);

          if (session?.user?.id) {
            // Store the Supabase user ID in localStorage for consistent user identification
            updateUserId(session.user.id, 'supabase');
          }

          setSession(session);
          setUser(session?.user ?? null);
        } catch (error) {
          console.error('Error getting initial session:', error);
        } finally {
          setIsLoading(false);
        }
      };

      getInitialSession();

      // Listen for auth changes with fallback
      let subscription;
      try {
        const result = supabase.auth.onAuthStateChange((_event, session) => {
          console.log('Auth state changed:', session);

          if (session?.user?.id) {
            // Store the Supabase user ID in localStorage for consistent user identification
            updateUserId(session.user.id, 'supabase');
          }

          setSession(session);
          setUser(session?.user ?? null);
          setIsLoading(false);
        });
        subscription = result.data.subscription;
      } catch (error) {
        console.error('Error with main Supabase auth subscription, trying fallback:', error);
        const fallbackClient = getFallbackSupabaseClient();
        if (fallbackClient) {
          const result = fallbackClient.auth.onAuthStateChange((_event, session) => {
            console.log('Auth state changed (fallback):', session);

            if (session?.user?.id) {
              updateUserId(session.user.id, 'supabase');
            }

            setSession(session);
            setUser(session?.user ?? null);
            setIsLoading(false);
          });
          subscription = result.data.subscription;
        }
      }

      return () => {
        subscription.unsubscribe();
      };
    }
  }, []);

  const signIn = async (provider: 'google' | 'github') => {
    try {
      // Try with main client first
      try {
        const { error } = await supabase.auth.signInWithOAuth({
          provider,
          options: {
            redirectTo: `${window.location.origin}/auth/callback`,
          },
        });

        if (error) {
          throw error;
        }
      } catch (mainError) {
        console.error(`Error signing in with main client, trying fallback:`, mainError);
        // Try with fallback client
        const fallbackClient = getFallbackSupabaseClient();
        if (!fallbackClient) {
          throw new Error('Fallback Supabase client creation failed');
        }

        const { error } = await fallbackClient.auth.signInWithOAuth({
          provider,
          options: {
            redirectTo: `${window.location.origin}/auth/callback`,
          },
        });

        if (error) {
          throw error;
        }
      }
    } catch (error) {
      console.error(`Error signing in with ${provider}:`, error);
      throw error;
    }
  };

  const signOut = async () => {
    try {
      // Clear user state immediately to ensure UI updates
      setUser(null);
      setSession(null);

      // Try with main client first
      try {
        const { error } = await supabase.auth.signOut();
        if (error) {
          console.warn('Error from main client signOut, continuing with cleanup:', error);
        }
      } catch (mainError) {
        console.warn('Exception from main client signOut, continuing with cleanup:', mainError);

        // Try with fallback client
        try {
          const fallbackClient = getFallbackSupabaseClient();
          if (fallbackClient) {
            const { error } = await fallbackClient.auth.signOut();
            if (error) {
              console.warn('Error from fallback client signOut, continuing with cleanup:', error);
            }
          }
        } catch (fallbackError) {
          console.warn('Exception from fallback client signOut, continuing with cleanup:', fallbackError);
        }
      }

      // Clear localStorage - this is the most important part for the client-side
      if (typeof window !== 'undefined') {
        // Clear Supabase session storage
        localStorage.removeItem('sb-ujiakzkncbxisdatygpo-auth-token');

        // Clear our custom storage
        localStorage.removeItem('ai-chat-user-id');
        localStorage.removeItem('ai-chat-user-id-source');
        localStorage.removeItem('ai-chat-auth-token');
        localStorage.removeItem('ai-chat-refresh-token');

        // Clear any other potential auth-related items
        localStorage.removeItem('supabase.auth.token');
        localStorage.removeItem('supabase-auth-token');
      }

      // Clear cookies if possible
      if (typeof document !== 'undefined') {
        document.cookie = 'sb-access-token=; Max-Age=0; path=/; domain=' + window.location.hostname;
        document.cookie = 'sb-refresh-token=; Max-Age=0; path=/; domain=' + window.location.hostname;
      }

      console.log('Successfully completed signOut process');
    } catch (error) {
      console.error('Error in signOut process:', error);
      // Don't throw the error, just log it
      // This ensures the UI can proceed with logout
    }
  };

  const value = {
    user,
    session,
    isLoading,
    signIn,
    signOut,
  };

  return (
    <SupabaseAuthContext.Provider value={value}>
      {children}
    </SupabaseAuthContext.Provider>
  );
};
