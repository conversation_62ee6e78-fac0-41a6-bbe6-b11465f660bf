'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';

export default function TestTokenBalance() {
  const { user } = useAuth();
  const [tokenBalance, setTokenBalance] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTokenBalance = async () => {
    setLoading(true);
    setError(null);

    try {
      if (!user || !user.id) {
        setError('No user ID available');
        setLoading(false);
        return;
      }

      // Use Next.js API route instead of direct backend call
      const url = `/api/tokens/balance?userId=${user.id}&nocache=${Date.now()}`;

      console.log('Making API call:', url);

      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': user.id,
          'Authorization': 'Bearer dummy-token'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Token balance data:', data);
        setTokenBalance(data);
      } else {
        setError(`Failed to fetch token balance: ${response.status}`);
      }
    } catch (error) {
      console.error('Error fetching token balance:', error);
      if (error instanceof Error) {
        setError(`Error: ${error.message}`);
      } else {
        setError('An unknown error occurred');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchTokenBalance();
    }
  }, [user]);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Token Balance Test</h1>

      {loading && <p>Loading token balance...</p>}

      {error && (
        <div className="p-4 bg-red-100 border border-red-300 rounded-md mb-4">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {tokenBalance && (
        <div className="p-4 bg-white border border-gray-300 rounded-md">
          <h2 className="text-xl font-semibold mb-2">Token Balance</h2>
          <p className="text-lg">User ID: {tokenBalance.user_id}</p>
          <p className="text-lg font-bold">Balance: {tokenBalance.balance.toLocaleString()} tokens</p>
          <p className="text-sm text-gray-600">Last Updated: {new Date(tokenBalance.updated_at).toLocaleString()}</p>
        </div>
      )}

      <button
        onClick={fetchTokenBalance}
        className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
      >
        Refresh Token Balance
      </button>
    </div>
  );
}
