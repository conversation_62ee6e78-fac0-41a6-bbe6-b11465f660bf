'use client';

import { useState, useEffect } from 'react';
import { useSupabaseAuth } from '../../../contexts/SupabaseAuthContext';
import Link from 'next/link';

export default function AuthTestPage() {
  const { user, session, isLoading, signIn } = useSupabaseAuth();
  const [testResult, setTestResult] = useState<'idle' | 'success' | 'error'>('idle');
  const [error, setError] = useState<string | null>(null);
  const [apiResponse, setApiResponse] = useState<any>(null);

  const testAuthenticatedEndpoint = async () => {
    if (!user) {
      setError('You must be logged in to test authenticated endpoints');
      return;
    }

    try {
      setTestResult('idle');
      setError(null);
      
      // Get the API URL from environment variables
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'https://api.createlex.com';
      
      // Call an authenticated endpoint
      const response = await fetch(`${backendUrl}/api/user/profile`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session?.access_token || ''}`,
          'x-user-id': user.id,
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setTestResult('success');
        setApiResponse(data);
      } else {
        setTestResult('error');
        setError(`API returned status: ${response.status}`);
        try {
          const errorData = await response.json();
          setApiResponse(errorData);
        } catch (e) {
          // If we can't parse the error as JSON, just show the status
        }
      }
    } catch (error: any) {
      setTestResult('error');
      setError(error.message || 'Unknown error occurred');
    }
  };

  return (
    <div className="container mx-auto p-8">
      <div className="mb-4">
        <Link href="/api-test" className="text-blue-500 hover:underline">
          ← Back to API Test
        </Link>
      </div>
      
      <h1 className="text-3xl font-bold mb-6">Authentication Test</h1>
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Authentication Status</h2>
        <div className={`p-4 rounded ${
          isLoading 
            ? 'bg-yellow-100 text-yellow-800' 
            : user 
              ? 'bg-green-100 text-green-800' 
              : 'bg-red-100 text-red-800'
        }`}>
          {isLoading && 'Checking authentication status...'}
          {!isLoading && user && 'You are authenticated! ✅'}
          {!isLoading && !user && 'You are not authenticated! ❌'}
        </div>
      </div>
      
      {user && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">User Information</h2>
          <pre className="p-4 bg-gray-100 rounded overflow-auto">
            {JSON.stringify({
              id: user.id,
              email: user.email,
              name: user.user_metadata?.full_name,
            }, null, 2)}
          </pre>
        </div>
      )}
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Actions</h2>
        {!user ? (
          <button
            onClick={() => signIn('google')}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Sign in with Google
          </button>
        ) : (
          <button
            onClick={testAuthenticatedEndpoint}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Test Authenticated Endpoint
          </button>
        )}
      </div>
      
      {testResult !== 'idle' && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Test Result</h2>
          <div className={`p-4 rounded ${
            testResult === 'success' 
              ? 'bg-green-100 text-green-800' 
              : 'bg-red-100 text-red-800'
          }`}>
            {testResult === 'success' && 'Authenticated API call successful! ✅'}
            {testResult === 'error' && 'Authenticated API call failed! ❌'}
          </div>
        </div>
      )}
      
      {error && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Error</h2>
          <div className="p-4 bg-red-100 text-red-800 rounded">
            {error}
          </div>
        </div>
      )}
      
      {apiResponse && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">API Response</h2>
          <pre className="p-4 bg-gray-100 rounded overflow-auto">
            {JSON.stringify(apiResponse, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
