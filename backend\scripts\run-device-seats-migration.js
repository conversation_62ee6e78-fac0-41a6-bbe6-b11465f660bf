#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to run the device seats migration
 * This creates the device_seats table and related functions
 */

require('dotenv').config({ path: '.env.production' });
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

async function runMigration() {
  console.log('🚀 Running device seats migration...\n');

  // Check required environment variables
  if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_KEY) {
    console.error('❌ Error: SUPABASE_URL and SUPABASE_SERVICE_KEY must be set in .env.production');
    process.exit(1);
  }

  // Create Supabase client
  const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
  );

  try {
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, '..', 'migrations', 'create_device_seats_table.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('📄 Reading migration file:', migrationPath);
    console.log('📝 Migration contains:', migrationSQL.split('\n').length, 'lines\n');

    // Execute the migration
    console.log('⚡ Executing migration...');
    const { error } = await supabase.rpc('exec_sql', {
      sql: migrationSQL
    });

    if (error) {
      // If exec_sql doesn't exist, try running the SQL directly
      console.log('⚠️  exec_sql function not found, trying direct execution...');
      
      // Split the SQL into individual statements
      const statements = migrationSQL
        .split(';')
        .filter(stmt => stmt.trim())
        .map(stmt => stmt.trim() + ';');

      for (const statement of statements) {
        if (statement.includes('CREATE TABLE')) {
          console.log('📊 Creating device_seats table...');
        } else if (statement.includes('CREATE INDEX')) {
          console.log('🔍 Creating index...');
        } else if (statement.includes('CREATE FUNCTION')) {
          console.log('🔧 Creating function...');
        }

        // For now, we'll need to run these manually in Supabase SQL editor
        console.log('\nStatement to run:');
        console.log('```sql');
        console.log(statement);
        console.log('```\n');
      }

      console.log('\n⚠️  Note: Since direct SQL execution is not available,');
      console.log('please run the following SQL in your Supabase SQL editor:');
      console.log('\n📋 Copy the contents of:', migrationPath);
      console.log('📍 Paste into: https://app.supabase.com/project/[YOUR_PROJECT]/sql/new');
      console.log('\n✅ After running the SQL, your device seats system will be ready!');
      return;
    }

    console.log('✅ Migration completed successfully!\n');

    // Verify the table was created
    console.log('🔍 Verifying migration...');
    const { data: tables } = await supabase
      .from('device_seats')
      .select('id')
      .limit(1);

    if (tables !== null) {
      console.log('✅ device_seats table verified successfully!');
    } else {
      console.log('⚠️  Could not verify device_seats table');
    }

    console.log('\n🎉 Device seats migration completed!');
    console.log('\n📝 Next steps:');
    console.log('1. Restart your backend server');
    console.log('2. Update your CreateLex Bridge to the latest version');
    console.log('3. Device seat management will be automatically enabled');

  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    console.error('\n💡 Troubleshooting:');
    console.error('1. Check your SUPABASE_URL and SUPABASE_SERVICE_KEY in .env.production');
    console.error('2. Ensure you have the necessary permissions');
    console.error('3. Try running the SQL manually in Supabase SQL editor');
    process.exit(1);
  }
}

// Run the migration
runMigration().catch(console.error); 