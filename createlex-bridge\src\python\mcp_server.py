import sys
import os
import socket
import json
from pathlib import Path
import asyncio
# Import the local FastMCP version from the protected directory
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'protected'))
from fastmcp import FastMCP
import atexit

# Load environment variables if available
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass  # dotenv is optional

# ----------------------------------------------------------------------
# NOTE: This file runs completely outside of Unreal's Python interpreter.
#       Do NOT import any unreal.* modules here. It's launched as a
#       standalone process by init_unreal.py.
# ----------------------------------------------------------------------

def write_pid_file():
    """
    Create a PID file so the Unreal plugin knows this process is running,
    and deletes it on exit.
    """
    try:
        pid = os.getpid()
        pid_dir = os.path.join(os.path.expanduser("~"), ".unrealgenai")
        os.makedirs(pid_dir, exist_ok=True)
        pid_path = os.path.join(pid_dir, "mcp_server.pid")

        with open(pid_path, "w") as f:
            # Write: <pid>\n<port>
            # We'll default to port 9877 here; Unreal must be listening on the same port.
            f.write(f"{pid}\n9877")

        def cleanup_pid_file():
            try:
                if os.path.exists(pid_path):
                    os.remove(pid_path)
            except:
                pass

        atexit.register(cleanup_pid_file)
        return pid_path

    except Exception as e:
        print(f"Failed to write PID file: {e}", file=sys.stderr)
        return None


# Write PID file on startup
pid_file = write_pid_file()
if pid_file:
    print(f"MCP Server started with PID file at: {pid_file}", file=sys.stderr)


# -----------------------------------------------------------------------------
# Create the FastMCP instance (this publishes whatever endpoints you've defined
# elsewhere in this plugin). "UnrealHandshake" is just an arbitrary identifier.
# -----------------------------------------------------------------------------
mcp = FastMCP("UnrealHandshake")


# -----------------------------------------------------------------------------
# send_to_unreal: open a TCP socket back to Unreal (default localhost:9877),
# send the JSON command, then wait for a complete JSON response. Return the
# parsed JSON as a Python dict/list.
#
# You can override host/port via env vars:
#   UNREAL_HOST (defaults to "localhost")
#   UNREAL_PORT (defaults to "9877")
# -----------------------------------------------------------------------------
def send_to_unreal(command):
    # Allow overriding via environment
    # Use localhost when running directly on host, host.docker.internal for Docker
    host = os.environ.get("UNREAL_HOST", "localhost")
    port = int(os.environ.get("UNREAL_PORT", "9877"))

    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.settimeout(10)  # 10 second timeout
            s.connect((host, port))
        except Exception as e:
            print(f"[mcp_server] ERROR: cannot connect to Unreal at {host}:{port} - {e}", file=sys.stderr)
            return None

        # Send JSON-encoded command
        try:
            json_str = json.dumps(command)
            s.sendall(json_str.encode("utf-8"))
        except Exception as e:
            print(f"[mcp_server] ERROR: failed to send JSON to Unreal - {e}", file=sys.stderr)
            return None

        # Read until we have valid JSON
        buffer_size = 8192
        response_data = b""
        while True:
            try:
                chunk = s.recv(buffer_size)
            except Exception as e:
                print(f"[mcp_server] ERROR: receive failed - {e}", file=sys.stderr)
                return None

            if not chunk:
                break
            response_data += chunk

            # Try parsing
            try:
                text = response_data.decode("utf-8")
                parsed = json.loads(text)
                return parsed
            except json.JSONDecodeError:
                # Not complete JSON yet; keep reading
                continue

        # If we exit loop without valid JSON:
        if response_data:
            try:
                text = response_data.decode("utf-8")
                parsed = json.loads(text)
                return parsed
            except Exception:
                print("[mcp_server] WARNING: received incomplete/invalid JSON from Unreal", file=sys.stderr)
                return None
        else:
            return None


# ---------------------------------------------------------------------
# Example MCP tools that communicate with Unreal Engine
# ---------------------------------------------------------------------

@mcp.tool()
def handshake_test() -> str:
    """Test connection to Unreal Engine with a simple handshake."""
    try:
        response = send_to_unreal({
            "type": "handshake",
            "message": "Hello from MCP Server"
        })
        if response:
            return f"Handshake successful: {response}"
        else:
            return "Handshake failed: No response from Unreal Engine"
    except Exception as e:
        return f"Handshake failed: {str(e)}"

@mcp.tool()
def spawn_actor(actor_class: str, x: float = 0.0, y: float = 0.0, z: float = 0.0) -> str:
    """Spawn an actor in Unreal Engine at the specified location."""
    try:
        response = send_to_unreal({
            "type": "spawn",
            "actor_class": actor_class,
            "location": [x, y, z]
        })
        if response:
            return f"Actor spawned successfully: {response}"
        else:
            return "Failed to spawn actor: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to spawn actor: {str(e)}"

@mcp.tool()
def get_scene_objects() -> str:
    """Get a list of all objects in the current Unreal Engine scene."""
    try:
        response = send_to_unreal({
            "type": "get_all_scene_objects"
        })
        if response:
            return f"Scene objects: {response}"
        else:
            return "Failed to get scene objects: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to get scene objects: {str(e)}"

@mcp.tool()
def create_material(material_name: str, base_color: list = [1.0, 1.0, 1.0], metallic: float = 0.0, roughness: float = 0.5) -> str:
    """Create a new material in Unreal Engine."""
    try:
        response = send_to_unreal({
            "type": "create_material",
            "material_name": material_name,
            "base_color": base_color,
            "metallic": metallic,
            "roughness": roughness
        })
        if response:
            return f"Material created successfully: {response}"
        else:
            return "Failed to create material: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to create material: {str(e)}"

@mcp.tool()
def modify_object(actor_name: str, property_name: str, property_value: str) -> str:
    """Modify a property of an object in Unreal Engine."""
    try:
        response = send_to_unreal({
            "type": "modify_object",
            "actor_name": actor_name,
            "property_name": property_name,
            "property_value": property_value
        })
        if response:
            return f"Object modified successfully: {response}"
        else:
            return "Failed to modify object: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to modify object: {str(e)}"

@mcp.tool()
def execute_python(script: str) -> str:
    """Execute Python script in Unreal Engine."""
    try:
        response = send_to_unreal({
            "type": "execute_python",
            "script": script
        })
        if response:
            return f"Python script executed: {response}"
        else:
            return "Failed to execute Python script: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to execute Python script: {str(e)}"

@mcp.tool()
def execute_unreal_command(command: str) -> str:
    """Execute Unreal Engine console command."""
    try:
        response = send_to_unreal({
            "type": "execute_unreal_command",
            "command": command
        })
        if response:
            return f"Unreal command executed: {response}"
        else:
            return "Failed to execute Unreal command: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to execute Unreal command: {str(e)}"

@mcp.tool()
def create_blueprint(blueprint_name: str, parent_class: str = "Actor", save_path: str = "/Game/Blueprints") -> str:
    """Create a new Blueprint in Unreal Engine."""
    try:
        response = send_to_unreal({
            "type": "create_blueprint",
            "blueprint_name": blueprint_name,
            "parent_class": parent_class,
            "save_path": save_path
        })
        if response:
            return f"Blueprint created successfully: {response}"
        else:
            return "Failed to create blueprint: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to create blueprint: {str(e)}"

@mcp.tool()
def add_component(blueprint_path: str, component_type: str, component_name: str) -> str:
    """Add a component to a Blueprint."""
    try:
        response = send_to_unreal({
            "type": "add_component",
            "blueprint_path": blueprint_path,
            "component_type": component_type,
            "component_name": component_name
        })
        if response:
            return f"Component added successfully: {response}"
        else:
            return "Failed to add component: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to add component: {str(e)}"

@mcp.tool()
def add_variable(blueprint_path: str, variable_name: str, variable_type: str, default_value: str = "") -> str:
    """Add a variable to a Blueprint."""
    try:
        response = send_to_unreal({
            "type": "add_variable",
            "blueprint_path": blueprint_path,
            "variable_name": variable_name,
            "variable_type": variable_type,
            "default_value": default_value
        })
        if response:
            return f"Variable added successfully: {response}"
        else:
            return "Failed to add variable: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to add variable: {str(e)}"

@mcp.tool()
def add_function(blueprint_path: str, function_name: str, return_type: str = "exec") -> str:
    """Add a function to a Blueprint."""
    try:
        response = send_to_unreal({
            "type": "add_function",
            "blueprint_path": blueprint_path,
            "function_name": function_name,
            "return_type": return_type
        })
        if response:
            return f"Function added successfully: {response}"
        else:
            return "Failed to add function: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to add function: {str(e)}"

@mcp.tool()
def add_node(blueprint_path: str, function_name: str, node_type: str, x: float = 0.0, y: float = 0.0) -> str:
    """Add a node to a Blueprint function."""
    try:
        response = send_to_unreal({
            "type": "add_node",
            "blueprint_path": blueprint_path,
            "function_name": function_name,
            "node_type": node_type,
            "x": x,
            "y": y
        })
        if response:
            return f"Node added successfully: {response}"
        else:
            return "Failed to add node: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to add node: {str(e)}"

@mcp.tool()
def connect_nodes(blueprint_path: str, function_name: str, output_node_guid: str, input_node_guid: str, output_pin: str = "exec", input_pin: str = "exec") -> str:
    """Connect two nodes in a Blueprint function."""
    try:
        response = send_to_unreal({
            "type": "connect_nodes",
            "blueprint_path": blueprint_path,
            "function_name": function_name,
            "output_node_guid": output_node_guid,
            "input_node_guid": input_node_guid,
            "output_pin": output_pin,
            "input_pin": input_pin
        })
        if response:
            return f"Nodes connected successfully: {response}"
        else:
            return "Failed to connect nodes: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to connect nodes: {str(e)}"

@mcp.tool()
def compile_blueprint(blueprint_path: str) -> str:
    """Compile a Blueprint."""
    try:
        response = send_to_unreal({
            "type": "compile_blueprint",
            "blueprint_path": blueprint_path
        })
        if response:
            return f"Blueprint compiled successfully: {response}"
        else:
            return "Failed to compile blueprint: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to compile blueprint: {str(e)}"

@mcp.tool()
def spawn_blueprint(blueprint_path: str, x: float = 0.0, y: float = 0.0, z: float = 0.0) -> str:
    """Spawn an instance of a Blueprint in the scene."""
    try:
        response = send_to_unreal({
            "type": "spawn_blueprint",
            "blueprint_path": blueprint_path,
            "location": [x, y, z]
        })
        if response:
            return f"Blueprint spawned successfully: {response}"
        else:
            return "Failed to spawn blueprint: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to spawn blueprint: {str(e)}"

@mcp.tool()
def get_all_nodes(blueprint_path: str, function_name: str) -> str:
    """Get all nodes in a Blueprint function."""
    try:
        response = send_to_unreal({
            "type": "get_all_nodes",
            "blueprint_path": blueprint_path,
            "function_name": function_name
        })
        if response:
            return f"Nodes retrieved: {response}"
        else:
            return "Failed to get nodes: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to get nodes: {str(e)}"

@mcp.tool()
def get_node_suggestions(search_term: str) -> str:
    """Get node suggestions for Blueprint creation."""
    try:
        response = send_to_unreal({
            "type": "get_node_suggestions",
            "search_term": search_term
        })
        if response:
            return f"Node suggestions: {response}"
        else:
            return "Failed to get node suggestions: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to get node suggestions: {str(e)}"

@mcp.tool()
def create_project_folder(folder_path: str) -> str:
    """Create a new folder in the Unreal Engine project."""
    try:
        response = send_to_unreal({
            "type": "create_project_folder",
            "folder_path": folder_path
        })
        if response:
            return f"Folder created successfully: {response}"
        else:
            return "Failed to create folder: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to create folder: {str(e)}"

@mcp.tool()
def get_files_in_folder(folder_path: str) -> str:
    """Get list of files in a project folder."""
    try:
        response = send_to_unreal({
            "type": "get_files_in_folder",
            "folder_path": folder_path
        })
        if response:
            return f"Files in folder: {response}"
        else:
            return "Failed to get files: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to get files: {str(e)}"

@mcp.tool()
def delete_node(blueprint_path: str, function_name: str, node_guid: str) -> str:
    """Delete a node from a Blueprint function."""
    try:
        response = send_to_unreal({
            "type": "delete_node",
            "blueprint_path": blueprint_path,
            "function_name": function_name,
            "node_guid": node_guid
        })
        if response:
            return f"Node deleted successfully: {response}"
        else:
            return "Failed to delete node: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to delete node: {str(e)}"

@mcp.tool()
def get_node_guid(blueprint_path: str, function_name: str, node_type: str) -> str:
    """Get the GUID of a specific node in a Blueprint function."""
    try:
        response = send_to_unreal({
            "type": "get_node_guid",
            "blueprint_path": blueprint_path,
            "function_name": function_name,
            "node_type": node_type
        })
        if response:
            return f"Node GUID: {response}"
        else:
            return "Failed to get node GUID: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to get node GUID: {str(e)}"

@mcp.tool()
def add_nodes_bulk(blueprint_path: str, function_name: str, nodes_data: str) -> str:
    """Add multiple nodes to a Blueprint function in bulk. nodes_data should be JSON string."""
    try:
        import json
        nodes = json.loads(nodes_data)
        response = send_to_unreal({
            "type": "add_nodes_bulk",
            "blueprint_path": blueprint_path,
            "function_name": function_name,
            "nodes": nodes
        })
        if response:
            return f"Nodes added in bulk: {response}"
        else:
            return "Failed to add nodes in bulk: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to add nodes in bulk: {str(e)}"

@mcp.tool()
def connect_nodes_bulk(blueprint_path: str, function_name: str, connections_data: str) -> str:
    """Connect multiple nodes in a Blueprint function in bulk. connections_data should be JSON string."""
    try:
        import json
        connections = json.loads(connections_data)
        response = send_to_unreal({
            "type": "connect_nodes_bulk",
            "blueprint_path": blueprint_path,
            "function_name": function_name,
            "connections": connections
        })
        if response:
            return f"Nodes connected in bulk: {response}"
        else:
            return "Failed to connect nodes in bulk: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to connect nodes in bulk: {str(e)}"

@mcp.tool()
def edit_component_property(actor_name: str, component_name: str, property_name: str, property_value: str) -> str:
    """Edit a property of a specific component on an actor."""
    try:
        response = send_to_unreal({
            "type": "edit_component_property",
            "actor_name": actor_name,
            "component_name": component_name,
            "property_name": property_name,
            "property_value": property_value
        })
        if response:
            return f"Component property edited: {response}"
        else:
            return "Failed to edit component property: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to edit component property: {str(e)}"

@mcp.tool()
def add_component_with_events(actor_name: str, component_type: str, component_name: str, events_data: str = "{}") -> str:
    """Add a component to an actor with event bindings. events_data should be JSON string."""
    try:
        import json
        events = json.loads(events_data)
        response = send_to_unreal({
            "type": "add_component_with_events",
            "actor_name": actor_name,
            "component_type": component_type,
            "component_name": component_name,
            "events": events
        })
        if response:
            return f"Component with events added: {response}"
        else:
            return "Failed to add component with events: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to add component with events: {str(e)}"

@mcp.tool()
def add_input_binding(action_name: str, key: str, binding_type: str = "Action") -> str:
    """Add an input binding to the project."""
    try:
        response = send_to_unreal({
            "type": "add_input_binding",
            "action_name": action_name,
            "key": key,
            "binding_type": binding_type
        })
        if response:
            return f"Input binding added: {response}"
        else:
            return "Failed to add input binding: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to add input binding: {str(e)}"

@mcp.tool()
def add_widget_to_user_widget(widget_blueprint_path: str, widget_type: str, widget_name: str, x: float = 0.0, y: float = 0.0) -> str:
    """Add a widget to a User Widget Blueprint."""
    try:
        response = send_to_unreal({
            "type": "add_widget_to_user_widget",
            "widget_blueprint_path": widget_blueprint_path,
            "widget_type": widget_type,
            "widget_name": widget_name,
            "x": x,
            "y": y
        })
        if response:
            return f"Widget added to User Widget: {response}"
        else:
            return "Failed to add widget: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to add widget: {str(e)}"

@mcp.tool()
def edit_widget_property(widget_blueprint_path: str, widget_name: str, property_name: str, property_value: str) -> str:
    """Edit a property of a widget in a User Widget Blueprint."""
    try:
        response = send_to_unreal({
            "type": "edit_widget_property",
            "widget_blueprint_path": widget_blueprint_path,
            "widget_name": widget_name,
            "property_name": property_name,
            "property_value": property_value
        })
        if response:
            return f"Widget property edited: {response}"
        else:
            return "Failed to edit widget property: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to edit widget property: {str(e)}"

@mcp.tool()
def server_status() -> str:
    """Get the current status of the MCP server and its connection to Unreal Engine."""
    try:
        # Use the same host configuration as send_to_unreal
        host = os.environ.get("UNREAL_HOST", "host.docker.internal")
        port = int(os.environ.get("UNREAL_PORT", "9877"))
        
        # Test connection
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(5)
            result = s.connect_ex((host, port))
            
        if result == 0:
            connection_status = f"✅ Connected to Unreal Engine at {host}:{port}"
        else:
            connection_status = f"❌ Cannot connect to Unreal Engine at {host}:{port}"
            
        return f"MCP Server Status:\n{connection_status}\nPID: {os.getpid()}\nPID File: {pid_file}"
    except Exception as e:
        return f"Status check failed: {str(e)}"

@mcp.tool()
def get_current_blueprint_context() -> str:
    """Get the current Blueprint context (which Blueprint is currently open/active in the editor)."""
    try:
        response = send_to_unreal({
            "type": "get_current_blueprint_context"
        })
        if response:
            return f"Current Blueprint context: {response}"
        else:
            return "Failed to get Blueprint context: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to get Blueprint context: {str(e)}"

@mcp.tool()
def generate_blueprint_function(
    function_name: str, 
    description: str,
    inputs: str = "[]",
    outputs: str = "[]",
    use_current_context: bool = True,
    blueprint_path: str = ""
) -> str:
    """Generate a complete Blueprint function with automatic node creation and wiring.
    
    Args:
        function_name: Name of the function to create
        description: Natural language description of what the function should do
        inputs: JSON string array of input parameters e.g. '[{"name": "Health", "type": "float"}, {"name": "Damage", "type": "float"}]'
        outputs: JSON string array of output parameters e.g. '[{"name": "NewHealth", "type": "float"}]'
        use_current_context: If True, uses the currently open Blueprint. If False, requires blueprint_path
        blueprint_path: Path to the Blueprint to add the function to (only used if use_current_context is False)
    """
    try:
        import json
        inputs_data = json.loads(inputs) if inputs else []
        outputs_data = json.loads(outputs) if outputs else []
        
        response = send_to_unreal({
            "type": "generate_blueprint_function",
            "function_name": function_name,
            "description": description,
            "inputs": inputs_data,
            "outputs": outputs_data,
            "use_current_context": use_current_context,
            "blueprint_path": blueprint_path
        })
        if response:
            return f"Blueprint function generated: {response}"
        else:
            return "Failed to generate Blueprint function: No response from Unreal Engine"
    except json.JSONDecodeError as e:
        return f"Failed to parse JSON inputs/outputs: {str(e)}"
    except Exception as e:
        return f"Failed to generate Blueprint function: {str(e)}"

@mcp.tool()
def analyze_blueprint_graph(
    graph_name: str = "EventGraph",
    use_current_context: bool = True,
    blueprint_path: str = "",
    include_connections: bool = True,
    include_node_details: bool = True
) -> str:
    """Analyze and explain what a Blueprint graph is doing by examining all nodes and connections.
    
    Args:
        graph_name: Name of the graph to analyze (e.g., "EventGraph", "ConstructionScript", or a function name)
        use_current_context: If True, uses the currently open Blueprint. If False, requires blueprint_path
        blueprint_path: Path to the Blueprint to analyze (only used if use_current_context is False)
        include_connections: Include information about how nodes are connected
        include_node_details: Include detailed information about each node's properties
    """
    try:
        response = send_to_unreal({
            "type": "analyze_blueprint_graph",
            "graph_name": graph_name,
            "use_current_context": use_current_context,
            "blueprint_path": blueprint_path,
            "include_connections": include_connections,
            "include_node_details": include_node_details
        })
        if response:
            return f"Blueprint graph analysis: {response}"
        else:
            return "Failed to analyze Blueprint graph: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to analyze Blueprint graph: {str(e)}"

@mcp.tool()
def generate_smart_blueprint_function(
    function_name: str,
    description: str,
    inputs: str = "[]",
    outputs: str = "[]",
    complexity: str = "medium"
) -> str:
    """Generate an intelligent Blueprint function with advanced node creation and wiring.
    
    Args:
        function_name: Name of the function to create
        description: Natural language description of what the function should do
        inputs: JSON string array of input parameters
        outputs: JSON string array of output parameters
        complexity: Function complexity level ("simple", "medium", "complex")
    """
    try:
        import json
        inputs_data = json.loads(inputs) if inputs else []
        outputs_data = json.loads(outputs) if outputs else []
        
        # Analyze the description and generate intelligent node plan
        node_plan = _analyze_function_description(description, inputs_data, outputs_data, complexity)
        
        # Send comprehensive generation request to Unreal
        response = send_to_unreal({
            "type": "generate_smart_blueprint_function",
            "function_name": function_name,
            "description": description,
            "inputs": inputs_data,
            "outputs": outputs_data,
            "node_plan": node_plan
        })
        
        if response and response.get("success"):
            return _format_function_generation_result(response)
        else:
            return f"Failed to generate smart Blueprint function: {response.get('error', 'Unknown error') if response else 'No response'}"
            
    except json.JSONDecodeError as e:
        return f"Failed to parse JSON inputs/outputs: {str(e)}"
    except Exception as e:
        return f"Failed to generate smart Blueprint function: {str(e)}"

@mcp.tool()
def get_blueprint_context_detailed() -> str:
    """Get detailed Blueprint context with full analysis capabilities."""
    try:
        response = send_to_unreal({
            "type": "get_blueprint_context_detailed"
        })
        if response and response.get("success"):
            return _process_blueprint_context(response)
        else:
            return "Failed to get detailed Blueprint context: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to get detailed Blueprint context: {str(e)}"

@mcp.tool()
def analyze_blueprint_graph_advanced(
    graph_name: str = "EventGraph",
    analysis_type: str = "comprehensive",
    include_ai_insights: bool = True
) -> str:
    """Perform advanced Blueprint graph analysis with AI-powered insights.
    
    Args:
        graph_name: Name of the graph to analyze
        analysis_type: Type of analysis ("basic", "comprehensive", "performance", "logic_flow")
        include_ai_insights: Whether to include AI-generated insights and suggestions
    """
    try:
        # Get raw graph data from Unreal
        response = send_to_unreal({
            "type": "get_blueprint_graph_data",
            "graph_name": graph_name,
            "include_full_details": True
        })
        
        if not response or not response.get("success"):
            return f"Failed to get graph data: {response.get('error', 'Unknown error')}"
        
        # Process the graph data with advanced analysis
        analysis_result = _perform_advanced_graph_analysis(
            response.get("graph_data", {}),
            analysis_type,
            include_ai_insights
        )
        
        return _format_advanced_analysis_result(analysis_result)
        
    except Exception as e:
        return f"Failed to perform advanced graph analysis: {str(e)}"

@mcp.tool()
def generate_context_aware_blueprint_function(
    function_description: str,
    function_name: str = "",
    complexity: str = "medium"
) -> str:
    """Generate a context-aware Blueprint function using current Blueprint state and C++ Asset Registry API.
    
    Args:
        function_description: Natural language description of what the function should do
        function_name: Optional custom function name (will be auto-generated if not provided)
        complexity: Function complexity level ("simple", "medium", "complex")
    """
    try:
        # First get the current Blueprint context using our C++ Asset Registry API
        context_response = send_to_unreal({
            "type": "get_current_blueprint_context"
        })
        
        if not context_response or not context_response.get("success"):
            return f"❌ Failed to get Blueprint context: {context_response.get('error', 'No response') if context_response else 'No response from Unreal Engine'}"
        
        # Extract Blueprint information
        blueprints = context_response.get("blueprints", [])
        if not blueprints:
            return "❌ No Blueprint information found. Make sure a Blueprint is open in UE5."
        
        blueprint_info = blueprints[0]
        
        # Generate function name if not provided
        if not function_name:
            function_name = _generate_function_name_from_description(function_description)
        
        # Create enhanced generation request with full context
        enhanced_request = {
            "type": "generate_context_aware_blueprint_function",
            "function_name": function_name,
            "function_description": function_description,
            "complexity": complexity,
            "blueprint_context": {
                "blueprint_name": blueprint_info.get("name", "Unknown"),
                "blueprint_type": blueprint_info.get("type", "Unknown"),
                "blueprint_path": blueprint_info.get("path", ""),
                "existing_variables": [var.get("name", "") for var in blueprint_info.get("variables", [])],
                "existing_functions": [func.get("name", "") for func in blueprint_info.get("functions", [])],
                "event_graph_nodes": blueprint_info.get("event_graph", {}).get("node_count", 0),
                "total_nodes": blueprint_info.get("total_nodes", 0),
                "node_patterns": blueprint_info.get("node_patterns", {}),
                "detection_method": blueprint_info.get("detection_method", "unknown")
            }
        }
        
        # Send the enhanced request to Unreal Engine
        generation_response = send_to_unreal(enhanced_request)
        
        if generation_response and generation_response.get("success"):
            result = generation_response.get("generation_result", {})
            return f"""✅ Context-Aware Blueprint Function Generated!

🎯 **Function Details:**
- **Name**: {function_name}
- **Description**: {function_description}
- **Complexity**: {complexity}

📊 **Blueprint Context Used:**
- **Blueprint**: {blueprint_info.get("name", "Unknown")} ({blueprint_info.get("type", "Unknown")})
- **Existing Nodes**: {blueprint_info.get("total_nodes", 0)}
- **Variables**: {len(blueprint_info.get("variables", []))}
- **Functions**: {len(blueprint_info.get("functions", []))}
- **Detection Method**: {blueprint_info.get("detection_method", "unknown")}

🔧 **Generation Result:**
{result.get("message", "Function generated successfully")}

{json.dumps(generation_response, indent=2)}"""
        else:
            error_msg = generation_response.get("error", "Unknown error") if generation_response else "No response from Unreal Engine"
            return f"""❌ Context-Aware Blueprint Function Generation Failed

**Error**: {error_msg}

📊 **Blueprint Context (was available):**
- **Blueprint**: {blueprint_info.get("name", "Unknown")}
- **Total Nodes**: {blueprint_info.get("total_nodes", 0)}
- **Detection Method**: {blueprint_info.get("detection_method", "unknown")}

**Enhanced Request**: {json.dumps(enhanced_request, indent=2)}"""
            
    except Exception as e:
        return f"❌ Failed to generate context-aware Blueprint function: {str(e)}"

def _generate_function_name_from_description(description):
    """Generate a function name from the description."""
    import re
    
    # Extract key words and convert to PascalCase
    words = re.findall(r'\b\w+\b', description.lower())
    
    # Remove common words
    stop_words = {'a', 'an', 'the', 'and', 'or', 'but', 'for', 'to', 'of', 'in', 'on', 'at', 'by', 'with', 'that', 'this', 'is', 'are', 'was', 'were'}
    meaningful_words = [word for word in words if word not in stop_words]
    
    # Take first 3-4 meaningful words and capitalize
    function_words = meaningful_words[:4]
    function_name = ''.join(word.capitalize() for word in function_words)
    
    # Ensure it starts with uppercase and has reasonable length
    if len(function_name) < 3:
        function_name = "GeneratedFunction"
    elif len(function_name) > 50:
        function_name = function_name[:50]
    
    return function_name

# Add a health check endpoint
# Note: Custom routes may not be available in all FastMCP versions
# The health check functionality is available via the server_status tool instead

# @mcp.custom_route("/health", methods=["GET"])
# async def health_check(request):
#     """Health check endpoint for monitoring."""
#     from starlette.responses import JSONResponse
#     
#     try:
#         host = os.environ.get("UNREAL_HOST", "localhost")
#         port = int(os.environ.get("UNREAL_PORT", "9877"))
#         
#         # Test connection to Unreal Engine
#         with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
#             s.settimeout(2)
#             result = s.connect_ex((host, port))
#             
#         unreal_connected = result == 0
#         
#         return JSONResponse({
#             "status": "healthy" if unreal_connected else "degraded",
#             "mcp_server": "running",
#             "unreal_engine": "connected" if unreal_connected else "disconnected",
#             "unreal_host": host,
#             "unreal_port": port,
#             "pid": os.getpid()
#         })
#     except Exception as e:
#         return JSONResponse({
#             "status": "unhealthy",
#             "error": str(e)
#         }, status_code=500)

# ===================================================================
# COMPREHENSIVE BLUEPRINT FUNCTION CATEGORIES - ENHANCED MCP TOOLS
# ===================================================================

@mcp.tool()
def generate_game_mechanics_function(
    function_name: str,
    mechanic_type: str,
    parameters: str = "{}",
    complexity: str = "medium"
) -> str:
    """Generate Blueprint functions for game mechanics (movement, combat, inventory, physics).

    Args:
        function_name: Name of the function to create
        mechanic_type: Type of game mechanic ("movement", "combat", "inventory", "physics", "interaction", "ai_behavior")
        parameters: JSON string with specific parameters for the mechanic type
        complexity: Function complexity level ("simple", "medium", "complex")
    """
    try:
        import json
        params = json.loads(parameters) if parameters else {}

        response = send_to_unreal({
            "type": "generate_game_mechanics_function",
            "function_name": function_name,
            "mechanic_type": mechanic_type,
            "parameters": params,
            "complexity": complexity
        })

        if response and response.get("success"):
            return f"✅ Game Mechanics Function Generated: {response.get('message', 'Success')}"
        else:
            return f"❌ Failed to generate game mechanics function: {response.get('error', 'Unknown error') if response else 'No response'}"

    except json.JSONDecodeError as e:
        return f"❌ Failed to parse parameters JSON: {str(e)}"
    except Exception as e:
        return f"❌ Failed to generate game mechanics function: {str(e)}"

@mcp.tool()
def generate_math_function(
    function_name: str,
    math_type: str,
    parameters: str = "{}",
    complexity: str = "medium"
) -> str:
    """Generate Blueprint functions for mathematical operations (vectors, rotations, calculations).

    Args:
        function_name: Name of the function to create
        math_type: Type of math operation ("vector", "rotation", "interpolation", "trigonometry", "random", "statistics")
        parameters: JSON string with specific parameters for the math type
        complexity: Function complexity level ("simple", "medium", "complex")
    """
    try:
        import json
        params = json.loads(parameters) if parameters else {}

        response = send_to_unreal({
            "type": "generate_math_function",
            "function_name": function_name,
            "math_type": math_type,
            "parameters": params,
            "complexity": complexity
        })

        if response and response.get("success"):
            return f"✅ Math Function Generated: {response.get('message', 'Success')}"
        else:
            return f"❌ Failed to generate math function: {response.get('error', 'Unknown error') if response else 'No response'}"

    except json.JSONDecodeError as e:
        return f"❌ Failed to parse parameters JSON: {str(e)}"
    except Exception as e:
        return f"❌ Failed to generate math function: {str(e)}"

@mcp.tool()
def generate_ui_function(
    function_name: str,
    ui_type: str,
    parameters: str = "{}",
    complexity: str = "medium"
) -> str:
    """Generate Blueprint functions for UI systems (menus, HUD, interactions, data binding).

    Args:
        function_name: Name of the function to create
        ui_type: Type of UI system ("menu", "hud", "widget", "animation", "data_binding", "input_handling")
        parameters: JSON string with specific parameters for the UI type
        complexity: Function complexity level ("simple", "medium", "complex")
    """
    try:
        import json
        params = json.loads(parameters) if parameters else {}

        response = send_to_unreal({
            "type": "generate_ui_function",
            "function_name": function_name,
            "ui_type": ui_type,
            "parameters": params,
            "complexity": complexity
        })

        if response and response.get("success"):
            return f"✅ UI Function Generated: {response.get('message', 'Success')}"
        else:
            return f"❌ Failed to generate UI function: {response.get('error', 'Unknown error') if response else 'No response'}"

    except json.JSONDecodeError as e:
        return f"❌ Failed to parse parameters JSON: {str(e)}"
    except Exception as e:
        return f"❌ Failed to generate UI function: {str(e)}"

@mcp.tool()
def generate_health_system_function(
    function_name: str,
    health_type: str,
    parameters: str = "{}",
    complexity: str = "medium"
) -> str:
    """Generate Blueprint functions for health systems (damage, healing, status effects).

    Args:
        function_name: Name of the function to create
        health_type: Type of health system ("damage", "healing", "status_effects", "regeneration", "shields", "buffs_debuffs")
        parameters: JSON string with specific parameters for the health type
        complexity: Function complexity level ("simple", "medium", "complex")
    """
    try:
        import json
        params = json.loads(parameters) if parameters else {}

        response = send_to_unreal({
            "type": "generate_health_system_function",
            "function_name": function_name,
            "health_type": health_type,
            "parameters": params,
            "complexity": complexity
        })

        if response and response.get("success"):
            return f"✅ Health System Function Generated: {response.get('message', 'Success')}"
        else:
            return f"❌ Failed to generate health system function: {response.get('error', 'Unknown error') if response else 'No response'}"

    except json.JSONDecodeError as e:
        return f"❌ Failed to parse parameters JSON: {str(e)}"
    except Exception as e:
        return f"❌ Failed to generate health system function: {str(e)}"

@mcp.tool()
def generate_utility_function(
    function_name: str,
    utility_type: str,
    parameters: str = "{}",
    complexity: str = "medium"
) -> str:
    """Generate Blueprint functions for utility operations (string manipulation, file I/O, debugging).

    Args:
        function_name: Name of the function to create
        utility_type: Type of utility ("string", "file_io", "debugging", "data_conversion", "validation", "logging")
        parameters: JSON string with specific parameters for the utility type
        complexity: Function complexity level ("simple", "medium", "complex")
    """
    try:
        import json
        params = json.loads(parameters) if parameters else {}

        response = send_to_unreal({
            "type": "generate_utility_function",
            "function_name": function_name,
            "utility_type": utility_type,
            "parameters": params,
            "complexity": complexity
        })

        if response and response.get("success"):
            return f"✅ Utility Function Generated: {response.get('message', 'Success')}"
        else:
            return f"❌ Failed to generate utility function: {response.get('error', 'Unknown error') if response else 'No response'}"

    except json.JSONDecodeError as e:
        return f"❌ Failed to parse parameters JSON: {str(e)}"
    except Exception as e:
        return f"❌ Failed to generate utility function: {str(e)}"

@mcp.tool()
def create_complete_blueprint_graph(
    function_name: str,
    graph_definition: str,
    auto_connect: bool = True,
    compile_after: bool = True
) -> str:
    """Create a complete Blueprint graph with nodes and connections in one operation.

    Args:
        function_name: Name of the function to create
        graph_definition: JSON string defining the complete graph structure
        auto_connect: Whether to automatically connect nodes based on logic flow
        compile_after: Whether to compile the Blueprint after creation
    """
    try:
        import json
        graph_data = json.loads(graph_definition) if graph_definition else {}

        response = send_to_unreal({
            "type": "create_complete_blueprint_graph",
            "function_name": function_name,
            "graph_definition": graph_data,
            "auto_connect": auto_connect,
            "compile_after": compile_after
        })

        if response and response.get("success"):
            return f"✅ Complete Blueprint Graph Created: {response.get('message', 'Success')}"
        else:
            return f"❌ Failed to create complete graph: {response.get('error', 'Unknown error') if response else 'No response'}"

    except json.JSONDecodeError as e:
        return f"❌ Failed to parse graph definition JSON: {str(e)}"
    except Exception as e:
        return f"❌ Failed to create complete graph: {str(e)}"

@mcp.tool()
def enhance_node_connections(
    blueprint_path: str,
    function_name: str,
    connection_strategy: str = "intelligent",
    validate_types: bool = True
) -> str:
    """Enhance node connections in a Blueprint function with intelligent pin matching.

    Args:
        blueprint_path: Path to the Blueprint
        function_name: Name of the function to enhance
        connection_strategy: Strategy for connections ("intelligent", "sequential", "parallel", "custom")
        validate_types: Whether to validate pin type compatibility
    """
    try:
        response = send_to_unreal({
            "type": "enhance_node_connections",
            "blueprint_path": blueprint_path,
            "function_name": function_name,
            "connection_strategy": connection_strategy,
            "validate_types": validate_types
        })

        if response and response.get("success"):
            return f"✅ Node Connections Enhanced: {response.get('message', 'Success')}"
        else:
            return f"❌ Failed to enhance connections: {response.get('error', 'Unknown error') if response else 'No response'}"

    except Exception as e:
        return f"❌ Failed to enhance connections: {str(e)}"

@mcp.tool()
def optimize_blueprint_layout(
    blueprint_path: str,
    function_name: str,
    layout_style: str = "hierarchical",
    spacing: int = 200
) -> str:
    """Optimize the layout of nodes in a Blueprint function for better readability.

    Args:
        blueprint_path: Path to the Blueprint
        function_name: Name of the function to optimize
        layout_style: Layout style ("hierarchical", "grid", "flow", "compact")
        spacing: Spacing between nodes in pixels
    """
    try:
        response = send_to_unreal({
            "type": "optimize_blueprint_layout",
            "blueprint_path": blueprint_path,
            "function_name": function_name,
            "layout_style": layout_style,
            "spacing": spacing
        })

        if response and response.get("success"):
            return f"✅ Blueprint Layout Optimized: {response.get('message', 'Success')}"
        else:
            return f"❌ Failed to optimize layout: {response.get('error', 'Unknown error') if response else 'No response'}"

    except Exception as e:
        return f"❌ Failed to optimize layout: {str(e)}"

@mcp.tool()
def validate_blueprint_function(
    blueprint_path: str,
    function_name: str,
    check_compilation: bool = True,
    check_connections: bool = True,
    check_logic_flow: bool = True
) -> str:
    """Validate a Blueprint function for errors, warnings, and logic issues.

    Args:
        blueprint_path: Path to the Blueprint
        function_name: Name of the function to validate
        check_compilation: Whether to check compilation errors
        check_connections: Whether to check connection validity
        check_logic_flow: Whether to check logical flow issues
    """
    try:
        response = send_to_unreal({
            "type": "validate_blueprint_function",
            "blueprint_path": blueprint_path,
            "function_name": function_name,
            "check_compilation": check_compilation,
            "check_connections": check_connections,
            "check_logic_flow": check_logic_flow
        })

        if response and response.get("success"):
            return f"✅ Blueprint Function Validated: {response.get('message', 'Success')}"
        else:
            return f"❌ Validation failed: {response.get('error', 'Unknown error') if response else 'No response'}"

    except Exception as e:
        return f"❌ Failed to validate function: {str(e)}"

# Advanced Blueprint Analysis Functions (Protected Logic in Bridge)

def _analyze_function_description(description, inputs, outputs, complexity):
    """Analyze function description and generate intelligent node plan."""
    node_plan = {
        "estimated_nodes": [],
        "connections": [],
        "layout_hints": {},
        "complexity_level": complexity
    }
    
    desc_lower = description.lower()
    
    # Mathematical operations
    if any(word in desc_lower for word in ["add", "sum", "plus", "+"]):
        node_plan["estimated_nodes"].append({
            "type": "K2Node_CallFunction",
            "function": "Add_FloatFloat" if "float" in str(inputs) else "Add_IntInt",
            "category": "Math",
            "priority": 1
        })
    
    if any(word in desc_lower for word in ["multiply", "times", "*", "product"]):
        node_plan["estimated_nodes"].append({
            "type": "K2Node_CallFunction", 
            "function": "Multiply_FloatFloat" if "float" in str(inputs) else "Multiply_IntInt",
            "category": "Math",
            "priority": 1
        })
    
    # Conditional logic
    if any(word in desc_lower for word in ["if", "condition", "check", "branch", "when"]):
        node_plan["estimated_nodes"].append({
            "type": "K2Node_IfThenElse",
            "category": "Flow Control",
            "priority": 2
        })
    
    # Loops
    if any(word in desc_lower for word in ["loop", "for each", "iterate", "repeat"]):
        node_plan["estimated_nodes"].append({
            "type": "K2Node_ForEachLoop",
            "category": "Flow Control", 
            "priority": 2
        })
    
    # Variable operations
    if any(word in desc_lower for word in ["get", "read", "retrieve", "access"]):
        node_plan["estimated_nodes"].append({
            "type": "K2Node_VariableGet",
            "category": "Variable",
            "priority": 3
        })
    
    if any(word in desc_lower for word in ["set", "assign", "store", "save"]):
        node_plan["estimated_nodes"].append({
            "type": "K2Node_VariableSet", 
            "category": "Variable",
            "priority": 3
        })
    
    # Function calls
    if any(word in desc_lower for word in ["call", "execute", "run", "invoke"]):
        node_plan["estimated_nodes"].append({
            "type": "K2Node_CallFunction",
            "category": "Function Call",
            "priority": 1
        })
    
    # Sort nodes by priority
    node_plan["estimated_nodes"].sort(key=lambda x: x.get("priority", 5))
    
    # Generate layout hints
    node_plan["layout_hints"] = {
        "flow_direction": "left_to_right",
        "spacing": {"x": 200, "y": 100},
        "group_by_category": True
    }
    
    return node_plan

def _process_blueprint_context(context_data):
    """Process Blueprint context with advanced analysis."""
    try:
        blueprint_info = context_data.get("blueprint_info", {})
        graphs = context_data.get("graphs", [])
        
        # Analyze Blueprint structure
        structure_analysis = {
            "blueprint_type": blueprint_info.get("type", "Unknown"),
            "complexity_score": _calculate_blueprint_complexity(graphs),
            "graph_count": len(graphs),
            "function_count": len([g for g in graphs if g.get("type") == "Function"]),
            "event_count": len([g for g in graphs if g.get("type") == "EventGraph"]),
            "recommendations": _generate_blueprint_recommendations(blueprint_info, graphs)
        }
        
        return json.dumps({
            "success": True,
            "blueprint_info": blueprint_info,
            "structure_analysis": structure_analysis,
            "available_graphs": graphs
        }, indent=2)
        
    except Exception as e:
        return f"Error processing Blueprint context: {str(e)}"

def _calculate_blueprint_complexity(graphs):
    """Calculate Blueprint complexity score."""
    score = 0
    for graph in graphs:
        node_count = graph.get("node_count", 0)
        connection_count = graph.get("connection_count", 0)
        
        # Base complexity from node count
        score += node_count * 1
        
        # Additional complexity from connections
        score += connection_count * 0.5
        
        # Bonus for complex node types
        if graph.get("has_branches", False):
            score += 10
        if graph.get("has_loops", False):
            score += 15
        if graph.get("has_custom_events", False):
            score += 5
    
    return min(score, 100)  # Cap at 100

def _generate_blueprint_recommendations(blueprint_info, graphs):
    """Generate intelligent recommendations for Blueprint improvement."""
    recommendations = []
    
    # Check for common issues
    total_nodes = sum(g.get("node_count", 0) for g in graphs)
    
    if total_nodes > 50:
        recommendations.append({
            "type": "performance",
            "message": "Consider breaking down large graphs into smaller functions for better maintainability",
            "priority": "medium"
        })
    
    function_graphs = [g for g in graphs if g.get("type") == "Function"]
    if len(function_graphs) == 0:
        recommendations.append({
            "type": "organization", 
            "message": "Consider creating custom functions to organize your Blueprint logic",
            "priority": "low"
        })
    
    # Check for missing documentation
    undocumented_functions = [g for g in function_graphs if not g.get("has_documentation", False)]
    if len(undocumented_functions) > 0:
        recommendations.append({
            "type": "documentation",
            "message": f"{len(undocumented_functions)} functions could benefit from documentation",
            "priority": "low"
        })
    
    return recommendations

def _perform_advanced_graph_analysis(graph_data, analysis_type, include_ai_insights):
    """Perform comprehensive graph analysis."""
    import time
    analysis = {
        "graph_name": graph_data.get("name", "Unknown"),
        "analysis_type": analysis_type,
        "timestamp": time.time()
    }
    
    nodes = graph_data.get("nodes", [])
    connections = graph_data.get("connections", [])
    
    if analysis_type in ["basic", "comprehensive"]:
        analysis["node_analysis"] = _analyze_nodes(nodes)
        analysis["connection_analysis"] = _analyze_connections(connections)
    
    if analysis_type in ["comprehensive", "logic_flow"]:
        analysis["execution_flow"] = _analyze_execution_flow(nodes, connections)
        analysis["data_flow"] = _analyze_data_flow(nodes, connections)
    
    if analysis_type in ["comprehensive", "performance"]:
        analysis["performance_metrics"] = _analyze_performance(nodes, connections)
    
    if include_ai_insights:
        analysis["ai_insights"] = _generate_ai_insights(graph_data, analysis)
    
    return analysis

def _analyze_nodes(nodes):
    """Analyze node distribution and types."""
    node_types = {}
    for node in nodes:
        node_type = node.get("type", "Unknown")
        node_types[node_type] = node_types.get(node_type, 0) + 1
    
    return {
        "total_nodes": len(nodes),
        "node_types": node_types,
        "most_common": max(node_types.items(), key=lambda x: x[1]) if node_types else ("None", 0)
    }

def _analyze_connections(connections):
    """Analyze connection patterns."""
    exec_connections = [c for c in connections if c.get("type") == "execution"]
    data_connections = [c for c in connections if c.get("type") == "data"]
    
    return {
        "total_connections": len(connections),
        "execution_connections": len(exec_connections),
        "data_connections": len(data_connections),
        "average_connections_per_node": len(connections) / max(len(set(c.get("from_node") for c in connections)), 1)
    }

def _analyze_execution_flow(nodes, connections):
    """Analyze execution flow patterns."""
    exec_connections = [c for c in connections if c.get("type") == "execution"]
    
    # Find entry points (nodes with no incoming execution)
    incoming_exec = set(c.get("to_node") for c in exec_connections)
    all_nodes = set(range(len(nodes)))
    entry_points = all_nodes - incoming_exec
    
    return {
        "entry_points": len(entry_points),
        "execution_paths": len(exec_connections),
        "has_branches": any(node.get("type") == "Branch" for node in nodes),
        "has_loops": any(node.get("type") in ["ForEachLoop", "WhileLoop"] for node in nodes)
    }

def _analyze_data_flow(nodes, connections):
    """Analyze data flow patterns."""
    data_connections = [c for c in connections if c.get("type") == "data"]
    
    return {
        "data_dependencies": len(data_connections),
        "variable_reads": len([n for n in nodes if n.get("type") == "GetVariable"]),
        "variable_writes": len([n for n in nodes if n.get("type") == "SetVariable"])
    }

def _analyze_performance(nodes, connections):
    """Analyze potential performance issues."""
    performance_score = 100
    issues = []
    
    # Check for expensive operations
    expensive_nodes = [n for n in nodes if n.get("type") in ["ForEachLoop", "WhileLoop"]]
    if len(expensive_nodes) > 3:
        performance_score -= 20
        issues.append("Multiple loops detected - consider optimization")
    
    # Check for excessive branching
    branch_nodes = [n for n in nodes if n.get("type") == "Branch"]
    if len(branch_nodes) > 5:
        performance_score -= 15
        issues.append("High branching complexity")
    
    return {
        "performance_score": max(performance_score, 0),
        "potential_issues": issues,
        "optimization_suggestions": _generate_optimization_suggestions(nodes, connections)
    }

def _generate_optimization_suggestions(nodes, connections):
    """Generate performance optimization suggestions."""
    suggestions = []
    
    # Check for redundant variable access
    var_gets = [n for n in nodes if n.get("type") == "GetVariable"]
    var_names = [n.get("variable_name") for n in var_gets]
    duplicates = set([name for name in var_names if var_names.count(name) > 1])
    
    if duplicates:
        suggestions.append("Consider caching frequently accessed variables")
    
    # Check for long execution chains
    exec_connections = [c for c in connections if c.get("type") == "execution"]
    if len(exec_connections) > 10:
        suggestions.append("Consider breaking long execution chains into functions")
    
    return suggestions

def _generate_ai_insights(graph_data, analysis):
    """Generate AI-powered insights about the Blueprint."""
    insights = []
    
    # Pattern recognition
    node_count = analysis.get("node_analysis", {}).get("total_nodes", 0)
    
    if node_count > 20:
        insights.append({
            "type": "complexity",
            "message": "This graph shows high complexity. Consider refactoring into smaller, focused functions.",
            "confidence": 0.8
        })
    
    # Logic pattern insights
    if analysis.get("execution_flow", {}).get("has_branches", False):
        insights.append({
            "type": "logic_pattern",
            "message": "Detected conditional logic patterns. Ensure all code paths are tested.",
            "confidence": 0.9
        })
    
    return insights

def _format_function_generation_result(response):
    """Format the function generation result for user display."""
    result = response.get("result", {})
    
    return f"""✅ Blueprint Function Generated Successfully!

Function: {result.get('function_name', 'Unknown')}
Blueprint: {result.get('blueprint_path', 'Unknown')}
Nodes Created: {result.get('nodes_created', 0)}
Connections Made: {result.get('connections_made', 0)}

Summary: {result.get('summary', 'Function created with basic implementation')}

{json.dumps(result, indent=2)}"""

def _format_advanced_analysis_result(analysis):
    """Format advanced analysis results for user display."""
    return f"""🔍 Advanced Blueprint Analysis Results

Graph: {analysis.get('graph_name', 'Unknown')}
Analysis Type: {analysis.get('analysis_type', 'Unknown')}

📊 Node Analysis:
- Total Nodes: {analysis.get('node_analysis', {}).get('total_nodes', 0)}
- Node Types: {len(analysis.get('node_analysis', {}).get('node_types', {}))}

🔗 Connection Analysis:
- Total Connections: {analysis.get('connection_analysis', {}).get('total_connections', 0)}
- Execution Flow: {analysis.get('connection_analysis', {}).get('execution_connections', 0)}
- Data Flow: {analysis.get('connection_analysis', {}).get('data_connections', 0)}

⚡ Performance Score: {analysis.get('performance_metrics', {}).get('performance_score', 'N/A')}

🤖 AI Insights: {len(analysis.get('ai_insights', []))} insights generated

{json.dumps(analysis, indent=2)}"""

def start_server(subscription_data=None):
    """Entry point expected by subscription manager to start the MCP service.
    The optional subscription_data dict can be used for telemetry or rate-limits in future."""
    try:
        print("[mcp_server] Starting via start_server entrypoint", file=sys.stderr)
        if subscription_data:
            import json
            print(f"[mcp_server] Subscription data: {json.dumps(subscription_data)[:200]}…", file=sys.stderr)

        # Write PID file for Unreal plugin tracking
        pid_path = write_pid_file()
        print(f"MCP Server started with PID file at: {pid_path}", file=sys.stderr)

        # Start FastMCP WebSocket server for bridge communication
        print("[mcp_server] Starting FastMCP WebSocket server on port 8080...", file=sys.stderr)
        mcp.run(host="localhost", port=8080)

    except Exception as e:
        print(f"[mcp_server] ERROR: failed to start FastMCP - {e}", file=sys.stderr)
        sys.exit(1)

# -----------------------------------------------------------------------------
# If you had any "main loop" or long-running logic, it would go here.
# But since FastMCP is event-driven, you don't need a while-loop. Just let
# FastMCP handle incoming HTTP (or WebSocket) requests:
# -----------------------------------------------------------------------------

if __name__ == "__main__":
    start_server()
