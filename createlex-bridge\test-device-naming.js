#!/usr/bin/env node

/**
 * Test script to verify improved device naming functionality
 */

const { AuthHandler } = require('./src/auth/auth-handler');

console.log('🧪 Testing Improved Device Name Detection\n');

// Create AuthHandler instance to test device info
const authHandler = new AuthHandler();
const deviceInfo = authHandler.getDeviceInfo();

console.log('📱 Device Information:');
console.log('  Device ID:', deviceInfo.deviceId.substring(0, 16) + '...');
console.log('  Device Name:', deviceInfo.deviceName);
console.log('  Platform:', deviceInfo.platform);
console.log('  OS Version:', deviceInfo.osVersion);

// Test the individual helper functions
const os = require('os');
const platform = os.platform();
const hostname = os.hostname();

console.log('\n🔍 Platform Detection Details:');
console.log('  Raw platform:', platform);
console.log('  Raw hostname:', hostname);
console.log('  Raw architecture:', os.arch());

// Test Windows-specific naming if on Windows
if (platform === 'win32') {
  console.log('\n🪟 Windows-Specific Testing:');
  const windowsName = authHandler.getWindowsDeviceName(hostname);
  console.log('  Generated Windows name:', windowsName);
  
  // Test various hostname scenarios
  const testHostnames = ['MacBook-Air', 'Desktop-PC', 'JOHN-LAPTOP', 'WIN-ABCD1234'];
  console.log('\n  Testing various hostname scenarios:');
  testHostnames.forEach(testHost => {
    const generatedName = authHandler.getWindowsDeviceName(testHost);
    console.log(`    ${testHost} -> ${generatedName}`);
  });
}

// Test macOS-specific naming if on macOS
if (platform === 'darwin') {
  console.log('\n🍎 macOS-Specific Testing:');
  const macName = authHandler.getMacDeviceName(hostname);
  console.log('  Generated Mac name:', macName);
  
  // Test various hostname scenarios
  const testHostnames = ['MacBook-Pro', 'iMac', 'Mac-Studio', 'Johns-MacBook-Air'];
  console.log('\n  Testing various hostname scenarios:');
  testHostnames.forEach(testHost => {
    const generatedName = authHandler.getMacDeviceName(testHost);
    console.log(`    ${testHost} -> ${generatedName}`);
  });
}

// Test Linux-specific naming if on Linux
if (platform === 'linux') {
  console.log('\n🐧 Linux-Specific Testing:');
  const linuxName = authHandler.getLinuxDeviceName(hostname);
  console.log('  Generated Linux name:', linuxName);
}

console.log('\n✅ Device naming test completed!');
console.log('\n📋 Summary:');
console.log('  • Windows devices will show as "Windows PC" or CPU-specific names');
console.log('  • macOS devices will show as "MacBook", "iMac", or "Mac" variants');
console.log('  • Linux devices will show with CPU info and memory details');
console.log('  • Platform names now use friendly names (Windows, macOS, Linux)');
console.log('  • Hostname misconfigurations are automatically corrected');

process.exit(0); 