'use client';

import { useState, useEffect } from 'react';

export default function ApiTestPage() {
  const [backendStatus, setBackendStatus] = useState<'loading' | 'online' | 'offline'>('loading');
  const [error, setError] = useState<string | null>(null);
  const [response, setResponse] = useState<any>(null);
  const [apiUrl, setApiUrl] = useState<string>('');

  useEffect(() => {
    // Get the API URL from environment variables
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'https://api.createlex.com';
    setApiUrl(backendUrl);
    
    // Test the backend connectivity
    const testBackend = async () => {
      try {
        // Try to fetch a simple endpoint
        const response = await fetch(`${backendUrl}/api/health`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        
        if (response.ok) {
          const data = await response.json();
          setBackendStatus('online');
          setResponse(data);
        } else {
          setBackendStatus('offline');
          setError(`Backend returned status: ${response.status}`);
        }
      } catch (error: any) {
        setBackendStatus('offline');
        setError(error.message || 'Unknown error occurred');
      }
    };
    
    testBackend();
  }, []);
  
  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-6">Backend API Test</h1>
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">API URL</h2>
        <div className="p-4 bg-gray-100 rounded">
          {apiUrl}
        </div>
      </div>
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Backend Status</h2>
        <div className={`p-4 rounded ${
          backendStatus === 'online' 
            ? 'bg-green-100 text-green-800' 
            : backendStatus === 'offline' 
              ? 'bg-red-100 text-red-800' 
              : 'bg-yellow-100 text-yellow-800'
        }`}>
          {backendStatus === 'loading' && 'Checking backend status...'}
          {backendStatus === 'online' && 'Backend is online! ✅'}
          {backendStatus === 'offline' && 'Backend is offline! ❌'}
        </div>
      </div>
      
      {error && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Error</h2>
          <div className="p-4 bg-red-100 text-red-800 rounded">
            {error}
          </div>
        </div>
      )}
      
      {response && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Response</h2>
          <pre className="p-4 bg-gray-100 rounded overflow-auto">
            {JSON.stringify(response, null, 2)}
          </pre>
        </div>
      )}
      
      <div className="mt-8">
        <h2 className="text-xl font-semibold mb-4">Additional Tests</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <a 
            href="/api-test/auth" 
            className="p-4 bg-blue-500 text-white rounded hover:bg-blue-600 text-center"
          >
            Test Authentication
          </a>
          <a 
            href="/api-test/subscription" 
            className="p-4 bg-purple-500 text-white rounded hover:bg-purple-600 text-center"
          >
            Test Subscription
          </a>
          <a 
            href="/api-test/tokens" 
            className="p-4 bg-green-500 text-white rounded hover:bg-green-600 text-center"
          >
            Test Token Balance
          </a>
          <a 
            href="/api-test/stripe" 
            className="p-4 bg-yellow-500 text-white rounded hover:bg-yellow-600 text-center"
          >
            Test Stripe Integration
          </a>
        </div>
      </div>
    </div>
  );
}
