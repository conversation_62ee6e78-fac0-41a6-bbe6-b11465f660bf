#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

// Detect if we're in development mode
const isDev = !process.env.NODE_ENV || process.env.NODE_ENV === 'development' || process.argv.includes('--dev');

// Set default environment variables for development
if (isDev) {
  // Only set if not already defined
  process.env.NODE_ENV = process.env.NODE_ENV || 'development';
  process.env.CREATELEX_BASE_URL = process.env.CREATELEX_BASE_URL || 'http://localhost:3000';
  process.env.API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:5001/api';
  process.env.DEV_MODE = process.env.DEV_MODE || 'true';

  console.log('🚀 CreateLex Bridge - Development Mode');
  console.log('📍 Base URL:', process.env.CREATELEX_BASE_URL);
  console.log('🔗 API URL:', process.env.API_BASE_URL);
  console.log('');
} else {
  console.log('🚀 CreateLex Bridge - Production Mode');
  console.log('📍 Base URL: https://createlex.com');
  console.log('');
}

// Start Electron
const electronPath = require('electron');
const appPath = path.join(__dirname, '.');

const electronProcess = spawn(electronPath, [appPath], {
  stdio: 'inherit',
  env: process.env
});

electronProcess.on('close', (code) => {
  console.log(`\n✅ CreateLex Bridge exited with code ${code}`);
  process.exit(code);
});

electronProcess.on('error', (err) => {
  console.error('❌ Failed to start CreateLex Bridge:', err);
  process.exit(1);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Stopping CreateLex Bridge...');
  electronProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Terminating CreateLex Bridge...');
  electronProcess.kill('SIGTERM');
}); 