# OAuth-like Authentication Flow for CreateLex Bridge

## Overview

The CreateLex Bridge now supports a modern, hybrid OAuth-like authentication flow that combines the security of web-based login with the performance of local frontend assets. This provides the best of both worlds: reliable authentication through createlex.com and fast, offline-capable local interface.

## Hybrid Authentication Architecture

### **🔄 Improved Flow Sequence**

```mermaid
sequenceDiagram
    participant User
    participant Bridge as Bridge App
    participant Browser
    participant Web as CreateLex Web
    participant Local as Local Callback
    participant Dashboard as Local Dashboard
    
    User->>Bridge: Click "Sign in with CreateLex"
    Bridge->>Bridge: Start callback server (port 7891)
    Bridge->>Browser: Open createlex.com/login
    Browser->>Web: Load secure login page
    User->>Web: Complete login (Google OAuth, etc.)
    Web->>Local: Redirect to local auth-callback
    Local->>Bridge: Send auth data to callback server
    Bridge->>Bridge: Process auth & stop server
    Bridge->>Dashboard: Open local dashboard
    User->>Dashboard: Use offline-capable interface
```

### **🎯 Why This Approach is Better**

| Aspect | Previous (All Web) | Current (Hybrid) |
|--------|-------------------|------------------|
| **Initial Login** | createlex.com ✅ | createlex.com ✅ |
| **After Auth** | Stay on web | Local interface ✅ |
| **Speed** | Network dependent | Fast local files ✅ |
| **Offline** | Requires internet | Works offline ✅ |
| **Security** | Web security ✅ | Web security ✅ |
| **Features** | All web features | Bridge-optimized ✅ |
| **Integration** | External | Native Electron ✅ |

## Key Components

### **1. OAuth Flow Handler** (`src/auth/oauth-flow.js`)
- Opens browser to createlex.com for secure login
- Redirects to local `auth-callback` page after login
- Receives auth data from local callback via HTTP POST
- Manages callback server lifecycle

### **2. Local Auth Callback** (`web/auth-callback/index.html`)
- Receives auth data from createlex.com redirect
- Processes and displays authentication status
- Sends data to bridge callback server
- Auto-closes with user feedback

### **3. Local Dashboard** (`web/dashboard/index.html`)
- Fast-loading local interface
- Bridge-specific MCP controls
- Offline-capable after authentication
- Direct Electron API integration

### **4. Frontend Integration** (`frontend/app/login/page.tsx`)
- Detects bridge authentication requests
- Redirects to local callback instead of bridge server
- Handles both file:// and http:// protocols

## Authentication Flow Details

### **Step 1: Initiate Login**
```javascript
// User clicks OAuth button
const result = await window.electronAPI.authenticateOAuth();
```

### **Step 2: Web Authentication**
- Bridge opens: `https://createlex.com/login?redirect=file:///.../auth-callback/index.html&source=bridge`
- User completes login on createlex.com (Google OAuth, email/password, etc.)
- Full web security and all authentication providers available

### **Step 3: Local Callback Processing**
- createlex.com redirects to local `auth-callback/index.html` with parameters:
  ```
  ?token=JWT_TOKEN&userId=USER_ID&email=USER_EMAIL&hasSubscription=true
  ```
- Local page processes the data and sends to bridge:
  ```javascript
  fetch('http://localhost:7891/auth/success', {
    method: 'POST',
    body: JSON.stringify({ token, userId, email, hasSubscription })
  })
  ```

### **Step 4: Bridge Integration**
- Bridge receives auth data and stores token
- Opens local dashboard with full offline capability
- MCP server can start with validated authentication

## Benefits of Hybrid Approach

### **🚀 Performance Benefits**
- **Fast Local Loading**: Dashboard loads instantly from local files
- **No Network Delays**: Interface responds immediately after auth
- **Cached Assets**: All UI resources stored locally

### **🔒 Security Benefits**
- **Web-Grade Auth**: Full createlex.com security for login
- **Token Security**: Secure token handling in Electron
- **Local Isolation**: Dashboard runs in isolated Electron context

### **📱 User Experience Benefits**
- **Familiar Login**: Standard web login flow users expect
- **Native Feel**: Local dashboard feels like native app
- **Offline Capable**: Works without internet after authentication
- **Bridge Optimized**: UI designed specifically for bridge functionality

### **🛠️ Developer Benefits**
- **Easy Maintenance**: Local files easy to update and customize
- **Bridge Integration**: Direct access to Electron APIs
- **Customizable**: Can modify local interface without web deployment
- **Testing**: Local files easier to test and debug

## File Structure

```
createlex-bridge/
├── web/                          # Local frontend assets
│   ├── login/
│   │   └── index.html           # Local login (fallback)
│   ├── auth-callback/
│   │   └── index.html           # OAuth callback processor ⭐
│   └── dashboard/
│       └── index.html           # Main bridge interface ⭐
├── src/auth/
│   ├── oauth-flow.js            # OAuth coordination
│   └── auth-handler.js          # Auth management
└── main.js                      # Electron main process
```

## Configuration

### **Environment Variables**
```bash
CREATELEX_BASE_URL=https://createlex.com
NODE_ENV=production              # Uses local files
NODE_ENV=development             # Uses localhost:3000
```

### **OAuth URLs**
- **Production**: `file:///.../web/auth-callback/index.html`
- **Development**: `http://localhost:3000/auth-callback`

## Build Process

### **Simple Build** (Recommended)
```bash
npm run build-win-simple
```

Creates local HTML files:
- ✅ OAuth authentication flow
- ✅ Local auth callback processor  
- ✅ Offline-capable dashboard
- ✅ Bridge-optimized interface

### **Full Build** (Complete Features)
```bash
npm run build-win
```

Uses exported Next.js frontend with all web features.

## Usage Examples

### **Starting OAuth Authentication**
```javascript
const result = await window.electronAPI.authenticateOAuth();
if (result.success) {
  // Local dashboard opens automatically
  console.log('Authenticated:', result.userData);
}
```

### **Dashboard Integration**
```javascript
// MCP controls work directly with Electron
await window.electronAPI.startMcp();
await window.electronAPI.checkSubscription();
```

## Troubleshooting

### **Common Issues**

1. **Auth callback not loading**
   - Check `web/auth-callback/index.html` exists
   - Verify file permissions
   - Check Electron file loading security

2. **Dashboard not opening**
   - Check `web/dashboard/index.html` exists
   - Verify main.js loadFile() paths
   - Check console for file loading errors

3. **Callback server not receiving data**
   - Verify port 7891 is available
   - Check local callback page JavaScript
   - Verify fetch request to localhost:7891

### **Debug Information**

The bridge logs detailed information:
```
OAuth callback server started on http://localhost:7891
Opening browser for authentication: https://createlex.com/login?redirect=...
Local callback will be at: file:///.../auth-callback/index.html
Received authentication from local callback: { userId: "...", email: "..." }
Loading dashboard page from: /.../web/dashboard/index.html
```

## Migration Notes

### **From Previous Version**
- All existing authentication methods continue to work
- OAuth flow now uses local callback for better UX
- Dashboard is now local for better performance
- No breaking changes to API

### **Backward Compatibility**
- Legacy credential authentication still available
- Fallback to web version if local files fail
- Direct callback still supported for compatibility

## Future Enhancements

- **SSO Integration**: Enterprise single sign-on support
- **Biometric Auth**: Fingerprint/face recognition on supported devices
- **Multi-Account**: Support for multiple CreateLex accounts
- **Offline Sync**: Sync settings when connection restored

This hybrid approach provides the optimal balance of security, performance, and user experience while maintaining full compatibility with existing systems. 