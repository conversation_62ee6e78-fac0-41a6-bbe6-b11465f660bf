'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import GoogleLoginButton from '../../components/GoogleLoginButton';
import GitHubLoginButton from '../../components/GitHubLoginButton';
import { useAuth } from '../../contexts/AuthContext';
import Image from 'next/image';
import Link from 'next/link';
import withSearchParamsProvider from '@/components/utils/withSearchParamsProvider';

function LoginPage() {
  const { isAuthenticated, isLoading, user, token } = useAuth();
  const router = useRouter();
  const [error, setError] = React.useState<string | null>(null);

  // Bridge authentication mode state
  const [bridgeAuthMode, setBridgeAuthMode] = useState(false);
  const [redirectUrl, setRedirectUrl] = useState<string | null>(null);
  const [processingBridgeAuth, setProcessing<PERSON>ridgeAuth] = useState(false);

  useEffect(() => {
    // Check URL parameters for bridge authentication FIRST
    const urlParams = new URLSearchParams(window.location.search);
    const redirect = urlParams.get('redirect');
    const source = urlParams.get('source');
    
    // If this is a bridge authentication request, handle it specially
    if (redirect && source === 'bridge') {
      console.log('Bridge authentication mode detected');
      setBridgeAuthMode(true);
      setRedirectUrl(redirect);
      
      // If user is already authenticated, trigger bridge auth immediately
      if (isAuthenticated && !isLoading && user) {
        console.log('User already authenticated, triggering bridge auth immediately');
        // Don't redirect to dashboard, handle bridge auth instead
        return;
      }
    }
    
    // Redirect to dashboard if already authenticated (but not in bridge mode)
    if (isAuthenticated && !isLoading && !bridgeAuthMode) {
      router.push('/dashboard');
    }

    // Check for error in URL query parameters
    const searchParams = new URLSearchParams(window.location.search);
    const errorParam = searchParams.get('error');

    if (errorParam === 'user_deleted') {
      setError('Your account has been deleted. Please sign up again if you wish to use our services.');
    } else if (errorParam === 'user_not_found') {
      setError('Your account was not found. Please sign up if you are a new user.');
    }

    console.log('Login page URL params:', { redirect, source });
  }, [isAuthenticated, isLoading, router]);

  const handleLoginSuccess = () => {
    // OAuth success is now handled by the useEffect that watches for authentication changes
    // This callback is kept for compatibility but the actual bridge auth logic is in useEffect
    console.log('Login button success callback - OAuth flow will be handled by useEffect');
  };

  const handleLoginError = (error: Error) => {
    console.error('Login error:', error);
    setError('Authentication failed. Please try again.');
  };

  // Handle bridge authentication callback
  const handleBridgeAuthSuccess = useCallback(async (user: any, session: any) => {
    if (!bridgeAuthMode || !redirectUrl) return;

    setProcessingBridgeAuth(true);

    try {
      // Get the token from the session or from Supabase directly
      let authToken = token || session?.access_token;
      
      // If we still don't have a token, try to get it from Supabase
      if (!authToken) {
        const { getSupabaseClient } = await import('../../lib/supabase-singleton');
        const supabase = getSupabaseClient();
        const { data: { session: currentSession } } = await supabase.auth.getSession();
        authToken = currentSession?.access_token;
      }

      if (!authToken) {
        console.error('No authentication token available');
        throw new Error('Authentication token not found');
      }

      // Get subscription status
      const subscriptionResponse = await fetch('/api/subscription/check', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      let hasActiveSubscription = false;
      if (subscriptionResponse.ok) {
        const subscriptionData = await subscriptionResponse.json();
        hasActiveSubscription = subscriptionData.hasActiveSubscription || false;
      }

      // For bridge authentication, redirect to our frontend bridge-callback page
      // This eliminates cross-origin issues since everything is on the same domain
      console.log('🔗 Redirecting to frontend bridge callback page');
      window.location.href = '/bridge-callback';
      
    } catch (error) {
      console.error('Error handling bridge authentication:', error);
      
      // Still redirect to bridge callback even with error
      // The bridge callback page will handle the error state
      console.log('⚠️ Redirecting to bridge callback with error state');
      window.location.href = '/bridge-callback';
    } finally {
      setProcessingBridgeAuth(false);
    }
  }, [bridgeAuthMode, redirectUrl, token]);

  // Watch for authentication changes to handle bridge callback (for both existing and fresh logins)
  useEffect(() => {
    const checkAndHandleBridgeAuth = async () => {
      console.log('Bridge auth check:', { bridgeAuthMode, hasUser: !!user, hasToken: !!token, isAuthenticated, isLoading });
      
      // Handle bridge auth for any authenticated user (existing or fresh login)
      if (bridgeAuthMode && user && isAuthenticated && !isLoading && !processingBridgeAuth) {
        console.log('User authenticated in bridge mode, triggering bridge auth callback');
        
        // Get the current session from Supabase
        const { getSupabaseClient } = await import('../../lib/supabase-singleton');
        const supabase = getSupabaseClient();
        const { data: { session } } = await supabase.auth.getSession();
        
        if (session) {
          handleBridgeAuthSuccess(user, session);
        } else {
          console.error('No session available for bridge authentication');
        }
      }
    };

    // Run this for any authenticated user when not loading
    if (!isLoading) {
      checkAndHandleBridgeAuth();
    }
  }, [user, token, bridgeAuthMode, isAuthenticated, isLoading, processingBridgeAuth, handleBridgeAuthSuccess]);

  if (isLoading || processingBridgeAuth) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-[#f7f7f7]">
        <div className="text-center">
          <div className="spinner mb-4"></div>
          <p>{processingBridgeAuth ? 'Authenticating with CreateLex Bridge...' : 'Loading...'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-[#f7f7f7]">
      {/* Header */}
      <header className="py-6 px-8 border-b border-gray-200 bg-white">
        <div className="max-w-screen-xl mx-auto flex justify-between items-center">
          <Link href="/" className="flex items-center space-x-2">
            <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full"></div>
            <span className="text-xl font-bold">CreateLex</span>
          </Link>
          <Link
            href="/"
            className="flex items-center text-sm text-gray-600 hover:text-blue-600 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              />
            </svg>
            Back to Home
          </Link>
        </div>
      </header>

      {/* Main content */}
      <main className="flex-1 flex justify-center items-center py-12 px-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 w-full max-w-md overflow-hidden">
          {/* Login form */}
          <div className="p-8">
            <div className="flex justify-center mb-6">
              <div className="h-10 w-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full"></div>
            </div>

            <h1 className="text-2xl font-semibold text-center mb-6">Log in to CreateLex</h1>

            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4 text-sm">
                {error}
              </div>
            )}

            <div className="space-y-4">
              <div className="flex flex-col items-center space-y-3">
                <GoogleLoginButton onSuccess={handleLoginSuccess} onError={handleLoginError} />
                <GitHubLoginButton onSuccess={handleLoginSuccess} onError={handleLoginError} />
              </div>

              <div className="relative my-6">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-200"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">OR</span>
                </div>
              </div>

              <div className="text-center">
                <p className="text-sm text-gray-600 mb-4">
                  Don't have an account?
                </p>
                <Link
                  href="/signup"
                  className="text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors"
                >
                  Sign up
                </Link>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="px-8 py-4 bg-gray-50 border-t border-gray-200 text-xs text-gray-500">
            <p className="text-center">
              By continuing, you agree to our <Link href="/terms" className="underline hover:text-gray-700">Terms of Service</Link> and <Link href="/privacy" className="underline hover:text-gray-700">Privacy Policy</Link>.
            </p>
          </div>

          {/* Add bridge-specific UI if in bridge mode */}
          {bridgeAuthMode && (
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <div>
                  <h3 className="text-sm font-medium text-blue-800">Bridge Authentication</h3>
                  <p className="text-sm text-blue-600 mt-1">
                    You're signing in to authenticate the CreateLex Bridge. After login, you'll be redirected back to the bridge automatically.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>

      {/* Footer */}
      <footer className="py-6 px-8 border-t border-gray-200 bg-white">
        <div className="max-w-screen-xl mx-auto flex flex-col md:flex-row justify-between items-center text-sm text-gray-500">
          <div className="mb-4 md:mb-0">
            © {new Date().getFullYear()} CreateLex. All rights reserved.
          </div>
          <div className="flex space-x-6">
            <Link href="/terms" className="hover:text-gray-700">Terms</Link>
            <Link href="/privacy" className="hover:text-gray-700">Privacy</Link>
            <Link href="/contact" className="hover:text-gray-700">Contact</Link>
          </div>
        </div>
      </footer>
    </div>
  );
}

// Export the wrapped component
export default withSearchParamsProvider(LoginPage);
