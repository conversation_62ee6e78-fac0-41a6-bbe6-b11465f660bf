#!/usr/bin/env python3
"""
Cloud MCP Wrapper
This provides both HTTP REST API and MCP stdio interfaces
"""

import asyncio
import json
import sys
import logging
import threading
import time
import subprocess
from typing import Any, Dict, List, Optional
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
UNREAL_ENGINE_HOST = "localhost"  # Will be tunneled through cloudflared
UNREAL_ENGINE_PORT = 9877

def sync_send_command_to_unreal(command_data: dict) -> dict:
    """Send command to Unreal Engine socket server (synchronous version)"""
    try:
        import socket
        
        # Create socket connection
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10.0)  # 10 second timeout
        
        # Connect to Unreal Engine
        sock.connect((UNREAL_ENGINE_HOST, UNREAL_ENGINE_PORT))
        
        # Send command as JSON
        command_json = json.dumps(command_data) + '\n'
        sock.send(command_json.encode())
        
        # Read response
        response_data = sock.recv(4096)
        sock.close()
        
        if response_data:
            return json.loads(response_data.decode().strip())
        else:
            return {"success": False, "error": "No response from Unreal Engine"}
            
    except Exception as e:
        logger.error(f"Error communicating with Unreal Engine: {e}")
        return {"success": False, "error": str(e)}

# Create FastAPI app
app = FastAPI(title="Unreal Engine Cloud MCP Server")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Available tools definition
AVAILABLE_TOOLS = [
    {
        "name": "spawn_actor",
        "description": "Spawn an actor in Unreal Engine at specified coordinates",
        "inputSchema": {
            "type": "object",
            "properties": {
                "actor_class": {"type": "string", "description": "Class of actor to spawn"},
                "x": {"type": "number", "default": 0, "description": "X coordinate"},
                "y": {"type": "number", "default": 0, "description": "Y coordinate"},
                "z": {"type": "number", "default": 0, "description": "Z coordinate"}
            },
            "required": ["actor_class"]
        }
    },
    {
        "name": "create_material",
        "description": "Create a material in Unreal Engine with specified properties",
        "inputSchema": {
            "type": "object",
            "properties": {
                "material_name": {"type": "string", "description": "Name of the material"},
                "base_color": {"type": "array", "items": {"type": "number"}, "default": [1.0, 1.0, 1.0], "description": "RGB color values"},
                "metallic": {"type": "number", "default": 0.0, "description": "Metallic value"},
                "roughness": {"type": "number", "default": 0.5, "description": "Roughness value"}
            },
            "required": ["material_name"]
        }
    },
    {
        "name": "execute_python",
        "description": "Execute Python script in Unreal Engine",
        "inputSchema": {
            "type": "object",
            "properties": {
                "script": {"type": "string", "description": "Python script to execute"}
            },
            "required": ["script"]
        }
    },
    {
        "name": "handshake_test",
        "description": "Test connection to Unreal Engine",
        "inputSchema": {
            "type": "object",
            "properties": {
                "message": {"type": "string", "default": "Cloud MCP Connection Test", "description": "Test message"}
            }
        }
    },
    {
        "name": "create_blueprint",
        "description": "Create a new Blueprint in Unreal Engine",
        "inputSchema": {
            "type": "object",
            "properties": {
                "blueprint_name": {"type": "string", "description": "Name of the blueprint"},
                "parent_class": {"type": "string", "default": "Actor", "description": "Parent class"}
            },
            "required": ["blueprint_name"]
        }
    }
]

def execute_tool(tool_name: str, arguments: dict) -> dict:
    """Execute a tool with given arguments"""
    command = {
        "id": f"{tool_name}_{int(time.time() * 1000)}",
        "type": tool_name,
        **arguments
    }
    
    return sync_send_command_to_unreal(command)

# MCP Protocol Endpoints

@app.post("/mcp/initialize")
async def mcp_initialize(request: dict):
    """MCP Initialize"""
    return {
        "jsonrpc": "2.0",
        "id": request.get("id"),
        "result": {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "tools": {"listChanged": True},
                "resources": {},
                "prompts": {}
            },
            "serverInfo": {
                "name": "unreal-cloud-mcp-server",
                "version": "1.0.0"
            }
        }
    }

@app.post("/mcp/tools/list")
async def mcp_list_tools(request: dict):
    """MCP List Tools"""
    return {
        "jsonrpc": "2.0",
        "id": request.get("id"),
        "result": {
            "tools": AVAILABLE_TOOLS
        }
    }

@app.post("/mcp/tools/call")
async def mcp_call_tool(request: dict):
    """MCP Call Tool"""
    try:
        params = request.get("params", {})
        tool_name = params.get("name")
        arguments = params.get("arguments", {})
        
        if not tool_name:
            raise HTTPException(status_code=400, detail="Tool name required")
        
        # Map tool names to command types
        tool_mapping = {
            "spawn_actor": "spawn",
            "create_material": "create_material",
            "execute_python": "execute_python",
            "handshake_test": "handshake",
            "create_blueprint": "create_blueprint"
        }
        
        command_type = tool_mapping.get(tool_name, tool_name)
        result = execute_tool(command_type, arguments)
        
        return {
            "jsonrpc": "2.0",
            "id": request.get("id"),
            "result": {
                "content": [
                    {
                        "type": "text",
                        "text": json.dumps(result)
                    }
                ],
                "isError": False
            }
        }
        
    except Exception as e:
        return {
            "jsonrpc": "2.0",
            "id": request.get("id"),
            "error": {
                "code": -1,
                "message": str(e)
            }
        }

@app.post("/mcp/resources/list")
async def mcp_list_resources(request: dict):
    """MCP List Resources"""
    return {
        "jsonrpc": "2.0",
        "id": request.get("id"),
        "result": {
            "resources": []
        }
    }

@app.post("/mcp/prompts/list")
async def mcp_list_prompts(request: dict):
    """MCP List Prompts"""
    return {
        "jsonrpc": "2.0",
        "id": request.get("id"),
        "result": {
            "prompts": []
        }
    }

# Health check and info endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Test connection to Unreal Engine
        test_result = sync_send_command_to_unreal({"type": "handshake", "message": "Health check"})
        unreal_status = "connected" if test_result.get("success") else "disconnected"
    except:
        unreal_status = "disconnected"
    
    return {
        "status": "healthy",
        "service": "Unreal Engine Cloud MCP Server",
        "unreal_engine": unreal_status,
        "tools_count": len(AVAILABLE_TOOLS)
    }

@app.get("/tools")
async def list_tools():
    """List available MCP tools"""
    return {
        "tools": [{"name": tool["name"], "description": tool["description"]} for tool in AVAILABLE_TOOLS],
        "count": len(AVAILABLE_TOOLS)
    }

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "Unreal Engine Cloud MCP Server",
        "status": "running",
        "endpoints": {
            "health": "/health",
            "tools": "/tools",
            "mcp": "/mcp/*"
        }
    }

# Run the server
if __name__ == "__main__":
    print("🌐 Starting Cloud MCP Wrapper Server...")
    print(f"📡 Connecting to Unreal Engine at {UNREAL_ENGINE_HOST}:{UNREAL_ENGINE_PORT}")
    print(f"🔧 Available tools: {len(AVAILABLE_TOOLS)}")
    
    # Test connection
    try:
        test_result = sync_send_command_to_unreal({"type": "handshake", "message": "Cloud wrapper startup test"})
        print(f"✅ Connection test: {test_result}")
    except Exception as e:
        print(f"⚠️ Connection test failed: {e}")
    
    # Run the FastAPI server
    uvicorn.run(app, host="0.0.0.0", port=8000) 