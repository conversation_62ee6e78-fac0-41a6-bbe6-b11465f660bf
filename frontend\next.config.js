/** @type {import('next').NextConfig} */
const nextConfig = {
  // Basic configuration
  reactStrictMode: true,
  swcMinify: true,

  // Configure image domains
  images: {
    domains: ['lh3.googleusercontent.com', 'avatars.githubusercontent.com', 'via.placeholder.com'],
  },

  // Disable static exports
  trailingSlash: false,

  // Disable eslint during build
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Output standalone build for easier deployment
  output: 'standalone',

  // Disable static exports completely
  distDir: process.env.BUILD_DIR || '.next',

  // Disable static generation
  generateEtags: false,

  // Force dynamic rendering for all pages
  experimental: {
    // Disable worker threads to prevent static generation
    workerThreads: false,
    cpus: 1
  },

  // Skip type checking to speed up build
  typescript: {
    ignoreBuildErrors: true
  },

  // Environment variables and build configuration
  env: {
    NEXT_DISABLE_STATIC_GENERATION: 'true',
    NEXT_SKIP_STATIC_EXPORT: 'true',
    NEXT_DISABLE_STATIC_EXPORT: 'true',
    // Ensure Supabase environment variables are available at build time
    NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://ujiakzkncbxisdatygpo.supabase.co',
    NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    SUPABASE_SERVICE_KEY: process.env.SUPABASE_SERVICE_KEY,
    // API URLs for production
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'https://createlex.com',
    NEXT_PUBLIC_BACKEND_API_URL: process.env.NEXT_PUBLIC_BACKEND_API_URL || 'https://api.createlex.com',
  },

  // Configure API routes to be fully dynamic
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Cache-Control', value: 'no-store, must-revalidate' },
          { key: 'Pragma', value: 'no-cache' },
          { key: 'Expires', value: '0' }
        ],
      },
    ];
  },
};

module.exports = nextConfig;
