require('dotenv').config();
const supabase = require('./src/services/supabaseClient');

async function checkUserAndGrantAdmin() {
  console.log('🔍 Checking your user account and granting admin access...');
  
  const YOUR_USER_ID = '5af3ff0f-0ccb-41a2-8dba-3e460aa9bd72';
  
  try {
    // First, check if the user exists and get their email
    console.log('1. Looking up your user account...');
    const { data: users, error: userError } = await supabase
      .from('users')
      .select('id, email, name, is_admin')
      .eq('id', YOUR_USER_ID);
      
    if (userError) {
      console.error('❌ Error finding user:', userError);
      return;
    }
    
    if (!users || users.length === 0) {
      console.error('❌ User not found with ID:', YOUR_USER_ID);
      console.log('🔍 Let me check all users to see what we have...');
      
      // Check all users
      const { data: allUsers, error: allError } = await supabase
        .from('users')
        .select('id, email, name, is_admin')
        .limit(10);
        
      if (allError) {
        console.error('❌ Error getting all users:', allError);
        return;
      }
      
      console.log('📋 Found users in database:');
      allUsers.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.email} (${user.id}) - Admin: ${user.is_admin || false}`);
      });
      return;
    }
    
    const user = users[0];
    console.log('✅ Found your user account:');
    console.log(`   📧 Email: ${user.email}`);
    console.log(`   👤 Name: ${user.name}`);
    console.log(`   🔑 ID: ${user.id}`);
    console.log(`   👑 Admin: ${user.is_admin || false}`);
    
    // Check if user is already admin
    if (user.is_admin) {
      console.log('🎉 You already have admin privileges!');
      console.log('🤔 The issue might be with authentication token passing.');
      console.log('💡 Try refreshing your browser and signing out/in again.');
      return;
    }
    
    // Check if is_admin column exists by trying to update
    console.log('2. Granting admin privileges...');
    const { data: updateData, error: updateError } = await supabase
      .from('users')
      .update({ is_admin: true })
      .eq('id', YOUR_USER_ID)
      .select();
      
    if (updateError) {
      if (updateError.message && updateError.message.includes('column "is_admin" does not exist')) {
        console.log('⚠️  is_admin column does not exist, need to create it first');
        console.log('📝 Please run this SQL in Supabase Dashboard > SQL Editor:');
        console.log('');
        console.log('   ALTER TABLE users ADD COLUMN IF NOT EXISTS is_admin BOOLEAN NOT NULL DEFAULT FALSE;');
        console.log(`   UPDATE users SET is_admin = TRUE WHERE id = '${YOUR_USER_ID}';`);
        console.log('');
        return;
      } else {
        console.error('❌ Error setting admin privileges:', updateError);
        return;
      }
    }
    
    console.log('✅ Successfully granted admin privileges!');
    
    // Verify the update
    console.log('3. Verifying admin access...');
    const { data: verifyUsers, error: verifyError } = await supabase
      .from('users')
      .select('id, email, name, is_admin')
      .eq('id', YOUR_USER_ID);
      
    if (verifyError) {
      console.error('❌ Error verifying update:', verifyError);
      return;
    }
    
    const verifyUser = verifyUsers[0];
    console.log('✅ Verification complete:');
    console.log(`   📧 Email: ${verifyUser.email}`);
    console.log(`   👤 Name: ${verifyUser.name}`);
    console.log(`   👑 Admin: ${verifyUser.is_admin}`);
    
    if (verifyUser.is_admin) {
      console.log('');
      console.log('🎉 SUCCESS! You now have admin access!');
      console.log('🔗 You can now access: https://createlex.com/admin/api-keys');
      console.log('🔄 Please refresh your browser and try again.');
      console.log('');
      console.log('💡 If you still get 403 errors:');
      console.log('   1. Clear browser cache and cookies');
      console.log('   2. Sign out and sign back in');
      console.log('   3. Check browser console for auth errors');
    } else {
      console.log('⚠️  Admin flag not set properly. Manual intervention needed.');
    }
    
  } catch (error) {
    console.error('💥 Unexpected error:', error);
    console.log('');
    console.log('📝 Manual SQL commands to run in Supabase Dashboard:');
    console.log('   1. Go to Supabase Dashboard > SQL Editor');
    console.log('   2. Run these commands:');
    console.log('      ALTER TABLE users ADD COLUMN IF NOT EXISTS is_admin BOOLEAN NOT NULL DEFAULT FALSE;');
    console.log(`      UPDATE users SET is_admin = TRUE WHERE id = '${YOUR_USER_ID}';`);
    console.log(`      SELECT id, email, name, is_admin FROM users WHERE id = '${YOUR_USER_ID}';`);
  }
}

// Run the script
console.log('🚀 Starting user check and admin grant process...');
console.log('👤 Target User ID: 5af3ff0f-0ccb-41a2-8dba-3e460aa9bd72');
console.log('');

checkUserAndGrantAdmin()
  .then(() => {
    console.log('');
    console.log('🏁 Process completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
  });
