'use client';

import { useState, useEffect } from 'react';
import { useSupabaseAuth } from '../../../contexts/SupabaseAuthContext';
import Link from 'next/link';

export default function StripeTestPage() {
  const { user, session } = useSupabaseAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [stripePublishableKey, setStripePublishableKey] = useState<string | null>(null);
  const [webhookTestResult, setWebhookTestResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Test the Stripe webhook
  const testWebhook = async () => {
    if (!user) {
      setError('You must be logged in to test the webhook');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      // Get the API URL from environment variables
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'https://api.createlex.com';
      
      // Call the webhook simulation endpoint
      const response = await fetch(`${backendUrl}/api/tokens/simulate-webhook`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session?.access_token || ''}`,
          'x-user-id': user.id,
        },
        body: JSON.stringify({
          userId: user.id,
          packageId: 'test-package-1', // Use a test package ID
          amount: 100, // Test amount
        }),
      });
      
      if (response.ok) {
        const data = await response.json();
        setWebhookTestResult({
          success: true,
          data,
        });
      } else {
        setError(`API returned status: ${response.status}`);
        try {
          const errorData = await response.json();
          setWebhookTestResult({
            success: false,
            error: errorData,
          });
        } catch (e) {
          // If we can't parse the error as JSON, just show the status
        }
      }
    } catch (error: any) {
      setError(error.message || 'Unknown error occurred');
      setWebhookTestResult({
        success: false,
        error: error.message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Get the Stripe publishable key
  const getStripePublishableKey = async () => {
    try {
      // This should be available as an environment variable
      const key = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
      if (key) {
        setStripePublishableKey(key);
      } else {
        console.warn('Stripe publishable key not found in environment variables');
      }
    } catch (error) {
      console.error('Error getting Stripe publishable key:', error);
    }
  };

  useEffect(() => {
    getStripePublishableKey();
  }, []);

  return (
    <div className="container mx-auto p-8">
      <div className="mb-4">
        <Link href="/api-test" className="text-blue-500 hover:underline">
          ← Back to API Test
        </Link>
      </div>
      
      <h1 className="text-3xl font-bold mb-6">Stripe Integration Test</h1>
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Authentication Status</h2>
        <div className={`p-4 rounded ${user ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
          {user ? 'You are authenticated! ✅' : 'You are not authenticated! ❌'}
        </div>
      </div>
      
      {!user && (
        <div className="mb-6">
          <p className="text-red-600">
            You need to be logged in to test Stripe integration.
          </p>
          <Link 
            href="/api-test/auth" 
            className="mt-2 inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Go to Authentication Test
          </Link>
        </div>
      )}
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Stripe Configuration</h2>
        <div className="p-4 bg-gray-100 rounded">
          <p>
            <strong>Stripe Publishable Key:</strong>{' '}
            {stripePublishableKey ? (
              <span className="text-green-600">
                {stripePublishableKey.substring(0, 8)}...{stripePublishableKey.substring(stripePublishableKey.length - 4)}
              </span>
            ) : (
              <span className="text-red-600">Not found</span>
            )}
          </p>
          <p className="mt-2">
            <strong>Webhook URL:</strong>{' '}
            <span className="text-blue-600">
              https://api.createlex.com/webhooks/stripe
            </span>
          </p>
        </div>
      </div>
      
      {user && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Actions</h2>
          <button
            onClick={testWebhook}
            disabled={isLoading}
            className={`px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {isLoading ? 'Testing...' : 'Test Webhook Simulation'}
          </button>
          <p className="mt-2 text-sm text-gray-600">
            This will simulate a webhook event for a token purchase.
          </p>
        </div>
      )}
      
      {error && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Error</h2>
          <div className="p-4 bg-red-100 text-red-800 rounded">
            {error}
          </div>
        </div>
      )}
      
      {webhookTestResult && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Webhook Test Result</h2>
          <div className={`p-4 rounded ${webhookTestResult.success ? 'bg-green-100' : 'bg-red-100'}`}>
            <p className={`font-bold ${webhookTestResult.success ? 'text-green-800' : 'text-red-800'}`}>
              {webhookTestResult.success ? 'Webhook simulation successful! ✅' : 'Webhook simulation failed! ❌'}
            </p>
          </div>
          <pre className="mt-4 p-4 bg-gray-100 rounded overflow-auto">
            {JSON.stringify(webhookTestResult, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
