'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { fetchWithAuth, getApiUrl } from '@/lib/auth-utils';

export default function TokenUsageCard() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<any>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('Fetching token usage statistics...');
        const apiUrl = getApiUrl();
        const requestUrl = `${apiUrl}/api/admin/token-usage/statistics?period=month`;

        console.log('Making request to:', requestUrl);

        const response = await fetchWithAuth(requestUrl);

        console.log('Response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Error response:', errorText);
          throw new Error(`Failed to fetch token usage statistics: ${response.status} ${errorText}`);
        }

        const data = await response.json();
        console.log('Token usage data received:', JSON.stringify(data, null, 2));
        setData(data);
      } catch (error) {
        console.error('Error fetching token usage:', error);
        setError('Failed to load token usage data: ' + (error instanceof Error ? error.message : String(error)));
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Token Usage</CardTitle>
          <CardDescription>Current month usage</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Token Usage</CardTitle>
          <CardDescription>Current month usage</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-red-500">{error}</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Token Usage</CardTitle>
        <CardDescription>Current month usage</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-3xl font-bold">
          {data?.totalUsage?.totalTokens?.toLocaleString() || 0}
        </div>
        <div className="mt-2 text-sm text-muted-foreground">
          <div>Prompt: {data?.totalUsage?.promptTokens?.toLocaleString() || 0}</div>
          <div>Completion: {data?.totalUsage?.completionTokens?.toLocaleString() || 0}</div>
        </div>
        <div className="mt-4">
          <div className="text-sm font-medium">Estimated Cost</div>
          <div className="text-xl font-bold">${data?.costs?.totalCost || '0.00'}</div>
        </div>
      </CardContent>
    </Card>
  );
}
