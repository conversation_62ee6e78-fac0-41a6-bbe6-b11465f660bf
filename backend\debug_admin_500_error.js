#!/usr/bin/env node
/**
 * Debug the exact 500 error from /api/admin/api-keys
 * This replicates the exact backend logic to find the issue
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Use the exact same Supabase configuration as your backend
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

console.log('🔍 Debugging Admin API Keys 500 Error');
console.log('=====================================');

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function debugApiKeysEndpoint() {
  console.log('\n🎯 Testing the exact backend logic...\n');

  try {
    // Step 1: Test the main api_keys query (line 42-45 in your backend)
    console.log('1️⃣ Testing main API keys query...');
    const { data: apiKeysData, error: apiKeysError } = await supabase
      .from('api_keys')
      .select('*')
      .order('created_at', { ascending: false });

    if (apiKeysError) {
      console.error('❌ API keys query failed:', apiKeysError.message);
      console.log('🔧 This is the source of your 500 error!');
      return;
    }

    console.log(`✅ API keys query successful - found ${apiKeysData.length} records`);
    
    if (apiKeysData.length > 0) {
      console.log('Sample API key structure:');
      const sample = { ...apiKeysData[0] };
      if (sample.api_key) sample.api_key = sample.api_key.substring(0, 8) + '...';
      console.log(JSON.stringify(sample, null, 2));
    }

    // Step 2: Test users table access (line 61-65 in your backend)
    console.log('\n2️⃣ Testing users table access...');
    
    if (apiKeysData.length === 0) {
      console.log('⚠️ No API keys found, skipping user lookup test');
    } else {
      const firstKey = apiKeysData[0];
      console.log(`Testing user lookup for user_id: ${firstKey.user_id}`);
      
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('email')
        .eq('id', firstKey.user_id)
        .single();

      if (userError) {
        console.error('❌ Users table query failed:', userError.message);
        console.log('🔧 This might be causing the 500 error!');
        
        if (userError.message.includes('relation "users" does not exist')) {
          console.log('\n💡 SOLUTION: Create the users table');
        } else if (userError.message.includes('No rows returned')) {
          console.log('\n💡 SOLUTION: User record missing or user_id mismatch');
        } else if (userError.message.includes('permission denied') || userError.message.includes('policy')) {
          console.log('\n💡 SOLUTION: Fix RLS policies for users table');
        }
      } else {
        console.log('✅ Users table query successful');
        console.log('User data:', userData);
      }
    }

    // Step 3: Test the complete join query
    console.log('\n3️⃣ Testing complete join query...');
    
    const { data: joinData, error: joinError } = await supabase
      .from('api_keys')
      .select(`
        *,
        users!inner(email)
      `)
      .order('created_at', { ascending: false });

    if (joinError) {
      console.error('❌ Join query failed:', joinError.message);
      console.log('🔧 This confirms the issue is with users table relationship');
    } else {
      console.log('✅ Join query successful');
      console.log(`Found ${joinData.length} API keys with user details`);
    }

    // Step 4: Check table existence
    console.log('\n4️⃣ Checking table existence...');
    
    const tables = ['api_keys', 'users'];
    for (const tableName of tables) {
      try {
        const { data, error } = await supabase
          .from(tableName)
          .select('count')
          .limit(1);
        
        if (error) {
          if (error.message.includes('relation') && error.message.includes('does not exist')) {
            console.log(`❌ Table "${tableName}" does not exist`);
          } else {
            console.log(`⚠️ Table "${tableName}" exists but has issues: ${error.message}`);
          }
        } else {
          console.log(`✅ Table "${tableName}" exists and is accessible`);
        }
      } catch (err) {
        console.log(`❌ Error checking table "${tableName}":`, err.message);
      }
    }

    // Step 5: Check RLS policies
    console.log('\n5️⃣ Checking RLS policies...');
    
    try {
      const { data: policies, error: policiesError } = await supabase
        .from('pg_policies')
        .select('tablename, policyname, cmd, roles')
        .in('tablename', ['api_keys', 'users']);

      if (policiesError) {
        console.log('⚠️ Could not check RLS policies:', policiesError.message);
      } else {
        console.log('RLS Policies found:');
        policies.forEach(policy => {
          console.log(`- ${policy.tablename}: ${policy.policyname} (${policy.cmd}) for roles: ${policy.roles}`);
        });
      }
    } catch (err) {
      console.log('⚠️ Could not check RLS policies:', err.message);
    }

    console.log('\n🎉 Debugging completed!');

  } catch (error) {
    console.error('❌ Unexpected error during debugging:', error.message);
    console.log('Full error:', error);
  }
}

// Helper function to create missing tables
async function createMissingTables() {
  console.log('\n🛠️ Creating missing tables...');
  
  const createUsersTableSQL = `
    CREATE TABLE IF NOT EXISTS users (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      email TEXT UNIQUE NOT NULL,
      name TEXT,
      picture TEXT,
      provider TEXT DEFAULT 'github',
      provider_id TEXT,
      subscription_status TEXT DEFAULT 'inactive',
      is_admin BOOLEAN DEFAULT false,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    ALTER TABLE users ENABLE ROW LEVEL SECURITY;
    
    DROP POLICY IF EXISTS "Service role can manage all users" ON users;
    CREATE POLICY "Service role can manage all users" ON users
      FOR ALL USING (auth.role() = 'service_role');
  `;

  console.log('SQL to create users table:');
  console.log(createUsersTableSQL);
  console.log('\nRun this SQL in your Supabase Dashboard SQL Editor!');
}

// Run the debugging
debugApiKeysEndpoint()
  .then(() => {
    console.log('\n📋 SUMMARY:');
    console.log('- If you see ❌ errors above, those are causing your 500 error');
    console.log('- Most likely: users table is missing or has RLS issues');
    console.log('- Use Supabase Dashboard SQL Editor to fix the issues');
    
    createMissingTables();
    
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Debug script failed:', error);
    process.exit(1);
  });
