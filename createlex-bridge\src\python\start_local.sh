#!/bin/bash

# Quick start script for local development and testing
# This script starts the MCP server locally without Docker

echo "🚀 Starting Unreal Engine MCP Server locally..."

# Check if Python is available
if ! command -v python &> /dev/null; then
    echo "❌ Python is not installed. Please install Python 3.8+ first."
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate 2>/dev/null || source venv/Scripts/activate 2>/dev/null

# Install dependencies
echo "📥 Installing dependencies..."
pip install -r requirements.txt

# Set default environment variables if not set
export UNREAL_HOST=${UNREAL_HOST:-localhost}
export UNREAL_PORT=${UNREAL_PORT:-9877}

echo "⚙️  Configuration:"
echo "   Unreal Engine: $UNREAL_HOST:$UNREAL_PORT"
echo "   MCP Server: localhost:8000"
echo ""

# Run tests first
echo "🧪 Running tests..."
python test_server.py

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Tests passed! Starting MCP server..."
    echo "🌐 Server will be available at: http://localhost:8000"
    echo "🛑 Press Ctrl+C to stop the server"
    echo ""
    
    # Start the server
    python mcp_server.py
else
    echo "❌ Tests failed. Please fix the issues before starting the server."
    exit 1
fi 