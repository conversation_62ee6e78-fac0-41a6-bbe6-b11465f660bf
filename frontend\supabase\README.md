# Supabase Setup for MCP Chat

This directory contains SQL migrations for setting up the Supabase database for the MCP Chat application.

## Creating the Chat Tables

To create the chat tables in your Supabase database:

1. Go to the [Supabase Dashboard](https://app.supabase.com/project/ujiakzkncbxisdatygpo)
2. Navigate to the SQL Editor
3. Click "New Query"
4. Copy and paste the contents of `migrations/create_chat_tables.sql` into the editor
5. Click "Run" to execute the SQL

## Table Structure

### Chats Table

- `id`: Text primary key
- `user_id`: Text (the user who owns the chat)
- `title`: Text (the title of the chat)
- `created_at`: Timestamp
- `updated_at`: Timestamp

### Messages Table

- `id`: Text primary key
- `chat_id`: Text (foreign key to chats.id)
- `role`: Text (user, assistant, system, etc.)
- `content`: JSONB (the message content)
- `created_at`: Timestamp

## Row Level Security

Row Level Security (RLS) is enabled on both tables to ensure that users can only access their own chats and messages.

## Indexes

Indexes are created on:
- `messages.chat_id` for faster message retrieval
- `chats.user_id` for faster chat retrieval
