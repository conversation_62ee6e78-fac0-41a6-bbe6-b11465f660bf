'use client';

import { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { setPrimaryUserId } from "@/lib/user-id";
import { getDeviceId } from "@/lib/device-id";

export function UserIdSync() {
  const [isOpen, setIsOpen] = useState(false);
  const [userId, setUserId] = useState('');
  const [currentUserId, setCurrentUserId] = useState('');
  const [deviceId, setDeviceId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  // Get the current user ID and device ID
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedUserId = localStorage.getItem('ai-chat-user-id');
      setCurrentUserId(storedUserId || '');
      setDeviceId(getDeviceId());
    }
  }, []);
  
  const handleSetPrimaryUserId = async () => {
    if (!userId.trim()) {
      toast.error("User ID cannot be empty");
      return;
    }
    
    setIsLoading(true);
    
    try {
      const success = await setPrimaryUserId(userId.trim());
      
      if (success) {
        toast.success("Primary user ID set successfully");
        setIsOpen(false);
        
        // Reload the page to ensure all components use the new user ID
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        toast.error("Failed to set primary user ID");
      }
    } catch (error) {
      console.error('Error setting primary user ID:', error);
      toast.error("An error occurred while setting the primary user ID");
    } finally {
      setIsLoading(false);
    }
  };
  
  if (!isOpen) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsOpen(true)}
          className="bg-white dark:bg-gray-800 shadow-md"
        >
          Sync User ID
        </Button>
      </div>
    );
  }
  
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black/50 z-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Sync User ID Across Devices</CardTitle>
          <CardDescription>
            Set the primary user ID for all your devices
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="currentUserId">Current User ID</Label>
            <Input
              id="currentUserId"
              value={currentUserId}
              readOnly
              className="font-mono text-xs"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="deviceId">Device ID</Label>
            <Input
              id="deviceId"
              value={deviceId}
              readOnly
              className="font-mono text-xs"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="userId">Primary User ID</Label>
            <Input
              id="userId"
              value={userId}
              onChange={(e) => setUserId(e.target.value)}
              placeholder="Enter the Supabase user ID to use"
              className="font-mono text-xs"
            />
            <p className="text-xs text-muted-foreground">
              Enter the Supabase user ID from your other device
            </p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSetPrimaryUserId}
            disabled={isLoading}
          >
            {isLoading ? "Setting..." : "Set Primary User ID"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
