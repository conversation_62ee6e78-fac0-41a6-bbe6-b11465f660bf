import { NextApiRequest, NextApiResponse } from 'next';
import { getSupabaseClient } from '@/lib/supabase-singleton';

/**
 * API endpoint to automatically sync user IDs across devices
 * This endpoint migrates all chats to use the same user ID
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get all user IDs from chats
    const supabase = getSupabaseClient();
    const { data: chats, error: chatsError } = await supabase
      .from('chats')
      .select('user_id, created_at')
      .order('created_at', { ascending: false });

    if (chatsError) {
      console.error('Error getting chats:', chatsError);
      return res.status(500).json({ error: 'Error getting chats' });
    }

    // Extract unique user IDs and identify Supabase UUIDs
    const userIds = new Set<string>();
    const supabaseUuids = new Set<string>();

    chats?.forEach(chat => {
      if (chat.user_id) {
        userIds.add(chat.user_id);

        // Check if the user ID looks like a UUID (Supabase format)
        if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(chat.user_id)) {
          supabaseUuids.add(chat.user_id);
        }
      }
    });

    // If we have Supabase UUIDs, use the most recent one as the primary user ID
    if (supabaseUuids.size > 0) {
      const primaryUserId = Array.from(supabaseUuids)[0];
      console.log(`API: Using Supabase UUID ${primaryUserId} as primary user ID`);

      // Migrate all chats to the primary user ID
      for (const userId of userIds) {
        if (userId !== primaryUserId) {
          console.log(`API: Migrating chats from ${userId} to ${primaryUserId}`);

          // Update all chats to use the primary user ID
          const { error: updateError } = await supabase
            .from('chats')
            .update({ user_id: primaryUserId })
            .eq('user_id', userId);

          if (updateError) {
            console.error(`API: Error migrating chats from ${userId} to ${primaryUserId}:`, updateError);
          } else {
            console.log(`API: Successfully migrated chats from ${userId} to ${primaryUserId}`);
          }
        }
      }

      // Update all user ID mappings to use the primary user ID
      const { error: mappingError } = await supabase
        .from('user_id_mappings')
        .update({ primary_user_id: primaryUserId });

      if (mappingError) {
        console.error('API: Error updating user ID mappings:', mappingError);
      } else {
        console.log('API: Successfully updated all user ID mappings');
      }

      return res.status(200).json({
        success: true,
        primaryUserId,
        migratedUserIds: Array.from(userIds).filter(id => id !== primaryUserId),
      });
    }

    // If we don't have any Supabase UUIDs, find the user ID with the most chats
    console.log('API: No Supabase UUIDs found, finding user ID with most chats');

    // Get the count of chats for each user ID
    const userIdCounts = new Map<string, number>();

    for (const userId of userIds) {
      const { count, error } = await supabase
        .from('chats')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', userId);

      if (error) {
        console.error(`API: Error getting chat count for user ID ${userId}:`, error);
      } else {
        userIdCounts.set(userId, count || 0);
        console.log(`API: User ID ${userId} has ${count} chats`);
      }
    }

    // Sort user IDs by chat count (descending)
    const sortedUserIds = Array.from(userIds).sort((a, b) => {
      return (userIdCounts.get(b) || 0) - (userIdCounts.get(a) || 0);
    });

    // If we have user IDs with chats, use the one with the most chats as the primary ID
    if (sortedUserIds.length > 0 && (userIdCounts.get(sortedUserIds[0]) || 0) > 0) {
      const primaryUserId = sortedUserIds[0];
      console.log(`API: Using user ID ${primaryUserId} with ${userIdCounts.get(primaryUserId)} chats as primary user ID`);

      // Migrate all chats to the primary user ID
      for (const userId of userIds) {
        if (userId !== primaryUserId) {
          console.log(`API: Migrating chats from ${userId} to ${primaryUserId}`);

          // Update all chats to use the primary user ID
          const { error: updateError } = await supabase
            .from('chats')
            .update({ user_id: primaryUserId })
            .eq('user_id', userId);

          if (updateError) {
            console.error(`API: Error migrating chats from ${userId} to ${primaryUserId}:`, updateError);
          } else {
            console.log(`API: Successfully migrated chats from ${userId} to ${primaryUserId}`);
          }
        }
      }

      // Update all user ID mappings to use the primary user ID
      const { error: mappingError } = await supabase
        .from('user_id_mappings')
        .update({ primary_user_id: primaryUserId });

      if (mappingError) {
        console.error('API: Error updating user ID mappings:', mappingError);
      } else {
        console.log('API: Successfully updated all user ID mappings');
      }

      return res.status(200).json({
        success: true,
        primaryUserId,
        migratedUserIds: Array.from(userIds).filter(id => id !== primaryUserId),
        message: 'Using user ID with most chats',
      });
    }

    // If no user has any chats, check if any of the user IDs look like Supabase UUIDs
    const uuidsFromUserIds = Array.from(userIds).filter(id =>
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id)
    );

    if (uuidsFromUserIds.length > 0) {
      // Use the first Supabase UUID as the primary user ID
      const primaryUserId = uuidsFromUserIds[0];
      console.log(`API: No user has any chats, using Supabase UUID ${primaryUserId} as primary user ID`);

      // Migrate all chats to the primary user ID (there shouldn't be any, but just in case)
      for (const userId of userIds) {
        if (userId !== primaryUserId) {
          console.log(`API: Migrating chats from ${userId} to ${primaryUserId}`);

          // Update all chats to use the primary user ID
          const { error: updateError } = await supabase
            .from('chats')
            .update({ user_id: primaryUserId })
            .eq('user_id', userId);

          if (updateError) {
            console.error(`API: Error migrating chats from ${userId} to ${primaryUserId}:`, updateError);
          } else {
            console.log(`API: Successfully migrated chats from ${userId} to ${primaryUserId}`);
          }
        }
      }

      // Update all user ID mappings to use the primary user ID
      const { error: mappingError } = await supabase
        .from('user_id_mappings')
        .update({ primary_user_id: primaryUserId });

      if (mappingError) {
        console.error('API: Error updating user ID mappings:', mappingError);
      } else {
        console.log('API: Successfully updated all user ID mappings');
      }

      return res.status(200).json({
        success: true,
        primaryUserId,
        migratedUserIds: Array.from(userIds).filter(id => id !== primaryUserId),
        message: 'Using Supabase UUID as primary user ID (no chats found)',
      });
    }

    // If no user has any chats and no Supabase UUIDs found, just return the list of user IDs
    return res.status(200).json({
      success: true,
      userIds: Array.from(userIds),
      message: 'No user has any chats and no Supabase UUIDs found, no migration performed',
    });
  } catch (error) {
    console.error('Error in auto-sync-user-ids API:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
