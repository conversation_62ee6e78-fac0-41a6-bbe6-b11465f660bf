import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Copy, Info, Shield } from "lucide-react";
import { updateUserId } from "@/lib/user-id";

interface UserIdManagerProps {
  userId: string;
  setUserId: (id: string) => void;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function UserIdManager({ userId, setUserId, open, onOpenChange }: UserIdManagerProps) {
  const [newUserId, setNewUserId] = useState(userId);
  const [showInfo, setShowInfo] = useState(false);
  const [userIdSource, setUserIdSource] = useState<string>('unknown');

  // Get the source of the user ID from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined' && open) {
      const source = localStorage.getItem('ai-chat-user-id-source') || 'unknown';

      // Check if the user ID looks like a UUID (Supabase format)
      const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(userId);

      // If the source is unknown but the ID looks like a UUID, assume it's from Supabase
      if (source === 'unknown' && isUUID) {
        console.log('User ID looks like a Supabase UUID, setting source to supabase');
        localStorage.setItem('ai-chat-user-id-source', 'supabase');
        setUserIdSource('supabase');
      } else {
        setUserIdSource(source);
      }
    }
  }, [open, userId]);

  const handleUpdateUserId = () => {
    if (!newUserId.trim()) {
      toast.error("User ID cannot be empty");
      return;
    }

    const trimmedUserId = newUserId.trim();

    // Check if the user ID looks like a UUID (Supabase format)
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(trimmedUserId);

    // If it looks like a UUID, mark it as from Supabase
    if (isUUID) {
      console.log('New user ID looks like a Supabase UUID');
      updateUserId(trimmedUserId, 'supabase');
    } else {
      // Otherwise, mark it as manually entered
      updateUserId(trimmedUserId, 'manual');
    }

    setUserId(trimmedUserId);
    onOpenChange(false);
    toast.success("User ID updated successfully");

    // Refresh the page to reload chats with new user ID
    window.location.reload();
  };

  const handleCopyUserId = () => {
    navigator.clipboard.writeText(userId);
    toast.success("User ID copied to clipboard");
  };

  return (
    <Dialog open={open} onOpenChange={(open) => {
      onOpenChange(open);
      if (open) {
        setNewUserId(userId);
      }
    }}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            User ID Manager
            <Button
              variant="ghost"
              size="icon"
              className="h-5 w-5 rounded-full"
              onClick={() => setShowInfo(!showInfo)}
            >
              <Info className="h-4 w-4" />
            </Button>
          </DialogTitle>
          <DialogDescription>
            Your user ID is used to identify your chats. For the best experience, log in to your account to use your Supabase user ID across all devices.
          </DialogDescription>
        </DialogHeader>

        {showInfo && (
          <div className="bg-muted/50 p-3 rounded-md text-sm text-muted-foreground mb-4">
            <p className="font-medium mb-2">User ID Types:</p>
            <ul className="list-disc pl-5 space-y-1">
              <li><span className="font-semibold text-blue-700">Supabase Auth</span>: This is your account's user ID. It will automatically be used on all devices where you log in.</li>
              <li><span className="font-semibold text-amber-700">Manual</span>: A user ID you entered manually. It won't automatically sync across devices.</li>
              <li><span className="font-semibold text-red-700">Browser Generated</span>: A temporary ID generated by your browser. It's only valid on this device.</li>
            </ul>

            <p className="font-medium mt-4 mb-2">For the best experience:</p>
            <ol className="list-decimal pl-5 space-y-1">
              <li>Log in to your account on all devices</li>
              <li>Your Supabase user ID will automatically be used</li>
              <li>Your chat history will be consistent across all devices</li>
            </ol>

            <p className="font-medium mt-4 mb-2">Manual sync (alternative):</p>
            <ol className="list-decimal pl-5 space-y-1">
              <li>Copy your user ID from this device</li>
              <li>On your other device, open the MCP-Chat application</li>
              <li>Click on your user icon in the sidebar</li>
              <li>Select "Edit User ID"</li>
              <li>Paste the copied user ID and save</li>
            </ol>
          </div>
        )}

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="currentUserId" className="flex items-center gap-2">
              Current User ID
              {userIdSource === 'supabase' && (
                <span className="inline-flex items-center text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                  <Shield className="h-3 w-3 mr-1" />
                  Supabase Auth
                </span>
              )}
              {userIdSource === 'manual' && (
                <span className="inline-flex items-center text-xs bg-amber-100 text-amber-800 px-2 py-0.5 rounded-full">
                  Manual
                </span>
              )}
              {userIdSource === 'generated' && (
                <span className="inline-flex items-center text-xs bg-red-100 text-red-800 px-2 py-0.5 rounded-full">
                  Browser Generated
                </span>
              )}
              {userIdSource === 'unknown' && (
                <span className="inline-flex items-center text-xs bg-gray-100 text-gray-800 px-2 py-0.5 rounded-full">
                  Unknown Source
                </span>
              )}
            </Label>
            <div className="flex items-center gap-2">
              <Input
                id="currentUserId"
                value={userId}
                readOnly
                className="font-mono text-xs"
              />
              <Button
                variant="outline"
                size="icon"
                onClick={handleCopyUserId}
                title="Copy User ID"
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {userIdSource === 'supabase'
                ? "This ID is from your Supabase account and will be consistent across all your devices when logged in"
                : userIdSource === 'manual'
                  ? "This is a manually entered ID. It will not automatically sync across devices."
                  : userIdSource === 'generated'
                    ? "This ID was generated by your browser and is only valid on this device. For a consistent experience across devices, log in to your account."
                    : "Copy this ID to use on other devices"}
            </p>
          </div>

          <div className="grid gap-2 mt-4">
            <Label htmlFor="newUserId">New User ID</Label>
            <Input
              id="newUserId"
              value={newUserId}
              onChange={(e) => setNewUserId(e.target.value)}
              placeholder="Enter a user ID"
              className="font-mono text-xs"
            />
            <p className="text-xs text-muted-foreground mt-1">
              Enter a user ID from another device to sync your chats
            </p>
            <div className="mt-2">
              <a
                href="/set-user-id"
                className="text-xs text-blue-600 hover:text-blue-800 hover:underline"
                target="_blank"
                rel="noopener noreferrer"
              >
                Open full-page User ID manager
              </a>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </Button>
          <Button onClick={handleUpdateUserId}>
            Update User ID
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
