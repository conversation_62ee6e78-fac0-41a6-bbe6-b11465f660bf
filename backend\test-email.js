// Load environment variables
require('dotenv').config();

const nodemailer = require('nodemailer');

// Create a test function
async function testEmail() {
  console.log('Starting email test...');

  // Log email configuration
  console.log('Email configuration:', {
    host: process.env.EMAIL_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.EMAIL_PORT || '587', 10),
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER ? `${process.env.EMAIL_USER.substring(0, 3)}...` : 'not set',
      pass: process.env.EMAIL_PASSWORD ? 'password set' : 'not set'
    }
  });

  // Create a transporter
  const transporter = nodemailer.createTransport({
    host: process.env.EMAIL_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.EMAIL_PORT || '587', 10),
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER || '',
      pass: process.env.EMAIL_PASSWORD || '',
    },
  });

  try {
    // Verify the connection with timeout
    console.log('Verifying connection to email server...');
    console.log('Using connection settings:', {
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      secure: process.env.EMAIL_SECURE === 'true'
    });

    // Set a timeout for the verification
    const verifyPromise = transporter.verify();
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Connection timeout after 10 seconds')), 10000)
    );

    await Promise.race([verifyPromise, timeoutPromise]);
    console.log('Connection to email server verified successfully!');

    // Send a test email
    console.log('Sending test email...');
    const info = await transporter.sendMail({
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: '<EMAIL>, <EMAIL>', // Added your personal email as a recipient
      subject: 'Test Email from CreateLex AI',
      text: 'This is a test email to verify that the email service is working correctly. Sent at: ' + new Date().toISOString(),
      html: '<h1>Test Email</h1><p>This is a test email to verify that the email service is working correctly.</p><p>Sent at: ' + new Date().toISOString() + '</p>'
    });

    console.log('Email sent successfully!');
    console.log('Message ID:', info.messageId);
    console.log('Preview URL:', nodemailer.getTestMessageUrl(info));

  } catch (error) {
    console.error('Error during email test:', error);

    // Provide more specific error information
    if (error.code === 'EAUTH') {
      console.error('Authentication error. Check your email credentials.');
    } else if (error.code === 'ESOCKET') {
      console.error('Socket error. Check your email host and port settings.');
    } else if (error.code === 'ECONNECTION') {
      console.error('Connection error. Check your network and email server settings.');
    }
  }
}

// Run the test
testEmail().catch(console.error);
