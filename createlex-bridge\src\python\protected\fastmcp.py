#!/usr/bin/env python3
import asyncio
import inspect
import json
import logging
import websockets
from typing import Any, Callable, Dict, List, Optional, Union, get_type_hints
import time
from datetime import datetime
import traceback

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('fastmcp')

class FastMCP:
    """
    Fast MCP (Model Control Protocol) implementation.
    
    This class provides a lightweight framework for defining tools that can be
    exposed to language models for execution, following the Model Control Protocol.
    """
    
    def __init__(self, name: str = "MCP"):
        """
        Initialize a new FastMCP instance.
        
        Args:
            name: Name of the MCP server
        """
        self.name = name
        self.tools = {}
        self.connected_clients = {}
        self.ws_server = None
        
        logger.info(f"Initialized FastMCP: {name}")
        
    def tool(self, func=None, **kwargs):
        """
        Decorator to register a function as an MCP tool.
        
        Args:
            func: The function to register
            **kwargs: Additional tool metadata
            
        Returns:
            The decorated function
        """
        def decorator(f):
            # Get parameter types and return type using type hints
            sig = inspect.signature(f)
            type_hints = get_type_hints(f)
            return_type = type_hints.get('return', Any).__name__
            
            # Build a parameter schema
            parameters = []
            for name, param in sig.parameters.items():
                param_type = type_hints.get(name, Any).__name__
                default = param.default if param.default is not inspect.Parameter.empty else None
                has_default = param.default is not inspect.Parameter.empty
                
                parameters.append({
                    'name': name,
                    'type': param_type,
                    'required': not has_default,
                    'default': default
                })
            
            # Get description from docstring
            description = f.__doc__ or "No description provided"
            
            # Register the tool
            tool_name = kwargs.get('name', f.__name__)
            self.tools[tool_name] = {
                'function': f,
                'name': tool_name,
                'description': description,
                'parameters': parameters,
                'return_type': return_type
            }
            
            logger.info(f"Registered tool: {tool_name}")
            return f
            
        if func is None:
            return decorator
        return decorator(func)
    
    def get_tools_description(self) -> List[Dict]:
        """
        Get a list of tool descriptions.
        
        Returns:
            List of tool metadata dictionaries
        """
        tools_info = []
        for name, tool in self.tools.items():
            tools_info.append({
                'name': name,
                'description': tool['description'],
                'parameters': tool['parameters'],
                'return_type': tool['return_type']
            })
        return tools_info
    
    def call_tool(self, name: str, arguments: Dict[str, Any]) -> Any:
        """
        Call a registered tool by name with arguments.
        
        Args:
            name: Name of the tool to call
            arguments: Dictionary of arguments to pass to the tool
            
        Returns:
            Result of the tool execution
            
        Raises:
            ValueError: If the tool is not found
        """
        if name not in self.tools:
            logger.error(f"Tool not found: {name}")
            raise ValueError(f"Tool not found: {name}")
        
        tool = self.tools[name]
        func = tool['function']
        
        logger.info(f"Calling tool: {name} with arguments: {arguments}")
        
        try:
            # Execute the tool with the provided arguments
            result = func(**arguments)
            logger.info(f"Tool {name} execution result: {result}")
            return result
        except Exception as e:
            logger.error(f"Error executing tool {name}: {e}")
            logger.error(traceback.format_exc())
            raise
    
    def run(self, host: str = 'localhost', port: int = 8765) -> None:
        """
        Start the MCP server.
        
        Args:
            host: Host to bind the server to
            port: Port to listen on
        """
        logger.info(f"Starting FastMCP server: {self.name} on {host}:{port}")
        
        asyncio.run(self._run_server(host, port))
    
    async def _run_server(self, host: str, port: int) -> None:
        """
        Internal method to run the WebSocket server.
        
        Args:
            host: Host to bind the server to
            port: Port to listen on
        """
        self.ws_server = await websockets.serve(self._handle_client, host, port)
        logger.info(f"WebSocket server started on {host}:{port}")
        
        await self.ws_server.wait_closed()
    
    async def _handle_client(self, websocket):
        """
        Handles a connected WebSocket client.
        """
        client_id = id(websocket)
        self.connected_clients[client_id] = websocket
        logger.info(f"Client {client_id} connected")
        
        try:
            async for message in websocket:
                try:
                    # Parse the message as JSON
                    try:
                        data = json.loads(message)
                    except json.JSONDecodeError:
                        logger.error(f"Invalid JSON from client {client_id}: {message}")
                        await websocket.send(json.dumps({
                            "type": "error",
                            "error": "Invalid JSON message format",
                            "timestamp": datetime.now().isoformat()
                        }))
                        continue
                    
                    # Generate a request ID if not provided
                    request_id = data.get("id", str(int(time.time() * 1000)))
                    if "id" not in data:
                        data["id"] = request_id
                        
                    # Process based on message type
                    if "type" not in data:
                        logger.error(f"Missing 'type' in message from client {client_id}: {data}")
                        await websocket.send(json.dumps({
                            "id": request_id,
                            "type": "error",
                            "error": "Missing 'type' in message",
                            "timestamp": datetime.now().isoformat()
                        }))
                        continue
                        
                    message_type = data["type"]
                    
                    # Check for tool request
                    if message_type == "tool_request":
                        if "tool" not in data or "arguments" not in data:
                            logger.error(f"Missing 'tool' or 'arguments' in tool request from client {client_id}: {data}")
                            await websocket.send(json.dumps({
                                "id": request_id,
                                "type": "error",
                                "error": "Tool request must include 'tool' and 'arguments'",
                                "timestamp": datetime.now().isoformat()
                            }))
                            continue
                            
                        tool_name = data["tool"]
                        arguments = data["arguments"]
                        
                        try:
                            result = self.call_tool(tool_name, arguments)
                            await websocket.send(json.dumps({
                                "id": request_id,
                                "type": "tool_response",
                                "result": result,
                                "tool": tool_name,
                                "status": "success",
                                "timestamp": datetime.now().isoformat()
                            }))
                        except Exception as e:
                            logger.error(f"Error calling tool {tool_name}: {e}")
                            await websocket.send(json.dumps({
                                "id": request_id,
                                "type": "tool_response",
                                "tool": tool_name,
                                "status": "error",
                                "error": str(e),
                                "timestamp": datetime.now().isoformat()
                            }))
                    
                    # Check for command request
                    elif message_type == "command" or message_type == "mcp_command":
                        if "command" not in data or "params" not in data:
                            logger.error(f"Missing 'command' or 'params' in command request from client {client_id}: {data}")
                            await websocket.send(json.dumps({
                                "id": request_id,
                                "type": "error",
                                "error": "Command request must include 'command' and 'params'",
                                "timestamp": datetime.now().isoformat()
                            }))
                            continue
                            
                        command_name = data["command"]
                        params = data["params"]
                        
                        try:
                            # Try to find a tool with this name
                            if command_name in self.tools:
                                result = self.call_tool(command_name, params)
                                await websocket.send(json.dumps({
                                    "id": request_id,
                                    "type": "command_response",
                                    "result": result,
                                    "command": command_name,
                                    "status": "success",
                                    "timestamp": datetime.now().isoformat()
                                }))
                            else:
                                logger.error(f"Command {command_name} not found")
                                await websocket.send(json.dumps({
                                    "id": request_id,
                                    "type": "command_response",
                                    "command": command_name,
                                    "status": "error",
                                    "error": f"Command '{command_name}' not found",
                                    "timestamp": datetime.now().isoformat()
                                }))
                        except Exception as e:
                            logger.error(f"Error executing command {command_name}: {e}")
                            await websocket.send(json.dumps({
                                "id": request_id,
                                "type": "command_response",
                                "command": command_name,
                                "status": "error",
                                "error": str(e),
                                "timestamp": datetime.now().isoformat()
                            }))
                    
                    # Check for tool list request
                    elif message_type == "list_tools":
                        tools_info = self.get_tools_description()
                        await websocket.send(json.dumps({
                            "id": request_id,
                            "type": "tool_list",
                            "tools": tools_info,
                            "count": len(tools_info),
                            "timestamp": datetime.now().isoformat()
                        }))
                    
                    # Unknown message type
                    else:
                        logger.error(f"Unknown message type from client {client_id}: {message_type}")
                        await websocket.send(json.dumps({
                            "id": request_id,
                            "type": "error",
                            "error": f"Unknown message type: {message_type}",
                            "timestamp": datetime.now().isoformat()
                        }))
                except Exception as e:
                    logger.error(f"Error processing message from client {client_id}: {e}")
                    logger.error(traceback.format_exc())
                    try:
                        await websocket.send(json.dumps({
                            "type": "error",
                            "error": f"Server error: {str(e)}",
                            "traceback": traceback.format_exc(),
                            "timestamp": datetime.now().isoformat()
                        }))
                    except:
                        logger.error("Failed to send error message to client")
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Client {client_id} disconnected")
        finally:
            # Clean up the client when they disconnect
            if client_id in self.connected_clients:
                del self.connected_clients[client_id] 