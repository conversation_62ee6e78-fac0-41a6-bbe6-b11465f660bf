'use client';

import { Suspense } from 'react';

/**
 * Higher-order component that wraps a component with a Suspense boundary
 * This prevents the "useSearchParams() should be wrapped in a suspense boundary" warning
 * 
 * @param Component - The component to wrap
 * @returns A new component wrapped in a Suspense boundary
 */
export default function withSearchParamsProvider<P extends object>(
  Component: React.ComponentType<P>
): React.FC<P> {
  return function WithSearchParamsProvider(props: P) {
    return (
      <Suspense fallback={<div>Loading...</div>}>
        <Component {...props} />
      </Suspense>
    );
  };
}
