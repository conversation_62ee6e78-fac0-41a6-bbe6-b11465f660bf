/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable static export for Electron bundling
  output: 'export',
  trailingSlash: true,
  skipTrailingSlashRedirect: true,
  
  // Configure for static export
  distDir: 'out',
  
  // Basic configuration
  reactStrictMode: true,
  swcMinify: true,

  // Configure image domains for static export
  images: {
    unoptimized: true, // Required for static export
    domains: ['lh3.googleusercontent.com', 'avatars.githubusercontent.com', 'via.placeholder.com'],
  },

  // Disable eslint during build
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Skip type checking to speed up build  
  typescript: {
    ignoreBuildErrors: true
  },

  // Environment variables for bridge mode
  env: {
    // Bridge-specific environment
    NEXT_PUBLIC_BRIDGE_MODE: 'true',
    NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://ujiakzkncbxisdatygpo.supabase.co',
    NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'https://createlex.com',
    NEXT_PUBLIC_BACKEND_API_URL: process.env.NEXT_PUBLIC_BACKEND_API_URL || 'https://api.createlex.com',
  },

  // Configure static export behavior
  async exportPathMap(defaultPathMap) {
    return {
      '/': { page: '/' },
      '/login': { page: '/login' },
      '/dashboard': { page: '/dashboard' },
      // Add other static pages as needed
    };
  },

  // Configure headers for static files
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          { key: 'X-Bridge-Mode', value: 'true' },
        ],
      },
    ];
  },

  // Webpack configuration for Electron compatibility
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }
    return config;
  },
};

module.exports = nextConfig; 