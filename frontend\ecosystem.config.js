module.exports = {
  apps: [
    {
      name: 'createlex-frontend',
      script: 'server.js',
      instances: 'max',
      exec_mode: 'cluster',
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        NEXT_PUBLIC_BYPASS_AUTH: 'false',
        NEXT_PUBLIC_BYPASS_SUBSCRIPTION: 'false',
        NEXT_PUBLIC_API_URL: ''
      },
      env_development: {
        NODE_ENV: 'development',
        PORT: 3000,
        NEXT_PUBLIC_BYPASS_AUTH: 'true',
        NEXT_PUBLIC_BYPASS_SUBSCRIPTION: 'false',
        NEXT_PUBLIC_API_URL: 'http://localhost:5001'
      }
    }
  ]
};
