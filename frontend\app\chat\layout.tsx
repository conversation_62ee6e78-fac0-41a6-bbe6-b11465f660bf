'use client';

import { ChatSidebar } from "@/components/chat-sidebar";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Menu } from "lucide-react";
import dynamic from 'next/dynamic';

// Import the MCP initializer with dynamic import to avoid SSR issues
const McpInitializer = dynamic(() => import('@/components/mcp-initializer'), {
  ssr: false,
});

export default function ChatLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex h-dvh w-full overflow-hidden">
      {/* Initialize MCP bridge for Electron */}
      <McpInitializer />

      <ChatSidebar />
      <main className="flex-1 flex flex-col relative min-w-0">
        <div className="fixed top-4 left-4 z-50">
          <SidebarTrigger>
            <button className="flex items-center justify-center h-8 w-8 bg-muted hover:bg-accent rounded-md transition-colors shadow-sm border border-border/50">
              <Menu className="h-4 w-4" />
            </button>
          </SidebarTrigger>
        </div>
        <div className="flex-1 flex justify-center w-full overflow-hidden">
          {children}
        </div>
      </main>
    </div>
  );
}
