#!/bin/bash

echo "🐧 Setting up Linux Testing Environment for CreateLex Bridge"
echo "============================================================"

# Update package list
echo "📦 Updating package list..."
sudo apt update

# Install Node.js and npm
echo "📦 Installing Node.js and npm..."
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Python 3 and pip
echo "📦 Installing Python 3 and pip..."
sudo apt-get install -y python3 python3-pip python3-venv

# Install PyInstaller
echo "📦 Installing PyInstaller..."
pip3 install pyinstaller

# Install Python dependencies for MCP server
echo "📦 Installing Python dependencies..."
pip3 install Flask>=2.2.0 fastmcp>=1.0.0 requests>=2.28.0 uvicorn>=0.18.0 python-dotenv>=0.19.0 websockets>=10.0

# Copy project files to Linux home directory
echo "📂 Setting up project directory..."
mkdir -p ~/createlex-bridge-linux
cp -r /mnt/c/Dev/AiWebplatform/createlex-bridge/* ~/createlex-bridge-linux/

# Navigate to project directory
cd ~/createlex-bridge-linux

# Install npm dependencies
echo "📦 Installing npm dependencies..."
npm install

echo ""
echo "✅ Linux testing environment setup complete!"
echo ""
echo "🧪 To test:"
echo "   cd ~/createlex-bridge-linux"
echo "   npm run test-cross-platform"
echo "   npm run build:mcp-exe"
echo "   npm run build-linux-protected"
echo ""
echo "🔍 To check if executable works:"
echo "   ./src/python-protected/mcp_server_linux"
echo "" 