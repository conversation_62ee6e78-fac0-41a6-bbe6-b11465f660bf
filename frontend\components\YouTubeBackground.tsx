'use client';

import React, { useEffect, useRef, useState } from 'react';

interface YouTubeBackgroundProps {
  videoId: string;
  startTime?: number;
  endTime?: number;
  muted?: boolean;
  loop?: boolean;
  opacity?: number;
}

const YouTubeBackground: React.FC<YouTubeBackgroundProps> = ({
  videoId,
  startTime = 0,
  endTime,
  muted = true,
  loop = true,
  opacity = 0.7,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const playerRef = useRef<YT.Player | null>(null);
  const [isReady, setIsReady] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);

  // Load YouTube API
  useEffect(() => {
    // Only load the API once
    if (window.YT) {
      return;
    }

    // Create script element
    const tag = document.createElement('script');
    tag.src = 'https://www.youtube.com/iframe_api';

    // Add script to page
    const firstScriptTag = document.getElementsByTagName('script')[0];
    firstScriptTag.parentNode?.insertBefore(tag, firstScriptTag);

    // Define callback for when API is ready
    window.onYouTubeIframeAPIReady = () => {
      console.log('YouTube API ready');
      setIsReady(true);
    };

    // Cleanup
    return () => {
      window.onYouTubeIframeAPIReady = () => {};
    };
  }, []);

  // Initialize player when API is ready
  useEffect(() => {
    if (!isReady || !containerRef.current) return;

    console.log('Initializing YouTube player');

    // Calculate aspect ratio to maintain 16:9 while covering the container
    const calculateSize = () => {
      if (!containerRef.current) return { width: '100%', height: '100%' };

      const containerWidth = window.innerWidth;
      const containerHeight = window.innerHeight;

      // Calculate the size needed to cover the container while maintaining aspect ratio
      const aspectRatio = 16 / 9;
      let width, height;

      if (containerWidth / containerHeight > aspectRatio) {
        // Container is wider than video aspect ratio
        width = containerWidth;
        height = containerWidth / aspectRatio;
      } else {
        // Container is taller than video aspect ratio
        height = containerHeight;
        width = containerHeight * aspectRatio;
      }

      return { width, height };
    };

    const { width, height } = calculateSize();

    // Create player
    // @ts-ignore - The YT namespace is loaded at runtime
    playerRef.current = new window.YT.Player(containerRef.current, {
      videoId,
      playerVars: {
        autoplay: 1,
        controls: 0,
        disablekb: 1,
        enablejsapi: 1,
        iv_load_policy: 3,
        modestbranding: 1,
        showinfo: 0,
        rel: 0,
        loop: 0, // We'll handle looping manually
        start: startTime,
        mute: muted ? 1 : 0,
        playsinline: 1,
      },
      width: typeof width === 'number' ? width : '100%',
      height: typeof height === 'number' ? height : '100%',
      events: {
        onReady: (event) => {
          console.log('YouTube player ready');
          event.target.playVideo();
          setIsPlaying(true);
        },
        onStateChange: (event) => {
          // If video ends and loop is enabled, restart it
          // @ts-ignore - The YT namespace is loaded at runtime
          if (event.data === window.YT.PlayerState.ENDED && loop) {
            event.target.seekTo(startTime);
            event.target.playVideo();
          }

          // If we have an end time set, check if we need to loop
          // @ts-ignore - The YT namespace is loaded at runtime
          if (endTime && event.data === window.YT.PlayerState.PLAYING) {
            const checkTime = () => {
              if (!playerRef.current) return;

              const currentTime = playerRef.current.getCurrentTime();
              if (currentTime >= endTime) {
                playerRef.current.seekTo(startTime);
              } else {
                setTimeout(checkTime, 1000);
              }
            };

            checkTime();
          }
        },
        onError: (event) => {
          console.error('YouTube player error:', event.data);
        },
      },
    });

    // Handle window resize
    const handleResize = () => {
      if (!playerRef.current || !containerRef.current) return;

      const { width, height } = calculateSize();

      // Update player size
      if (typeof width === 'number' && typeof height === 'number') {
        playerRef.current.setSize(width, height);
      }
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);

      if (playerRef.current) {
        playerRef.current.destroy();
        playerRef.current = null;
      }
    };
  }, [isReady, videoId, startTime, endTime, muted, loop]);

  return (
    <div className="absolute top-0 left-0 w-full h-full overflow-hidden -z-10">
      <div
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none"
        style={{ opacity }}
      >
        <div ref={containerRef} />
      </div>
    </div>
  );
};

export default YouTubeBackground;

// Add YouTube Player API types
declare global {
  namespace YT {
    interface Player {
      destroy(): void;
      getCurrentTime(): number;
      getPlayerState(): number;
      playVideo(): void;
      seekTo(seconds: number): void;
      setSize(width: number, height: number): void;
    }

    interface PlayerEvent {
      target: Player;
      data: any;
    }

    interface PlayerOptions {
      videoId: string;
      width?: string | number;
      height?: string | number;
      playerVars?: {
        autoplay?: 0 | 1;
        controls?: 0 | 1;
        disablekb?: 0 | 1;
        enablejsapi?: 0 | 1;
        iv_load_policy?: 1 | 3;
        modestbranding?: 0 | 1;
        showinfo?: 0 | 1;
        rel?: 0 | 1;
        loop?: 0 | 1;
        start?: number;
        mute?: 0 | 1;
        playsinline?: 0 | 1;
        [key: string]: any;
      };
      events?: {
        onReady?: (event: PlayerEvent) => void;
        onStateChange?: (event: PlayerEvent) => void;
        onError?: (event: PlayerEvent) => void;
        [key: string]: ((event: PlayerEvent) => void) | undefined;
      };
    }

    enum PlayerState {
      UNSTARTED = -1,
      ENDED = 0,
      PLAYING = 1,
      PAUSED = 2,
      BUFFERING = 3,
      CUED = 5
    }

    interface PlayerConstructor {
      new(element: HTMLElement | string, options: PlayerOptions): Player;
    }
  }

  interface Window {
    YT: {
      Player: YT.PlayerConstructor;
      PlayerState: typeof YT.PlayerState;
    };
    onYouTubeIframeAPIReady: () => void;
  }
}
