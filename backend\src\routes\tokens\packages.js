const express = require('express');
const router = express.Router();

/**
 * @route GET /api/tokens/packages
 * @description Get available token packages
 * @access Private
 */
router.get('/', async (req, res) => {
  try {
    // Define token packages
    const tokenPackages = {
      small: {
        tokens: 100000, // 100K tokens
        price: 5.00,
        priceId: 'price_small_tokens'
      },
      medium: {
        tokens: 500000, // 500K tokens
        price: 20.00,
        priceId: 'price_medium_tokens'
      },
      large: {
        tokens: 1000000, // 1M tokens
        price: 35.00,
        priceId: 'price_large_tokens'
      }
    };
    
    return res.json(tokenPackages);
  } catch (error) {
    console.error('Error in token packages route:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

module.exports = router;
