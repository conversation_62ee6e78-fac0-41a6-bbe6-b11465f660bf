"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter, usePathname } from "next/navigation";
import { MessageSquare, PlusCircle, Trash2, Settings, Sparkles, LayoutDashboard, LogOut, ServerIcon } from "lucide-react";
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarMenuBadge,
    useSidebar
} from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import Image from "next/image";
import { ThemeToggle } from "./theme-toggle";
import { getUserId, getUserIdAsync } from "@/lib/user-id";
import { useChats } from "@/lib/hooks/use-chats";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Skeleton } from "@/components/ui/skeleton";
import { AnimatePresence, motion } from "motion/react";
import { MCPServerManager } from "./mcp-server-manager";
import { useMCP } from "@/lib/context/mcp-context";

export function ChatSidebar() {
    const router = useRouter();
    const pathname = usePathname();
    const [userId, setUserId] = useState<string>('');
    const [mcpSettingsOpen, setMcpSettingsOpen] = useState(false);
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const { state } = useSidebar();
    const isCollapsed = state === "collapsed";
    const dropdownRef = useRef<HTMLDivElement>(null);

    // Get MCP server data from context
    const { mcpServers, setMcpServers, selectedMcpServers, setSelectedMcpServers } = useMCP();

    // Initialize userId
    useEffect(() => {
        const initUserId = async () => {
            try {
                const id = await getUserIdAsync();
                setUserId(id);
            } catch (error) {
                console.error('Error getting user ID:', error);
                // Fallback to synchronous version
                setUserId(getUserId());
            }
        };

        initUserId();
    }, []);

    // Handle clicking outside the dropdown to close it and keyboard events
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setDropdownOpen(false);
            }
        };

        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key === 'Escape') {
                setDropdownOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        document.addEventListener('keydown', handleKeyDown);

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [dropdownRef]);

    // Use TanStack Query to fetch chats
    const { chats, isLoading, deleteChat } = useChats(userId);

    // Start a new chat
    const handleNewChat = () => {
        router.push('/chat');
    };

    // Delete a chat
    const handleDeleteChat = async (chatId: string, e: React.MouseEvent) => {
        e.stopPropagation();
        e.preventDefault();

        deleteChat(chatId);

        // If we're currently on the deleted chat's page, navigate to home
        if (pathname === `/chat/${chatId}`) {
            router.push('/');
        }
    };

    // Show loading state if user ID is not yet initialized
    if (!userId) {
        return null; // Or a loading spinner
    }

    // Create chat loading skeletons
    const renderChatSkeletons = () => {
        return Array(3).fill(0).map((_, index) => (
            <SidebarMenuItem key={`skeleton-${index}`}>
                <div className={`flex items-center gap-2 px-3 py-2 ${isCollapsed ? "justify-center" : ""}`}>
                    <Skeleton className="h-4 w-4 rounded-full" />
                    {!isCollapsed && (
                        <>
                            <Skeleton className="h-4 w-full max-w-[180px]" />
                            <Skeleton className="h-5 w-5 ml-auto rounded-md flex-shrink-0" />
                        </>
                    )}
                </div>
            </SidebarMenuItem>
        ));
    };

    return (
        <Sidebar className="shadow-sm bg-background/80 dark:bg-background/40 backdrop-blur-md z-50" collapsible="icon">
            <SidebarHeader className="p-4 border-b border-border/40">
                <div className="flex items-center justify-start">
                    <div className={`flex items-center gap-2 ${isCollapsed ? "justify-center w-full" : ""}`}>
                        <div className={`relative rounded-full bg-primary/70 flex items-center justify-center ${isCollapsed ? "size-5 p-3" : "size-6"}`}>
                            <Image src="/scira.png" alt="CreateLex Logo" width={24} height={24} className="absolute transform scale-75" unoptimized quality={100} />
                        </div>
                        {!isCollapsed && (
                            <div className="font-semibold text-lg text-foreground/90">CreateLex</div>
                        )}
                    </div>
                </div>
            </SidebarHeader>

            <SidebarContent className="flex flex-col h-[calc(100vh-8rem)]">
                <SidebarGroup className="flex-1 min-h-0">
                    <SidebarGroupLabel className={cn(
                        "px-4 text-xs font-medium text-muted-foreground/80 uppercase tracking-wider",
                        isCollapsed ? "sr-only" : ""
                    )}>
                        Chats
                    </SidebarGroupLabel>
                    <SidebarGroupContent className={cn(
                        "overflow-y-auto pt-1",
                        isCollapsed ? "overflow-x-hidden" : ""
                    )}>
                        <SidebarMenu>
                            {isLoading ? (
                                renderChatSkeletons()
                            ) : chats.length === 0 ? (
                                <div className={`flex items-center justify-center py-3 ${isCollapsed ? "" : "px-4"}`}>
                                    {isCollapsed ? (
                                        <div className="flex h-6 w-6 items-center justify-center rounded-md border border-border/50 bg-background/50">
                                            <MessageSquare className="h-3 w-3 text-muted-foreground" />
                                        </div>
                                    ) : (
                                        <div className="flex items-center gap-3 w-full px-3 py-2 rounded-md border border-dashed border-border/50 bg-background/50">
                                            <MessageSquare className="h-4 w-4 text-muted-foreground" />
                                            <span className="text-xs text-muted-foreground font-normal">No conversations yet</span>
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <AnimatePresence initial={false}>
                                    {chats.map((chat) => (
                                        <motion.div
                                            key={chat.id}
                                            initial={{ opacity: 0, height: 0, y: -10 }}
                                            animate={{ opacity: 1, height: "auto", y: 0 }}
                                            exit={{ opacity: 0, height: 0 }}
                                            transition={{ duration: 0.2 }}
                                        >
                                            <SidebarMenuItem>
                                                <SidebarMenuButton
                                                    asChild
                                                    tooltip={isCollapsed ? chat.title : undefined}
                                                    data-active={pathname === `/chat/${chat.id}`}
                                                    className={cn(
                                                        "transition-all hover:bg-primary/10 active:bg-primary/15",
                                                        pathname === `/chat/${chat.id}` ? "bg-secondary/60 hover:bg-secondary/60" : ""
                                                    )}
                                                >
                                                    <Link
                                                        href={`/chat/${chat.id}`}
                                                        className="flex items-center justify-between w-full gap-1"
                                                    >
                                                        <div className="flex items-center min-w-0 overflow-hidden flex-1 pr-2">
                                                            <MessageSquare className={cn(
                                                                "h-4 w-4 flex-shrink-0",
                                                                pathname === `/chat/${chat.id}` ? "text-foreground" : "text-muted-foreground"
                                                            )} />
                                                            {!isCollapsed && (
                                                                <span className={cn(
                                                                    "ml-2 truncate text-sm",
                                                                    pathname === `/chat/${chat.id}` ? "text-foreground font-medium" : "text-foreground/80"
                                                                )} title={chat.title}>
                                                                    {chat.title.length > 18 ? `${chat.title.slice(0, 18)}...` : chat.title}
                                                                </span>
                                                            )}
                                                        </div>
                                                        {!isCollapsed && (
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                className="h-6 w-6 text-muted-foreground hover:text-foreground flex-shrink-0"
                                                                onClick={(e) => handleDeleteChat(chat.id, e)}
                                                                title="Delete chat"
                                                            >
                                                                <Trash2 className="h-3.5 w-3.5" />
                                                            </Button>
                                                        )}
                                                    </Link>
                                                </SidebarMenuButton>
                                            </SidebarMenuItem>
                                        </motion.div>
                                    ))}
                                </AnimatePresence>
                            )}
                        </SidebarMenu>
                    </SidebarGroupContent>
                </SidebarGroup>

                <div className="relative my-0">
                    <div className="absolute inset-x-0">
                        <Separator className="w-full h-px bg-border/40" />
                    </div>
                </div>

                <SidebarGroup className="flex-shrink-0">
                    <SidebarGroupLabel className={cn(
                        "px-4 pt-0 text-xs font-medium text-muted-foreground/80 uppercase tracking-wider",
                        isCollapsed ? "sr-only" : ""
                    )}>
                        Servers
                    </SidebarGroupLabel>
                    <SidebarGroupContent>
                        <SidebarMenu>
                            <SidebarMenuItem>
                                <SidebarMenuButton
                                    onClick={() => setMcpSettingsOpen(true)}
                                    className={cn(
                                        "w-full flex items-center gap-2 transition-all",
                                        "hover:bg-secondary/50 active:bg-secondary/70"
                                    )}
                                    tooltip={isCollapsed ? "Servers" : undefined}
                                >
                                    <ServerIcon className={cn(
                                        "h-4 w-4 flex-shrink-0",
                                        selectedMcpServers.length > 0 ? "text-primary" : "text-muted-foreground"
                                    )} />
                                    {!isCollapsed && (
                                        <span className="flex-grow text-sm text-foreground/80">CreateLex Servers</span>
                                    )}
                                    {selectedMcpServers.length > 0 && !isCollapsed ? (
                                        <Badge
                                            variant="secondary"
                                            className="ml-auto text-[10px] px-1.5 py-0 h-5 bg-secondary/80"
                                        >
                                            {selectedMcpServers.length}
                                        </Badge>
                                    ) : selectedMcpServers.length > 0 && isCollapsed ? (
                                        <SidebarMenuBadge className="bg-secondary/80 text-secondary-foreground">
                                            {selectedMcpServers.length}
                                        </SidebarMenuBadge>
                                    ) : null}
                                </SidebarMenuButton>
                            </SidebarMenuItem>
                        </SidebarMenu>
                    </SidebarGroupContent>
                </SidebarGroup>
            </SidebarContent>

            <SidebarFooter className="p-4 border-t border-border/40 mt-auto">
                <div className={`flex flex-col ${isCollapsed ? "items-center" : ""} gap-3`}>
                    <motion.div
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                    >
                        <Button
                            variant="default"
                            className={cn(
                                "w-full bg-primary text-primary-foreground hover:bg-primary/90",
                                isCollapsed ? "w-8 h-8 p-0" : ""
                            )}
                            onClick={handleNewChat}
                            title={isCollapsed ? "New Chat" : undefined}
                        >
                            <PlusCircle className={`${isCollapsed ? "" : "mr-2"} h-4 w-4`} />
                            {!isCollapsed && <span>New Chat</span>}
                        </Button>
                    </motion.div>

                    <div className="relative" ref={dropdownRef}>
                        {isCollapsed ? (
                            <Button
                                variant="ghost"
                                className="w-8 h-8 p-0 flex items-center justify-center"
                                onClick={() => setDropdownOpen(!dropdownOpen)}
                            >
                                <Avatar className="h-6 w-6 rounded-xl bg-secondary/60">
                                    <AvatarFallback className="rounded-xl text-xs font-medium text-secondary-foreground">
                                        {userId.substring(0, 2).toUpperCase()}
                                    </AvatarFallback>
                                </Avatar>
                            </Button>
                        ) : (
                            <Button
                                variant="outline"
                                className="w-full justify-between font-normal bg-transparent border border-border/60 shadow-none px-2 h-10 hover:bg-secondary/50"
                                onClick={() => setDropdownOpen(!dropdownOpen)}
                            >
                                <div className="flex items-center gap-2">
                                    <Avatar className="h-7 w-7 rounded-xl bg-secondary/60">
                                        <AvatarFallback className="rounded-xl text-sm font-medium text-secondary-foreground">
                                            {userId.substring(0, 2).toUpperCase()}
                                        </AvatarFallback>
                                    </Avatar>
                                    <div className="grid text-left text-sm leading-tight">
                                        <span className="truncate font-medium text-foreground/90">
                                            CreateLex AI
                                        </span>
                                        <span className="truncate text-xs text-muted-foreground">{userId.substring(0, 16)}...</span>
                                    </div>
                                </div>
                            </Button>
                        )}

                        {dropdownOpen && (
                            <div className="fixed inset-0 bg-black/20 backdrop-blur-sm z-[9998] animate-in fade-in duration-200" onClick={() => setDropdownOpen(false)}>
                                <div
                                    className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 rounded-2xl shadow-xl bg-popover border border-border z-[9999] animate-in zoom-in-95 duration-200"
                                    onClick={(e) => e.stopPropagation()}
                                >
                                    <div className="p-4">
                                        <div className="flex items-center gap-3 px-2 py-3 text-left text-sm border-b border-border/50 mb-3">
                                            <Avatar className="h-10 w-10 rounded-xl bg-secondary/60">
                                                <AvatarFallback className="rounded-xl text-sm font-medium text-secondary-foreground">
                                                    {userId.substring(0, 2).toUpperCase()}
                                                </AvatarFallback>
                                            </Avatar>
                                            <div className="grid flex-1 text-left text-sm leading-tight">
                                                <span className="truncate font-semibold text-foreground/90 text-base">CreateLex AI</span>
                                                <span className="truncate text-xs text-muted-foreground">{userId}</span>
                                            </div>
                                        </div>

                                        <div className="space-y-1">
                                            <button
                                                className="flex w-full items-center gap-2 rounded-xl px-3 py-2 text-sm hover:bg-secondary/80 cursor-pointer transition-colors"
                                                onClick={() => {
                                                    window.location.href = "/dashboard";
                                                }}
                                            >
                                                <LayoutDashboard className="h-4 w-4 mr-2" />
                                                Dashboard
                                            </button>

                                            <button
                                                className="flex w-full items-center gap-2 rounded-xl px-3 py-2 text-sm hover:bg-secondary/80 cursor-pointer transition-colors"
                                                onClick={() => {
                                                    // First sign out from Supabase
                                                    const signOut = async () => {
                                                      try {
                                                        // Clear local storage first
                                                        if (typeof window !== 'undefined') {
                                                          // Clear Supabase session storage
                                                          localStorage.removeItem('sb-ujiakzkncbxisdatygpo-auth-token');

                                                          // Clear our custom storage
                                                          localStorage.removeItem('ai-chat-user-id');
                                                          localStorage.removeItem('ai-chat-user-id-source');
                                                          localStorage.removeItem('ai-chat-auth-token');
                                                          localStorage.removeItem('ai-chat-refresh-token');

                                                          // Clear any other potential auth-related items
                                                          localStorage.removeItem('supabase.auth.token');
                                                          localStorage.removeItem('supabase-auth-token');
                                                        }

                                                        // Clear cookies if possible
                                                        if (typeof document !== 'undefined') {
                                                          document.cookie = 'sb-access-token=; Max-Age=0; path=/; domain=' + window.location.hostname;
                                                          document.cookie = 'sb-refresh-token=; Max-Age=0; path=/; domain=' + window.location.hostname;
                                                        }

                                                        // Import the signOutAndClearStorage function from supabase-singleton.ts
                                                        try {
                                                          const { signOutAndClearStorage } = await import('@/lib/supabase-singleton');
                                                          await signOutAndClearStorage();
                                                        } catch (signOutError) {
                                                          console.warn('Error during signOutAndClearStorage, continuing with redirect:', signOutError);
                                                        }

                                                        // Then redirect to the login page
                                                        console.log('Redirecting to login page after sign out');
                                                        window.location.href = "/login";
                                                      } catch (error) {
                                                        console.error('Error signing out:', error);
                                                        // Redirect anyway
                                                        window.location.href = "/login";
                                                      }
                                                    };
                                                    signOut();
                                                }}
                                            >
                                                <LogOut className="h-4 w-4 mr-2" />
                                                Sign Out
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                <MCPServerManager
                    servers={mcpServers}
                    onServersChange={setMcpServers}
                    selectedServers={selectedMcpServers}
                    onSelectedServersChange={setSelectedMcpServers}
                    open={mcpSettingsOpen}
                    onOpenChange={setMcpSettingsOpen}
                />
            </SidebarFooter>
        </Sidebar>
    );
}
