import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

interface NavbarProps {
  isScrolled: boolean;
  isAuthenticated: boolean;
}

const Navbar: React.FC<NavbarProps> = ({ isScrolled, isAuthenticated }) => {
  const router = useRouter();

  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled ? 'bg-gray-900/90 backdrop-blur-sm shadow-md py-2' : 'bg-transparent py-4'
    }`}>
      <div className="container mx-auto px-4 flex justify-between items-center">
        <Link href="/" className="flex items-center">
          <div className="relative h-10 w-10 mr-2">
            <Image
              src="/createlexlogo.png"
              alt="CreateLex Logo"
              fill
              style={{ objectFit: 'contain' }}
              priority
            />
          </div>
          <span className={`font-bold text-xl ${isScrolled ? 'text-blue-400' : 'text-white'}`}>
            CreateLex
          </span>
        </Link>

        <div className="hidden md:flex space-x-6">
          <Link
            href="/products"
            className={`font-medium transition-colors ${
              isScrolled ? 'text-gray-200 hover:text-blue-400' : 'text-white hover:text-blue-200'
            }`}
          >
            Products
          </Link>
          <NavLink href="#features" label="Features" isScrolled={isScrolled} />
          <NavLink href="#demo" label="Demo" isScrolled={isScrolled} />
          <NavLink href="#pricing" label="Pricing" isScrolled={isScrolled} />
          <Link href="/download" className={`font-medium transition-colors ${
            isScrolled ? 'text-gray-200 hover:text-blue-400' : 'text-white hover:text-blue-200'
          }`}>
            Download
          </Link>
          <Link href="/faq" className={`font-medium transition-colors ${
            isScrolled ? 'text-gray-200 hover:text-blue-400' : 'text-white hover:text-blue-200'
          }`}>
            FAQ
          </Link>
          <Link href="/contact" className={`font-medium transition-colors ${
            isScrolled ? 'text-gray-200 hover:text-blue-400' : 'text-white hover:text-blue-200'
          }`}>
            Contact
          </Link>
        </div>

        <div className="flex items-center space-x-4">
          {isAuthenticated ? (
            <Link
              href="/dashboard"
              className={`px-4 py-2 rounded-md transition-all inline-block ${
                isScrolled
                  ? 'bg-gradient-to-r from-blue-600 to-blue-400 text-white hover:from-blue-500 hover:to-blue-300 shadow-lg hover:shadow-blue-500/50'
                  : 'bg-white text-blue-600 hover:bg-gray-100'
              }`}
            >
              Dashboard
            </Link>
          ) : (
            <>
              <Link
                href="/login"
                className={`px-4 py-2 rounded-md transition-colors inline-block ${
                  isScrolled
                    ? 'border border-blue-400 text-blue-400 hover:bg-blue-900/30'
                    : 'border border-white text-white hover:bg-white hover:bg-opacity-10'
                }`}
              >
                Login
              </Link>
              <Link
                href="/signup"
                className={`px-4 py-2 rounded-md transition-all inline-block ${
                  isScrolled
                    ? 'bg-gradient-to-r from-blue-600 to-blue-400 text-white hover:from-blue-500 hover:to-blue-300 shadow-lg hover:shadow-blue-500/50'
                    : 'bg-white text-blue-600 hover:bg-gray-100'
                }`}
              >
                Sign Up
              </Link>
            </>
          )}
        </div>
      </div>
    </nav>
  );
};

interface NavLinkProps {
  href: string;
  label: string;
  isScrolled: boolean;
}

const NavLink: React.FC<NavLinkProps> = ({ href, label, isScrolled }) => (
  <a
    href={href}
    className={`font-medium transition-colors ${
      isScrolled ? 'text-gray-200 hover:text-blue-400' : 'text-white hover:text-blue-200'
    }`}
  >
    {label}
  </a>
);

export default Navbar;
