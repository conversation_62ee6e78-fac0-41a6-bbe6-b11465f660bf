const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs-extra');

(async () => {
  try {
    const frontendDir = path.resolve(__dirname, '..', '..', 'frontend');
    const destDir = path.resolve(__dirname, '..', 'web');

    console.log('📦 Building simple frontend for Electron bridge...');
    
    // Ensure destination directory exists (but don't empty it)
    await fs.ensureDir(destDir);

    console.log('🚚 Using pre-built static files...');
    
    // Create basic login page directory
    const loginDir = path.join(destDir, 'login');
    await fs.ensureDir(loginDir);
    
    // Always write the login page
    const loginFile = path.join(loginDir, 'index.html');
    console.log('📝 Writing login page...');
    await fs.writeFile(loginFile, `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CreateLex Bridge - Login</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #333;
    }

    .login-container {
      background: white;
      border-radius: 12px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      padding: 40px;
      width: 100%;
      max-width: 420px;
      text-align: center;
    }

    .logo {
      width: 64px;
      height: 64px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 16px;
      margin: 0 auto 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-weight: bold;
      color: white;
    }

    h1 {
      font-size: 28px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #1a202c;
    }

    .subtitle {
      color: #718096;
      margin-bottom: 32px;
      font-size: 16px;
    }

    .oauth-button {
      width: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 16px;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    .oauth-button:hover {
      transform: translateY(-1px);
      box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    }

    .oauth-button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    .status {
      background: #f0fdf4;
      border: 1px solid #bbf7d0;
      color: #16a34a;
      padding: 12px;
      border-radius: 8px;
      margin: 20px 0;
      font-size: 14px;
      display: none;
    }

    .error {
      background: #fef2f2;
      border: 1px solid #fecaca;
      color: #dc2626;
    }

    .loading {
      display: none;
      margin-top: 16px;
      color: #667eea;
    }

    .spinner {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 8px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="login-container">
    <div class="logo">CL</div>
    <h1>Welcome to CreateLex</h1>
    <p class="subtitle">Sign in to access your MCP bridge</p>

    <button id="oauthBtn" class="oauth-button">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
      </svg>
      Sign in with CreateLex
    </button>

    <!-- Loading State -->
    <div id="loading" class="loading">
      <span class="spinner"></span>
      <span>Opening browser for authentication...</span>
    </div>

    <!-- Messages -->
    <div id="message" class="status"></div>
  </div>

  <script>
    // Check if we're in Electron
    const isElectron = typeof window !== 'undefined' && window.electronAPI;

    const oauthBtn = document.getElementById('oauthBtn');
    const loading = document.getElementById('loading');
    const message = document.getElementById('message');

    function showMessage(text, isError = false) {
      message.textContent = text;
      message.className = isError ? 'status error' : 'status';
      message.style.display = 'block';
    }

    function showLoading() {
      loading.style.display = 'block';
      oauthBtn.disabled = true;
    }

    function hideLoading() {
      loading.style.display = 'none';
      oauthBtn.disabled = false;
    }

    // OAuth authentication
    oauthBtn.addEventListener('click', async () => {
      if (!isElectron) {
        showMessage('OAuth authentication is only available in the desktop app', true);
        return;
      }

      showLoading();

      try {
        const result = await window.electronAPI.authenticateOAuth();
        
        if (result.success) {
          showMessage('Authentication successful! Opening local dashboard...');
          // The main process will handle showing the local dashboard
        } else {
          hideLoading();
          showMessage(result.error || 'Authentication failed', true);
        }
      } catch (err) {
        hideLoading();
        showMessage('Authentication error: ' + err.message, true);
      }
    });

    // Auto-start OAuth if in Electron
    if (isElectron) {
      // Check if already authenticated
      window.electronAPI.checkAuthStatus().then(status => {
        if (status.authenticated) {
          showMessage('Already authenticated. You can close this window.');
        }
      }).catch(() => {
        // Ignore errors
      });
    } else {
      showMessage('This is the CreateLex Bridge login page. Please use the desktop application.', true);
    }
  </script>
</body>
</html>`);

    // Create auth-callback directory and page
    const authCallbackDir = path.join(destDir, 'auth-callback');
    await fs.ensureDir(authCallbackDir);
    
    // Always write the auth callback page
    const authCallbackFile = path.join(authCallbackDir, 'index.html');
    console.log('📝 Writing auth callback page...');
    await fs.writeFile(authCallbackFile, `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CreateLex Bridge - Authentication Success</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #333;
    }

    .callback-container {
      background: white;
      border-radius: 12px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      padding: 40px;
      width: 100%;
      max-width: 500px;
      text-align: center;
    }

    .logo {
      width: 64px;
      height: 64px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 16px;
      margin: 0 auto 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-weight: bold;
      color: white;
    }

    .success-icon {
      color: #16a34a;
      font-size: 48px;
      margin-bottom: 20px;
    }

    .processing-icon {
      color: #667eea;
      font-size: 48px;
      margin-bottom: 20px;
    }

    h1 {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 12px;
      color: #1a202c;
    }

    .message {
      color: #6b7280;
      margin-bottom: 24px;
      font-size: 16px;
      line-height: 1.5;
    }

    .status {
      background: #f0fdf4;
      border: 1px solid #bbf7d0;
      color: #16a34a;
      padding: 12px;
      border-radius: 8px;
      margin: 20px 0;
      font-size: 14px;
    }

    .info {
      background: #eff6ff;
      border: 1px solid #bfdbfe;
      color: #1d4ed8;
    }

    .error {
      background: #fef2f2;
      border: 1px solid #fecaca;
      color: #dc2626;
    }

    .spinner {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 8px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .button {
      background: #667eea;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 16px;
      font-weight: 500;
      transition: all 0.2s ease;
      margin: 8px;
    }

    .button:hover {
      opacity: 0.9;
      transform: translateY(-1px);
    }

    .button-secondary {
      background: #6b7280;
    }
  </style>
</head>
<body>
  <div class="callback-container">
    <div class="logo">CL</div>
    
    <div id="loadingState">
      <div class="processing-icon">
        <span class="spinner"></span>
      </div>
      <h1>Processing Authentication...</h1>
      <p class="message">Please wait while we complete your login to the CreateLex Bridge.</p>
    </div>

    <div id="successState" style="display: none;">
      <div class="success-icon">✅</div>
      <h1>Authentication Successful!</h1>
      <p class="message">You have been successfully authenticated with the CreateLex Bridge.</p>
      
      <div class="status">
        <strong>✓ User authenticated</strong><br>
        <span id="userInfo"></span>
      </div>

      <div class="status info">
        🎯 Redirecting to local bridge dashboard...
      </div>

      <p class="message">
        The CreateLex Bridge application will open automatically.
        You can close this browser window.
      </p>

      <button class="button" onclick="closeWindow()">Close Window</button>
    </div>

    <div id="errorState" style="display: none;">
      <div style="color: #dc2626; font-size: 48px; margin-bottom: 20px;">❌</div>
      <h1>Authentication Error</h1>
      <p class="message">There was an error processing your authentication.</p>
      
      <div class="status error">
        <span id="errorMessage"></span>
      </div>

      <button class="button" onclick="retryAuth()">Try Again</button>
      <button class="button button-secondary" onclick="closeWindow()">Close Window</button>
    </div>
  </div>

  <script>
    console.log('🔗 CreateLex Bridge Auth Callback Page Loaded');
    
    // Parse URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    const userId = urlParams.get('userId');
    const email = urlParams.get('email');
    const hasSubscription = urlParams.get('hasSubscription') === 'true';
    const error = urlParams.get('error');

    console.log('📥 Received auth data:', { token: !!token, userId, email, hasSubscription, error });

    // Check if this is the expected callback
    if (token && userId) {
      // Success case
      processAuthSuccess(token, userId, email, hasSubscription);
    } else if (error) {
      // Error case
      showError(error);
    } else {
      // No expected parameters - might be a direct access
      showError('Invalid authentication callback. Missing required parameters. Please try logging in again.');
    }

    async function processAuthSuccess(token, userId, email, hasSubscription) {
      try {
        console.log('✅ Processing successful authentication');
        
        // Update UI with user info
        document.getElementById('userInfo').innerHTML = \`
          User ID: \${userId}<br>
          Email: \${email || 'Not provided'}<br>
          Subscription: \${hasSubscription ? 'Active ✅' : 'None ❌'}
        \`;

        // Send auth data to bridge callback server
        const authData = {
          token,
          userId,
          email,
          hasSubscription,
          timestamp: new Date().toISOString()
        };

        console.log('📤 Sending auth data to bridge server...');

        // Try to notify the bridge via fetch to the callback server
        try {
          const response = await fetch('http://localhost:7891/auth/success', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(authData)
          });

          if (response.ok) {
            console.log('✅ Successfully notified bridge server');
            const result = await response.json();
            console.log('📨 Bridge server response:', result);
          } else {
            console.warn('⚠️ Bridge notification failed:', response.status);
            throw new Error(\`Bridge server responded with status \${response.status}\`);
          }
        } catch (fetchError) {
          console.error('❌ Could not reach bridge callback server:', fetchError);
          
          // Show a different message if the bridge server is not available
          showError('Bridge application not found. Please make sure the CreateLex Bridge application is running and try the authentication process again.');
          return;
        }

        // Show success state
        document.getElementById('loadingState').style.display = 'none';
        document.getElementById('successState').style.display = 'block';

        // Auto-close after 5 seconds
        setTimeout(() => {
          console.log('⏰ Auto-closing window...');
          closeWindow();
        }, 5000);

      } catch (err) {
        console.error('❌ Error processing auth success:', err);
        showError('Failed to process authentication: ' + err.message);
      }
    }

    function showError(message) {
      console.error('❌ Auth error:', message);
      document.getElementById('errorMessage').textContent = message;
      document.getElementById('loadingState').style.display = 'none';
      document.getElementById('errorState').style.display = 'block';
    }

    function closeWindow() {
      console.log('🔄 Attempting to close window...');
      
      // Try multiple methods to close the window
      if (window.opener) {
        window.opener.postMessage({
          type: 'CREATELEX_AUTH_COMPLETE',
          success: true
        }, '*');
      }
      
      // Try to close the window
      window.close();
      
      // If window.close() doesn't work, show instructions
      setTimeout(() => {
        console.log('ℹ️ Window close failed, showing instructions');
        alert('Please close this browser tab and return to the CreateLex Bridge application.');
      }, 1000);
    }

    function retryAuth() {
      console.log('🔄 Retrying authentication...');
      // Redirect back to login
      window.location.href = 'https://createlex.com/login?redirect=' + 
        encodeURIComponent(window.location.href.split('?')[0]) + '&source=bridge';
    }

    // Handle messages from parent window
    window.addEventListener('message', (event) => {
      if (event.data.type === 'CREATELEX_AUTH_REQUEST') {
        console.log('📨 Received auth request from parent window');
        // Send current auth data if available
        if (token && userId) {
          event.source.postMessage({
            type: 'CREATELEX_AUTH_RESPONSE',
            success: true,
            token,
            userId,
            email,
            hasSubscription
          }, event.origin);
        }
      }
    });

    // Add some debugging info
    console.log('🔧 Debug Info:');
    console.log('- Current URL:', window.location.href);
    console.log('- URL Parameters:', Object.fromEntries(urlParams));
    console.log('- Window Origin:', window.location.origin);
  </script>
</body>
</html>`);

    // Create basic dashboard structure
    const dashboardDir = path.join(destDir, 'dashboard');
    await fs.ensureDir(dashboardDir);
    
    const dashboardFile = path.join(dashboardDir, 'index.html');
    if (!(await fs.pathExists(dashboardFile))) {
      console.log('📝 Creating basic dashboard page...');
      await fs.writeFile(dashboardFile, `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CreateLex Bridge - Dashboard</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 40px;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }
    h1 { color: #333; margin-bottom: 20px; }
    .status { padding: 12px; border-radius: 6px; margin: 20px 0; }
    .success { background: #f0fdf4; border: 1px solid #bbf7d0; color: #16a34a; }
    .info { background: #eff6ff; border: 1px solid #bfdbfe; color: #1d4ed8; }
    button {
      background: #667eea;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      margin: 8px;
    }
    button:hover { opacity: 0.9; }
  </style>
</head>
<body>
  <div class="container">
    <h1>CreateLex Bridge Dashboard</h1>
    
    <div class="status success">
      ✅ Bridge authentication successful
    </div>

    <div class="status info">
      📡 This is a simplified dashboard for the CreateLex Bridge. 
      For full functionality, please visit <a href="https://createlex.com/dashboard" target="_blank">createlex.com/dashboard</a>
    </div>

    <div>
      <button onclick="startMCP()">Start MCP Server</button>
      <button onclick="stopMCP()">Stop MCP Server</button>
      <button onclick="checkStatus()">Check Status</button>
    </div>

    <div id="status"></div>
  </div>

  <script>
    const isElectron = typeof window !== 'undefined' && window.electronAPI;
    const statusDiv = document.getElementById('status');

    async function startMCP() {
      if (!isElectron) {
        statusDiv.innerHTML = '<div class="status info">MCP controls only available in desktop app</div>';
        return;
      }

      try {
        const result = await window.electronAPI.startMcp();
        statusDiv.innerHTML = \`<div class="status \${result.success ? 'success' : 'error'}">
          \${result.success ? '✅ MCP Server started' : '❌ Failed to start MCP Server: ' + result.error}
        </div>\`;
      } catch (err) {
        statusDiv.innerHTML = '<div class="status error">❌ Error: ' + err.message + '</div>';
      }
    }

    async function stopMCP() {
      if (!isElectron) return;

      try {
        const result = await window.electronAPI.stopMcp();
        statusDiv.innerHTML = \`<div class="status \${result.success ? 'success' : 'error'}">
          \${result.success ? '✅ MCP Server stopped' : '❌ Failed to stop MCP Server: ' + result.error}
        </div>\`;
      } catch (err) {
        statusDiv.innerHTML = '<div class="status error">❌ Error: ' + err.message + '</div>';
      }
    }

    async function checkStatus() {
      if (!isElectron) return;

      try {
        const status = await window.electronAPI.getMcpStatus();
        statusDiv.innerHTML = \`<div class="status info">
          📊 MCP Server Status: \${status.running ? 'Running' : 'Stopped'}<br/>
          Port: \${status.port || 'N/A'}<br/>
          PID: \${status.pid || 'N/A'}
        </div>\`;
      } catch (err) {
        statusDiv.innerHTML = '<div class="status error">❌ Error: ' + err.message + '</div>';
      }
    }

    // Check status on load
    if (isElectron) {
      checkStatus();
    }
  </script>
</body>
</html>
      `);
    }

    // Always write the files to ensure they have the latest content
    console.log('📝 Writing all HTML files...');

    // Verify all critical files exist
    const filesToCheck = [
      { path: path.join(destDir, 'login', 'index.html'), name: 'Login page' },
      { path: path.join(destDir, 'auth-callback', 'index.html'), name: 'Auth callback page' },
      { path: path.join(destDir, 'dashboard', 'index.html'), name: 'Dashboard page' }
    ];

    for (const file of filesToCheck) {
      if (await fs.pathExists(file.path)) {
        console.log(`✅ ${file.name} written successfully`);
      } else {
        console.warn(`⚠️ ${file.name} not found at ${file.path}`);
      }
    }

    console.log('✅ Basic frontend structure ready');
    console.log('💡 OAuth flow: createlex.com/login → local auth-callback → local dashboard');
    console.log('📱 This provides offline functionality after initial authentication');
    
  } catch (err) {
    console.error('❌ Failed to create simple frontend', err);
    process.exit(1);
  }
})(); 