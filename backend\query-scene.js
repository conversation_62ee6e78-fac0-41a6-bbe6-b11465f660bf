const McpBridge = require('./src/services/mcpBridge');

async function queryScene() {
  const bridge = McpBridge.getInstance();
  
  console.log('Sending get_all_scene_objects command...');
  
  bridge.sendCommand({
    command: 'get_all_scene_objects',
    params: {}
  }, (err, res) => {
    if (err) {
      console.error('Error querying scene:', err);
    } else {
      console.log('Scene query result:');
      console.log(JSON.stringify(res, null, 2));
    }
    
    // Exit after 2 seconds to allow any async operations to complete
    setTimeout(() => process.exit(), 2000);
  });
}

queryScene();
