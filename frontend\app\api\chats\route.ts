import { NextResponse } from "next/server";
import { getChats } from "@/lib/chat-store";
import { nanoid } from "nanoid";
import { supabaseAdmin } from "@/lib/supabase";

export async function GET(request: Request) {
  try {
    const userId = request.headers.get('x-user-id');

    if (!userId) {
      return NextResponse.json({ error: "User ID is required" }, {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    console.log(`API route: Getting chats for user ${userId}`);

    try {
      const chats = await getChats(userId);
      console.log(`API route: Successfully retrieved ${chats.length} chats for user ${userId}`);
      return NextResponse.json(chats, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
    } catch (chatsError) {
      console.error(`API route: Error getting chats:`, chatsError);
      // Return an empty array as a fallback
      return NextResponse.json([], {
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
  } catch (error) {
    console.error("Error in GET chats API route:", error);
    return NextResponse.json(
      { error: "Failed to fetch chats" },
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}

export async function POST(request: Request) {
  try {
    const userId = request.headers.get('x-user-id');

    if (!userId) {
      return NextResponse.json({ error: "User ID is required" }, {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    const body = await request.json();
    const chatId = body.id || nanoid();
    const title = body.title || 'New Chat';

    console.log(`API route: Creating new chat with ID: ${chatId}, userId: ${userId}, title: ${title}`);

    try {
      // First check if the chat already exists
      const { data: existingChat, error: checkError } = await supabaseAdmin
        .from('chats')
        .select('*')
        .eq('id', chatId)
        .single();

      if (existingChat) {
        console.log(`API route: Chat with ID ${chatId} already exists, returning existing chat`);

        // Map Supabase field names to our schema
        const chat = {
          id: existingChat.id,
          userId: existingChat.user_id,
          title: existingChat.title,
          createdAt: new Date(existingChat.created_at),
          updatedAt: new Date(existingChat.updated_at),
          messages: []
        };

        return NextResponse.json(chat, {
          headers: {
            'Content-Type': 'application/json'
          }
        });
      }

      // Create a new chat using the admin client to bypass RLS policies
      const { data, error } = await supabaseAdmin
        .from('chats')
        .insert({
          id: chatId,
          user_id: userId,
          title: title,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        console.error("API route: Error creating chat in Supabase:", error);

        // If the error is a duplicate key error, try to get the existing chat
        if (error.code === '23505') {
          const { data: existingChat, error: fetchError } = await supabaseAdmin
            .from('chats')
            .select('*')
            .eq('id', chatId)
            .single();

          if (!fetchError && existingChat) {
            console.log(`API route: Retrieved existing chat with ID ${chatId} after duplicate key error`);

            // Map Supabase field names to our schema
            const chat = {
              id: existingChat.id,
              userId: existingChat.user_id,
              title: existingChat.title,
              createdAt: new Date(existingChat.created_at),
              updatedAt: new Date(existingChat.updated_at),
              messages: []
            };

            return NextResponse.json(chat, {
              headers: {
                'Content-Type': 'application/json'
              }
            });
          }
        }

        // If we can't retrieve the existing chat, create a mock chat as a fallback
        console.log(`API route: Creating mock chat as fallback for ID ${chatId}`);
        const mockChat = {
          id: chatId,
          userId: userId,
          title: title,
          createdAt: new Date(),
          updatedAt: new Date(),
          messages: []
        };

        return NextResponse.json(mockChat, {
          headers: {
            'Content-Type': 'application/json'
          }
        });
      }

      console.log(`API route: Successfully created chat with ID ${chatId}`);

      // Map Supabase field names to our schema
      const chat = {
        id: data.id,
        userId: data.user_id,
        title: data.title,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
        messages: []
      };

      return NextResponse.json(chat, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
    } catch (supabaseError) {
      console.error("API route: Error in Supabase operations:", supabaseError);

      // Create a mock chat as a fallback
      const mockChat = {
        id: chatId,
        userId: userId,
        title: title,
        createdAt: new Date(),
        updatedAt: new Date(),
        messages: []
      };

      return NextResponse.json(mockChat, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
  } catch (error) {
    console.error("Error in POST chat API route:", error);
    return NextResponse.json(
      { error: "Failed to create chat" },
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}