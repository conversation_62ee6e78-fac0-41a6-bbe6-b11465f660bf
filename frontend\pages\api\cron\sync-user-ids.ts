import { NextApiRequest, NextApiResponse } from 'next';

/**
 * API endpoint for scheduled tasks to sync user IDs
 * This endpoint is meant to be called by a cron job
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Verify the request is authorized
  // In a production environment, you would use a secret token
  const authHeader = req.headers.authorization;
  const expectedToken = process.env.CRON_SECRET || 'default-cron-secret';
  
  if (authHeader !== `Bearer ${expectedToken}`) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  
  try {
    // Call the auto-sync-user-ids API
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001'}/api/auto-sync-user-ids`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      console.error('CRON: Error triggering auto-sync:', response.statusText);
      return res.status(500).json({ error: 'Error triggering auto-sync' });
    }
    
    const data = await response.json();
    
    if (data.success) {
      console.log('CRON: Auto-sync completed successfully:', data);
      return res.status(200).json({ success: true, data });
    } else {
      console.error('CRON: Auto-sync failed:', data);
      return res.status(500).json({ error: 'Auto-sync failed', data });
    }
  } catch (error) {
    console.error('CRON: Error in scheduled sync-user-ids:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
