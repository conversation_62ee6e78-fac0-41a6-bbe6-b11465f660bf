import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../lib/supabase';
import fs from 'fs';
import path from 'path';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Read the SQL script
    const sqlScript = fs.readFileSync(path.join(process.cwd(), 'supabase/migrations/fix_tables.sql'), 'utf8');
    
    // Execute the SQL script
    const { data, error } = await supabase.rpc('exec_sql', { sql: sqlScript });
    
    if (error) {
      console.error('Error executing SQL script:', error);
      return res.status(500).json({ error: 'Error executing SQL script', details: error });
    }
    
    return res.status(200).json({ success: true, message: 'Tables fixed successfully' });
  } catch (error) {
    console.error('Error fixing tables:', error);
    return res.status(500).json({ error: 'Error fixing tables', details: error });
  }
}
