const supabase = require('./supabaseClient');
const authService = require('./supabaseAuthService');

class TokenUsageService {
  constructor() {
    // Define token limits by subscription tier
    this.USAGE_LIMITS = {
      basic: {
        dailyTokens: 50000,     // 50K tokens per day
        monthlyTokens: 1000000, // 1M tokens per month (profitable at $20/month)
        maxRequestLength: 4000  // Max prompt length in tokens
      },
      pro: {
        dailyTokens: 100000,    // 100K tokens per day
        monthlyTokens: 2000000, // 2M tokens per month (profitable at $30/month)
        maxRequestLength: 8000  // Max prompt length in tokens
      }
    };

    // Define cost per 1K tokens (for admin reporting)
    this.COST_PER_1K_TOKENS = {
      prompt: 0.003,      // $0.003 per 1K input tokens for Claude 3.7 Sonnet
      completion: 0.015   // $0.015 per 1K output tokens for Claude 3.7 Sonnet
    };
  }

  /**
   * Track token usage for a user
   * @param {string} userId - The user ID
   * @param {string} modelId - The model ID (e.g., 'claude-3-opus-20240229')
   * @param {number} promptTokens - Number of tokens in the prompt
   * @param {number} completionTokens - Number of tokens in the completion
   * @param {string} requestType - Type of request (e.g., 'chat', 'completion')
   * @param {boolean} usePurchasedTokens - Whether to use purchased tokens
   * @returns {Promise<Object>} - The inserted record
   */
  async trackTokenUsage(userId, modelId, promptTokens, completionTokens, requestType = 'chat', usePurchasedTokens = false) {
    try {
      console.log('=== TOKEN USAGE TRACKING ===');
      console.log(`Tracking tokens for user: ${userId}`);
      console.log(`Model: ${modelId}`);
      console.log(`Prompt tokens: ${promptTokens}`);
      console.log(`Completion tokens: ${completionTokens}`);
      console.log(`Request type: ${requestType}`);
      console.log(`Using purchased tokens: ${usePurchasedTokens}`);

      // Get user's subscription plan
      const user = await authService.getUserById(userId);
      console.log(`User found: ${user ? 'Yes' : 'No'}`);
      console.log(`Subscription status: ${user?.subscriptionStatus}`);

      const subscriptionPlan = user?.subscriptionStatus === 'active' ?
        (user.subscriptionPlan || 'basic') : 'none';
      console.log(`Subscription plan: ${subscriptionPlan}`);

      // Calculate total tokens
      const totalTokens = promptTokens + completionTokens;
      console.log(`Total tokens: ${totalTokens}`);

      // Check if we need to use purchased tokens
      if (!usePurchasedTokens) {
        // If not explicitly set to use purchased tokens, check if we need to
        const usageStatus = await this.checkUsageLimits(userId);

        if (usageStatus.hasExceededLimits) {
          console.log(`User ${userId} has exceeded usage limits, checking for purchased tokens`);

          // Check if user has purchased tokens
          const tokenPurchaseService = require('./tokenPurchaseService');
          const tokenBalance = await tokenPurchaseService.getUserTokenBalance(userId);

          // If user has purchased tokens, use them
          if (tokenBalance && tokenBalance.balance > 0) {
            console.log(`User ${userId} has ${tokenBalance.balance} purchased tokens available, using them`);
            usePurchasedTokens = true;
          } else {
            console.log(`User ${userId} has no purchased tokens available`);
          }
        }
      }

      // If using purchased tokens, deduct from token balance
      let tokenUsageResult = null;
      if (usePurchasedTokens) {
        console.log(`Using purchased tokens for user ${userId}`);

        // Use the token purchase service to deduct tokens
        const tokenPurchaseService = require('./tokenPurchaseService');
        const result = await tokenPurchaseService.useTokens(
          userId,
          totalTokens,
          `${requestType}_${Date.now()}`
        );

        if (!result || !result.success) {
          console.error(`Failed to use purchased tokens for user ${userId}:`, result?.error || 'Unknown error');
          // Continue with normal tracking, but don't mark as using purchased tokens
          usePurchasedTokens = false;
        } else {
          console.log(`Successfully used ${totalTokens} purchased tokens for user ${userId}. New balance: ${result.balance}`);
          tokenUsageResult = result;

          // Dispatch a custom event to notify other components about the token balance update
          try {
            // This is a server-side event, so we can't use the browser's CustomEvent
            // Instead, we'll emit an event through a global event emitter if available
            if (global.eventEmitter) {
              global.eventEmitter.emit('tokenBalanceUpdated', {
                userId,
                balance: result.balance
              });
              console.log(`Emitted tokenBalanceUpdated event with balance: ${result.balance}`);
            }
          } catch (eventError) {
            console.error('Error emitting token balance updated event:', eventError);
          }
        }
      }

      // Insert usage record
      console.log('Inserting usage record into Supabase...');
      const { data, error } = await supabase
        .from('token_usage')
        .insert({
          user_id: userId,
          model_id: modelId,
          prompt_tokens: promptTokens,
          completion_tokens: completionTokens,
          total_tokens: totalTokens,
          request_type: requestType,
          subscription_plan: subscriptionPlan,
          // Remove the used_purchased_tokens field as it doesn't exist in the table
          timestamp: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        console.error('Error tracking token usage:', error);
        console.log('Supabase error details:', JSON.stringify(error));
        return null;
      }

      console.log(`Successfully tracked ${totalTokens} tokens for user ${userId}`);
      console.log('Record ID:', data.id);
      console.log('=== END TOKEN USAGE TRACKING ===');

      // Return the data with token usage result if available
      if (tokenUsageResult) {
        return {
          ...data,
          tokenUsageResult
        };
      }

      return data;
    } catch (error) {
      console.error('Error in trackTokenUsage:', error);
      console.log('Error details:', error.message);
      console.log('Error stack:', error.stack);
      return null;
    }
  }

  /**
   * Check if a user has exceeded their usage limits
   * @param {string} userId - The user ID
   * @returns {Promise<Object>} - Usage status and limits
   */
  async checkUsageLimits(userId) {
    try {
      // Get user's subscription plan
      const user = await authService.getUserById(userId);
      const plan = user?.subscriptionStatus === 'active' ?
        (user.subscriptionPlan || 'basic') : 'basic';

      // Get limits for the plan
      const limits = this.USAGE_LIMITS[plan];

      // Check daily usage
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Get all daily usage records and sum manually
      const { data: dailyUsageRecords, error: dailyError } = await supabase
        .from('token_usage')
        .select('total_tokens')
        .eq('user_id', userId)
        .gte('timestamp', today.toISOString());

      if (dailyError) {
        console.error('Error checking daily usage:', dailyError);
        return { hasExceededLimits: false, error: 'Error checking limits' };
      }

      // Calculate sum manually
      const dailyTotal = dailyUsageRecords?.reduce((sum, record) => sum + record.total_tokens, 0) || 0;
      console.log(`Daily usage for user ${userId}: ${dailyTotal} tokens`);

      // Check monthly usage
      const firstDayOfMonth = new Date();
      firstDayOfMonth.setDate(1);
      firstDayOfMonth.setHours(0, 0, 0, 0);

      // Get all monthly usage records and sum manually
      const { data: monthlyUsageRecords, error: monthlyError } = await supabase
        .from('token_usage')
        .select('total_tokens')
        .eq('user_id', userId)
        .gte('timestamp', firstDayOfMonth.toISOString());

      if (monthlyError) {
        console.error('Error checking monthly usage:', monthlyError);
        return { hasExceededLimits: false, error: 'Error checking limits' };
      }

      // Calculate sum manually
      const monthlyTotal = monthlyUsageRecords?.reduce((sum, record) => sum + record.total_tokens, 0) || 0;
      console.log(`Monthly usage for user ${userId}: ${monthlyTotal} tokens`);

      // Check if limits are exceeded
      const dailyExceeded = dailyTotal >= limits.dailyTokens;
      const monthlyExceeded = monthlyTotal >= limits.monthlyTokens;

      // Calculate percentages
      const dailyPercentage = (dailyTotal / limits.dailyTokens) * 100;
      const monthlyPercentage = (monthlyTotal / limits.monthlyTokens) * 100;

      return {
        hasExceededLimits: dailyExceeded || monthlyExceeded,
        dailyUsage: {
          used: dailyTotal || 0,
          limit: limits.dailyTokens,
          percentage: dailyPercentage || 0,
          exceeded: dailyExceeded
        },
        monthlyUsage: {
          used: monthlyTotal || 0,
          limit: limits.monthlyTokens,
          percentage: monthlyPercentage || 0,
          exceeded: monthlyExceeded
        },
        plan,
        maxRequestLength: limits.maxRequestLength
      };
    } catch (error) {
      console.error('Error in checkUsageLimits:', error);
      return { hasExceededLimits: false, error: 'Error checking limits' };
    }
  }

  /**
   * Reset token usage for a user
   * @param {string} userId - The user ID
   * @returns {Promise<Object>} - Result of the operation
   */
  async resetUserTokenUsage(userId) {
    try {
      console.log(`Resetting token usage for user ${userId}`);

      // Get the current date
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Get the first day of the current month
      const firstDayOfMonth = new Date();
      firstDayOfMonth.setDate(1);
      firstDayOfMonth.setHours(0, 0, 0, 0);

      // Create a new token usage record with negative values to offset the current usage
      // First, get the current daily and monthly usage
      const dailyUsage = await this.getDailyTokenUsage(userId);
      const monthlyUsage = await this.getMonthlyTokenUsage(userId);

      console.log(`Current daily usage for user ${userId}: ${dailyUsage.totalTokens} tokens`);
      console.log(`Current monthly usage for user ${userId}: ${monthlyUsage.totalTokens} tokens`);

      // Only create offset records if there is usage to offset
      if (dailyUsage.totalTokens > 0 || monthlyUsage.totalTokens > 0) {
        // Create a reset record
        const { data, error } = await supabase
          .from('token_usage')
          .insert({
            user_id: userId,
            model_id: 'admin-reset',
            prompt_tokens: -dailyUsage.promptTokens,
            completion_tokens: -dailyUsage.completionTokens,
            total_tokens: -dailyUsage.totalTokens,
            request_type: 'admin-reset',
            subscription_plan: 'admin',
            timestamp: new Date().toISOString()
          })
          .select()
          .single();

        if (error) {
          console.error('Error resetting token usage:', error);
          return { success: false, error: 'Failed to reset token usage' };
        }

        console.log(`Successfully reset token usage for user ${userId}`);
        console.log('Reset record ID:', data.id);
      } else {
        console.log(`No token usage to reset for user ${userId}`);
      }

      return { success: true, message: 'Token usage reset successfully' };
    } catch (error) {
      console.error('Error in resetUserTokenUsage:', error);
      return { success: false, error: 'Failed to reset token usage' };
    }
  }

  /**
   * Get daily token usage for a user
   * @param {string} userId - The user ID
   * @returns {Promise<Object>} - Daily token usage
   */
  async getDailyTokenUsage(userId) {
    try {
      // Get the current date
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Get all daily usage records and sum manually
      const { data: dailyUsageRecords, error: dailyError } = await supabase
        .from('token_usage')
        .select('prompt_tokens, completion_tokens, total_tokens')
        .eq('user_id', userId)
        .gte('timestamp', today.toISOString());

      if (dailyError) {
        console.error('Error getting daily token usage:', dailyError);
        return { promptTokens: 0, completionTokens: 0, totalTokens: 0 };
      }

      // Calculate sums manually
      const promptTokens = dailyUsageRecords?.reduce((sum, record) => sum + record.prompt_tokens, 0) || 0;
      const completionTokens = dailyUsageRecords?.reduce((sum, record) => sum + record.completion_tokens, 0) || 0;
      const totalTokens = dailyUsageRecords?.reduce((sum, record) => sum + record.total_tokens, 0) || 0;

      return { promptTokens, completionTokens, totalTokens };
    } catch (error) {
      console.error('Error in getDailyTokenUsage:', error);
      return { promptTokens: 0, completionTokens: 0, totalTokens: 0 };
    }
  }

  /**
   * Get monthly token usage for a user
   * @param {string} userId - The user ID
   * @returns {Promise<Object>} - Monthly token usage
   */
  async getMonthlyTokenUsage(userId) {
    try {
      // Get the first day of the current month
      const firstDayOfMonth = new Date();
      firstDayOfMonth.setDate(1);
      firstDayOfMonth.setHours(0, 0, 0, 0);

      // Get all monthly usage records and sum manually
      const { data: monthlyUsageRecords, error: monthlyError } = await supabase
        .from('token_usage')
        .select('prompt_tokens, completion_tokens, total_tokens')
        .eq('user_id', userId)
        .gte('timestamp', firstDayOfMonth.toISOString());

      if (monthlyError) {
        console.error('Error getting monthly token usage:', monthlyError);
        return { promptTokens: 0, completionTokens: 0, totalTokens: 0 };
      }

      // Calculate sums manually
      const promptTokens = monthlyUsageRecords?.reduce((sum, record) => sum + record.prompt_tokens, 0) || 0;
      const completionTokens = monthlyUsageRecords?.reduce((sum, record) => sum + record.completion_tokens, 0) || 0;
      const totalTokens = monthlyUsageRecords?.reduce((sum, record) => sum + record.total_tokens, 0) || 0;

      return { promptTokens, completionTokens, totalTokens };
    } catch (error) {
      console.error('Error in getMonthlyTokenUsage:', error);
      return { promptTokens: 0, completionTokens: 0, totalTokens: 0 };
    }
  }

  /**
   * Get token usage statistics for admin dashboard
   * @param {string} period - 'day', 'week', 'month', or 'year'
   * @returns {Promise<Object>} - Usage statistics
   */
  async getUsageStatistics(period = 'month') {
    try {
      console.log(`Getting token usage statistics for period: ${period}`);

      // Calculate start date based on period
      const startDate = new Date();

      switch (period) {
        case 'day':
          startDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case 'month':
          startDate.setDate(1);
          startDate.setHours(0, 0, 0, 0);
          break;
        case 'year':
          startDate.setMonth(0, 1);
          startDate.setHours(0, 0, 0, 0);
          break;
        default:
          startDate.setDate(1);
          startDate.setHours(0, 0, 0, 0);
      }

      console.log(`Start date for period ${period}: ${startDate.toISOString()}`);

      // Try to query the token_usage table directly to check if it exists
      try {
        const { data: testData, error: testError } = await supabase
          .from('token_usage')
          .select('*')
          .limit(1);

        if (testError) {
          console.log('Error querying token_usage table:', testError);
          console.log('Token usage table does not exist, returning empty statistics');
          return {
            period,
            totalUsage: { promptTokens: 0, completionTokens: 0, totalTokens: 0 },
            costs: { promptCost: '0.00', completionCost: '0.00', totalCost: '0.00' },
            revenue: { basicUsers: 0, proUsers: 0, totalUsers: 0, basicRevenue: '0.00', proRevenue: '0.00', totalRevenue: '0.00' },
            profit: { totalProfit: '0.00', margin: '0.00%' },
            userBreakdown: []
          };
        }

        console.log('Token usage table exists, found', testData.length, 'records');
      } catch (error) {
        console.error('Error checking if token_usage table exists:', error);
        console.log('Token usage table does not exist, returning empty statistics');
        return {
          period,
          totalUsage: { promptTokens: 0, completionTokens: 0, totalTokens: 0 },
          costs: { promptCost: '0.00', completionCost: '0.00', totalCost: '0.00' },
          revenue: { basicUsers: 0, proUsers: 0, totalUsers: 0, basicRevenue: '0.00', proRevenue: '0.00', totalRevenue: '0.00' },
          profit: { totalProfit: '0.00', margin: '0.00%' },
          userBreakdown: []
        };
      }

      // Get total usage by querying all records and summing manually
      const { data: totalUsageData, error: totalError } = await supabase
        .from('token_usage')
        .select('prompt_tokens, completion_tokens, total_tokens')
        .gte('timestamp', startDate.toISOString());

      // Calculate the sums manually
      let totalUsage = [{ prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 }];
      if (!totalError && totalUsageData && totalUsageData.length > 0) {
        const promptTokensSum = totalUsageData.reduce((sum, record) => sum + record.prompt_tokens, 0);
        const completionTokensSum = totalUsageData.reduce((sum, record) => sum + record.completion_tokens, 0);
        const totalTokensSum = totalUsageData.reduce((sum, record) => sum + record.total_tokens, 0);

        totalUsage = [{
          prompt_tokens: promptTokensSum,
          completion_tokens: completionTokensSum,
          total_tokens: totalTokensSum
        }];
      }

      if (totalError) {
        console.error('Error getting total usage:', totalError);
        return {
          period,
          totalUsage: { promptTokens: 0, completionTokens: 0, totalTokens: 0 },
          costs: { promptCost: '0.00', completionCost: '0.00', totalCost: '0.00' },
          revenue: { basicUsers: 0, proUsers: 0, totalUsers: 0, basicRevenue: '0.00', proRevenue: '0.00', totalRevenue: '0.00' },
          profit: { totalProfit: '0.00', margin: '0.00%' },
          userBreakdown: []
        };
      }

      console.log('Total usage data:', totalUsage);

      // Get usage by user - query all records and group manually
      const { data: userUsageData, error: userError } = await supabase
        .from('token_usage')
        .select('user_id, prompt_tokens, completion_tokens, total_tokens, subscription_plan')
        .gte('timestamp', startDate.toISOString());

      // Group and sum manually
      let userUsage = [];
      if (!userError && userUsageData && userUsageData.length > 0) {
        // Group by user_id
        const userGroups = {};

        for (const record of userUsageData) {
          const userId = record.user_id;
          const plan = record.subscription_plan || 'basic';

          if (!userGroups[userId]) {
            userGroups[userId] = {
              user_id: userId,
              prompt_tokens: 0,
              completion_tokens: 0,
              total_tokens: 0,
              subscription_plan: plan
            };
          }

          userGroups[userId].prompt_tokens += record.prompt_tokens;
          userGroups[userId].completion_tokens += record.completion_tokens;
          userGroups[userId].total_tokens += record.total_tokens;
        }

        // Convert to array and sort by total_tokens
        userUsage = Object.values(userGroups).sort((a, b) => b.total_tokens - a.total_tokens);
      }

      if (userError) {
        console.error('Error getting user usage:', userError);
        return {
          period,
          totalUsage: {
            promptTokens: totalUsage[0]?.prompt_tokens || 0,
            completionTokens: totalUsage[0]?.completion_tokens || 0,
            totalTokens: totalUsage[0]?.total_tokens || 0
          },
          costs: { promptCost: '0.00', completionCost: '0.00', totalCost: '0.00' },
          revenue: { basicUsers: 0, proUsers: 0, totalUsers: 0, basicRevenue: '0.00', proRevenue: '0.00', totalRevenue: '0.00' },
          profit: { totalProfit: '0.00', margin: '0.00%' },
          userBreakdown: []
        };
      }

      console.log(`User usage data: ${userUsage?.length || 0} users`);

      // If no data, return empty statistics
      if (!totalUsage || !totalUsage[0] || !userUsage || userUsage.length === 0) {
        console.log('No token usage data found');
        return {
          period,
          totalUsage: { promptTokens: 0, completionTokens: 0, totalTokens: 0 },
          costs: { promptCost: '0.00', completionCost: '0.00', totalCost: '0.00' },
          revenue: { basicUsers: 0, proUsers: 0, totalUsers: 0, basicRevenue: '0.00', proRevenue: '0.00', totalRevenue: '0.00' },
          profit: { totalProfit: '0.00', margin: '0.00%' },
          userBreakdown: []
        };
      }

      // Calculate costs
      const totalPromptCost = (totalUsage[0]?.prompt_tokens || 0) / 1000 * this.COST_PER_1K_TOKENS.prompt;
      const totalCompletionCost = (totalUsage[0]?.completion_tokens || 0) / 1000 * this.COST_PER_1K_TOKENS.completion;
      const totalCost = totalPromptCost + totalCompletionCost;

      // Calculate revenue (assuming all users with usage have active subscriptions)
      const basicUsers = userUsage.filter(u => u.subscription_plan === 'basic').length;
      const proUsers = userUsage.filter(u => u.subscription_plan === 'pro').length;
      const totalRevenue = (basicUsers * 20) + (proUsers * 30);

      // Calculate profit and margin
      const totalProfit = totalRevenue - totalCost;
      const margin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;

      console.log('Cost calculations:', { totalPromptCost, totalCompletionCost, totalCost });
      console.log('Revenue calculations:', { basicUsers, proUsers, totalRevenue });
      console.log('Profit calculations:', { totalProfit, margin });

      // Format user breakdown with costs
      const userBreakdown = await Promise.all(userUsage.map(async (user) => {
        const promptCost = (user.prompt_tokens / 1000) * this.COST_PER_1K_TOKENS.prompt;
        const completionCost = (user.completion_tokens / 1000) * this.COST_PER_1K_TOKENS.completion;
        const userCost = promptCost + completionCost;
        const userRevenue = user.subscription_plan === 'pro' ? 30 : 20;
        const userProfit = userRevenue - userCost;
        const userMargin = userRevenue > 0 ? (userProfit / userRevenue) * 100 : 0;

        // Get user details
        const userDetails = await authService.getUserById(user.user_id);

        console.log(`User details for ${user.user_id}:`, userDetails);

        return {
          userId: user.user_id,
          email: userDetails?.email || 'Unknown',
          name: userDetails?.name || 'Unknown',
          plan: user.subscription_plan || 'basic',
          promptTokens: user.prompt_tokens,
          completionTokens: user.completion_tokens,
          totalTokens: user.total_tokens,
          cost: userCost.toFixed(2),
          revenue: userRevenue.toFixed(2),
          profit: userProfit.toFixed(2),
          margin: userMargin.toFixed(2) + '%'
        };
      }));

      console.log(`Processed user breakdown: ${userBreakdown.length} users`);

      const result = {
        period,
        totalUsage: {
          promptTokens: totalUsage[0]?.prompt_tokens || 0,
          completionTokens: totalUsage[0]?.completion_tokens || 0,
          totalTokens: totalUsage[0]?.total_tokens || 0
        },
        costs: {
          promptCost: totalPromptCost.toFixed(2),
          completionCost: totalCompletionCost.toFixed(2),
          totalCost: totalCost.toFixed(2)
        },
        revenue: {
          basicUsers,
          proUsers,
          totalUsers: basicUsers + proUsers,
          basicRevenue: (basicUsers * 20).toFixed(2),
          proRevenue: (proUsers * 30).toFixed(2),
          totalRevenue: totalRevenue.toFixed(2)
        },
        profit: {
          totalProfit: totalProfit.toFixed(2),
          margin: margin.toFixed(2) + '%'
        },
        userBreakdown
      };

      console.log('Returning token usage statistics');
      return result;
    } catch (error) {
      console.error('Error in getUsageStatistics:', error);
      console.error('Error stack:', error.stack);

      // Return empty statistics on error
      return {
        period,
        totalUsage: { promptTokens: 0, completionTokens: 0, totalTokens: 0 },
        costs: { promptCost: '0.00', completionCost: '0.00', totalCost: '0.00' },
        revenue: { basicUsers: 0, proUsers: 0, totalUsers: 0, basicRevenue: '0.00', proRevenue: '0.00', totalRevenue: '0.00' },
        profit: { totalProfit: '0.00', margin: '0.00%' },
        userBreakdown: []
      };
    }
  }

  /**
   * Estimate token count for a text string
   * @param {string} text - The text to estimate
   * @returns {number} - Estimated token count
   */
  estimateTokenCount(text) {
    if (!text) return 0;
    // Rough estimate: 4 chars per token for English text
    return Math.ceil(text.length / 4);
  }

  /**
   * Optimize a prompt to reduce token usage
   * @param {string} prompt - The original prompt
   * @param {number} maxLength - Maximum token length
   * @returns {string} - Optimized prompt
   */
  optimizePrompt(prompt, maxLength = 4000) {
    if (!prompt) return '';

    // Simple truncation if prompt is too long
    if (this.estimateTokenCount(prompt) > maxLength) {
      return this.truncateToTokenLimit(prompt, maxLength);
    }

    // Remove redundant whitespace
    let optimized = prompt.replace(/\s+/g, ' ').trim();

    // Remove unnecessary instructions that might be repeated
    const commonPhrases = [
      "Please provide",
      "I would like you to",
      "Can you help me with",
      "I need assistance with"
    ];

    for (const phrase of commonPhrases) {
      if (optimized.toLowerCase().includes(phrase.toLowerCase())) {
        optimized = optimized.replace(new RegExp(phrase, 'i'), '');
        break; // Only remove one redundant phrase
      }
    }

    return optimized.trim();
  }

  /**
   * Truncate text to token limit
   * @param {string} text - The text to truncate
   * @param {number} maxTokens - Maximum token count
   * @returns {string} - Truncated text
   */
  truncateToTokenLimit(text, maxTokens) {
    // Simple truncation strategy
    const estimatedChars = maxTokens * 4;
    if (text.length <= estimatedChars) return text;

    // Try to truncate at a sentence boundary
    const truncated = text.substring(0, estimatedChars);
    const lastPeriod = truncated.lastIndexOf('.');

    if (lastPeriod > estimatedChars * 0.7) {
      // If we can find a period in the latter part of the text, cut there
      return truncated.substring(0, lastPeriod + 1);
    }

    return truncated;
  }
}

module.exports = new TokenUsageService();
