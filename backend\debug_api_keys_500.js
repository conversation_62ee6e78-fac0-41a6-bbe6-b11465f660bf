#!/usr/bin/env node
/**
 * Debug script for API Keys 500 error
 * This script helps diagnose the 500 error when fetching API keys from /admin/api-keys
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

console.log('🔍 Debugging API Keys 500 Error');
console.log('================================');

// Check environment variables
console.log('\n1. Environment Variables:');
console.log('SUPABASE_URL:', supabaseUrl ? '✅ Found' : '❌ Missing');
console.log('SUPABASE_SERVICE_KEY:', supabaseKey ? `✅ Found (${supabaseKey.length} chars)` : '❌ Missing');

if (!supabaseUrl || !supabaseKey) {
  console.error('\n❌ Missing required environment variables!');
  console.log('Please check your .env file and ensure SUPABASE_URL and SUPABASE_SERVICE_KEY are set.');
  process.exit(1);
}

// Create Supabase client
let supabase;
try {
  supabase = createClient(supabaseUrl, supabaseKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
  console.log('✅ Supabase client created successfully');
} catch (error) {
  console.error('❌ Error creating Supabase client:', error.message);
  process.exit(1);
}

async function runDiagnostics() {
  console.log('\n2. Testing Supabase Connection:');
  
  try {
    // Test basic connection
    const { data: connectionTest, error: connectionError } = await supabase
      .from('api_keys')
      .select('count')
      .limit(1);
    
    if (connectionError) {
      console.error('❌ Connection test failed:', connectionError.message);
      console.log('Error details:', connectionError);
      
      // Check if it's a table not found error
      if (connectionError.message.includes('relation "api_keys" does not exist')) {
        console.log('\n🔍 The "api_keys" table does not exist in your database.');
        console.log('You need to create the api_keys table. Here\'s the SQL:');
        console.log(`
CREATE TABLE api_keys (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL,
  key_name TEXT NOT NULL,
  api_key TEXT NOT NULL UNIQUE,
  rate_limit INTEGER DEFAULT 100,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;

-- Policy for service role (backend)
CREATE POLICY "Service role can manage all api_keys" ON api_keys
  FOR ALL USING (auth.role() = 'service_role');
        `);
        return;
      }
      
      // Check if it's an authentication error
      if (connectionError.message.includes('JWT') || connectionError.message.includes('authentication')) {
        console.log('\n🔍 Authentication error detected.');
        console.log('Your SUPABASE_SERVICE_KEY might be incorrect or expired.');
        console.log('Please check your Supabase dashboard for the correct service role key.');
        return;
      }
      
      return;
    }
    
    console.log('✅ Basic connection successful');
    
    // Test querying api_keys table
    console.log('\n3. Testing API Keys Table:');
    const { data: apiKeys, error: apiKeysError } = await supabase
      .from('api_keys')
      .select('*')
      .limit(5);
    
    if (apiKeysError) {
      console.error('❌ Error querying api_keys table:', apiKeysError.message);
      console.log('Error details:', apiKeysError);
      return;
    }
    
    console.log(`✅ Successfully queried api_keys table`);
    console.log(`Found ${apiKeys.length} API keys`);
    
    if (apiKeys.length > 0) {
      console.log('Sample API key structure:');
      const sampleKey = { ...apiKeys[0] };
      // Mask the actual API key for security
      if (sampleKey.api_key) {
        sampleKey.api_key = sampleKey.api_key.substring(0, 8) + '...';
      }
      console.log(JSON.stringify(sampleKey, null, 2));
    }
    
    // Test querying users table (needed for user details)
    console.log('\n4. Testing Users Table:');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email')
      .limit(1);
    
    if (usersError) {
      console.error('❌ Error querying users table:', usersError.message);
      console.log('This might cause issues when fetching user details for API keys.');
      
      if (usersError.message.includes('relation "users" does not exist')) {
        console.log('\n🔍 The "users" table does not exist.');
        console.log('You need to create the users table or check your table name.');
      }
    } else {
      console.log('✅ Users table accessible');
    }
    
    // Test the exact query used in the API endpoint
    console.log('\n5. Testing Exact API Endpoint Query:');
    
    try {
      // This is the exact query from the API endpoint
      const { data: apiKeysData, error: apiKeysQueryError } = await supabase
        .from('api_keys')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (apiKeysQueryError) {
        console.error('❌ API endpoint query failed:', apiKeysQueryError.message);
        return;
      }
      
      console.log('✅ API endpoint query successful');
      
      // Test getting user details for each key (this is where it might fail)
      if (apiKeysData.length > 0) {
        console.log('\n6. Testing User Details Lookup:');
        
        const firstKey = apiKeysData[0];
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('email')
          .eq('id', firstKey.user_id)
          .single();
        
        if (userError) {
          console.error('❌ User details lookup failed:', userError.message);
          console.log('This is likely causing the 500 error in the API endpoint.');
          
          if (userError.message.includes('No rows returned')) {
            console.log(`🔍 User with ID ${firstKey.user_id} not found in users table.`);
            console.log('This suggests data inconsistency between api_keys and users tables.');
          }
        } else {
          console.log('✅ User details lookup successful');
          console.log('User email:', userData.email);
        }
      }
      
    } catch (error) {
      console.error('❌ Unexpected error in API endpoint query:', error.message);
    }
    
    console.log('\n🎉 Diagnostics completed!');
    console.log('\nIf you see any ❌ errors above, those are likely causing the 500 error.');
    console.log('Fix those issues and the API should work correctly.');
    
  } catch (error) {
    console.error('❌ Unexpected error during diagnostics:', error.message);
    console.log('Full error:', error);
  }
}

// Run the diagnostics
runDiagnostics()
  .then(() => {
    console.log('\n✅ Diagnostics script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Diagnostics script failed:', error);
    process.exit(1);
  });
