const express = require('express');
const router = express.Router();
const { authenticateJWT, authenticateJWTWithFallback } = require('../../middleware/auth');
const tokenPurchaseService = require('../../services/tokenPurchaseService');

/**
 * @route POST /api/tokens/simulate-webhook
 * @description Simulate a Stripe webhook event for token purchases
 * @access Private
 */
router.post('/', authenticateJWTWithFallback, async (req, res) => {
  // Generate a unique webhook ID for tracking
  const webhookId = `webhook-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
  
  try {
    console.log(`[${webhookId}] SIMULATE WEBHOOK: Received request to simulate webhook event`);
    
    // Get user ID from the authenticated user object
    const userId = req.user.id;
    
    // Get the package ID and token amount from the request body
    const { packageId } = req.body;
    
    if (!packageId) {
      console.error(`[${webhookId}] SIMULATE WEBHOOK: Missing required fields`);
      return res.status(400).json({
        success: false,
        error: 'Missing required fields',
        webhookId
      });
    }
    
    // Get the token amount for the package
    const tokenPackages = tokenPurchaseService.getTokenPackages();
    const tokenPackage = tokenPackages[packageId];
    
    if (!tokenPackage) {
      console.error(`[${webhookId}] SIMULATE WEBHOOK: Invalid package ID: ${packageId}`);
      return res.status(400).json({
        success: false,
        error: `Invalid package ID: ${packageId}`,
        webhookId
      });
    }
    
    const tokenAmount = tokenPackage.tokens;
    
    console.log(`[${webhookId}] SIMULATE WEBHOOK: Selected package ${packageId} with ${tokenAmount} tokens`);
    
    // Create a transaction ID for this purchase
    const transactionId = `sim-${webhookId}`;
    
    // Add tokens to the user's account
    console.log(`[${webhookId}] SIMULATE WEBHOOK: Calling addTokensToUser with userId=${userId}, tokens=${tokenAmount}, transactionId=${transactionId}`);
    
    try {
      // Call the addTokensToUser method directly
      const result = await tokenPurchaseService.addTokensToUser(userId, tokenAmount, transactionId);
      
      console.log(`[${webhookId}] SIMULATE WEBHOOK: Token purchase processed successfully:`, result);
      
      return res.json({
        success: true,
        message: `Successfully added ${tokenAmount} tokens to user ${userId}`,
        result,
        webhookId
      });
    } catch (error) {
      console.error(`[${webhookId}] SIMULATE WEBHOOK: Error adding tokens to user:`, error);
      return res.status(500).json({
        success: false,
        error: `Error adding tokens to user: ${error.message}`,
        webhookId
      });
    }
  } catch (error) {
    console.error(`[${webhookId}] SIMULATE WEBHOOK: Unexpected error:`, error);
    return res.status(500).json({
      success: false,
      error: `Unexpected error: ${error.message}`,
      webhookId
    });
  }
});

module.exports = router;
