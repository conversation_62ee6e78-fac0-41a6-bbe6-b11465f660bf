const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs').promises;
const os = require('os');
const { authenticateJWT } = require('../../middleware/auth');
const { checkSubscription } = require('../../middleware/subscription');

// Storage path for MCP update files (platform-aware)
const UPDATES_STORAGE_PATH = path.resolve(__dirname, '..', '..', '..', 'storage', 'mcp-updates');

// Platform detection
const platform = process.platform;
const isWindows = platform === 'win32';
const isMacOS = platform === 'darwin';
const isLinux = platform === 'linux';

console.log(`[MCP Updates] Backend initialized for platform: ${platform}`);

// In-memory version database (in production, this would be a real database)
const mcpVersions = [
  {
    id: 1,
    version: '1.0.0',
    description: 'Initial CreateLex MCP Server release with 21+ Unreal Engine tools',
    release_notes: 'Initial release with comprehensive Unreal Engine integration',
    file_path: 'mcp-server-v1.0.0.zip',
    file_size: 45000,
    checksum: 'initial_version_checksum',
    critical: false,
    min_app_version: '1.0.0',
    created_at: new Date('2024-12-19'),
    published_at: new Date('2024-12-19'),
    is_active: true,
    platforms: ['win32', 'darwin', 'linux'] // Supported platforms
  },
  {
    id: 2,
    version: '1.1.0',
    description: 'CreateLex MCP Server v1.1.0 with enhanced tools and performance improvements',
    release_notes: 'Added new Blueprint tools, improved performance, and enhanced error handling',
    file_path: 'mcp-server-v1.1.0.zip',
    file_size: 50990,
    checksum: 'a60ddcec0cff79a7544ea8ce3d60955390728106dd01fdf880faf1f75b7a4fa6',
    critical: false,
    min_app_version: '1.0.0',
    created_at: new Date('2024-12-20'),
    published_at: new Date('2024-12-20'),
    is_active: true,
    platforms: ['win32', 'darwin', 'linux'] // Supported platforms
  }
];

// Helper function to compare versions (simple semantic versioning)
function compareVersions(v1, v2) {
  const parts1 = v1.split('.').map(Number);
  const parts2 = v2.split('.').map(Number);
  
  for (let i = 0; i < Math.max(parts1.length, parts2.length); i++) {
    const part1 = parts1[i] || 0;
    const part2 = parts2[i] || 0;
    
    if (part1 > part2) return 1;
    if (part1 < part2) return -1;
  }
  
  return 0;
}

// Helper function to get latest version for a specific platform
function getLatestVersion(clientPlatform = null) {
  return mcpVersions
    .filter(v => v.is_active)
    .filter(v => !clientPlatform || !v.platforms || v.platforms.includes(clientPlatform))
    .sort((a, b) => compareVersions(b.version, a.version))[0];
}

// Helper function to extract platform from User-Agent
function extractPlatformFromUserAgent(userAgent) {
  if (!userAgent) return null;
  
  const match = userAgent.match(/\(([^)]+)\)/);
  if (match) {
    const platformString = match[1].toLowerCase();
    if (platformString.includes('win32') || platformString.includes('windows')) return 'win32';
    if (platformString.includes('darwin') || platformString.includes('macos')) return 'darwin';
    if (platformString.includes('linux')) return 'linux';
  }
  
  return null;
}

// Helper function to ensure storage directory exists
async function ensureStorageDirectory() {
  try {
    await fs.mkdir(UPDATES_STORAGE_PATH, { recursive: true });
  } catch (error) {
    if (error.code !== 'EEXIST') {
      console.error('[MCP Updates] Failed to create storage directory:', error);
      throw error;
    }
  }
}

/**
 * @route   GET /api/mcp-updates/check
 * @desc    Check for available MCP server updates
 * @access  Protected (requires subscription)
 */
router.get('/check', authenticateJWT, checkSubscription, async (req, res) => {
  try {
    const { current_version } = req.query;
    
    if (!current_version) {
      return res.status(400).json({
        error: 'Missing required parameter: current_version'
      });
    }

    // Extract client platform from User-Agent header
    const userAgent = req.headers['user-agent'] || '';
    const clientPlatform = extractPlatformFromUserAgent(userAgent);
    
    console.log(`[MCP Updates] Checking for updates. Current: ${current_version}, Platform: ${clientPlatform || 'unknown'}`);

    const latestVersion = getLatestVersion(clientPlatform);
    
    if (!latestVersion) {
      return res.status(500).json({
        error: 'No versions available for your platform',
        platform: clientPlatform
      });
    }

    const hasUpdate = compareVersions(latestVersion.version, current_version) > 0;

    if (hasUpdate) {
      console.log(`[MCP Updates] Update available: ${current_version} → ${latestVersion.version} (${clientPlatform || 'unknown'})`);
      
      res.json({
        hasUpdate: true,
        latestVersion: latestVersion.version,
        platform: clientPlatform,
        updateInfo: {
          size: latestVersion.file_size,
          description: latestVersion.description,
          releaseNotes: latestVersion.release_notes,
          critical: latestVersion.critical,
          minAppVersion: latestVersion.min_app_version,
          supportedPlatforms: latestVersion.platforms
        }
      });
    } else {
      console.log(`[MCP Updates] No update needed. Current version ${current_version} is up to date (${clientPlatform || 'unknown'})`);
      
      res.json({
        hasUpdate: false,
        currentVersion: current_version,
        platform: clientPlatform
      });
    }

  } catch (error) {
    console.error('[MCP Updates] Error checking for updates:', error);
    res.status(500).json({
      error: 'Failed to check for updates',
      message: error.message
    });
  }
});

/**
 * @route   GET /api/mcp-updates/check-dev
 * @desc    Check for available MCP server updates (development only - no auth)
 * @access  Public (development only)
 */
router.get('/check-dev', async (req, res) => {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return res.status(404).json({ error: 'Not found' });
    }

    const { current_version } = req.query;
    
    if (!current_version) {
      return res.status(400).json({
        error: 'Missing required parameter: current_version'
      });
    }

    // Extract client platform from User-Agent header
    const userAgent = req.headers['user-agent'] || '';
    const clientPlatform = extractPlatformFromUserAgent(userAgent);

    console.log(`[MCP Updates DEV] Checking for updates. Current: ${current_version}, Platform: ${clientPlatform || 'unknown'}`);

    const latestVersion = getLatestVersion(clientPlatform);
    
    if (!latestVersion) {
      return res.status(500).json({
        error: 'No versions available for your platform',
        platform: clientPlatform
      });
    }

    const hasUpdate = compareVersions(latestVersion.version, current_version) > 0;

    if (hasUpdate) {
      console.log(`[MCP Updates DEV] Update available: ${current_version} → ${latestVersion.version} (${clientPlatform || 'unknown'})`);
      
      res.json({
        hasUpdate: true,
        latestVersion: latestVersion.version,
        platform: clientPlatform,
        updateInfo: {
          size: latestVersion.file_size,
          description: latestVersion.description,
          releaseNotes: latestVersion.release_notes,
          critical: latestVersion.critical,
          minAppVersion: latestVersion.min_app_version,
          supportedPlatforms: latestVersion.platforms
        }
      });
    } else {
      console.log(`[MCP Updates DEV] No update needed. Current version ${current_version} is up to date (${clientPlatform || 'unknown'})`);
      
      res.json({
        hasUpdate: false,
        currentVersion: current_version,
        platform: clientPlatform
      });
    }

  } catch (error) {
    console.error('[MCP Updates DEV] Error checking for updates:', error);
    res.status(500).json({
      error: 'Failed to check for updates',
      message: error.message
    });
  }
});

/**
 * @route   GET /api/mcp-updates/download/:version
 * @desc    Download MCP server update package
 * @access  Protected (requires subscription)
 */
router.get('/download/:version', authenticateJWT, checkSubscription, async (req, res) => {
  try {
    const { version } = req.params;
    
    console.log(`[MCP Updates] Download requested for version: ${version}`);

    // Find the version in our database
    const versionData = mcpVersions.find(v => v.version === version && v.is_active);
    
    if (!versionData) {
      const availableVersions = mcpVersions
        .filter(v => v.is_active)
        .map(v => v.version);
        
      return res.status(404).json({
        error: 'Version not found',
        version: version,
        available_versions: availableVersions
      });
    }

    // Ensure storage directory exists
    await ensureStorageDirectory();

    // Construct file path (platform-aware)
    const filePath = path.resolve(UPDATES_STORAGE_PATH, versionData.file_path);
    
    try {
      // Check if file exists
      await fs.access(filePath);
      
      // Get file stats
      const stats = await fs.stat(filePath);
      
      console.log(`[MCP Updates] Serving file: ${filePath} (${stats.size} bytes)`);

      // Set response headers
      res.set({
        'Content-Type': 'application/zip',
        'Content-Length': stats.size,
        'x-checksum': versionData.checksum,
        'Content-Disposition': `attachment; filename="${versionData.file_path}"`
      });

      // Stream the file
      const fileStream = require('fs').createReadStream(filePath);
      fileStream.pipe(res);
      
      fileStream.on('error', (error) => {
        console.error('[MCP Updates] File stream error:', error);
        if (!res.headersSent) {
          res.status(500).json({ error: 'Failed to stream file' });
        }
      });

    } catch (fileError) {
      console.error('[MCP Updates] File not found:', filePath);
      res.status(404).json({
        error: 'Update file not found',
        version: version
      });
    }

  } catch (error) {
    console.error('[MCP Updates] Error downloading update:', error);
    res.status(500).json({
      error: 'Failed to download update',
      message: error.message
    });
  }
});

/**
 * @route   GET /api/mcp-updates/download-dev/:version
 * @desc    Download MCP server update package (development only - no auth)
 * @access  Public (development only)
 */
router.get('/download-dev/:version', async (req, res) => {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return res.status(404).json({ error: 'Not found' });
    }

    const { version } = req.params;
    
    console.log(`[MCP Updates DEV] Download requested for version: ${version}`);

    // Find the version in our database
    const versionData = mcpVersions.find(v => v.version === version && v.is_active);
    
    if (!versionData) {
      const availableVersions = mcpVersions
        .filter(v => v.is_active)
        .map(v => v.version);
        
      return res.status(404).json({
        error: 'Version not found',
        version: version,
        available_versions: availableVersions
      });
    }

    // Ensure storage directory exists
    await ensureStorageDirectory();

    // Construct file path (platform-aware)
    const filePath = path.resolve(UPDATES_STORAGE_PATH, versionData.file_path);
    
    try {
      // Check if file exists
      await fs.access(filePath);
      
      // Get file stats
      const stats = await fs.stat(filePath);
      
      console.log(`[MCP Updates DEV] Serving file: ${filePath} (${stats.size} bytes)`);

      // Set response headers
      res.set({
        'Content-Type': 'application/zip',
        'Content-Length': stats.size,
        'x-checksum': versionData.checksum,
        'Content-Disposition': `attachment; filename="${versionData.file_path}"`
      });

      // Stream the file
      const fileStream = require('fs').createReadStream(filePath);
      fileStream.pipe(res);
      
      fileStream.on('error', (error) => {
        console.error('[MCP Updates DEV] File stream error:', error);
        if (!res.headersSent) {
          res.status(500).json({ error: 'Failed to stream file' });
        }
      });

    } catch (fileError) {
      console.error('[MCP Updates DEV] File not found:', filePath);
      res.status(404).json({
        error: 'Update file not found',
        version: version
      });
    }

  } catch (error) {
    console.error('[MCP Updates DEV] Error downloading update:', error);
    res.status(500).json({
      error: 'Failed to download update',
      message: error.message
    });
  }
});

/**
 * @route   POST /api/mcp-updates/upload
 * @desc    Upload new MCP server update (admin only)
 * @access  Admin only
 */
router.post('/upload', authenticateJWT, async (req, res) => {
  try {
    // Check if user is admin (you can implement this based on your admin system)
    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    
    if (!req.user || !adminEmails.includes(req.user.email?.toLowerCase())) {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const {
      version,
      description,
      release_notes,
      file_path,
      file_size,
      checksum,
      critical = false,
      min_app_version = '1.0.0'
    } = req.body;

    if (!version || !description || !file_path || !file_size || !checksum) {
      return res.status(400).json({
        error: 'Missing required fields: version, description, file_path, file_size, checksum'
      });
    }

    // Check if version already exists
    const existingVersion = mcpVersions.find(v => v.version === version);
    if (existingVersion) {
      return res.status(409).json({
        error: 'Version already exists',
        version: version
      });
    }

    // Add new version to database
    const newVersion = {
      id: mcpVersions.length + 1,
      version,
      description,
      release_notes: release_notes || '',
      file_path,
      file_size: parseInt(file_size),
      checksum,
      critical: Boolean(critical),
      min_app_version,
      created_at: new Date(),
      published_at: new Date(),
      is_active: true
    };

    mcpVersions.push(newVersion);

    console.log(`[MCP Updates] New version uploaded: ${version}`);

    res.json({
      success: true,
      version: newVersion,
      message: `Version ${version} uploaded successfully`
    });

  } catch (error) {
    console.error('[MCP Updates] Error uploading version:', error);
    res.status(500).json({
      error: 'Failed to upload version',
      message: error.message
    });
  }
});

/**
 * @route   GET /api/mcp-updates/versions
 * @desc    Get all available MCP versions (admin only)
 * @access  Admin only
 */
router.get('/versions', authenticateJWT, async (req, res) => {
  try {
    // Check if user is admin
    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    
    if (!req.user || !adminEmails.includes(req.user.email?.toLowerCase())) {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const versions = mcpVersions
      .sort((a, b) => compareVersions(b.version, a.version))
      .map(v => ({
        ...v,
        file_path: undefined // Don't expose file paths to frontend
      }));

    res.json({
      versions,
      total: versions.length,
      latest: getLatestVersion()?.version
    });

  } catch (error) {
    console.error('[MCP Updates] Error getting versions:', error);
    res.status(500).json({
      error: 'Failed to get versions',
      message: error.message
    });
  }
});

module.exports = router;