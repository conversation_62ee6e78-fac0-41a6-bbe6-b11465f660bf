require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('SUPABASE_URL or SUPABASE_SERVICE_KEY is not set in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Test user ID - replace with a real user ID from your system
const TEST_USER_ID = '077f1533-9f81-429c-b1b1-52d9c83f146c';

async function testTokenBalance() {
  try {
    console.log(`Testing token balance for user ${TEST_USER_ID}`);

    // Check if the token_balance table exists
    const { data: tableExists, error: tableError } = await supabase
      .from('token_balance')
      .select('*')
      .limit(1);

    if (tableError) {
      console.error('Error checking token_balance table:', tableError);
      console.log('The token_balance table may not exist. Please create it first.');
      return;
    }

    console.log('Token balance table exists.');

    // Check if the user has a token balance record
    const { data: userBalance, error: userError } = await supabase
      .from('token_balance')
      .select('*')
      .eq('user_id', TEST_USER_ID)
      .single();

    if (userError && userError.code !== 'PGRST116') {
      console.error('Error checking user token balance:', userError);
      return;
    }

    if (!userBalance) {
      console.log(`No token balance record found for user ${TEST_USER_ID}. Creating one...`);

      // Create a token balance record for the user
      const { data: newBalance, error: createError } = await supabase
        .from('token_balance')
        .insert([
          { user_id: TEST_USER_ID, balance: 50000 }
        ])
        .select()
        .single();

      if (createError) {
        console.error('Error creating token balance record:', createError);
        return;
      }

      console.log('Token balance record created:', newBalance);
    } else {
      console.log('Existing token balance record found:', userBalance);

      // Simulate a token purchase
      console.log('Simulating a token purchase...');

      const purchasedTokens = 100000;
      const newBalance = userBalance.balance + purchasedTokens;

      const { data: updatedBalance, error: updateError } = await supabase
        .from('token_balance')
        .update({ balance: newBalance, updated_at: new Date().toISOString() })
        .eq('id', userBalance.id)
        .select()
        .single();

      if (updateError) {
        console.error('Error updating token balance:', updateError);
        return;
      }

      console.log('Token balance updated:', updatedBalance);

      // Record the transaction
      const { data: transaction, error: transactionError } = await supabase
        .from('token_transactions')
        .insert([
          {
            user_id: TEST_USER_ID,
            amount: purchasedTokens,
            type: 'purchase',
            reference_id: 'test-purchase-' + Date.now(),
            previous_balance: userBalance.balance,
            new_balance: newBalance
          }
        ])
        .select()
        .single();

      if (transactionError) {
        console.error('Error recording token transaction:', transactionError);
        return;
      }

      console.log('Token transaction recorded:', transaction);
    }

    console.log('Token balance test completed successfully!');
  } catch (error) {
    console.error('Error testing token balance:', error);
  }
}

testTokenBalance();
