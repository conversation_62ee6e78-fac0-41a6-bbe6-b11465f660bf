import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { FiAlertCircle, FiCheckCircle, FiInfo, FiShoppingCart } from 'react-icons/fi';
import { toast } from 'react-hot-toast';
import { fetchWithAuth } from '../lib/authUtils';

interface CreditUsageProps {
  className?: string;
  purchaseSuccess?: boolean;
}

interface CreditUsageData {
  dailyUsage: {
    used: number;
    limit: number;
    percentage: number;
    exceeded: boolean;
  };
  monthlyUsage: {
    used: number;
    limit: number;
    percentage: number;
    exceeded: boolean;
  };
  plan: string;
  maxRequestLength: number;
}

interface CreditBalance {
  id?: string;
  user_id: string;
  balance: number;
  created_at: string;
  updated_at: string;
}

interface CreditPackage {
  credits: number;
  price: number;
  priceId: string;
}

interface CreditPackages {
  [key: string]: CreditPackage;
}

const CreditUsage: React.FC<CreditUsageProps> = ({ className = '', purchaseSuccess = false }) => {
  const { user, token } = useAuth();
  const [creditUsage, setCreditUsage] = useState<CreditUsageData | null>(null);
  const [creditBalance, setCreditBalance] = useState<CreditBalance | null>(null);
  const [creditPackages, setCreditPackages] = useState<CreditPackages | null>(null);
  const [loading, setLoading] = useState(true);
  const [showPurchaseOptions, setShowPurchaseOptions] = useState(false);

  // Define refreshData function before it's used
  const refreshData = async (isPurchase = false) => {
    setLoading(true);
    console.log(`refreshData: Refreshing credit usage data${isPurchase ? ' after purchase' : ''}`);

    // Always refresh credit balance first if this is after a purchase
    if (isPurchase && user?.id) {
      console.log('refreshData: Purchase detected, refreshing credit balance first');
      await fetchCreditBalance(true);
    }

    // Try a direct fetch to the test endpoint with no-cache
    if (user?.id) {
      try {
        console.log('refreshData: Trying test endpoint with user ID:', user.id);
        const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
        const url = `${apiUrl}/api/tokens/test-usage?userId=${user.id}&t=${Date.now()}`; // Add timestamp to prevent caching

        const response = await fetch(url, {
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
            'x-user-id': user.id
          }
        });

        console.log('refreshData: Test endpoint response status:', response.status);

        if (response.ok) {
          const data = await response.json();
          console.log('refreshData: Test endpoint data:', data);

          if (data && data.dailyUsage && data.monthlyUsage) {
            console.log('refreshData: Setting credit usage data from test endpoint');
            console.log('refreshData: Daily usage:', data.dailyUsage.used);
            console.log('refreshData: Monthly usage:', data.monthlyUsage.used);
            setCreditUsage(data);

            // Also refresh credit balance and packages (if not already refreshed)
            if (!isPurchase) {
              fetchCreditBalance(false);
            }
            fetchCreditPackages();

            setLoading(false);
            return;
          } else {
            console.error('refreshData: Invalid data format from test endpoint:', data);
          }
        } else {
          console.error('refreshData: Failed to fetch from test endpoint, status:', response.status);
        }
      } catch (error) {
        console.error('refreshData: Error with test endpoint:', error);
      }
    }

    // If test endpoint failed, try the normal fetch methods
    fetchCreditUsage();

    // Only fetch credit balance if not already fetched for a purchase
    if (!isPurchase) {
      fetchCreditBalance(false);
    }

    fetchCreditPackages();
  };

  useEffect(() => {
    if (user && token) {
      console.log('CreditUsage: User and token available, fetching data');
      console.log('CreditUsage: User ID:', user.id);

      if (purchaseSuccess) {
        console.log('CreditUsage: Purchase success detected, refreshing data with priority on credit balance');
        refreshData(true); // Now this is safe because refreshData is defined before this useEffect
      } else {
        // Regular data fetch
        fetchCreditUsage();
        fetchCreditBalance(false);
        fetchCreditPackages();
      }
    } else {
      console.log('CreditUsage: User or token not available', { user: !!user, token: !!token });
    }
  }, [user, token, purchaseSuccess]);

  const fetchCreditUsage = async () => {
    try {
      if (!user?.id) {
        console.error('Cannot fetch credit usage: missing user ID');
        setLoading(false);
        return;
      }

      console.log('fetchCreditUsage: Starting request with user ID:', user.id);
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';

      // Try the test endpoint directly first
      console.log('fetchCreditUsage: Trying test endpoint directly');
      const testUrl = `${apiUrl}/api/tokens/test-usage?userId=${user.id}`;
      console.log('fetchCreditUsage: Test endpoint URL:', testUrl);

      try {
        const testResponse = await fetch(testUrl, {
          headers: {
            'Content-Type': 'application/json'
          }
        });

        console.log('fetchCreditUsage: Test endpoint response status:', testResponse.status);

        if (testResponse.ok) {
          const testData = await testResponse.json();
          console.log('fetchCreditUsage: Test endpoint data:', testData);

          if (testData && testData.dailyUsage && testData.monthlyUsage) {
            console.log('fetchCreditUsage: Setting credit usage data from test endpoint');
            setCreditUsage(testData);
            setLoading(false);
            return;
          } else {
            console.error('fetchCreditUsage: Invalid data format from test endpoint:', testData);
          }
        } else {
          console.error('fetchCreditUsage: Failed to fetch from test endpoint, status:', testResponse.status);
        }
      } catch (testError) {
        console.error('fetchCreditUsage: Error with test endpoint:', testError);
      }

      // If test endpoint failed, try the subscription check endpoint
      console.log('fetchCreditUsage: Trying subscription check endpoint');
      const url = `${apiUrl}/api/subscription/check?userId=${user.id}`;
      console.log('fetchCreditUsage: Subscription check URL:', url);

      const response = await fetchWithAuth(url, token);
      console.log('fetchCreditUsage: Subscription check response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('fetchCreditUsage: Subscription check data received:', data);

        if (data && data.dailyUsage && data.monthlyUsage) {
          console.log('fetchCreditUsage: Daily usage:', data.dailyUsage.used);
          console.log('fetchCreditUsage: Monthly usage:', data.monthlyUsage.used);
          setCreditUsage(data);
        } else {
          console.error('fetchCreditUsage: Invalid data format received from subscription check:', data);
        }
      } else {
        console.error('fetchCreditUsage: Failed to fetch from subscription check, status:', response.status);
        const errorText = await response.text();
        console.error('fetchCreditUsage: Error response from subscription check:', errorText);
      }
    } catch (error) {
      console.error('fetchCreditUsage: Error fetching credit usage:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCreditBalance = async (isPurchase = false) => {
    try {
      if (!user?.id) {
        console.error('Cannot fetch credit balance: missing user ID');
        return;
      }

      // Try direct backend call first
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
      console.log(`Fetching credit balance directly from backend${isPurchase ? ' after purchase' : ''}`);

      try {
        // Add timestamp to prevent caching and forceRefresh=true to ensure we get the latest data
        const timestamp = Date.now();
        const directResponse = await fetch(`${apiUrl}/api/tokens/balance?userId=${user.id}&forceRefresh=true&nocache=${timestamp}`, {
          headers: {
            'Content-Type': 'application/json',
            'x-user-id': user.id,
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        });

        if (directResponse.ok) {
          const data = await directResponse.json();
          console.log('Direct credit balance data:', data);
          if (data && typeof data.balance === 'number') {
            setCreditBalance(data);
            return;
          }
        }
      } catch (directError) {
        console.error('Error with direct credit balance fetch:', directError);
      }

      // Fallback to Next.js API route
      console.log(`Falling back to Next.js API route for credit balance${isPurchase ? ' after purchase' : ''}`);

      // Add timestamp to prevent caching and forceRefresh=true to ensure we get the latest data
      const timestamp = Date.now();
      const response = await fetch(`/api/tokens/balance?forceRefresh=true&nocache=${timestamp}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'x-user-id': user.id,
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Credit balance data from Next.js API:', data);
        setCreditBalance(data);
      } else {
        console.error('Failed to fetch credit balance');
      }
    } catch (error) {
      console.error('Error fetching credit balance:', error);
    }
  };

  const fetchCreditPackages = async () => {
    try {
      if (!user?.id) {
        console.error('Cannot fetch credit packages: missing user ID');
        return;
      }

      // Try direct backend call first
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
      console.log('Fetching credit packages directly from backend');

      try {
        const directResponse = await fetch(`${apiUrl}/api/tokens/packages`, {
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (directResponse.ok) {
          const data = await directResponse.json();
          console.log('Direct credit packages data:', data);
          if (data && Object.keys(data).length > 0) {
            // Convert tokens to credits in the response
            const creditPackagesData: CreditPackages = {};
            Object.entries(data).forEach(([key, pkg]: [string, any]) => {
              creditPackagesData[key] = {
                ...pkg,
                credits: pkg.tokens
              };
            });
            setCreditPackages(creditPackagesData);
            return;
          }
        }
      } catch (directError) {
        console.error('Error with direct credit packages fetch:', directError);
      }

      // Fallback to Next.js API route
      console.log('Falling back to Next.js API route for credit packages');
      const response = await fetch('/api/tokens/packages', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Credit packages data from Next.js API:', data);
        // Convert tokens to credits in the response
        const creditPackagesData: CreditPackages = {};
        Object.entries(data).forEach(([key, pkg]: [string, any]) => {
          creditPackagesData[key] = {
            ...pkg,
            credits: pkg.tokens
          };
        });
        setCreditPackages(creditPackagesData);
      } else {
        console.error('Failed to fetch credit packages');
      }
    } catch (error) {
      console.error('Error fetching credit packages:', error);
    }
  };

  const handlePurchaseCredits = async (packageId: string) => {
    try {
      if (!user?.id) {
        console.error('Cannot purchase credits: missing user ID');
        toast.error('User ID is missing. Please try again later.');
        return;
      }

      if (!token) {
        console.error('Cannot purchase credits: missing authentication token');
        toast.error('Authentication token is missing. Please try again later or log in again.');
        return;
      }

      // Show loading toast
      const loadingToast = toast.loading(`Preparing ${packageId} credit package checkout...`);

      try {
        // Try direct backend call first
        const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
        console.log(`Trying direct backend call to ${apiUrl}/api/tokens/purchase`);

        // Create the request body
        const requestBody = {
          packageId,
          userId: user.id, // Explicitly include user ID
          successUrl: `${window.location.origin}/dashboard?purchase=success&package=${packageId}&userId=${user.id}`,
          cancelUrl: `${window.location.origin}/dashboard?purchase=cancelled`,
        };

        console.log(`Sending credit purchase request for package: ${packageId}`);
        console.log(`User ID: ${user.id}`);
        console.log('Request body:', requestBody);

        // Try direct backend call first
        try {
          const directResponse = await fetch(`${apiUrl}/api/tokens/purchase`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'x-user-id': user.id
            },
            body: JSON.stringify(requestBody)
          });

          console.log('Direct backend response status:', directResponse.status);

          if (directResponse.ok) {
            const data = await directResponse.json();
            console.log('Direct backend response data:', data);

            if (data.url) {
              // Show success toast before redirecting
              toast.success('Redirecting to secure checkout...');

              // Short delay before redirect for better UX
              setTimeout(() => {
                // Redirect to Stripe checkout
                console.log('Redirecting to Stripe checkout URL:', data.url);
                window.location.href = data.url;
              }, 1000);

              // Dismiss loading toast
              toast.dismiss(loadingToast);
              return;
            }
          }
        } catch (directError) {
          console.error('Error with direct backend call:', directError);
        }

        // Fallback to Next.js API route
        console.log('Falling back to Next.js API route for token purchase');

        // Use the Next.js API route instead of directly calling the backend
        const response = await fetch('/api/tokens/purchase', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
            'x-user-id': user.id // Add user ID to headers as well
          },
          body: JSON.stringify(requestBody)
        });

        // Log the response status
        console.log('Response status:', response.status);

        if (response.ok) {
          const data = await response.json();
          console.log('Credit purchase response:', data);

          if (data.url) {
            // Show success toast before redirecting
            toast.success('Redirecting to secure checkout...');

            // Short delay before redirect for better UX
            setTimeout(() => {
              // Redirect to Stripe checkout
              console.log('Redirecting to Stripe checkout URL:', data.url);
              window.location.href = data.url;
            }, 1000);
          } else {
            console.error('No checkout URL returned:', data);
            toast.error('Failed to create checkout session. Please try again.');
          }
        } else {
          let errorMessage = 'Failed to create checkout session';

          try {
            const errorData = await response.json();
            console.error('Error response:', errorData);
            errorMessage = errorData.error || errorMessage;

            if (errorData.details) {
              console.error('Error details:', errorData.details);
            }
          } catch (parseError) {
            console.error('Error parsing error response:', parseError);
          }

          toast.error(errorMessage);
        }
      } catch (fetchError) {
        console.error('Error making purchase request:', fetchError);
        toast.error('Failed to connect to the server. Please try again later.');
      } finally {
        // Dismiss loading toast
        toast.dismiss(loadingToast);
      }
    } catch (error) {
      console.error('Error purchasing credits:', error);
      toast.error('An error occurred while processing your request. Please try again later.');
    }
  };

  if (loading) {
    return (
      <div className={`p-4 bg-white rounded-lg shadow-md ${className}`}>
        <h2 className="text-lg font-semibold mb-4">Credit Usage</h2>
        <p>Loading usage data...</p>
      </div>
    );
  }

  // Add a direct debug function to fetch and display data from the test endpoint
  const debugFetchDirectData = async () => {
    if (!user?.id) {
      console.error('Cannot fetch debug data: missing user ID');
      return;
    }

    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
      const url = `${apiUrl}/api/tokens/test-usage?userId=${user.id}&t=${Date.now()}`;

      console.log('debugFetchDirectData: Fetching from URL:', url);

      const response = await fetch(url);
      console.log('debugFetchDirectData: Response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('debugFetchDirectData: Data received:', data);

        // Force update the credit usage data
        if (data && data.dailyUsage && data.monthlyUsage) {
          console.log('debugFetchDirectData: Setting credit usage data');
          setCreditUsage(data);
        }
      }
    } catch (error) {
      console.error('debugFetchDirectData: Error:', error);
    }
  };

  return (
    <div className={`p-4 bg-white rounded-lg shadow-md ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">Credit Usage</h2>
        <div className="flex space-x-2">
          <button
            onClick={debugFetchDirectData}
            className="flex items-center space-x-1 px-3 py-1 rounded-md text-sm transition-colors bg-green-50 text-green-600 hover:bg-green-100"
          >
            <span>Debug Fetch</span>
          </button>
          <button
            onClick={() => refreshData(false)}
            className="flex items-center space-x-1 px-3 py-1 rounded-md text-sm transition-colors bg-blue-50 text-blue-600 hover:bg-blue-100"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Debug section */}
      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
        <h3 className="text-sm font-medium mb-2">API Data</h3>
        <div className="grid grid-cols-2 gap-2">
          <div className="p-2 bg-white rounded border border-blue-100">
            <p className="text-xs font-medium">Daily Usage:</p>
            {creditUsage ? (
              <p className="text-sm font-bold">
                {creditUsage.dailyUsage?.used?.toLocaleString() || 0} / {creditUsage.dailyUsage?.limit?.toLocaleString() || 0} credits ({(creditUsage.dailyUsage?.percentage || 0).toFixed(3)}%)
              </p>
            ) : (
              <p className="text-sm font-bold text-gray-400">No data available</p>
            )}
          </div>
          <div className="p-2 bg-white rounded border border-blue-100">
            <p className="text-xs font-medium">Monthly Usage:</p>
            {creditUsage ? (
              <p className="text-sm font-bold">
                {creditUsage.monthlyUsage?.used?.toLocaleString() || 0} / {creditUsage.monthlyUsage?.limit?.toLocaleString() || 0} credits ({(creditUsage.monthlyUsage?.percentage || 0).toFixed(2)}%)
              </p>
            ) : (
              <p className="text-sm font-bold text-gray-400">No data available</p>
            )}
          </div>
        </div>
      </div>

      {creditUsage ? (
        <div className="mb-4">
          <div className="mb-2">
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium">Daily Usage</span>
              <span className="text-sm font-medium">
                {creditUsage.dailyUsage?.used?.toLocaleString() || 0} / {creditUsage.dailyUsage?.limit?.toLocaleString() || 0} credits
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className={`h-2.5 rounded-full ${
                  (creditUsage.dailyUsage?.percentage || 0) > 90 ? 'bg-red-500' :
                  (creditUsage.dailyUsage?.percentage || 0) > 75 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                style={{ width: `${Math.min(creditUsage.dailyUsage?.percentage || 0, 100)}%` }}
              ></div>
            </div>
          </div>

          <div className="mb-2">
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium">Monthly Usage</span>
              <span className="text-sm font-medium">
                {creditUsage.monthlyUsage?.used?.toLocaleString() || 0} / {creditUsage.monthlyUsage?.limit?.toLocaleString() || 0} credits
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className={`h-2.5 rounded-full ${
                  (creditUsage.monthlyUsage?.percentage || 0) > 90 ? 'bg-red-500' :
                  (creditUsage.monthlyUsage?.percentage || 0) > 75 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                style={{ width: `${Math.min(creditUsage.monthlyUsage?.percentage || 0, 100)}%` }}
              ></div>
            </div>
          </div>

          <div className="mt-4 text-sm">
            <p className="font-medium">Subscription Plan: {creditUsage.plan === 'pro' ? 'Pro' : 'Basic'}</p>
            {(creditUsage.monthlyUsage?.percentage || 0) > 75 && (
              <div className="mt-2 flex items-start p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                <FiAlertCircle className="text-yellow-500 mr-2 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-yellow-700">
                    You're using your subscription at a high rate ({Math.round(creditUsage.monthlyUsage?.percentage || 0)}% of monthly limit).
                  </p>
                  {creditUsage.plan !== 'pro' && (
                    <p className="text-yellow-700 mt-1">
                      Consider upgrading to Pro for double the credit limit.
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Debug section */}
          <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-md">
            <h3 className="text-sm font-medium mb-2">Debug Information</h3>
            <pre className="text-xs overflow-auto max-h-40 bg-gray-100 p-2 rounded">
              {JSON.stringify(creditUsage, null, 2)}
            </pre>
          </div>
        </div>
      ) : (
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
          <p className="text-sm text-yellow-700">No credit usage data available. Click refresh to try again.</p>
        </div>
      )}

      <div className="mt-4 p-3 bg-blue-50 border border-blue-100 rounded-md">
        <h3 className="text-md font-medium mb-2 flex items-center">
          <FiInfo className="mr-2 text-blue-500" />
          Additional Credits
        </h3>
        <p className="text-sm mb-2">
          You have <span className="font-bold">{creditBalance && creditBalance.balance > 0 ? creditBalance.balance.toLocaleString() : '0'}</span> additional credits available.
        </p>
        <p className="text-xs text-gray-600">
          These credits are used when you exceed your subscription limits.
        </p>
      </div>

      <div className="mt-4">
        <button
          onClick={() => setShowPurchaseOptions(!showPurchaseOptions)}
          className="flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <FiShoppingCart className="mr-2" />
          {showPurchaseOptions ? 'Hide Purchase Options' : 'Purchase Additional Credits'}
        </button>
      </div>

      {showPurchaseOptions && (
        <div className="mt-4">
          {creditPackages ? (
            <div>
              <h3 className="text-lg font-medium mb-3">Purchase Additional Credits</h3>
              <p className="text-sm text-gray-600 mb-4">
                Additional credits are used when you exceed your subscription limits. They never expire.
              </p>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                {Object.entries(creditPackages).map(([id, pkg]) => (
                  <div key={id} className="border rounded-md p-4 hover:shadow-md transition-shadow bg-white">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-medium text-lg">{id.charAt(0).toUpperCase() + id.slice(1)}</h3>
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-semibold rounded-full">
                        Best {id === 'large' ? 'Value' : id === 'medium' ? 'Seller' : 'Starter'}
                      </span>
                    </div>
                    <p className="text-3xl font-bold mb-1">${pkg.price}</p>
                    <p className="text-sm text-gray-600 mb-3">{(pkg.credits / 1000).toLocaleString()}K credits</p>
                    <p className="text-xs text-gray-500 mb-4">
                      {id === 'small' ? 'Perfect for occasional use' :
                       id === 'medium' ? 'Great for regular users' :
                       'Ideal for power users'}
                    </p>
                    <button
                      onClick={() => handlePurchaseCredits(id)}
                      className={`mt-2 w-full px-3 py-2 text-sm font-medium text-white rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                        id === 'medium'
                          ? 'bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500'
                          : 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
                      }`}
                    >
                      Purchase Now
                    </button>
                  </div>
                ))}
              </div>
              <p className="text-xs text-gray-500 mt-4">
                Credits are processed securely through Stripe. You'll be redirected to a secure checkout page.
              </p>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="flex justify-center mb-4">
                <svg className="animate-spin h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
              <p className="text-gray-600">Loading credit packages...</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CreditUsage;