require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;

// Try to use the service role key from the environment
// If not available, use a hardcoded service role key for testing
// IMPORTANT: This is just for testing - in production, always use environment variables
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVqaWFremtuY2J4aXNkYXR5Z3BvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDg2MDU5OCwiZXhwIjoyMDYwNDM2NTk4fQ.tWg9uXXdP_9Tz5sFgCGxJQjPG3KTuBOzSDxJszox-KG_1UqtJgI';

if (!supabaseUrl) {
  console.error('Error: SUPABASE_URL must be set in .env file');
  process.exit(1);
}

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key (first 10 chars):', supabaseKey.substring(0, 10) + '...');

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testSupabase() {
  try {
    // Test if we can insert data into an existing table
    console.log('Checking if users table exists...');

    const { data: tableData, error: tableError } = await supabase
      .from('users')
      .select('*')
      .limit(1);

    if (tableError) {
      console.error('Error checking users table:', tableError);
      console.log('The users table might not exist yet. Please run the SQL script to create it.');
    } else {
      console.log('Users table exists!');
      console.log('Sample data:', tableData);

      // Try to insert a test user
      console.log('\nInserting test user...');
      const testUser = {
        id: 'test-user-' + Date.now(),
        email: '<EMAIL>',
        name: 'Test User',
        picture: 'https://example.com/test.jpg',
        subscription_status: 'inactive',
        created_at: new Date().toISOString()
      };

      const { data: insertData, error: insertError } = await supabase
        .from('users')
        .insert([testUser])
        .select();

      if (insertError) {
        console.error('Error inserting test user:', insertError);
      } else {
        console.log('Successfully inserted test user!');
        console.log('Inserted data:', insertData);
      }
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

testSupabase();
