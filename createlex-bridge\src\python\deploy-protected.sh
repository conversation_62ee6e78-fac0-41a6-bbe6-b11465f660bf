#!/bin/bash

# UnrealGenAI Subscription-Protected MCP Server Deployment Script

set -e

echo "🚀 UnrealGenAI Protected MCP Server Deployment"
echo "=============================================="

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="unrealgenai-mcp"
COMPOSE_FILE="docker-compose.protected.yml"
ENV_FILE="env.protected.example"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}ℹ${NC} $1"
}

print_success() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Prerequisites checked"
}

# Setup environment
setup_environment() {
    print_status "Setting up environment..."
    
    if [ ! -f ".env" ]; then
        if [ -f "$ENV_FILE" ]; then
            cp "$ENV_FILE" ".env"
            print_warning "Created .env file from $ENV_FILE. Please update it with your actual values."
        else
            print_error "Environment template file $ENV_FILE not found."
            exit 1
        fi
    fi
    
    # Create necessary directories
    mkdir -p config logs cache protected nginx/ssl
    
    print_success "Environment setup complete"
}

# Compile protected modules
compile_protected() {
    print_status "Compiling protected modules..."
    
    if [ -f "compile_protected.py" ]; then
        python3 compile_protected.py
        print_success "Protected modules compiled"
    else
        print_warning "compile_protected.py not found. Skipping compilation."
    fi
}

# Generate license key (for testing)
generate_test_license() {
    print_status "Generating test license key..."
    
    if [ ! -f "config/license.key" ]; then
        # Generate a test license key
        TEST_LICENSE=$(openssl rand -hex 32)
        echo "$TEST_LICENSE" > config/license.key
        print_warning "Generated test license key. Replace with real license for production."
    fi
}

# Build and deploy
deploy_containers() {
    print_status "Building and deploying containers..."
    
    # Stop existing containers
    docker-compose -f "$COMPOSE_FILE" --project-name "$PROJECT_NAME" down
    
    # Build images
    docker-compose -f "$COMPOSE_FILE" --project-name "$PROJECT_NAME" build --no-cache
    
    # Start services
    docker-compose -f "$COMPOSE_FILE" --project-name "$PROJECT_NAME" up -d
    
    print_success "Containers deployed"
}

# Health check
health_check() {
    print_status "Performing health check..."
    
    # Wait for services to start
    sleep 10
    
    # Check if MCP server is responding
    if curl -sf http://localhost:8000/health > /dev/null; then
        print_success "MCP server is healthy"
    else
        print_warning "MCP server health check failed. Check logs with: docker-compose -f $COMPOSE_FILE logs unrealgenai-mcp"
    fi
}

# Display status
show_status() {
    echo ""
    echo "📊 Deployment Status:"
    echo "===================="
    
    docker-compose -f "$COMPOSE_FILE" --project-name "$PROJECT_NAME" ps
    
    echo ""
    echo "🔗 Service URLs:"
    echo "MCP Server: http://localhost:8000"
    echo "License Server: http://localhost:9000"
    echo "Health Check: http://localhost:8000/health"
    
    echo ""
    echo "📋 Useful Commands:"
    echo "View logs: docker-compose -f $COMPOSE_FILE --project-name $PROJECT_NAME logs -f"
    echo "Stop services: docker-compose -f $COMPOSE_FILE --project-name $PROJECT_NAME down"
    echo "Restart: docker-compose -f $COMPOSE_FILE --project-name $PROJECT_NAME restart"
}

# Main deployment process
main() {
    cd "$SCRIPT_DIR"
    
    check_prerequisites
    setup_environment
    compile_protected
    generate_test_license
    deploy_containers
    health_check
    show_status
    
    echo ""
    print_success "🎉 UnrealGenAI Protected MCP Server deployed successfully!"
    echo ""
    print_warning "⚠️  IMPORTANT: Update .env file with your actual license server and API keys"
    print_warning "⚠️  IMPORTANT: Replace test license key with real license for production"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "stop")
        print_status "Stopping services..."
        docker-compose -f "$COMPOSE_FILE" --project-name "$PROJECT_NAME" down
        print_success "Services stopped"
        ;;
    "restart")
        print_status "Restarting services..."
        docker-compose -f "$COMPOSE_FILE" --project-name "$PROJECT_NAME" restart
        print_success "Services restarted"
        ;;
    "logs")
        docker-compose -f "$COMPOSE_FILE" --project-name "$PROJECT_NAME" logs -f
        ;;
    "status")
        docker-compose -f "$COMPOSE_FILE" --project-name "$PROJECT_NAME" ps
        ;;
    "clean")
        print_status "Cleaning up..."
        docker-compose -f "$COMPOSE_FILE" --project-name "$PROJECT_NAME" down -v
        docker system prune -f
        print_success "Cleanup complete"
        ;;
    *)
        echo "Usage: $0 {deploy|stop|restart|logs|status|clean}"
        exit 1
        ;;
esac 