require('dotenv').config();
const { Pool } = require('pg');

// Extract database connection info from Supabase URL
// Format: postgres://postgres:[PASSWORD]@db.[PROJECT_REF].supabase.co:5432/postgres
const supabaseUrl = process.env.SUPABASE_URL;
const serviceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl) {
  console.error('SUPABASE_URL environment variable is not set');
  process.exit(1);
}

if (!serviceKey) {
  console.error('SUPABASE_SERVICE_KEY environment variable is not set');
  process.exit(1);
}

// Extract project reference from Supabase URL
const projectRef = supabaseUrl.match(/https:\/\/([^.]+)\.supabase\.co/)[1];
if (!projectRef) {
  console.error('Could not extract project reference from Supabase URL');
  process.exit(1);
}

// Construct PostgreSQL connection string
const connectionString = `postgres://postgres.${projectRef}:${serviceKey}@db.${projectRef}.supabase.co:5432/postgres`;

// Create a new PostgreSQL client
const pool = new Pool({ connectionString });

async function createTokenUsageTable() {
  const client = await pool.connect();
  
  try {
    console.log('Connected to PostgreSQL database');
    
    // Create token_usage table
    console.log('Creating token_usage table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS token_usage (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL,
        model_id TEXT NOT NULL,
        prompt_tokens INTEGER NOT NULL,
        completion_tokens INTEGER NOT NULL,
        total_tokens INTEGER NOT NULL,
        timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        request_type TEXT,
        subscription_plan TEXT
      );
    `);
    
    // Create indexes
    console.log('Creating indexes...');
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_token_usage_user_id ON token_usage(user_id);
      CREATE INDEX IF NOT EXISTS idx_token_usage_timestamp ON token_usage(timestamp);
      CREATE INDEX IF NOT EXISTS idx_token_usage_user_timestamp ON token_usage(user_id, timestamp);
    `);
    
    // Create daily view
    console.log('Creating daily view...');
    await client.query(`
      CREATE OR REPLACE VIEW daily_token_usage AS
      SELECT 
        user_id,
        DATE_TRUNC('day', timestamp) AS usage_date,
        SUM(prompt_tokens) AS prompt_tokens,
        SUM(completion_tokens) AS completion_tokens,
        SUM(total_tokens) AS total_tokens,
        subscription_plan
      FROM token_usage
      GROUP BY user_id, DATE_TRUNC('day', timestamp), subscription_plan
      ORDER BY DATE_TRUNC('day', timestamp) DESC, SUM(total_tokens) DESC;
    `);
    
    // Create monthly view
    console.log('Creating monthly view...');
    await client.query(`
      CREATE OR REPLACE VIEW monthly_token_usage AS
      SELECT 
        user_id,
        DATE_TRUNC('month', timestamp) AS usage_month,
        SUM(prompt_tokens) AS prompt_tokens,
        SUM(completion_tokens) AS completion_tokens,
        SUM(total_tokens) AS total_tokens,
        subscription_plan
      FROM token_usage
      GROUP BY user_id, DATE_TRUNC('month', timestamp), subscription_plan
      ORDER BY DATE_TRUNC('month', timestamp) DESC, SUM(total_tokens) DESC;
    `);
    
    console.log('Token usage table and views created successfully!');
    
    // Insert a test record
    console.log('Inserting test record...');
    await client.query(`
      INSERT INTO token_usage (
        user_id, 
        model_id, 
        prompt_tokens, 
        completion_tokens, 
        total_tokens, 
        request_type, 
        subscription_plan
      ) VALUES (
        '00000000-0000-0000-0000-000000000000',
        'test-model',
        10,
        20,
        30,
        'test',
        'test'
      );
    `);
    
    // Query the test record
    console.log('Querying test record...');
    const result = await client.query(`
      SELECT * FROM token_usage 
      WHERE user_id = '00000000-0000-0000-0000-000000000000'
      ORDER BY timestamp DESC
      LIMIT 1;
    `);
    
    console.log('Test record:', result.rows[0]);
    
    // Delete the test record
    console.log('Deleting test record...');
    await client.query(`
      DELETE FROM token_usage 
      WHERE user_id = '00000000-0000-0000-0000-000000000000';
    `);
    
    console.log('Setup complete!');
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

createTokenUsageTable();
