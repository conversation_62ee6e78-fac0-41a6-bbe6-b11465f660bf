#!/bin/bash

# CreateLex Bridge Development Start Script for Mac/Linux
# This script sets up the environment for local development

echo "🚀 Starting CreateLex Bridge in Development Mode..."
echo "📍 Base URL: http://localhost:3000"
echo "🔗 API URL: http://localhost:5001/api"
echo ""

# Set environment variables for development
export NODE_ENV=development
export CREATELEX_BASE_URL=http://localhost:3000
export API_BASE_URL=http://localhost:5001/api
export DEV_MODE=true

# Optional: Don't bypass subscription validation (for testing real subscription status)
# export BYPASS_SUBSCRIPTION=false

echo "🔧 Environment variables set:"
echo "   NODE_ENV=$NODE_ENV"
echo "   CREATELEX_BASE_URL=$CREATELEX_BASE_URL"
echo "   API_BASE_URL=$API_BASE_URL"
echo "   DEV_MODE=$DEV_MODE"
echo ""

echo "⚡ Starting Electron app..."
npm start

echo "✅ CreateLex Bridge stopped." 