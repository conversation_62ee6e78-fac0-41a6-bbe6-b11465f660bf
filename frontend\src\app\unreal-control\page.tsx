'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function UnrealControlRedirect() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to dashboard where the Unreal Engine Control Panel is now located
    // This works for both:
    // - Development: http://localhost:3000/dashboard
    // - Production: https://createlex.com/dashboard
    router.replace('/dashboard');
  }, [router]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Redirecting to Dashboard</h2>
        <p className="text-gray-600">
          The Unreal Engine Control Panel is now part of the main dashboard.
        </p>
      </div>
    </div>
  );
}
