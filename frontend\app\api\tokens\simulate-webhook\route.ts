import { NextRequest, NextResponse } from 'next/server';
import { apiRequest } from '../../../../lib/api-client';

/**
 * Simulate a webhook event for token purchases
 * This is used as a fallback mechanism for local development
 * when Stripe webhooks can't reach the local server
 */
export async function POST(req: NextRequest) {
  try {
    console.log('[API] Simulate webhook endpoint called');

    // Parse the request body
    const body = await req.json();

    // Validate required fields
    if (!body.userId || !body.packageId) {
      return NextResponse.json(
        {
          error: 'Missing required fields: userId and packageId are required'
        },
        { status: 400 }
      );
    }

    console.log(`[API] Simulating webhook for user ${body.userId}, package ${body.packageId}`);

    // Forward the request to the backend
    try {
      const backendResponse = await apiRequest('/api/tokens/purchase/simulate-webhook', {
        method: 'POST',
        headers: {
          'x-user-id': body.userId,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      }, undefined, body.userId);

      console.log('[API] Backend response received:', backendResponse);

      // Return the backend response
      return NextResponse.json(backendResponse);
    } catch (backendError: any) {
      console.error('[API] Error from backend:', backendError.message);

      // Return a detailed error response
      return NextResponse.json(
        {
          success: false,
          error: 'Backend error',
          details: backendError.message
        },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('[API] Simulate webhook error:', error.message);

    // Return an error response
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error.message
      },
      { status: 500 }
    );
  }
}
