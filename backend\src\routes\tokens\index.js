const express = require('express');
const router = express.Router();
const { authenticateJWT, authenticateJWTWithFallback } = require('../../middleware/auth');
const tokenPurchaseService = require('../../services/tokenPurchaseService');

// Import test usage route
const testUsageRoute = require('./test-usage');

// Import add test data route
const addTestDataRoute = require('./add-test-data');

// Import purchase route
const purchaseRoute = require('./purchase');

// Import simulate-webhook route
const simulateWebhookRoute = require('./purchase/simulate-webhook');

// Import balance route
const balanceRoute = require('./balance');

// Import force update balance route
const forceUpdateBalanceRoute = require('./force-update-balance');

// Import process checkout route
const processCheckoutRoute = require('./process-checkout');

// Mount the test usage route
router.use('/test-usage', testUsageRoute);

// Mount the add test data route
router.use('/add-test-data', addTestDataRoute);

// Mount the purchase route
router.use('/purchase', purchaseRoute);

// Mount the balance route
router.use('/balance', balanceRoute);

// Mount the force update balance route
router.use('/force-update-balance', forceUpdateBalanceRoute);

// Mount the process checkout route
router.use('/process-checkout', processCheckoutRoute);

// Mount the simulate-webhook route
router.use('/purchase/simulate-webhook', simulateWebhookRoute);

/**
 * Get available token packages
 * GET /api/tokens/packages
 */
router.get('/packages', authenticateJWTWithFallback, async (req, res) => {
  try {
    // If this is a mock user, provide mock package data
    if (req.user.id === 'mock-user-id') {
      console.log('[Tokens] Returning mock token packages');
      return res.json({
        small: { tokens: 100000, price: 5 },
        medium: { tokens: 500000, price: 20 },
        large: { tokens: 1000000, price: 35 }
      });
    }

    const packages = tokenPurchaseService.getTokenPackages();
    res.json(packages);
  } catch (error) {
    console.error('Error getting token packages:', error);

    // Return mock data on error
    console.log('[Tokens] Returning mock token packages due to error');
    return res.json({
      small: { tokens: 100000, price: 5 },
      medium: { tokens: 500000, price: 20 },
      large: { tokens: 1000000, price: 35 }
    });
  }
});

// The balance route is now handled by the separate balance.js file

/**
 * Get user's token transaction history
 * GET /api/tokens/transactions?limit=10&offset=0
 */
router.get('/transactions', authenticateJWTWithFallback, async (req, res) => {
  try {
    const userId = req.user.id;
    const limit = parseInt(req.query.limit) || 10;
    const offset = parseInt(req.query.offset) || 0;

    // If this is a mock user, provide mock transaction data
    if (userId === 'mock-user-id') {
      console.log('[Tokens] Returning mock token transactions');

      // Generate mock transactions
      const mockTransactions = [];
      const types = ['purchase', 'usage'];

      for (let i = 0; i < limit; i++) {
        const type = types[Math.floor(Math.random() * types.length)];
        const amount = type === 'purchase' ?
          [10000, 50000, 100000][Math.floor(Math.random() * 3)] :
          -1 * Math.floor(Math.random() * 1000 + 100);

        mockTransactions.push({
          id: `mock-transaction-${i}`,
          user_id: userId,
          amount,
          type,
          reference_id: `mock-ref-${Date.now()}-${i}`,
          previous_balance: 50000 - amount,
          new_balance: 50000,
          created_at: new Date(Date.now() - i * 86400000).toISOString() // Each day back
        });
      }

      return res.json(mockTransactions);
    }

    const transactions = await tokenPurchaseService.getUserTransactionHistory(userId, limit, offset);
    res.json(transactions);
  } catch (error) {
    console.error('Error getting token transactions:', error);

    // Return mock data on error
    console.log('[Tokens] Returning mock token transactions due to error');
    return res.json([
      {
        id: 'mock-transaction-1',
        user_id: req.user.id,
        amount: 50000,
        type: 'purchase',
        reference_id: `mock-ref-${Date.now()}-1`,
        previous_balance: 0,
        new_balance: 50000,
        created_at: new Date().toISOString()
      }
    ]);
  }
});

/**
 * Create checkout session for token purchase
 * POST /api/tokens/purchase
 * Body: { packageId: 'small|medium|large', successUrl: 'string', cancelUrl: 'string' }
 */
router.post('/purchase', authenticateJWTWithFallback, async (req, res) => {
  try {
    const userId = req.user.id;
    const { packageId, successUrl, cancelUrl } = req.body;

    // Validate required fields
    if (!packageId || !successUrl || !cancelUrl) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // If this is a mock user, provide mock checkout session
    if (userId === 'mock-user-id') {
      console.log('[Tokens] Returning mock checkout session');
      return res.json({
        success: true,
        sessionId: 'mock-session-id',
        url: successUrl || 'https://example.com/mock-checkout-success'
      });
    }

    const session = await tokenPurchaseService.createCheckoutSession(
      userId,
      packageId,
      successUrl,
      cancelUrl
    );

    res.json({
      success: true,
      sessionId: session.id,
      url: session.url
    });
  } catch (error) {
    console.error('Error creating checkout session:', error);

    // Return mock data on error
    console.log('[Tokens] Returning mock checkout session due to error');
    return res.json({
      success: true,
      sessionId: 'mock-session-id',
      url: req.body.successUrl || 'https://example.com/mock-checkout-success'
    });
  }
});

module.exports = router;
