# CreateLex Bridge - Build Instructions

## Overview

The CreateLex Bridge has two build modes for the frontend:

1. **Full Build** - Uses the complete Next.js frontend with static export
2. **Simple Build** - Uses basic HTML files for faster building and smaller bundle size

## Quick Start

### Option 1: Simple Build (Recommended for testing)

```bash
npm run build-win-simple
```

This creates a basic but functional bridge with:
- OAuth authentication flow
- Simple dashboard with MCP controls
- Minimal dependencies
- Fast build time

### Option 2: Full Build (Complete functionality)

```bash
# First, ensure frontend dependencies are installed
cd ../frontend
npm install
cd ../createlex-bridge

# Then build
npm run build-win
```

This includes the complete Next.js frontend with full functionality.

## Build Commands

### Windows
- `npm run build-win` - Full build for Windows
- `npm run build-win-simple` - Simple build for Windows

### macOS
- `npm run build-mac` - Full build for macOS
- `npm run build-mac-simple` - Simple build for macOS

### Linux
- `npm run build-linux` - Full build for Linux
- `npm run build-linux-simple` - Simple build for Linux

## Development

### Running in Development
```bash
npm start
```

### Testing the Bridge
```bash
npm run test-bridge
```

### Building Frontend Only
```bash
# Full Next.js build
npm run build:frontend

# Simple HTML build
npm run build:frontend-simple
```

## Troubleshooting

### Error: Missing script "export"

This happens when the frontend doesn't have the export script. Solutions:

1. **Use Simple Build** (easiest):
   ```bash
   npm run build-win-simple
   ```

2. **Fix Frontend Dependencies**:
   ```bash
   cd ../frontend
   npm install
   npm run export  # Test if export works
   cd ../createlex-bridge
   npm run build-win
   ```

3. **Manual Frontend Setup**:
   ```bash
   cd ../frontend
   npm install
   npx next build --config-file=next.config.bridge.js
   cd ../createlex-bridge
   npm run build:frontend-simple
   npm run build-win-simple
   ```

### Error: ENOENT or Missing Files

Ensure dependencies are installed:
```bash
npm install
cd ../frontend
npm install
cd ../createlex-bridge
```

### Port 7891 Already in Use

The OAuth callback server uses port 7891. If you get port conflicts:
1. Close any applications using that port
2. Or modify the port in `src/auth/oauth-flow.js`

## Build Output

### Simple Build Includes:
- OAuth authentication with external browser
- Basic login page (`web/login/index.html`)
- Simple dashboard (`web/dashboard/index.html`)
- MCP server controls
- Subscription status checking

### Full Build Includes:
- Everything from simple build
- Complete Next.js frontend
- Full dashboard functionality
- All React components
- Advanced features

## Configuration

### Environment Variables

Create a `.env` file in the bridge directory:
```
CREATELEX_BASE_URL=https://createlex.com
API_BASE_URL=https://createlex.com/api
NODE_ENV=production
```

### Bridge-Specific Settings

The bridge uses these configurations:
- **Callback Port**: 7891 (for OAuth)
- **Frontend Mode**: Static export for Electron
- **MCP Server Port**: Configured in MCP bridge settings

## File Structure After Build

```
createlex-bridge/
├── dist/                     # Built Electron app
├── web/                      # Frontend files (copied from build)
│   ├── login/
│   │   └── index.html       # OAuth login page
│   └── dashboard/
│       └── index.html       # Bridge dashboard
├── src/
│   ├── auth/
│   │   ├── oauth-flow.js    # OAuth implementation
│   │   └── auth-handler.js  # Authentication management
│   └── mcp/                 # MCP server integration
└── main.js                  # Electron main process
```

## OAuth Authentication Flow

The built bridge includes:

1. **Login Process**:
   - User clicks "Sign in with CreateLex"
   - Opens browser to CreateLex website
   - User logs in normally
   - Bridge receives authentication data
   - User returns to authenticated bridge

2. **Subscription Integration**:
   - Automatically checks subscription status
   - Uses subscription data for MCP server access
   - Refreshes status as needed

## Testing Your Build

After building, test these features:

1. **OAuth Authentication**:
   ```bash
   # Start the built app and test login
   ./dist/CreateLex Bridge.exe  # Windows
   open "dist/CreateLex Bridge.app"  # macOS
   ./dist/CreateLex-Bridge  # Linux
   ```

2. **MCP Server Integration**:
   - Authenticate with OAuth
   - Start MCP server from dashboard
   - Verify server is running on expected port

3. **Subscription Status**:
   - Check that subscription status is retrieved
   - Verify MCP server respects subscription rules

## Support

If you encounter build issues:

1. Try the simple build first: `npm run build-win-simple`
2. Check that all dependencies are installed
3. Verify the frontend directory exists and has package.json
4. Check the build logs for specific error messages

The simple build provides all OAuth functionality and should work in most cases. Use the full build only when you need the complete frontend features. 