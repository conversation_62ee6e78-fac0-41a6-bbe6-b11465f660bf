require('dotenv').config();
const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function runMigration() {
  try {
    console.log('Running admin tables migration...');
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, '../src/db/migrations/admin_tables.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Split the SQL into individual statements
    const statements = sql
      .split(';')
      .map(statement => statement.trim())
      .filter(statement => statement.length > 0);
    
    // Execute each statement
    for (const statement of statements) {
      console.log(`Executing SQL statement: ${statement.substring(0, 50)}...`);
      
      const { error } = await supabase.rpc('run_sql', { query: statement });
      
      if (error) {
        console.error('Error executing SQL statement:', error);
        console.error('Statement:', statement);
      }
    }
    
    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Error running migration:', error);
  }
}

// Create the run_sql function if it doesn't exist
async function createRunSqlFunction() {
  try {
    console.log('Creating run_sql function...');
    
    const { error } = await supabase.rpc('create_run_sql_function');
    
    if (error && error.message !== 'function create_run_sql_function() does not exist') {
      console.error('Error creating run_sql function:', error);
      return false;
    }
    
    // Create the function manually
    const createFunctionSql = `
      CREATE OR REPLACE FUNCTION run_sql(query text)
      RETURNS void
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      BEGIN
        EXECUTE query;
      END;
      $$;
    `;
    
    const { error: createError } = await supabase.rpc('run_sql_direct', { query: createFunctionSql });
    
    if (createError) {
      console.error('Error creating run_sql function directly:', createError);
      
      // Try one more approach - use the REST API to execute SQL
      const response = await fetch(`${supabaseUrl}/rest/v1/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${supabaseKey}`,
          'apikey': supabaseKey
        },
        body: JSON.stringify({
          query: createFunctionSql
        })
      });
      
      if (!response.ok) {
        console.error('Error creating run_sql function via REST API:', await response.text());
        return false;
      }
    }
    
    console.log('run_sql function created successfully!');
    return true;
  } catch (error) {
    console.error('Error creating run_sql function:', error);
    return false;
  }
}

// Create the run_sql_direct function if it doesn't exist
async function createRunSqlDirectFunction() {
  try {
    console.log('Creating run_sql_direct function...');
    
    // Use the REST API to create the function
    const createFunctionSql = `
      CREATE OR REPLACE FUNCTION run_sql_direct(query text)
      RETURNS void
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      BEGIN
        EXECUTE query;
      END;
      $$;
    `;
    
    const response = await fetch(`${supabaseUrl}/rest/v1/rpc/sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseKey}`,
        'apikey': supabaseKey
      },
      body: JSON.stringify({
        query: createFunctionSql
      })
    });
    
    if (!response.ok) {
      console.error('Error creating run_sql_direct function:', await response.text());
      return false;
    }
    
    console.log('run_sql_direct function created successfully!');
    return true;
  } catch (error) {
    console.error('Error creating run_sql_direct function:', error);
    return false;
  }
}

// Main function
async function main() {
  try {
    // Try to create the SQL execution functions
    const runSqlCreated = await createRunSqlFunction();
    const runSqlDirectCreated = await createRunSqlDirectFunction();
    
    if (!runSqlCreated && !runSqlDirectCreated) {
      console.error('Failed to create SQL execution functions. Cannot proceed with migration.');
      process.exit(1);
    }
    
    // Run the migration
    await runMigration();
  } catch (error) {
    console.error('Error in main function:', error);
    process.exit(1);
  }
}

// Run the main function
main();
