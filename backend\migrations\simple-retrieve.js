require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key (first 10 chars):', supabaseKey.substring(0, 10) + '...');

const supabase = createClient(supabaseUrl, supabaseKey);

async function listAllUsers() {
  try {
    console.log('Listing all users...');
    
    const { data, error } = await supabase
      .from('users')
      .select('*');
    
    if (error) {
      console.error('Error:', error);
    } else {
      console.log(`Found ${data.length} users:`);
      console.log(data);
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

listAllUsers();
