#!/usr/bin/env python3

import sys
import os
import asyncio

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_tools_async():
    """Test the tools using async methods"""
    try:
        # Import the MCP server
        from mcp_server import mcp
        
        print("MCP instance type:", type(mcp))
        
        # Try to use the list_tools method (async)
        try:
            tools_result = await mcp.list_tools()
            print("list_tools() result type:", type(tools_result))
            print("list_tools() result:", tools_result)
            
            if hasattr(tools_result, 'tools'):
                tools = [tool.name for tool in tools_result.tools]
            elif isinstance(tools_result, dict) and 'tools' in tools_result:
                tools = [tool['name'] for tool in tools_result['tools']]
            elif isinstance(tools_result, list):
                tools = [tool.name if hasattr(tool, 'name') else str(tool) for tool in tools_result]
            else:
                tools = []
                
            print(f"Found {len(tools)} registered MCP tools:")
            print("=" * 50)
            
            for i, tool_name in enumerate(sorted(tools), 1):
                print(f"{i:2d}. {tool_name}")
                
        except Exception as e:
            print(f"Error calling list_tools(): {e}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"Error in async test: {e}")
        import traceback
        traceback.print_exc()

def test_tools_sync():
    """Test the tools synchronously"""
    try:
        # Import the MCP server
        from mcp_server import mcp
        
        print("\nChecking if all tool functions exist in module...")
        
        # Try to manually count the decorated functions
        import mcp_server
        expected_tools = [
            'handshake_test', 'spawn_actor', 'get_scene_objects', 'create_material', 
            'modify_object', 'execute_python', 'execute_unreal_command', 'create_blueprint',
            'add_component', 'add_variable', 'add_function', 'add_node', 'connect_nodes',
            'compile_blueprint', 'spawn_blueprint', 'get_all_nodes', 'get_node_suggestions',
            'create_project_folder', 'get_files_in_folder', 'delete_node', 'get_node_guid',
            'add_nodes_bulk', 'connect_nodes_bulk', 'edit_component_property', 
            'add_component_with_events', 'add_input_binding', 'add_widget_to_user_widget',
            'edit_widget_property', 'server_status'
        ]
        
        found_functions = []
        missing_functions = []
        
        for tool_name in expected_tools:
            if hasattr(mcp_server, tool_name):
                found_functions.append(tool_name)
            else:
                missing_functions.append(tool_name)
        
        print(f"Found {len(found_functions)}/{len(expected_tools)} expected tool functions:")
        for i, func_name in enumerate(sorted(found_functions), 1):
            print(f"{i:2d}. {func_name}")
        
        if missing_functions:
            print(f"\nMissing {len(missing_functions)} functions:")
            for func_name in sorted(missing_functions):
                print(f"  - {func_name}")
        else:
            print("\n✅ All expected tool functions are present!")
        
    except Exception as e:
        print(f"Error in sync test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Run async test
    asyncio.run(test_tools_async())
    
    # Run sync test
    test_tools_sync() 