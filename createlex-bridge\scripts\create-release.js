#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const packageJson = require('../package.json');
const version = packageJson.version;

console.log(`🚀 Creating release for CreateLex Bridge v${version}`);

// Check if we're in the right directory
const distPath = path.join(__dirname, '..', 'dist');
if (!fs.existsSync(distPath)) {
  console.error('❌ dist/ directory not found. Please build the application first.');
  console.error('   Run: npm run build-win-simple');
  process.exit(1);
}

// Check for Windows installer
const windowsInstaller = path.join(distPath, `CreateLex Bridge Setup ${version}.exe`);
if (fs.existsSync(windowsInstaller)) {
  const stats = fs.statSync(windowsInstaller);
  const sizeMB = Math.round(stats.size / 1024 / 1024);
  console.log(`✅ Windows installer found: ${sizeMB} MB`);
} else {
  console.log(`⚠️  Windows installer not found at: ${windowsInstaller}`);
}

// Check for other builds
const macInstaller = path.join(distPath, `CreateLex Bridge-${version}.dmg`);
const linuxInstaller = path.join(distPath, `CreateLex Bridge-${version}.AppImage`);

if (fs.existsSync(macInstaller)) {
  const stats = fs.statSync(macInstaller);
  const sizeMB = Math.round(stats.size / 1024 / 1024);
  console.log(`✅ macOS installer found: ${sizeMB} MB`);
} else {
  console.log(`⚠️  macOS installer not found (build on macOS)`);
}

if (fs.existsSync(linuxInstaller)) {
  const stats = fs.statSync(linuxInstaller);
  const sizeMB = Math.round(stats.size / 1024 / 1024);
  console.log(`✅ Linux installer found: ${sizeMB} MB`);
} else {
  console.log(`⚠️  Linux installer not found (build failed on Windows)`);
}

// Generate release notes
const releaseNotes = `# CreateLex Bridge v${version}

## 🎉 What's New

- ✅ Cross-platform desktop application (Windows, Mac, Linux)
- ✅ Secure OAuth authentication with CreateLex
- ✅ Automatic subscription validation
- ✅ Local MCP (Model Context Protocol) server
- ✅ System tray integration
- ✅ Auto-update functionality
- ✅ Offline functionality after initial authentication

## 📥 Downloads

### Windows
- **File:** \`CreateLex-Bridge-Setup-Windows.exe\`
- **Size:** ~356 MB
- **Requirements:** Windows 10/11 (64-bit)

### macOS  
- **File:** \`CreateLex-Bridge-macOS.dmg\`
- **Size:** ~400 MB
- **Requirements:** macOS 10.15+ (Intel & Apple Silicon)

### Linux
- **File:** \`CreateLex-Bridge-Linux.AppImage\`
- **Size:** ~350 MB  
- **Requirements:** Ubuntu 18.04+, CentOS 7+, or equivalent

## 🔧 Installation

1. Download the installer for your platform
2. Follow platform-specific installation instructions
3. Launch the app and sign in with your CreateLex account
4. Ensure you have an active subscription
5. Start the MCP server to begin using features

## 🐛 Bug Fixes & Improvements

- Fixed subscription validation logic
- Improved authentication flow
- Better error handling and user feedback
- Enhanced cross-platform compatibility
- Optimized build size and performance

## 📞 Support

- **Email:** <EMAIL>
- **Discord:** https://discord.gg/createlex
- **Issues:** https://github.com/AlexKissiJr/AiWebplatform/issues

---

**Full Changelog:** https://github.com/AlexKissiJr/AiWebplatform/compare/v${parseFloat(version) - 0.1}...v${version}
`;

// Write release notes to file
const releaseNotesPath = path.join(__dirname, '..', 'RELEASE_NOTES.md');
fs.writeFileSync(releaseNotesPath, releaseNotes);
console.log(`📝 Release notes written to: ${releaseNotesPath}`);

// Generate GitHub CLI command
let ghCommand = `gh release create v${version} \\
  --title "CreateLex Bridge v${version}" \\
  --notes-file RELEASE_NOTES.md \\
  --draft`;

if (fs.existsSync(windowsInstaller)) {
  ghCommand += ` \\\n  "${windowsInstaller}#CreateLex-Bridge-Setup-Windows.exe"`;
}

if (fs.existsSync(macInstaller)) {
  ghCommand += ` \\\n  "${macInstaller}#CreateLex-Bridge-macOS.dmg"`;
}

if (fs.existsSync(linuxInstaller)) {
  ghCommand += ` \\\n  "${linuxInstaller}#CreateLex-Bridge-Linux.AppImage"`;
}

console.log('\n🎯 To create the GitHub release, run:');
console.log('```bash');
console.log(ghCommand);
console.log('```');

console.log('\n📋 Manual Release Steps:');
console.log('1. Go to https://github.com/AlexKissiJr/AiWebplatform/releases/new');
console.log(`2. Tag version: v${version}`);
console.log(`3. Release title: CreateLex Bridge v${version}`);
console.log('4. Copy release notes from RELEASE_NOTES.md');
console.log('5. Upload installer files');
console.log('6. Publish release');

console.log('\n✅ Release preparation complete!'); 