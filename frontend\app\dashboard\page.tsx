'use client';

import React, { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../contexts/AuthContext';
import { useSupabaseAuth } from '../../contexts/SupabaseAuthContext';
import Link from 'next/link';
import { fetchWithAuth } from '../../lib/authUtils';
import CreditUsage from '../../components/CreditUsage';
import TokenPurchaseProcessor from '../../components/TokenPurchaseProcessor';
import UnrealControlPanel from '../../components/UnrealControlPanel';
import CreateLexBridgeDownload from '../../components/CreateLexBridgeDownload';
import DeviceSeatsManager from '../../components/DeviceSeatsManager';
import { toast } from 'react-hot-toast';
import withSearchParamsProvider from '@/components/utils/withSearchParamsProvider';

function DashboardPage() {
  const { isAuthenticated, isLoading, user, hasActiveSubscription, logout, updateSubscription, token } = useAuth();
  const { user: supabaseUser, isLoading: supabaseLoading } = useSupabaseAuth();
  const router = useRouter();
  const [greeting, setGreeting] = useState('');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastChecked, setLastChecked] = useState<string | null>(null);
  const [isFindingCustomer, setIsFindingCustomer] = useState(false);
  const [customerInfo, setCustomerInfo] = useState<any>(null);
  const [hasCheckedSubscription, setHasCheckedSubscription] = useState(false);
  const [lastCheckedUserId, setLastCheckedUserId] = useState<string | null>(null);
  const [lastCheckTimestamp, setLastCheckTimestamp] = useState<number | null>(null);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [creditPurchaseSuccess, setCreditPurchaseSuccess] = useState(false);
  const settingsRef = useRef<HTMLDivElement>(null);
  const isInitialMount = useRef(true);
  const apiCallInProgress = useRef(false);

  // Function to refresh subscription status
  const refreshSubscriptionStatus = async (forceCheck = false) => {
    // Return early if no token or user ID
    if (!token || !user?.id) {
      console.log('Dashboard: Cannot refresh subscription status - missing token or user ID');
      return;
    }

    // Prevent multiple simultaneous API calls
    if (apiCallInProgress.current) {
      console.log('Dashboard: API call already in progress, skipping');
      return;
    }

    // Check if cache is expired (1 minute)
    const now = Date.now();
    const cacheExpired = !lastCheckTimestamp || (now - lastCheckTimestamp) > 1 * 60 * 1000;

    // Force check if cache is expired
    if (cacheExpired) {
      console.log('Dashboard: Cache expired, forcing refresh');
      forceCheck = true;
    }

    // Skip check if we've already checked and this isn't a forced check
    if (hasCheckedSubscription && !forceCheck) {
      console.log('Dashboard: Skipping subscription check - already checked for user', user.id);
      return;
    }

    // Set API call in progress flag
    apiCallInProgress.current = true;

    // Update the last check timestamp
    setLastCheckTimestamp(now);

    // Log the current user ID for debugging
    console.log('Dashboard: Refreshing subscription status for user ID:', user.id);

    // Set a timeout to ensure we don't get stuck in loading state
    const loadingTimeout = setTimeout(() => {
      setIsRefreshing(false);
      setHasCheckedSubscription(true);
      apiCallInProgress.current = false;
    }, 5000); // 5 seconds max loading time

    // Set refreshing state
    setIsRefreshing(true);

    try {
      // Make the API call to get subscription status directly from Stripe
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
      const response = await fetchWithAuth(
        `${apiUrl}/api/subscription/status`,
        token
      );

      if (response.ok) {
        const data = await response.json();
        console.log('Dashboard: Subscription status refreshed for user', user.id, ':', data);

        // Always respect the actual subscription status
        updateSubscription(data.hasActiveSubscription);
        setLastChecked(new Date().toLocaleTimeString());

        // If we have an active subscription but no customer info, try to fetch it
        if (data.hasActiveSubscription && !customerInfo) {
          try {
            const customerResponse = await fetchWithAuth(
              `${apiUrl}/api/subscription/find-customer`,
              token
            );

            if (customerResponse.ok) {
              const customerData = await customerResponse.json();
              console.log('Dashboard: Stripe customer search result:', customerData);
              setCustomerInfo(customerData);
            }
          } catch (customerError) {
            console.error('Dashboard: Error fetching customer info:', customerError);
          }
        }
      } else {
        console.error('Dashboard: Failed to refresh subscription status for user', user.id, ':', response.status, response.statusText);
        // Default to NOT showing the button if we can't check subscription
        updateSubscription(false);
      }
    } catch (error) {
      console.error('Dashboard: Error refreshing subscription status for user', user.id, ':', error);
      // Default to NOT showing the button if we can't check subscription
      updateSubscription(false);
    } finally {
      // Clear the timeout
      clearTimeout(loadingTimeout);
      // Always set refreshing state to false when done
      setIsRefreshing(false);
      // Mark that we've checked the subscription
      setHasCheckedSubscription(true);
      // Reset API call in progress flag
      apiCallInProgress.current = false;
    }
  };

  // Function to find Stripe customer by email
  const findStripeCustomer = async () => {
    // Return early if no token, user ID, or already finding customer
    if (!token || !user?.id || isFindingCustomer || apiCallInProgress.current) {
      console.log('Dashboard: Cannot find Stripe customer - missing token or user ID, or API call already in progress');
      return;
    }

    try {
      setIsFindingCustomer(true);
      apiCallInProgress.current = true;

      // Log the current user ID for debugging
      console.log('Dashboard: Finding Stripe customer for user ID:', user.id);

      // Make the API call to get subscription status directly from Stripe
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';

      // First, check the subscription status
      const statusResponse = await fetchWithAuth(
        `${apiUrl}/api/subscription/status`,
        token
      );

      if (statusResponse.ok) {
        const statusData = await statusResponse.json();
        console.log('Dashboard: Subscription status check result:', statusData);

        // Update subscription status based on the response
        updateSubscription(statusData.hasActiveSubscription);

        // Now get the customer details
        const customerResponse = await fetchWithAuth(
          `${apiUrl}/api/subscription/find-customer`,
          token
        );

        if (customerResponse.ok) {
          const customerData = await customerResponse.json();
          console.log('Dashboard: Stripe customer search result:', customerData);

          // If we have customer data but no subscription data, create a combined result
          if (customerData.success && !customerData.subscription && statusData.hasActiveSubscription) {
            // Create a complete customer info object with subscription details
            const enhancedCustomerInfo = {
              ...customerData,
              hasActiveSubscription: statusData.hasActiveSubscription,
              subscription: {
                id: customerData.subscriptionId || 'Unknown',
                status: statusData.subscriptionStatus || 'active',
                currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // Fallback: 30 days from now
              }
            };
            setCustomerInfo(enhancedCustomerInfo);
          } else {
            // Use the customer data as is
            setCustomerInfo(customerData);
          }

          // Update timestamps and flags in a batch to reduce re-renders
          setLastChecked(new Date().toLocaleTimeString());
          setHasCheckedSubscription(true);
          setLastCheckTimestamp(Date.now());
        } else {
          console.error('Dashboard: Failed to find Stripe customer:', customerResponse.status);
          // Still use the subscription status we got earlier
          setLastChecked(new Date().toLocaleTimeString());
          setHasCheckedSubscription(true);
          setLastCheckTimestamp(Date.now());
        }
      } else {
        console.error('Dashboard: Failed to check subscription status:', statusResponse.status);
        // Default to NOT showing the button if we can't check subscription
        updateSubscription(false);
        setHasCheckedSubscription(true);
      }
    } catch (error) {
      console.error('Dashboard: Error finding Stripe customer:', error);
      // Default to NOT showing the button if we can't check subscription
      updateSubscription(false);
    } finally {
      setIsFindingCustomer(false);
      apiCallInProgress.current = false;
    }
  };

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!isLoading && !supabaseLoading) {
      if (!isAuthenticated || !supabaseUser) {
        router.push('/login');
      }
    }
  }, [isAuthenticated, isLoading, supabaseUser, supabaseLoading, router]);

  // Handle clicking outside the settings dropdown and keyboard events
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (settingsRef.current && !settingsRef.current.contains(event.target as Node)) {
        setIsSettingsOpen(false);
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsSettingsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [settingsRef]);

  useEffect(() => {
    // Set greeting based on time of day
    const hour = new Date().getHours();
    if (hour < 12) {
      setGreeting('Good morning');
    } else if (hour < 18) {
      setGreeting('Good afternoon');
    } else {
      setGreeting('Good evening');
    }

    // Only reset subscription status on initial mount
    if (isInitialMount.current) {
      console.log('Dashboard: Initial setup on component mount');
      updateSubscription(false);
      setHasCheckedSubscription(false);
      setCustomerInfo(null);
      setLastChecked(null);
      isInitialMount.current = false;

      // Check if there was a recent credit purchase (from sessionStorage)
      const purchaseSuccess = sessionStorage.getItem('creditPurchaseSuccess');
      if (purchaseSuccess === 'true') {
        console.log('Dashboard: Detected recent credit purchase from sessionStorage');
        setCreditPurchaseSuccess(true);
        // Don't clear the flag from sessionStorage yet - we'll do that after the balance updates
      }

      // Check for credit purchase success or cancellation, or subscription success
      const urlParams = new URLSearchParams(window.location.search);
      const purchaseStatus = urlParams.get('purchase');
      const packageType = urlParams.get('package');
      const userId = urlParams.get('userId');
      const sessionId = urlParams.get('sessionId');
      const subscriptionStatus = urlParams.get('subscription');

      if (subscriptionStatus === 'success') {
        // Show success message for subscription
        toast.success('Subscription activated successfully!');

        // Force a refresh of subscription status
        setTimeout(() => {
          refreshSubscriptionStatus(true);
        }, 1000);

        // Remove the query parameters from the URL without refreshing the page
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
      } else if (purchaseStatus === 'success') {
        // Show success message for credit purchase
        toast.success(
          packageType
            ? `Successfully purchased ${packageType} credit package!`
            : 'Credit purchase successful!'
        );

        // Store purchase success in sessionStorage to persist through page refreshes
        sessionStorage.setItem('creditPurchaseSuccess', 'true');

        // Don't remove the query parameters yet, as they're needed for the TokenPurchaseProcessor
        // The TokenPurchaseProcessor component will handle removing them after processing
      } else if (purchaseStatus === 'cancelled') {
        // Show cancellation message
        toast('Credit purchase was cancelled.', {
          icon: '🔔',
          style: {
            borderRadius: '10px',
            background: '#f0f9ff',
            color: '#0369a1',
          },
        });

        // Remove the query parameters from the URL without refreshing the page
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
      }
    }

    // Add page visibility change listener
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        console.log('Dashboard: Page became visible again');

        // Force a refresh of subscription status without resetting everything
        if (user?.id && token && !apiCallInProgress.current) {
          // Only reset if it's been more than 1 minute since last check
          const now = Date.now();
          const timeSinceLastCheck = lastCheckTimestamp ? now - lastCheckTimestamp : Infinity;

          if (timeSinceLastCheck > 60 * 1000) { // 1 minute
            console.log('Dashboard: More than 1 minute since last check, forcing refresh');
            refreshSubscriptionStatus(true);
          } else {
            console.log('Dashboard: Less than 1 minute since last check, skipping refresh');
          }
        }
      }
    };

    // Add event listener
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup function
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Set a default value for hasActiveSubscription to prevent undefined state
  useEffect(() => {
    if (hasActiveSubscription === undefined) {
      updateSubscription(false);
    }
  }, [hasActiveSubscription, updateSubscription]);

  // Detect user changes and update tracking
  useEffect(() => {
    if (user?.id && !apiCallInProgress.current) {
      // Only check if user ID has changed
      if (lastCheckedUserId !== user.id) {
        console.log('Dashboard: User ID changed or detected:', user.id);

        // Update the last checked user ID
        setLastCheckedUserId(user.id);

        // Force a refresh of subscription status after a short delay
        const timer = setTimeout(() => {
          if (!apiCallInProgress.current) {
            refreshSubscriptionStatus(true);
          }
        }, 300);

        return () => clearTimeout(timer);
      }
    }
  }, [user?.id]);

  // Check subscription status when authentication is complete
  useEffect(() => {
    if (isAuthenticated && !isLoading && token && user?.id && !apiCallInProgress.current) {
      // Only refresh if we haven't checked recently
      const now = Date.now();
      const timeSinceLastCheck = lastCheckTimestamp ? now - lastCheckTimestamp : Infinity;

      if (timeSinceLastCheck > 60 * 1000 || !hasCheckedSubscription) { // 1 minute or never checked
        console.log('Dashboard: Authentication complete, checking subscription status');

        // Use a timeout to prevent race conditions with other effects
        const timer = setTimeout(() => {
          if (!apiCallInProgress.current) {
            refreshSubscriptionStatus(true);
          }
        }, 500);

        return () => clearTimeout(timer);
      }
    }

    // Cleanup function to reset subscription status when component unmounts
    return () => {
      console.log('Dashboard: Component unmounting, resetting subscription status');
      updateSubscription(false);
    };
  }, [isAuthenticated, isLoading, token, user?.id]);

  // Add this useEffect after the CreditUsage render block
  useEffect(() => {
    if (creditPurchaseSuccess) {
      console.log('Dashboard: Credit purchase success flag is active, will reset after delay');

      // Reset the flag after a longer delay to ensure CreditUsage has time to refresh
      const timer = setTimeout(() => {
        console.log('Dashboard: Resetting credit purchase success flag');
        setCreditPurchaseSuccess(false);

        // Now it's safe to clear the sessionStorage flag
        console.log('Dashboard: Clearing creditPurchaseSuccess from sessionStorage');
        sessionStorage.removeItem('creditPurchaseSuccess');
      }, 5000); // 5 seconds to ensure the balance has time to update

      return () => clearTimeout(timer);
    }
  }, [creditPurchaseSuccess]);

  if (isLoading || supabaseLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-[#f7f7f7]">
        <div className="text-center">
          <div className="spinner mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-[#f7f7f7]">
      {/* Header */}
      <header className="py-4 px-6 border-b border-gray-200 bg-white">
        <div className="max-w-screen-xl mx-auto flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full"></div>
            <span className="text-xl font-bold">CreateLex</span>
          </div>
          <div className="flex items-center space-x-4">
            {supabaseUser && (
              <div className="flex items-center space-x-4">
                <div className="relative" ref={settingsRef}>
                  <button
                    className="text-gray-600 hover:text-gray-800 focus:outline-none"
                    onClick={() => setIsSettingsOpen(!isSettingsOpen)}
                    aria-haspopup="true"
                    aria-expanded={isSettingsOpen}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </button>
                  {isSettingsOpen && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 transition-opacity duration-150 ease-in-out">
                      <Link href="/profile" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Profile
                      </Link>
                      <Link href="/settings" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Settings
                      </Link>
                      <Link href="/docs" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Documentation
                      </Link>
                      {/* Admin Dashboard Link - Only visible for admin users */}
                      {(supabaseUser?.email === '<EMAIL>' || supabaseUser?.email === '<EMAIL>') && (
                        <Link href="/admin" className="block px-4 py-2 text-sm text-indigo-600 hover:bg-gray-100">
                          Admin Dashboard
                        </Link>
                      )}
                      <button
                        onClick={() => {
                          setIsSettingsOpen(false);
                          logout();
                        }}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Sign out
                      </button>
                    </div>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">{supabaseUser.email}</span>
                  {supabaseUser.user_metadata?.avatar_url ? (
                    <Link href="/profile">
                      <img
                        src={supabaseUser.user_metadata.avatar_url}
                        alt={supabaseUser.user_metadata.full_name || 'User'}
                        className="h-8 w-8 rounded-full cursor-pointer"
                      />
                    </Link>
                  ) : (
                    <Link href="/profile">
                      <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 hover:bg-gray-300 transition-colors cursor-pointer">
                        {supabaseUser.email?.charAt(0).toUpperCase() || 'U'}
                      </div>
                    </Link>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="flex-1 py-8 px-6">
        <div className="max-w-screen-xl mx-auto">
          {/* Token Purchase Processor - Only shown when purchase=success is in the URL */}
          {(() => {
            const urlParams = typeof window !== 'undefined' ? new URLSearchParams(window.location.search) : new URLSearchParams('');
            const purchaseStatus = urlParams.get('purchase');
            const packageType = urlParams.get('package');
            const userId = urlParams.get('userId');
            const sessionId = urlParams.get('sessionId');

            if (purchaseStatus === 'success' && packageType) {
              return (
                <div className="mb-8">
                  <TokenPurchaseProcessor
                    purchase={purchaseStatus}
                    packageId={packageType}
                    userId={userId || user?.id}
                    sessionId={sessionId}
                  />
                </div>
              );
            }
            return null;
          })()}

          <div className="mb-8">
            <h1 className="text-2xl font-bold text-gray-800">{greeting}, {supabaseUser?.user_metadata?.full_name || user?.name || 'there'}!</h1>
            <p className="text-gray-600 mt-1">Welcome to your CreateLex dashboard</p>
          </div>

          {/* Subscription Status */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Subscription Status</h2>
              <div className="flex space-x-2">
                <button
                  onClick={findStripeCustomer}
                  disabled={isFindingCustomer}
                  className={`flex items-center space-x-1 px-3 py-1 rounded-md text-sm transition-colors ${isFindingCustomer ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-indigo-50 text-indigo-600 hover:bg-indigo-100'}`}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ${isFindingCustomer ? 'animate-spin' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  <span>{isFindingCustomer ? 'Searching...' : 'Find Subscription'}</span>
                </button>
                <button
                  onClick={() => refreshSubscriptionStatus(true)}
                  disabled={isRefreshing}
                  className={`flex items-center space-x-1 px-3 py-1 rounded-md text-sm transition-colors ${isRefreshing ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-blue-50 text-blue-600 hover:bg-blue-100'}`}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  <span>{isRefreshing ? 'Refreshing...' : 'Refresh'}</span>
                </button>
              </div>
            </div>

            {/* Customer info display */}
            {customerInfo && (
              <div className="mb-4 p-3 bg-gray-50 rounded-md border border-gray-200">
                <h3 className="text-sm font-medium text-gray-700 mb-2">Customer Information</h3>
                {customerInfo.success ? (
                  <div className="text-xs text-gray-600">
                    <p><span className="font-medium">Email:</span> {customerInfo.customer.email}</p>
                    {customerInfo.hasActiveSubscription && customerInfo.subscription && (
                      <>
                        <p><span className="font-medium">Status:</span> {customerInfo.subscription.status}</p>
                        <p><span className="font-medium">Renews:</span> {new Date(customerInfo.subscription.currentPeriodEnd).toLocaleDateString()}</p>
                      </>
                    )}
                  </div>
                ) : (
                  <p className="text-xs text-gray-600">{customerInfo.message}</p>
                )}
              </div>
            )}

            {isRefreshing || apiCallInProgress.current ? (
              <div>
                <div className="flex items-center space-x-2 text-blue-600 mb-4">
                  <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Checking subscription status...</span>
                </div>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-500">
                    <span>Loading...</span>
                    {lastChecked && (
                      <span className="ml-2 text-xs text-gray-400">Last checked: {lastChecked}</span>
                    )}
                  </div>
                </div>
              </div>
            ) : hasActiveSubscription ? (
              <div>
                <div className="flex items-center space-x-2 text-green-600 mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span>Active subscription</span>
                </div>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-500">
                    <span>Active</span>
                    {lastChecked && (
                      <span className="ml-2 text-xs text-gray-400">Last checked: {lastChecked}</span>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <div>
                <div className="flex items-center space-x-2 text-red-600 mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <span>No active subscription</span>
                </div>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-500">
                    {lastChecked && (
                      <span className="text-xs text-gray-400">Last checked: {lastChecked}</span>
                    )}
                  </div>
                  <Link
                    href="/subscription"
                    className="inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    Subscribe Now
                  </Link>
                </div>
              </div>
            )}
          </div>

          {/* MCP Authentication for AI Editors */}
          {hasActiveSubscription && (
            <div className="mb-8">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h2 className="text-xl font-semibold flex items-center">
                      <div className="h-8 w-8 bg-blue-100 rounded-lg flex items-center justify-center text-blue-600 mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      </div>
                      AI Editor Integration (MCP)
                    </h2>
                    <p className="text-gray-600 mt-1">Connect your AI editors (Claude Desktop, Cursor, Windsurf) to CreateLex</p>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Status */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="h-3 w-3 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium">Dashboard Authenticated</span>
                    </div>
                    <p className="text-xs text-gray-600">Ready for MCP integration</p>
                  </div>

                  {/* Quick Actions */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="text-sm font-medium mb-2">Quick Setup</h4>
                    <button
                      onClick={() => {
                        const mcpUrl = `/api/mcp/auth/callback?success=true&userId=${user?.id}&subscriptionStatus=${hasActiveSubscription}`;
                        window.open(mcpUrl, 'mcp-auth', 'width=600,height=500,scrollbars=yes,resizable=yes');
                      }}
                      className="w-full text-xs px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                    >
                      Authenticate AI Editor
                    </button>
                  </div>

                  {/* Instructions */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="text-sm font-medium mb-2">Instructions</h4>
                    <ol className="text-xs text-gray-600 space-y-1">
                      <li>1. Click "Authenticate AI Editor"</li>
                      <li>2. Return to your AI editor</li>
                      <li>3. Try MCP commands</li>
                    </ol>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-start space-x-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-600 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div>
                      <p className="text-sm text-blue-800 font-medium">Ready to use with AI editors!</p>
                      <p className="text-xs text-blue-700 mt-1">
                        Your authentication data is available for MCP integration. 
                        Click the button above when prompted by your AI editor for CreateLex authentication.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Credit Usage */}
          {hasActiveSubscription && (
            <div className="mb-8">
              <div className="mb-4">
                <h2 className="text-xl font-semibold">Token Usage</h2>
              </div>
              <CreditUsage
                key={user?.id || 'no-user'}
                purchaseSuccess={creditPurchaseSuccess}
              />
            </div>
          )}

          {/* Device Seats Management */}
          {hasActiveSubscription && (
            <div className="mb-8">
              <DeviceSeatsManager />
            </div>
          )}

          {/* CreateLex Bridge Downloads */}
          <CreateLexBridgeDownload className="mb-8" />

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {/* Unreal Engine Control Panel - HIDDEN FOR NOW */}
            {false && (
              <UnrealControlPanel
                hasActiveSubscription={hasActiveSubscription || false}
                isRefreshing={isRefreshing || apiCallInProgress.current}
              />
            )}
            {/* Unreal Engine AI Assistant - HIDDEN FOR NOW */}
            {false && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div className="p-6">
                  <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center text-purple-600 mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Unreal Engine AI Assistant</h3>
                  <p className="text-gray-600 mb-4">Powerful AI assistant designed specifically for Unreal Engine developers. Generate blueprints, materials, and solve complex development challenges.</p>
                  {isRefreshing || apiCallInProgress.current ? (
                    <button className="inline-block px-4 py-2 bg-gray-300 text-gray-600 rounded-md cursor-not-allowed" disabled>
                      <span className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Checking Access...
                      </span>
                    </button>
                  ) : hasActiveSubscription ? (
                    <Link href="/chat" className="inline-block px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
                      Open CreateLex AI
                    </Link>
                  ) : (
                    <button className="inline-block px-4 py-2 bg-gray-300 text-gray-600 rounded-md cursor-not-allowed" disabled>
                      Requires Subscription
                    </button>
                  )}
                </div>
              </div>
            )}

            {/* Documentation Feature */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="p-6">
                <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center text-purple-600 mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold mb-2">Documentation</h3>
                <p className="text-gray-600 mb-4">Learn how to use our AI assistant with detailed guides and examples.</p>
                <Link
                  href="/docs"
                  className="inline-block px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
                >
                  View Docs
                </Link>
              </div>
            </div>

            {/* Plugin Feature */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="p-6">
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center text-green-600 mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold mb-2">Unreal Engine Plugin</h3>
                <p className="text-gray-600 mb-4">Download our Unreal Engine plugin to integrate AI directly into your workflow.</p>
                <div className="flex space-x-2">
                  <Link
                    href="/download"
                    className="inline-block px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                  >
                    Download Plugin
                  </Link>
                  <a
                    href="https://github.com/CreateLex/CreatelexGenAI"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-block px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
                  >
                    View on GitHub
                  </a>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
            {isRefreshing || apiCallInProgress.current ? (
              <div className="text-center py-8">
                <div className="flex justify-center mb-4">
                  <svg className="animate-spin h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
                <p className="text-gray-600">Checking subscription status...</p>
              </div>
            ) : hasActiveSubscription ? (
              <div className="space-y-4">
                <p className="text-gray-600">Your recent chat sessions and generated content will appear here.</p>
                <div className="border-t border-gray-200 pt-4 text-gray-500 italic">
                  No recent activity yet. Start a chat to see your activity here.
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-600 mb-4">Subscribe to access chat history and activity tracking.</p>
                <Link
                  href="/subscription"
                  className="inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  Subscribe Now
                </Link>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="py-6 px-8 border-t border-gray-200 bg-white mt-auto">
        <div className="max-w-screen-xl mx-auto flex flex-col md:flex-row justify-between items-center text-sm text-gray-500">
          <div className="mb-4 md:mb-0">
            © {new Date().getFullYear()} CreateLex. All rights reserved.
          </div>
          <div className="flex space-x-6">
            <Link href="/terms" className="hover:text-gray-700">Terms</Link>
            <Link href="/privacy" className="hover:text-gray-700">Privacy</Link>
            <Link href="/docs" className="hover:text-gray-700">Documentation</Link>
            <Link href="/contact" className="hover:text-gray-700">Contact</Link>
          </div>
        </div>
      </footer>
    </div>
  );
}

// Export the wrapped component
export default withSearchParamsProvider(DashboardPage);
