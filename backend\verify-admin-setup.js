require('dotenv').config();
const supabase = require('./src/services/supabaseClient');

async function verifyAdminSetup() {
  console.log('Verifying admin setup...');
  
  try {
    // Check if users table has is_admin column
    console.log('Checking for is_admin column...');
    const { data: userColumns, error: columnsError } = await supabase.rpc('get_columns', {
      table_name: 'users'
    });
    
    if (columnsError) {
      console.error('Error getting columns:', columnsError);
      console.log('Will check indirectly by querying users with is_admin field');
      
      // Try to query users with is_admin field
      const { data: adminUsers, error: adminError } = await supabase
        .from('users')
        .select('id, email, name, is_admin')
        .eq('is_admin', true);
        
      if (adminError && adminError.message && adminError.message.includes('column "is_admin" does not exist')) {
        console.log('is_admin column does not exist');
      } else if (adminError) {
        console.error('Error querying admin users:', adminError);
      } else {
        console.log('is_admin column exists');
        console.log('Admin users:', adminUsers);
      }
    } else {
      console.log('User table columns:', userColumns);
      const hasIsAdmin = userColumns.some(col => col.column_name === 'is_admin');
      console.log('Has is_admin column:', hasIsAdmin);
    }
    
    // Check if tables exist
    const tables = ['usage_logs', 'api_keys', 'billing_records'];
    for (const table of tables) {
      try {
        console.log(`Checking if ${table} exists...`);
        const { data, error } = await supabase
          .from(table)
          .select('count');
          
        if (error && error.code === '42P01') {
          console.log(`Table '${table}' does not exist`);
        } else if (error) {
          console.log(`Error checking table '${table}':`, error);
        } else {
          console.log(`Table '${table}' exists and has ${data[0].count} records`);
        }
      } catch (err) {
        console.log(`Exception checking table '${table}':`, err);
      }
    }
    
    // Get total user count
    const { count, error: countError } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true });
      
    if (countError) {
      console.error('Error getting user count:', countError);
    } else {
      console.log(`Total users: ${count}`);
    }
    
  } catch (error) {
    console.error('Exception in verification:', error);
  }
}

// Run the verification
verifyAdminSetup()
  .then(() => {
    console.log('Verification completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
