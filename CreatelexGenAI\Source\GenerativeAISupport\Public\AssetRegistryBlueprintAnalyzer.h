#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Engine/Blueprint.h"
#include "Dom/JsonObject.h"
#include "AssetRegistryBlueprintAnalyzer.generated.h"

// Forward declarations
class UBlueprint;

/**
 * Lightweight Blueprint analyzer using Unreal Engine's Asset Registry API
 * Much more efficient than parsing .umap files directly
 * Provides comprehensive Blueprint information for AI generation context
 */
UCLASS(BlueprintType, Blueprintable)
class GENERATIVEAISUPPORT_API UAssetRegistryBlueprintAnalyzer : public UObject
{
    GENERATED_BODY()

public:
    /**
     * Get the current Level Script Blueprint from the active world
     * Uses direct UE API access instead of file parsing
     * @return JSON string with Level Script Blueprint information
     */
    UFUNCTION(BlueprintCallable, Category = "Asset Registry", CallInEditor)
    static FString GetCurrentLevelScriptBlueprint();

    /**
     * Get Blueprint asset information using Asset Registry
     * Lightweight operation that doesn't require loading the entire asset
     * @param BlueprintPath - Object path to the Blueprint asset
     * @return JSON string with Blueprint information
     */
    UFUNCTION(BlueprintCallable, Category = "Asset Registry", CallInEditor)
    static FString GetBlueprintAssetInfo(const FString& BlueprintPath);

    /**
     * Get all Level Script Blueprints in the project
     * Uses Asset Registry for fast enumeration
     * @return JSON string with array of Level Script Blueprints
     */
    UFUNCTION(BlueprintCallable, Category = "Asset Registry", CallInEditor)
    static FString GetAllLevelScriptBlueprints();

    /**
     * Get detailed event graph information from a Blueprint
     * Analyzes nodes, connections, and structure
     * @param BlueprintPath - Object path to the Blueprint asset
     * @return JSON string with event graph details
     */
    UFUNCTION(BlueprintCallable, Category = "Asset Registry", CallInEditor)
    static FString GetBlueprintEventGraph(const FString& BlueprintPath);

private:
    /**
     * Analyze Blueprint details including variables, functions, and graphs
     * @param Blueprint - The Blueprint object to analyze
     * @param ResponseJson - JSON object to populate with analysis results
     */
    static void AnalyzeBlueprintDetails(UBlueprint* Blueprint, TSharedPtr<FJsonObject> ResponseJson);

    /**
     * Analyze individual graph nodes for detailed information
     * @param Graph - The graph to analyze
     * @param NodesArray - Array to populate with node information
     */
    static void AnalyzeGraphNodes(class UEdGraph* Graph, TArray<TSharedPtr<FJsonValue>>& NodesArray);

    /**
     * Convert JSON object to string
     * @param JsonObject - The JSON object to convert
     * @return JSON string representation
     */
    static FString JsonObjectToString(TSharedPtr<FJsonObject> JsonObject);
}; 