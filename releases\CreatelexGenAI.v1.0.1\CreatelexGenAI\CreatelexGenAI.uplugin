{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "CreatelexGenAI", "Description": "Advanced AI integration for Unreal Engine with MCP support, LLM APIs (GPT-4, Claude, Grok), and intelligent game development tools by CreateLex", "Category": "AI Tools", "CreatedBy": "CreateLex Inc.", "CreatedByURL": "https://createlex.com/", "DocsURL": "https://docs.createlex.com/unreal-ai-studio", "MarketplaceURL": "", "SupportURL": "https://support.createlex.com/", "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": false, "Installed": false, "SupportedTargetPlatforms": ["Win64", "<PERSON>", "Linux"], "Modules": [{"Name": "GenerativeAISupport", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "SupportedTargetPlatforms": ["Win64", "<PERSON>", "Linux"]}, {"Name": "GenerativeAISupportEditor", "Type": "Editor", "LoadingPhase": "PostEngineInit", "SupportedTargetPlatforms": ["Win64", "<PERSON>", "Linux"]}], "Plugins": [{"Name": "EditorScriptingUtilities", "Enabled": true}]}