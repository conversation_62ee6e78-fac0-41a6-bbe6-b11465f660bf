"use client";

import { defaultModel, type modelID } from "@/ai/providers";
import { Message, useChat } from "@ai-sdk/react";
import { useState, useEffect, useMemo, useCallback, useRef } from "react";
import { Textarea } from "./textarea";
import { ProjectOverview } from "./project-overview";
import { Messages } from "./messages";
import { toast } from "sonner";
import { useRouter, useParams } from "next/navigation";
import { getUserId, getUserIdAsync, updateUserId, getUserIdFromMainApp } from "@/lib/user-id";
import { getSupabaseClient } from "@/lib/supabase-singleton";
import { useLocalStorage } from "@/lib/hooks/use-local-storage";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { convertToUIMessages, getChatById } from "@/lib/chat-store";
import { type Message as DBMessage } from "@/lib/db/schema";
import { nanoid } from "nanoid";
import { useMCP } from "@/lib/context/mcp-context";
import { useAuth } from "@/contexts/AuthContext";
import { useSocket } from "@/contexts/SocketContext";
import { UserIdManager } from "./user-id-manager";
import { Button } from "@/components/ui/button";
import { UserIcon, AlertTriangle } from "lucide-react";

// Type for chat data returned from the query function
type ChatData = {
  id: string;
  title?: string;
  messages: DBMessage[];
  createdAt: string;
  updatedAt?: string;
  userId: string;
}

export default function Chat() {
  const router = useRouter();
  const params = useParams();
  const chatId = params?.id as string | undefined;
  const queryClient = useQueryClient();

  const [selectedModel, setSelectedModel] = useLocalStorage<modelID>("selectedModel", defaultModel);
  const [userId, setUserId] = useState<string>('');
  const [generatedChatId, setGeneratedChatId] = useState<string>('');
  const [userIdManagerOpen, setUserIdManagerOpen] = useState<boolean>(false);
  const [tokenLimitExceeded, setTokenLimitExceeded] = useState<boolean>(false);
  const [tokenUsageData, setTokenUsageData] = useState<any>(null);
  const [showTokenNotification, setShowTokenNotification] = useState<boolean>(true);

  // Use local storage to remember if the user has dismissed the notification
  const [hasSeenTokenNotification, setHasSeenTokenNotification] = useLocalStorage<string>(`token-notification-${new Date().toISOString().split('T')[0]}`, 'false');

  // Get MCP server data from context
  const { mcpServersForApi } = useMCP();

  // Get user ID and token from auth context
  const { userId: authUserId, isAuthenticated: isAuthenticatedUser, token: authToken } = useAuth();

  // Get socket context to check for token limit errors
  const { error: socketError, hasTokenLimitError, clearError: clearSocketError } = useSocket();

  // Initialize userId
  useEffect(() => {
    const initUserId = async () => {
      console.log('CHAT: Initializing user ID in chat component');

      // If authenticated, use the user ID from the auth context
      if (isAuthenticatedUser && authUserId) {
        console.log('CHAT: Using authenticated user ID from auth context:', authUserId);
        // Store the auth user ID in localStorage for consistency
        updateUserId(authUserId, 'supabase');
        setUserId(authUserId);

        // Migrate any existing chats to this authenticated user ID
        try {
          await migrateChatsToAuthenticatedUser(authUserId);
        } catch (migrationError) {
          console.error('CHAT: Error migrating chats:', migrationError);
        }

        return; // Exit early since we have the auth user ID
      }

      // Check if we have a Supabase user ID in localStorage
      if (typeof window !== 'undefined') {
        const storedUserId = localStorage.getItem('ai-chat-user-id');
        const storedUserIdSource = localStorage.getItem('ai-chat-user-id-source');

        if (storedUserId && storedUserIdSource === 'supabase') {
          console.log('CHAT: Using stored Supabase user ID from localStorage:', storedUserId);
          setUserId(storedUserId);
          return; // Exit early since we have a stored Supabase user ID
        }
      }

      // Try to get the user ID from the main application
      try {
        console.log('CHAT: Checking for user ID from main app');
        const mainAppUserId = await getUserIdFromMainApp();

        if (mainAppUserId) {
          console.log('CHAT: Found user ID from main app:', mainAppUserId);
          updateUserId(mainAppUserId, 'supabase');
          setUserId(mainAppUserId);

          // Migrate any existing chats to this authenticated user ID
          try {
            await migrateChatsToAuthenticatedUser(mainAppUserId);
          } catch (migrationError) {
            console.error('CHAT: Error migrating chats:', migrationError);
          }

          return; // Exit early since we found a user ID from the main app
        }
      } catch (error) {
        console.error('CHAT: Error checking for user ID from main app:', error);
      }

      // If no user ID from main app, try to get from Supabase directly
      try {
        console.log('CHAT: Checking for Supabase user');
        const supabase = getSupabaseClient();
        const { data, error } = await supabase.auth.getUser();

        if (!error && data?.user?.id) {
          console.log('CHAT: Found Supabase user:', data.user.id);
          updateUserId(data.user.id, 'supabase');
          setUserId(data.user.id);

          // Migrate any existing chats to this authenticated user ID
          try {
            await migrateChatsToAuthenticatedUser(data.user.id);
          } catch (migrationError) {
            console.error('CHAT: Error migrating chats:', migrationError);
          }

          return; // Exit early since we found a Supabase user
        }
      } catch (error) {
        console.error('CHAT: Error checking for Supabase user:', error);
      }

      // Otherwise, use the async version to get user ID
      try {
        console.log('CHAT: Getting user ID asynchronously');
        const id = await getUserIdAsync();
        console.log('CHAT: Got user ID asynchronously:', id);
        setUserId(id);
      } catch (error) {
        console.error('CHAT: Error getting user ID:', error);
        // Fallback to synchronous version
        console.log('CHAT: Falling back to synchronous user ID');
        setUserId(getUserId());
      }
    };

    // Function to migrate chats from browser-generated IDs to authenticated user ID
    const migrateChatsToAuthenticatedUser = async (userId: string) => {
      console.log('CHAT: Migrating chats to authenticated user ID:', userId);

      // Get the current user ID from localStorage
      const currentUserId = localStorage.getItem('ai-chat-user-id');

      // If the current user ID is different from the authenticated user ID,
      // we need to migrate chats from the current user ID to the authenticated user ID
      if (currentUserId && currentUserId !== userId) {
        console.log(`CHAT: Current user ID (${currentUserId}) differs from authenticated user ID (${userId})`);

        try {
          // Call the Supabase API directly to update all chats
          const supabase = getSupabaseClient();
          const { error } = await supabase
            .from('chats')
            .update({ user_id: userId })
            .eq('user_id', currentUserId);

          if (error) {
            console.error('CHAT: Error migrating chats:', error);
          } else {
            console.log('CHAT: Successfully migrated chats to authenticated user ID');

            // Invalidate the chats query to refresh the sidebar
            queryClient.invalidateQueries({ queryKey: ['chats', userId] });
          }
        } catch (error) {
          console.error('CHAT: Error migrating chats:', error);
        }
      } else {
        console.log('CHAT: No migration needed, user IDs match or no current user ID');
      }
    };

    initUserId();
  }, [isAuthenticatedUser, authUserId, queryClient]);

  // Generate a chat ID if needed
  useEffect(() => {
    if (!chatId) {
      setGeneratedChatId(nanoid());
    }
  }, [chatId]);

  // Check token usage directly
  useEffect(() => {
    const checkTokenUsage = async () => {
      if (!userId) return;

      try {
        console.log('Checking token usage for user:', userId);
        const response = await fetch(`/api/token-usage?userId=${userId}&t=${Date.now()}`);

        if (response.ok) {
          const data = await response.json();
          console.log('Token usage data:', data);
          setTokenUsageData(data);

          // Check if daily or monthly limit is exceeded AND if there are no additional tokens
          if ((data.dailyUsage?.exceeded || data.monthlyUsage?.exceeded) && !data.hasAdditionalTokens) {
            console.log('Token limit exceeded and no additional tokens available:', data);
            setTokenLimitExceeded(true);

            // Only show toast if user hasn't seen it today
            if (hasSeenTokenNotification !== 'true') {
              toast.error(
                data.dailyUsage?.exceeded
                  ? `You've reached your daily limit of ${data.dailyUsage.limit.toLocaleString()} tokens. This will reset tomorrow.`
                  : `You've reached your monthly limit of ${data.monthlyUsage.limit.toLocaleString()} tokens. This will reset on the 1st of next month.`
              );
              // Mark as seen
              setHasSeenTokenNotification('true');
            }
          } else if ((data.dailyUsage?.exceeded || data.monthlyUsage?.exceeded) && data.hasAdditionalTokens) {
            console.log('Token limit exceeded but additional tokens available:', data);
            // Don't set token limit exceeded flag if additional tokens are available
            setTokenLimitExceeded(false);

            // Only show toast if user hasn't seen it today
            if (hasSeenTokenNotification !== 'true') {
              toast.info(
                `You're now using your additional tokens. You have ${data.tokenBalance.toLocaleString()} tokens available.`
              );
              // Mark as seen
              setHasSeenTokenNotification('true');
            }
          } else {
            setTokenLimitExceeded(false);
          }
        } else {
          console.error('Error fetching token usage:', response.status, response.statusText);
        }
      } catch (error) {
        console.error('Error checking token usage:', error);
      }
    };

    checkTokenUsage();

    // Set up an interval to check token usage every minute
    const intervalId = setInterval(checkTokenUsage, 60000);

    return () => clearInterval(intervalId);
  }, [userId, hasSeenTokenNotification, setHasSeenTokenNotification]);

  // Use React Query to fetch chat history
  const { data: chatData, isLoading: isLoadingChat } = useQuery({
    queryKey: ['chat', chatId, userId, authToken] as const,
    queryFn: async ({ queryKey }) => {
      const [_, chatId, userId, authToken] = queryKey;
      if (!chatId || !userId) return null;

      try {
        // Use the getChatById function from the chat store
        const chat = await getChatById(chatId || '', userId, authToken || undefined);

        // Convert the chat to the expected format
        if (chat) {
          return {
            id: chat.id,
            title: chat.title || '',
            createdAt: typeof chat.createdAt === 'string' ? chat.createdAt : chat.createdAt.toISOString(),
            updatedAt: typeof chat.updatedAt === 'string' ? chat.updatedAt : chat.updatedAt?.toISOString(),
            userId: chat.userId,
            messages: chat.messages || []
          };
        }
        return null;
      } catch (error) {
        console.error('Error loading chat history:', error);
        toast.error('Failed to load chat history');
        throw error;
      }
    },
    enabled: !!chatId && !!userId,
    retry: 1,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false
  });

  // Prepare initial messages from query data
  const initialMessages = useMemo(() => {
    if (!chatData || !chatData.messages || chatData.messages.length === 0) {
      return [];
    }

    // Convert DB messages to UI format, then ensure it matches the Message type from @ai-sdk/react
    const uiMessages = convertToUIMessages(chatData.messages);
    return uiMessages.map(msg => ({
      id: msg.id,
      role: msg.role as Message['role'], // Ensure role is properly typed
      content: msg.content,
      parts: msg.parts,
    } as Message));
  }, [chatData]);

  // Function to refresh token usage data
  const refreshTokenUsage = useCallback(async () => {
    if (!userId) return;

    try {
      console.log('Refreshing token usage after message completion');
      const response = await fetch(`/api/token-usage?userId=${userId}&t=${Date.now()}`);

      if (response.ok) {
        const data = await response.json();
        console.log('Updated token usage data:', data);
        setTokenUsageData(data);

        // Check if we need to update the token limit exceeded flag
        if ((data.dailyUsage?.exceeded || data.monthlyUsage?.exceeded) && !data.hasAdditionalTokens) {
          setTokenLimitExceeded(true);
        } else {
          setTokenLimitExceeded(false);
        }
      }
    } catch (error) {
      console.error('Error refreshing token usage:', error);
    }
  }, [userId]);

  // Track the last time we checked the token balance to avoid too many requests
  const lastTokenBalanceCheckRef = useRef(0);
  const tokenBalanceCheckInProgressRef = useRef(false);

  // Function to get the latest token balance directly
  const getLatestTokenBalance = useCallback(async (force = false) => {
    if (!userId) return;

    // Skip if a check is already in progress
    if (tokenBalanceCheckInProgressRef.current) {
      console.log('Token balance check already in progress, skipping');
      return;
    }

    // Check if we've checked recently (within the last 2 seconds) unless forced
    const now = Date.now();
    const timeSinceLastCheck = now - lastTokenBalanceCheckRef.current;
    if (!force && timeSinceLastCheck < 2000) {
      console.log(`Skipping token balance check (checked ${timeSinceLastCheck}ms ago)`);
      return;
    }

    // Mark check as in progress and update last check time
    tokenBalanceCheckInProgressRef.current = true;
    lastTokenBalanceCheckRef.current = now;

    try {
      console.log('Getting latest token balance directly');
      const response = await fetch(`/api/tokens/get-latest-balance?userId=${userId}&t=${now}`);

      if (response.ok) {
        const data = await response.json();
        console.log('Latest token balance data:', data);

        // Update the token usage data with the latest balance
        if (tokenUsageData) {
          const updatedData = {
            ...tokenUsageData,
            tokenBalance: data.balance || 0,
            hasAdditionalTokens: (data.balance || 0) > 0
          };
          console.log('Updated token usage data with latest balance:', updatedData);
          setTokenUsageData(updatedData);

          // Dispatch a custom event to notify other components (like the dashboard) about the token balance update
          const event = new CustomEvent('tokenBalanceUpdated', {
            detail: {
              balance: data.balance,
              userId: userId
            }
          });
          window.dispatchEvent(event);
          console.log('Dispatched tokenBalanceUpdated event with balance:', data.balance);
        }
      } else if (response.status === 429) {
        console.warn('Rate limit exceeded for token balance check, will try again later');
      }
    } catch (error) {
      console.error('Error getting latest token balance:', error);
    } finally {
      // Mark check as complete
      tokenBalanceCheckInProgressRef.current = false;
    }
  }, [userId, tokenUsageData]);

  const { messages, input, handleInputChange, handleSubmit, status, stop } =
    useChat({
      id: chatId || generatedChatId, // Use generated ID if no chatId in URL
      initialMessages,
      maxSteps: 20,
      body: {
        selectedModel,
        mcpServers: mcpServersForApi,
        chatId: chatId || generatedChatId, // Use generated ID if no chatId in URL
        userId,
        authToken, // Include the authentication token
      },
      experimental_throttle: 500,
      onFinish: () => {
        // Invalidate the chats query to refresh the sidebar
        if (userId) {
          queryClient.invalidateQueries({ queryKey: ['chats', userId] });

          // Schedule a few token balance checks after completion
          // This ensures we catch the updated balance even if there's a delay in processing
          // Use fewer checks with longer intervals to avoid overwhelming the API
          const checkIntervals = [1000, 5000]; // Check at 1s and 5s after completion

          checkIntervals.forEach(delay => {
            setTimeout(() => {
              console.log(`Checking token balance ${delay/1000}s after completion`);
              getLatestTokenBalance(delay === 5000); // Force the second check
            }, delay);
          });

          // Also refresh the full token usage data
          setTimeout(() => {
            refreshTokenUsage();
          }, 2000);
        }
      },
      onError: (error) => {
        toast.error(
          error.message.length > 0
            ? error.message
            : "An error occured, please try again later.",
          { position: "top-center", richColors: true },
        );
      },
    });

  // Custom submit handler
  const handleFormSubmit = useCallback((e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Check if token limit is exceeded
    if (tokenLimitExceeded) {
      console.log('Token limit exceeded, preventing submission');
      toast.error('Cannot send message: Token limit exceeded');
      return;
    }

    if (!chatId && generatedChatId && input.trim()) {
      // If this is a new conversation, redirect to the chat page with the generated ID
      const effectiveChatId = generatedChatId;

      // Submit the form
      handleSubmit(e);

      // Schedule a couple token balance checks after submission
      // This ensures we catch the updated balance even if there's a delay in processing
      // Use fewer checks with longer intervals to avoid overwhelming the API
      const checkIntervals = [2000, 8000]; // Check at 2s and 8s after submission

      checkIntervals.forEach(delay => {
        setTimeout(() => {
          console.log(`Checking token balance ${delay/1000}s after submission`);
          getLatestTokenBalance(delay === 8000); // Force the second check
        }, delay);
      });

      // Also refresh the full token usage data
      setTimeout(() => {
        refreshTokenUsage();
      }, 3000);

      // Redirect to the chat page with the generated ID
      router.push(`/chat/${effectiveChatId}`);
    } else {
      // Normal submission for existing chats
      handleSubmit(e);

      // Schedule a couple token balance checks after submission
      // Use fewer checks with longer intervals to avoid overwhelming the API
      const checkIntervals = [2000, 8000]; // Check at 2s and 8s after submission

      checkIntervals.forEach(delay => {
        setTimeout(() => {
          console.log(`Checking token balance ${delay/1000}s after submission`);
          getLatestTokenBalance(delay === 8000); // Force the second check
        }, delay);
      });

      // Also refresh the full token usage data
      setTimeout(() => {
        refreshTokenUsage();
      }, 3000);
    }
  }, [chatId, generatedChatId, input, handleSubmit, router, tokenLimitExceeded, refreshTokenUsage, getLatestTokenBalance]);

  // Set up a separate effect for token balance checks during active chat
  useEffect(() => {
    // Only run this if we have messages (active chat) and userId
    if (!messages.length || !userId) return;

    console.log('Setting up token balance checks for active chat');

    // Check token balance immediately when component mounts and there are messages
    // Use a small delay to avoid overwhelming the API
    const initialCheckTimeout = setTimeout(() => {
      console.log('Performing initial token balance check on chat load');
      getLatestTokenBalance(true); // Force the initial check
    }, 1000); // Wait 1 second before initial check

    // Check token balance less frequently during active chat to avoid overwhelming the API
    const balanceCheckIntervalId = setInterval(() => {
      // Only check if we're not currently loading a response
      if (status !== 'streaming' && status !== 'submitted') {
        console.log('Performing periodic token balance check during active chat');
        getLatestTokenBalance();
      }
    }, 30000); // Check every 30 seconds

    return () => {
      clearInterval(balanceCheckIntervalId);
      clearTimeout(initialCheckTimeout);
    };
  }, [userId, messages.length, status, getLatestTokenBalance]);

  const isLoading = status === "streaming" || status === "submitted" || isLoadingChat;

  // Token limit error message component
  const TokenLimitErrorMessage = () => {
    // Don't show anything if we don't have token usage data or if notifications are hidden
    if (!tokenUsageData || !showTokenNotification) return null;

    // Determine which limit was exceeded
    const dailyExceeded = tokenUsageData.dailyUsage?.exceeded;
    const monthlyExceeded = tokenUsageData.monthlyUsage?.exceeded;

    // If no limits are exceeded, don't show anything
    if (!dailyExceeded && !monthlyExceeded) return null;

    // Check if user has additional tokens
    const hasAdditionalTokens = tokenUsageData.hasAdditionalTokens;
    const additionalTokens = tokenUsageData.tokenBalance || 0;

    // If user has additional tokens, show an info message instead of an error
    const isError = !hasAdditionalTokens;

    // Create appropriate message
    let message;
    if (isError) {
      message = dailyExceeded
        ? `You've reached your daily limit of ${tokenUsageData.dailyUsage.limit.toLocaleString()} tokens. This will reset tomorrow.`
        : `You've reached your monthly limit of ${tokenUsageData.monthlyUsage.limit.toLocaleString()} tokens. This will reset on the 1st of next month.`;
    } else {
      message = dailyExceeded
        ? `You've exceeded your daily limit of ${tokenUsageData.dailyUsage.limit.toLocaleString()} tokens, but you have ${additionalTokens.toLocaleString()} additional tokens available.`
        : `You've exceeded your monthly limit of ${tokenUsageData.monthlyUsage.limit.toLocaleString()} tokens, but you have ${additionalTokens.toLocaleString()} additional tokens available.`;
    }

    // Determine if upgrade should be suggested
    const canUpgrade = tokenUsageData.plan === 'basic';

    // Handler for dismissing the notification
    const handleDismiss = () => {
      setShowTokenNotification(false);
    };

    // If user has additional tokens, show an info message
    if (hasAdditionalTokens && !tokenLimitExceeded) {
      return (
        <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg relative">
          {/* Dismiss button */}
          <button
            onClick={handleDismiss}
            className="absolute top-2 right-2 text-blue-500 hover:text-blue-700"
            aria-label="Dismiss notification"
          >
            ✕
          </button>

          <div className="flex items-start pr-6">
            <div className="text-blue-500 mr-3 mt-0.5 flex-shrink-0">ℹ️</div>
            <div>
              <h3 className="font-medium text-blue-800">Using Additional Tokens</h3>
              <p className="mt-1 text-sm text-blue-700">{message}</p>

              <div className="mt-2 text-sm">
                <p className="text-blue-700">
                  Daily usage: {Math.round(tokenUsageData.dailyUsage?.percentage || 0)}%
                  ({tokenUsageData.dailyUsage?.used.toLocaleString()} / {tokenUsageData.dailyUsage?.limit.toLocaleString()} tokens)
                </p>
                <p className="text-blue-700">
                  Monthly usage: {Math.round(tokenUsageData.monthlyUsage?.percentage || 0)}%
                  ({tokenUsageData.monthlyUsage?.used.toLocaleString()} / {tokenUsageData.monthlyUsage?.limit.toLocaleString()} tokens)
                </p>
                <p className="text-blue-700 font-medium mt-1">
                  Additional tokens available: {additionalTokens.toLocaleString()} tokens
                </p>
              </div>
            </div>
          </div>
        </div>
      );
    }

    // Only show the error message if tokenLimitExceeded is true
    if (!tokenLimitExceeded) return null;

    return (
      <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg relative">
        {/* Dismiss button */}
        <button
          onClick={handleDismiss}
          className="absolute top-2 right-2 text-red-500 hover:text-red-700"
          aria-label="Dismiss notification"
        >
          ✕
        </button>

        <div className="flex items-start pr-6">
          <AlertTriangle className="text-red-500 mr-3 mt-0.5 flex-shrink-0" />
          <div>
            <h3 className="font-medium text-red-800">Token Limit Exceeded</h3>
            <p className="mt-1 text-sm text-red-700">{message}</p>

            <div className="mt-2 text-sm">
              <p className="text-red-700">
                Daily usage: {Math.round(tokenUsageData.dailyUsage?.percentage || 0)}%
                ({tokenUsageData.dailyUsage?.used.toLocaleString()} / {tokenUsageData.dailyUsage?.limit.toLocaleString()} tokens)
              </p>
              <p className="text-red-700">
                Monthly usage: {Math.round(tokenUsageData.monthlyUsage?.percentage || 0)}%
                ({tokenUsageData.monthlyUsage?.used.toLocaleString()} / {tokenUsageData.monthlyUsage?.limit.toLocaleString()} tokens)
              </p>
            </div>

            {canUpgrade && (
              <div className="mt-3">
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-white text-red-600 border-red-300 hover:bg-red-50"
                  onClick={() => router.push('/settings?tab=subscription')}
                >
                  Upgrade to Pro Plan
                </Button>
                <p className="mt-1 text-xs text-red-600">
                  Upgrade to Pro for double the {dailyExceeded ? 'daily' : 'monthly'} token limit.
                </p>
              </div>
            )}

            <div className="mt-3">
              <Button
                variant="outline"
                size="sm"
                className="bg-white text-red-600 border-red-300 hover:bg-red-50"
                onClick={() => router.push('/dashboard')}
              >
                Purchase Additional Tokens
              </Button>
              <p className="mt-1 text-xs text-red-600">
                Purchase additional tokens to continue using the service.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col justify-center w-full max-w-3xl mx-auto px-4 sm:px-6 md:py-4 pt-16 min-w-0 overflow-hidden">
      {/* User ID Manager Button */}
      <div className="absolute top-4 right-4 z-10">
        {/* <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-1 text-xs"
          onClick={() => setUserIdManagerOpen(true)}
          title="Manage User ID"
        >
          <UserIcon className="h-3 w-3" />
          <span className="hidden sm:inline">User ID</span>
        </Button> */}
      </div>

      {/* Token Limit Error Message */}
      <TokenLimitErrorMessage />

      {messages.length === 0 && !isLoadingChat ? (
        <div className="max-w-xl mx-auto w-full">
          <ProjectOverview />
          <form
            onSubmit={handleFormSubmit}
            className="mt-4 w-full mx-auto"
          >
            <Textarea
              selectedModel={selectedModel}
              setSelectedModel={setSelectedModel}
              handleInputChange={handleInputChange}
              input={input}
              isLoading={isLoading}
              status={status}
              stop={stop}
              tokenLimitExceeded={tokenLimitExceeded}
            />
          </form>
        </div>
      ) : (
        <>
          <div className="flex-1 overflow-y-auto min-h-0 pb-2 w-full">
            <Messages messages={messages} isLoading={isLoading} status={status} />
          </div>
          <form
            onSubmit={handleFormSubmit}
            className="mt-2 w-full mx-auto mb-4 sm:mb-auto"
          >
            <Textarea
              selectedModel={selectedModel}
              setSelectedModel={setSelectedModel}
              handleInputChange={handleInputChange}
              input={input}
              isLoading={isLoading}
              status={status}
              stop={stop}
              tokenLimitExceeded={tokenLimitExceeded}
            />
          </form>
        </>
      )}

      {/* User ID Manager Dialog */}
      <UserIdManager
        userId={userId}
        setUserId={setUserId}
        open={userIdManagerOpen}
        onOpenChange={setUserIdManagerOpen}
      />
    </div>
  );
}
