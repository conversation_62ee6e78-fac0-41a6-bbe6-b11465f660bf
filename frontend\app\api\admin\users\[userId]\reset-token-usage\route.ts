import { NextRequest, NextResponse } from 'next/server';
import { adminApiRequest } from '@/lib/admin-api';

export async function POST(
  req: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const userId = params.userId;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Forward the request to the backend using our helper function
    const response = await adminApiRequest(`/api/admin/users/${userId}/reset-token-usage`, {
      method: 'POST'
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response from backend:', errorText);

      return NextResponse.json(
        { error: `Failed to reset token usage: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in token usage reset route:', error);

    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}
