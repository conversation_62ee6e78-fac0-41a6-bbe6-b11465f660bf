// <PERSON><PERSON><PERSON> to check the authentication token in localStorage
// Run this in the browser console to debug authentication issues

function checkAuthToken() {
  console.log('Checking authentication token...');
  
  // Check if localStorage is available
  if (typeof localStorage === 'undefined') {
    console.error('localStorage is not available');
    return;
  }
  
  // Get the auth token
  const token = localStorage.getItem('authToken');
  
  if (!token) {
    console.error('No authToken found in localStorage');
    console.log('You need to log in first or set a token manually');
    return;
  }
  
  console.log('Auth token found in localStorage');
  console.log('Token prefix:', token.substring(0, 15) + '...');
  
  // Try to decode the token (if it's a JWT)
  try {
    const parts = token.split('.');
    if (parts.length === 3) {
      // It's a JWT
      const payload = JSON.parse(atob(parts[1]));
      console.log('Decoded token payload:', payload);
      
      // Check expiration
      if (payload.exp) {
        const expirationDate = new Date(payload.exp * 1000);
        const now = new Date();
        
        console.log('Token expires at:', expirationDate.toLocaleString());
        console.log('Current time:', now.toLocaleString());
        
        if (expirationDate < now) {
          console.error('Token is expired!');
        } else {
          console.log('Token is valid (not expired)');
        }
      }
      
      // Check user info
      if (payload.sub || payload.id) {
        console.log('User ID:', payload.sub || payload.id);
      }
      
      if (payload.email) {
        console.log('User email:', payload.email);
      }
    } else {
      console.log('Token is not a standard JWT format');
    }
  } catch (error) {
    console.error('Error decoding token:', error);
    console.log('Token might not be in JWT format');
  }
  
  // Test a simple API call with the token
  console.log('Testing API call with the token...');
  const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
  
  fetch(`${apiUrl}/api/user`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  })
    .then(response => {
      console.log('API response status:', response.status);
      return response.json();
    })
    .then(data => {
      console.log('API response data:', data);
    })
    .catch(error => {
      console.error('API call error:', error);
    });
}

// Run the check
checkAuthToken();
