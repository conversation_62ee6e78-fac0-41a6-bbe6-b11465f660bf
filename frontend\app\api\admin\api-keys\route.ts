import { NextRequest, NextResponse } from 'next/server';
import { adminApiRequest } from '@/lib/admin-api';

export async function GET(req: NextRequest) {
  try {
    // Get the API key ID from the URL if present
    const url = new URL(req.url);
    const keyId = url.searchParams.get('keyId');

    // Forward the request to the backend
    let backendUrl = '/api/admin/api-keys';

    if (keyId) {
      backendUrl += `/${keyId}`;

      // Check if we're requesting usage statistics
      if (url.searchParams.get('usage') === 'true') {
        backendUrl += '/usage';
      }
    }

    const response = await adminApiRequest(backendUrl, {
      method: 'GET'
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response from backend:', errorText);

      return NextResponse.json(
        { error: `Failed to get API keys: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in API keys route:', error);

    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Get the request body
    const body = await req.json();

    // Forward the request to the backend
    const response = await adminApiRequest('/api/admin/api-keys', {
      method: 'POST',
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response from backend:', errorText);

      return NextResponse.json(
        { error: `Failed to create API key: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in API keys route:', error);

    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}
