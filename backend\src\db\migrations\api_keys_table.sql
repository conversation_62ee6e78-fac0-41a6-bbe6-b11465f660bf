-- Create API keys table
CREATE TABLE IF NOT EXISTS api_keys (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  key_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  api_key VARCHAR(255) NOT NULL UNIQUE,
  rate_limit INTEGER NOT NULL DEFAULT 100,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  last_used_at TIMESTAMP WITH TIME ZONE
);

-- Create index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS api_keys_user_id_idx ON api_keys(user_id);

-- Create index on api_key for faster lookups
CREATE INDEX IF NOT EXISTS api_keys_api_key_idx ON api_keys(api_key);

-- Create RLS policies for API keys table
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;

-- Only allow users to see their own API keys
CREATE POLICY api_keys_select_policy ON api_keys
  FOR SELECT
  USING (auth.uid() = user_id);

-- Only allow users to insert their own API keys
CREATE POLICY api_keys_insert_policy ON api_keys
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Only allow users to update their own API keys
CREATE POLICY api_keys_update_policy ON api_keys
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Only allow users to delete their own API keys
CREATE POLICY api_keys_delete_policy ON api_keys
  FOR DELETE
  USING (auth.uid() = user_id);

-- Create function to get API key usage
CREATE OR REPLACE FUNCTION get_api_key_usage(key_id UUID)
RETURNS TABLE (
  total_requests BIGINT,
  average_response_time NUMERIC,
  error_rate NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COUNT(*)::BIGINT AS total_requests,
    AVG(response_time)::NUMERIC AS average_response_time,
    (SUM(CASE WHEN status_code >= 400 THEN 1 ELSE 0 END)::NUMERIC / COUNT(*)::NUMERIC * 100)::NUMERIC AS error_rate
  FROM api_requests
  WHERE api_key_id = key_id
  AND created_at >= NOW() - INTERVAL '24 hours';
END;
$$ LANGUAGE plpgsql;
