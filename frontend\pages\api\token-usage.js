// Next.js API route support: https://nextjs.org/docs/api-routes/introduction

// Store the last successful token usage data as a simple cache
let lastTokenUsageData = {};
let lastTokenBalanceData = {};
let lastFetchTime = 0;
const CACHE_TTL = 60000; // 1 minute cache TTL

export default async function handler(req, res) {
  try {
    // Get the user ID from the query parameters
    const { userId } = req.query;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Check if we have cached data that's still fresh
    const now = Date.now();
    const timeSinceLastFetch = now - lastFetchTime;
    const hasCachedData = lastTokenUsageData[userId] && lastTokenBalanceData[userId];

    // If we have cached data and it's less than 1 minute old, use it
    if (hasCachedData && timeSinceLastFetch < CACHE_TTL) {
      console.log('Using cached token usage data for user:', userId);

      // Combine the cached data
      const cachedData = {
        ...lastTokenUsageData[userId],
        tokenBalance: lastTokenBalanceData[userId]?.balance || 0,
        hasAdditionalTokens: (lastTokenBalanceData[userId]?.balance || 0) > 0
      };

      return res.status(200).json(cachedData);
    }

    console.log('Fetching token usage for user:', userId);

    // Add a timestamp to prevent caching
    const timestamp = Date.now();

    // Determine the backend URL based on the environment
    const isDevelopment = process.env.NODE_ENV === 'development';
    const backendUrl = isDevelopment
      ? 'http://localhost:5001'
      : (process.env.NEXT_PUBLIC_BACKEND_API_URL || 'https://api.createlex.com');

    console.log(`Using backend URL: ${backendUrl} (${isDevelopment ? 'development' : 'production'} mode)`);

    // Forward the request to the backend with the user ID and cache-busting parameter
    const response = await fetch(`${backendUrl}/api/tokens/test-usage?userId=${userId}&t=${timestamp}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      },
    });

    // Handle rate limiting specifically
    if (response.status === 429) {
      console.warn('Rate limit exceeded for token usage fetch');

      // If we have cached data, use it even if it's older than the TTL
      if (lastTokenUsageData[userId]) {
        console.log('Using stale cached token usage data due to rate limiting');

        // Combine the cached data
        const cachedData = {
          ...lastTokenUsageData[userId],
          tokenBalance: lastTokenBalanceData[userId]?.balance || 0,
          hasAdditionalTokens: (lastTokenBalanceData[userId]?.balance || 0) > 0,
          fromCache: true,
          cacheReason: 'rate_limited'
        };

        // Return the cached data with a 200 status
        return res.status(200).json(cachedData);
      }

      // If we don't have cached data, return a 429 status
      return res.status(429).json({
        error: 'Rate limit exceeded. Please try again later.',
        retryAfter: 60 // Suggest retry after 60 seconds
      });
    }

    // Handle other errors
    if (!response.ok) {
      // If we have cached data, use it even if it's older than the TTL
      if (lastTokenUsageData[userId]) {
        console.log(`Backend error ${response.status}, using cached token usage data`);

        // Combine the cached data
        const cachedData = {
          ...lastTokenUsageData[userId],
          tokenBalance: lastTokenBalanceData[userId]?.balance || 0,
          hasAdditionalTokens: (lastTokenBalanceData[userId]?.balance || 0) > 0,
          fromCache: true,
          cacheReason: 'backend_error'
        };

        // Return the cached data with a 200 status
        return res.status(200).json(cachedData);
      }

      throw new Error(`Backend API error: ${response.status}`);
    }

    const data = await response.json();
    console.log('Token usage data from backend:', data);

    // Cache the token usage data
    lastTokenUsageData[userId] = data;
    lastFetchTime = Date.now();

    // Also fetch the token balance with cache-busting
    let balanceData = null;
    try {
      console.log('Fetching token balance for user:', userId);
      const balanceTimestamp = Date.now();

      // Use the same backend URL as before
      const balanceResponse = await fetch(`${backendUrl}/api/tokens/balance?userId=${userId}&t=${balanceTimestamp}&forceRefresh=true`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': userId,
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
      });

      if (balanceResponse.ok) {
        balanceData = await balanceResponse.json();
        console.log('Token balance data from backend:', balanceData);

        // Cache the token balance data
        lastTokenBalanceData[userId] = balanceData;

        // Add token balance to the response
        data.tokenBalance = balanceData.balance || 0;
        data.hasAdditionalTokens = balanceData.balance > 0;
      } else if (balanceResponse.status === 429) {
        console.warn('Rate limit exceeded for token balance fetch');

        // If we have cached balance data, use it
        if (lastTokenBalanceData[userId]) {
          console.log('Using cached token balance data due to rate limiting');
          data.tokenBalance = lastTokenBalanceData[userId].balance || 0;
          data.hasAdditionalTokens = (lastTokenBalanceData[userId].balance || 0) > 0;
        } else {
          // Set default values if no cached data
          data.tokenBalance = 0;
          data.hasAdditionalTokens = false;
        }
      } else {
        console.warn(`Failed to fetch token balance: ${balanceResponse.status}`);

        // If we have cached balance data, use it
        if (lastTokenBalanceData[userId]) {
          console.log('Using cached token balance data due to backend error');
          data.tokenBalance = lastTokenBalanceData[userId].balance || 0;
          data.hasAdditionalTokens = (lastTokenBalanceData[userId].balance || 0) > 0;
        } else {
          // Set default values if no cached data
          data.tokenBalance = 0;
          data.hasAdditionalTokens = false;
        }
      }
    } catch (balanceError) {
      console.error('Error fetching token balance:', balanceError);

      // If we have cached balance data, use it
      if (lastTokenBalanceData[userId]) {
        console.log('Using cached token balance data due to error');
        data.tokenBalance = lastTokenBalanceData[userId].balance || 0;
        data.hasAdditionalTokens = (lastTokenBalanceData[userId].balance || 0) > 0;
      } else {
        // Set default values if no cached data
        data.tokenBalance = 0;
        data.hasAdditionalTokens = false;
      }
    }

    // Return the combined data to the frontend
    res.status(200).json(data);
  } catch (error) {
    console.error('Error in token-usage API route:', error);
    res.status(500).json({ error: 'Failed to fetch token usage data' });
  }
}
