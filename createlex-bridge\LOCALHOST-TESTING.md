# CreateLex Bridge - Localhost Testing Guide

This guide helps you test the OAuth authentication flow using your local development environment.

## Prerequisites

1. **Frontend Server** - Running on http://localhost:3000
2. **Backend Server** - Running on http://localhost:5001
3. **Bridge Application** - The CreateLex Bridge desktop app

## Starting the Services

### 1. Start Backend Server
```bash
cd backend
npm start
```
Make sure it's running on http://localhost:5001

### 2. Start Frontend Server
```bash
cd frontend
npm run dev
```
Make sure it's running on http://localhost:3000

### 3. Start Bridge in Development Mode

**Option A - Using PowerShell:**
```powershell
cd createlex-bridge
.\start-dev.ps1
```

**Option B - Using Command Prompt:**
```cmd
cd createlex-bridge
start-dev.bat
```

**Option C - Using npm:**
```bash
cd createlex-bridge
npm run dev
```

## OAuth Flow Testing

1. **Bridge starts** → Shows local login page
2. **Click "Sign in with <PERSON><PERSON><PERSON><PERSON>"** → Opens browser to http://localhost:3000/login
3. **Complete login** → Should redirect to http://localhost:7891/web/auth-callback/index.html
4. **Bridge receives auth** → Shows dashboard

## Troubleshooting

### If redirect doesn't work automatically:

1. After logging in at localhost:3000, check if you see the redirect URL in the browser
2. The auth-callback page has a manual authentication option:
   - Go to http://localhost:7891/web/auth-callback/index.html
   - Follow the instructions to manually complete authentication

### Check console logs:

The bridge will show:
```
=== OAuth Flow Configuration ===
Base URL: http://localhost:3000
Opening browser for authentication: http://localhost:3000/login?redirect=...
Local callback will be at: http://localhost:7891/web/auth-callback/index.html
================================
```

### Verify frontend changes:

Make sure your frontend login page has the bridge authentication handling code deployed.

## Environment Variables

The development scripts set these automatically:
- `NODE_ENV=development`
- `CREATELEX_BASE_URL=http://localhost:3000`
- `API_BASE_URL=http://localhost:5001/api`
- `DEV_MODE=true`

## Features in Dev Mode

- Subscription checks bypassed
- Uses localhost URLs instead of production
- More detailed console logging
- Manual authentication fallback 