-- Create chats table
CREATE TABLE IF NOT EXISTS chats (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  title TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create messages table
CREATE TABLE IF NOT EXISTS messages (
  id TEXT PRIMARY KEY,
  chat_id TEXT NOT NULL REFERENCES chats(id) ON DELETE CASCADE,
  role TEXT NOT NULL,
  content JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Add index for faster queries
  CONSTRAINT fk_chat
    FOREIGN KEY (chat_id)
    REFERENCES chats(id)
    ON DELETE CASCADE
);

-- Create index on chat_id for faster queries
CREATE INDEX IF NOT EXISTS idx_messages_chat_id ON messages(chat_id);

-- <PERSON>reate index on user_id for faster queries
CREATE INDEX IF NOT EXISTS idx_chats_user_id ON chats(user_id);

-- Create RLS policies
ALTER TABLE chats ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

-- Create policy for chats
CREATE POLICY "Users can only access their own chats"
  ON chats
  FOR ALL
  USING (auth.uid()::text = user_id);

-- Create policy for messages
CREATE POLICY "Users can only access messages from their own chats"
  ON messages
  FOR ALL
  USING (
    chat_id IN (
      SELECT id FROM chats WHERE user_id = auth.uid()::text
    )
  );

-- Grant access to authenticated users
GRANT ALL ON chats TO authenticated;
GRANT ALL ON messages TO authenticated;
