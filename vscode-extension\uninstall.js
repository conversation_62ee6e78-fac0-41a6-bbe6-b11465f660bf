"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));

// src/uninstall.ts
var fs = __toESM(require("fs"));
var path = __toESM(require("path"));
var os = __toESM(require("os"));
function getVSCodeSettingsPath() {
  const appDataPath = process.env.APPDATA || (process.platform === "darwin" ? path.join(os.homedir(), "Library", "Application Support") : path.join(os.homedir(), ".config"));
  return path.join(appDataPath, "Code", "User", "settings.json");
}
function removeAllMCPData() {
  try {
    const settingsPath = getVSCodeSettingsPath();
    console.log(`Looking for VSCode settings at: ${settingsPath}`);
    if (!fs.existsSync(settingsPath)) {
      console.log("VSCode settings file not found.");
      return;
    }
    const settingsContent = fs.readFileSync(settingsPath, "utf8");
    let settings;
    try {
      settings = JSON.parse(settingsContent);
    } catch (error) {
      console.error("Failed to parse VSCode settings:", error);
      return;
    }
    settings = JSON.parse(JSON.stringify(settings));
    let modified = false;
    if (settings && settings.mcp) {
      console.log("Found MCP configuration, removing it...");
      delete settings.mcp;
      modified = true;
    }
    if (settings && settings["chat.mcp"]) {
      console.log("Found chat.mcp configuration, removing it...");
      delete settings["chat.mcp"];
      modified = true;
    }
    if (modified) {
      fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2), "utf8");
      console.log("All MCP data removed successfully from settings.json.");
    } else {
      console.log("No MCP data found in VSCode settings.");
    }
  } catch (error) {
    console.error("Error removing MCP data from settings:", error);
  }
}
removeAllMCPData();
//# sourceMappingURL=uninstall.js.map
