const express = require('express');
const router = express.Router();
const emailService = require('../../services/emailService');

// Test route for sending emails
router.post('/', async (req, res) => {
  try {
    console.log('[Test Email] Received test email request');
    
    // Send a test email
    const emailResult = await emailService.sendEmail({
      to: '<EMAIL>',
      subject: 'Test Email from CreateLex AI',
      text: 'This is a test email to verify that the email service is working correctly.',
      html: '<h1>Test Email</h1><p>This is a test email to verify that the email service is working correctly.</p>'
    });
    
    console.log('[Test Email] Email result:', emailResult);
    
    return res.json({
      success: true,
      message: 'Test email sent',
      result: emailResult
    });
  } catch (error) {
    console.error('[Test Email] Error sending test email:', error);
    return res.status(500).json({
      error: 'Failed to send test email',
      message: error.message
    });
  }
});

module.exports = router;
