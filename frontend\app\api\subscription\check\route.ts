import { NextRequest, NextResponse } from 'next/server';
import { apiRequest } from '../../../../lib/api-client';

// Completely bypass Supabase during build time
const isBuildTime = process.env.NODE_ENV === 'production' && typeof window === 'undefined';
let supabase: any = null;

// Only import and initialize Supabase if we're not in build time
if (!isBuildTime) {
  try {
    // Dynamic import to prevent build-time evaluation
    const { createClient } = require('@supabase/supabase-js');
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

    if (supabaseUrl && supabaseAnonKey) {
      supabase = createClient(supabaseUrl, supabaseAnonKey);
      console.log('[Supabase] Client initialized successfully');
    } else {
      console.log('[Supabase] Missing URL or key, client not initialized');
    }
  } catch (error) {
    console.error('[Supabase] Error initializing client:', error);
  }
}

export async function GET(req: NextRequest) {
  try {
    console.log('[API] Subscription check endpoint called');

    // During build time or if Supabase is not initialized, return default response
    if (isBuildTime || !supabase) {
      console.log('[API] Build-time environment or Supabase not initialized, returning default response');

      // Check if we should bypass subscription checks
      const bypassSubscription = process.env.NEXT_PUBLIC_BYPASS_SUBSCRIPTION === 'true';

      return NextResponse.json({
        hasActiveSubscription: bypassSubscription, // Respect bypass setting
        source: 'build_time_fallback',
        dailyUsage: {
          used: 5000,
          limit: 50000,
          percentage: 10
        },
        monthlyUsage: {
          used: 50000,
          limit: 1000000,
          percentage: 5
        },
        plan: 'basic'
      });
    }

    // Check for userId parameter (for Unreal Engine plugin)
    const { searchParams } = new URL(req.url);
    const userIdParam = searchParams.get('userId');

    // Get the authorization header
    const authHeader = req.headers.get('authorization');

    if (!authHeader && !userIdParam) {
      console.log('[API] No authorization header or userId parameter provided');
      return NextResponse.json(
        {
          hasActiveSubscription: false,
          dailyUsage: { used: 0, limit: 50000, percentage: 0 },
          monthlyUsage: { used: 0, limit: 1000000, percentage: 0 },
          plan: 'basic',
          error: 'No authorization header or userId parameter provided'
        },
        { status: 200 }
      );
    }

    let user: any = null;
    let token: string | null = null;

    if (authHeader) {
      // Extract the token
      token = authHeader.replace('Bearer ', '');

      // Get user from token
      const { data: { user: authUser }, error: userError } = await supabase.auth.getUser(token);

      if (userError || !authUser) {
        console.log('[API] Error getting user from token:', userError?.message);
        return NextResponse.json(
          {
            hasActiveSubscription: false,
            dailyUsage: { used: 0, limit: 50000, percentage: 0 },
            monthlyUsage: { used: 0, limit: 1000000, percentage: 0 },
            plan: 'basic',
            error: userError?.message || 'User not found'
          },
          { status: 200 }
        );
      }

      user = authUser;
      console.log(`[API] User found from token: ${user.id}`);
    } else if (userIdParam) {
      // Use the provided userId parameter (for Unreal Engine plugin)
      user = { id: userIdParam };
      console.log(`[API] Using userId parameter: ${userIdParam}`);
    }

    try {
      // Forward the request to the backend
      const headers: any = {
        'x-user-id': user.id
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const backendResponse = await apiRequest('/api/subscription/check', {
        method: 'GET',
        headers
      }, token, user.id);

      console.log('[API] Backend response received:', backendResponse);

      // Return the backend response
      return NextResponse.json(backendResponse);
    } catch (backendError: any) {
      console.error('[API] Error from backend:', backendError.message);

      // Check if we should bypass subscription checks
      const bypassSubscription = process.env.NEXT_PUBLIC_BYPASS_SUBSCRIPTION === 'true';

      // Return a default response if the backend is unavailable
      return NextResponse.json({
        hasActiveSubscription: bypassSubscription, // Respect bypass setting
        userId: user.id,
        source: 'frontend_fallback',
        dailyUsage: {
          used: 5000,
          limit: 50000,
          percentage: 10
        },
        monthlyUsage: {
          used: 50000,
          limit: 1000000,
          percentage: 5
        },
        plan: 'basic'
      });
    }
  } catch (error: any) {
    console.error('[API] Subscription check error:', error.message);

    // Check if we should bypass subscription checks
    const bypassSubscription = process.env.NEXT_PUBLIC_BYPASS_SUBSCRIPTION === 'true';

    // Return a default response in case of error
    return NextResponse.json({
      hasActiveSubscription: bypassSubscription, // Respect bypass setting
      source: 'error_fallback',
      dailyUsage: {
        used: 5000,
        limit: 50000,
        percentage: 10
      },
      monthlyUsage: {
        used: 50000,
        limit: 1000000,
        percentage: 5
      },
      plan: 'basic'
    });
  }
}
