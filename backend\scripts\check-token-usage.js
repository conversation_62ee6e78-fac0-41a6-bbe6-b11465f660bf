require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials from environment
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

console.log('Supabase URL:', supabaseUrl ? 'Available' : 'Missing');
console.log('Supabase Key:', supabaseKey ? 'Available (first 10 chars): ' + supabaseKey.substring(0, 10) + '...' : 'Missing');

// Create a new Supabase client for testing
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function checkTokenUsage() {
  try {
    console.log('Checking token_usage table...');
    
    // Check if the table exists
    const { data: tableExists, error: tableError } = await supabase
      .from('token_usage')
      .select('count(*)', { count: 'exact', head: true });
    
    if (tableError) {
      console.error('Error checking table:', tableError);
      return;
    }
    
    console.log('Token usage table exists!');
    
    // Get count of records
    const { count, error: countError } = await supabase
      .from('token_usage')
      .select('*', { count: 'exact', head: true });
    
    if (countError) {
      console.error('Error getting count:', countError);
      return;
    }
    
    console.log(`Token usage table has ${count} records`);
    
    // Get the most recent records
    const { data: records, error: recordsError } = await supabase
      .from('token_usage')
      .select('*')
      .order('timestamp', { ascending: false })
      .limit(5);
    
    if (recordsError) {
      console.error('Error getting records:', recordsError);
      return;
    }
    
    if (records.length === 0) {
      console.log('No records found in token_usage table');
      return;
    }
    
    console.log('Most recent records:');
    records.forEach(record => {
      console.log(`- User: ${record.user_id}, Model: ${record.model_id}, Tokens: ${record.total_tokens}, Time: ${record.timestamp}`);
    });
    
    // Get unique user IDs
    const { data: users, error: usersError } = await supabase
      .from('token_usage')
      .select('user_id')
      .order('user_id')
      .limit(100);
    
    if (usersError) {
      console.error('Error getting users:', usersError);
      return;
    }
    
    const uniqueUsers = [...new Set(users.map(u => u.user_id))];
    console.log(`Token usage table has data for ${uniqueUsers.length} unique users`);
    
    // Insert a test record
    console.log('Inserting test record...');
    const { data: insertData, error: insertError } = await supabase
      .from('token_usage')
      .insert({
        user_id: '00000000-0000-0000-0000-000000000000', // Test user ID
        model_id: 'test-script',
        prompt_tokens: 10,
        completion_tokens: 20,
        total_tokens: 30,
        request_type: 'test',
        subscription_plan: 'test'
      })
      .select();
    
    if (insertError) {
      console.error('Error inserting test record:', insertError);
      return;
    }
    
    console.log('Test record inserted successfully!');
    console.log('Record ID:', insertData[0].id);
    
    // Delete the test record
    const { error: deleteError } = await supabase
      .from('token_usage')
      .delete()
      .eq('id', insertData[0].id);
    
    if (deleteError) {
      console.error('Error deleting test record:', deleteError);
      return;
    }
    
    console.log('Test record deleted successfully!');
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

checkTokenUsage();
