const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');
const os = require('os');

class DeviceSeatService {
  constructor() {
    this.supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_KEY
    );
    
    // Seat limits per subscription type
    this.SEAT_LIMITS = {
      free: 1,
      basic: 2,
      premium: 2,
      pro: 5, // Allow more seats for pro users
      enterprise: 10 // Enterprise gets more seats
    };
  }

  /**
   * Generate a unique device ID based on hardware characteristics
   */
  generateDeviceId() {
    const cpus = os.cpus();
    const networkInterfaces = os.networkInterfaces();
    const hostname = os.hostname();
    const platform = os.platform();
    const arch = os.arch();
    
    // Combine various hardware characteristics
    const hardwareString = JSON.stringify({
      hostname,
      platform,
      arch,
      cpuModel: cpus[0]?.model || 'unknown',
      cpuCount: cpus.length,
      // Get MAC addresses (filter out internal interfaces)
      macAddresses: Object.values(networkInterfaces)
        .flat()
        .filter(iface => !iface.internal && iface.mac && iface.mac !== '00:00:00:00:00:00')
        .map(iface => iface.mac)
        .sort()
    });
    
    // Generate a consistent hash
    return crypto.createHash('sha256').update(hardwareString).digest('hex');
  }

  /**
   * Get device information
   */
  getDeviceInfo() {
    const platform = os.platform();
    const arch = os.arch();
    const hostname = os.hostname();
    
    // Generate user-friendly platform name
    let platformName = `${platform}-${arch}`;
    if (platform === 'win32') {
      platformName = `Windows-${arch}`;
    } else if (platform === 'darwin') {
      platformName = `macOS-${arch}`;
    } else if (platform === 'linux') {
      platformName = `Linux-${arch}`;
    }
    
    return {
      deviceId: this.generateDeviceId(),
      deviceName: hostname,
      platform: platformName,
      osVersion: os.release()
    };
  }

  /**
   * Check if a device can be registered for a user
   */
  async checkSeatAvailability(userId, deviceId, subscriptionPlan = 'basic') {
    try {
      // Get seat limit for the subscription plan
      const seatLimit = this.SEAT_LIMITS[subscriptionPlan] || 2;
      
      // Check if device is already registered
      const { data: existingDevice } = await this.supabase
        .from('device_seats')
        .select('id, last_active')
        .eq('user_id', userId)
        .eq('device_id', deviceId)
        .single();
      
      if (existingDevice) {
        // Device already registered, update last active
        await this.supabase
          .from('device_seats')
          .update({ 
            last_active: new Date().toISOString(),
            is_active: true 
          })
          .eq('id', existingDevice.id);
        
        return { 
          canRegister: true, 
          isExisting: true,
          message: 'Device already registered' 
        };
      }
      
      // Count active seats (active within last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const { data: activeSeats, count } = await this.supabase
        .from('device_seats')
        .select('*', { count: 'exact' })
        .eq('user_id', userId)
        .eq('is_active', true)
        .gte('last_active', thirtyDaysAgo.toISOString());
      
      if (count < seatLimit) {
        return { 
          canRegister: true, 
          isExisting: false,
          availableSeats: seatLimit - count,
          message: `You have ${seatLimit - count} seat(s) available` 
        };
      }
      
      // Return the list of active devices
      return { 
        canRegister: false, 
        isExisting: false,
        seatLimit,
        activeSeats: activeSeats || [],
        message: `Seat limit reached. Maximum ${seatLimit} devices allowed.` 
      };
    } catch (error) {
      console.error('Error checking seat availability:', error);
      throw error;
    }
  }

  /**
   * Register a device for a user
   */
  async registerDevice(userId, deviceInfo, ipAddress, subscriptionPlan = 'basic') {
    try {
      const availability = await this.checkSeatAvailability(userId, deviceInfo.deviceId, subscriptionPlan);
      
      if (!availability.canRegister) {
        return {
          success: false,
          error: availability.message,
          activeDevices: availability.activeSeats,
          seatLimit: availability.seatLimit
        };
      }
      
      // Register the device
      const { data, error } = await this.supabase
        .from('device_seats')
        .upsert({
          user_id: userId,
          device_id: deviceInfo.deviceId,
          device_name: deviceInfo.deviceName,
          platform: deviceInfo.platform,
          ip_address: ipAddress,
          last_active: new Date().toISOString(),
          is_active: true
        }, {
          onConflict: 'user_id,device_id'
        })
        .select()
        .single();
      
      if (error) throw error;
      
      // Get updated seat count
      const { count: seatCount } = await this.supabase
        .from('device_seats')
        .select('*', { count: 'exact' })
        .eq('user_id', userId)
        .eq('is_active', true);
      
      return {
        success: true,
        deviceId: deviceInfo.deviceId,
        seatCount,
        seatLimit: this.SEAT_LIMITS[subscriptionPlan] || 2,
        message: availability.isExisting ? 'Device reactivated' : 'Device registered successfully'
      };
    } catch (error) {
      console.error('Error registering device:', error);
      throw error;
    }
  }

  /**
   * Deactivate a device
   */
  async deactivateDevice(userId, deviceId) {
    try {
      const { data, error } = await this.supabase
        .from('device_seats')
        .update({ is_active: false })
        .eq('user_id', userId)
        .eq('device_id', deviceId)
        .select()
        .single();
      
      if (error) throw error;
      
      return {
        success: true,
        message: 'Device deactivated successfully'
      };
    } catch (error) {
      console.error('Error deactivating device:', error);
      throw error;
    }
  }

  /**
   * Get all devices for a user
   */
  async getUserDevices(userId) {
    try {
      const { data, error } = await this.supabase
        .from('device_seats')
        .select('*')
        .eq('user_id', userId)
        .order('last_active', { ascending: false });
      
      if (error) throw error;
      
      return data || [];
    } catch (error) {
      console.error('Error getting user devices:', error);
      throw error;
    }
  }

  /**
   * Update device activity
   */
  async updateDeviceActivity(userId, deviceId) {
    try {
      const { error } = await this.supabase
        .from('device_seats')
        .update({ last_active: new Date().toISOString() })
        .eq('user_id', userId)
        .eq('device_id', deviceId);
      
      if (error) throw error;
      
      return { success: true };
    } catch (error) {
      console.error('Error updating device activity:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Clean up inactive devices (run periodically)
   */
  async cleanupInactiveDevices(daysInactive = 30) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysInactive);
      
      const { data, error } = await this.supabase
        .from('device_seats')
        .update({ is_active: false })
        .lt('last_active', cutoffDate.toISOString())
        .eq('is_active', true)
        .select();
      
      if (error) throw error;
      
      return {
        success: true,
        deactivatedCount: data?.length || 0
      };
    } catch (error) {
      console.error('Error cleaning up inactive devices:', error);
      throw error;
    }
  }
}

module.exports = new DeviceSeatService(); 