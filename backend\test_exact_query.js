#!/usr/bin/env node
/**
 * Test the exact query that's failing in the admin API
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

console.log('🔍 Testing Exact Admin API Query');
console.log('================================');

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testExactQuery() {
  try {
    console.log('1️⃣ Testing API keys query (line 42-45 in backend)...');
    
    // This is the exact query from your backend
    const { data: apiKeysData, error: apiKeysError } = await supabase
      .from('api_keys')
      .select('*')
      .order('created_at', { ascending: false });

    if (apiKeysError) {
      console.error('❌ API keys query failed:', apiKeysError);
      return;
    }

    console.log(`✅ API keys query successful - found ${apiKeysData.length} records`);
    
    if (apiKeysData.length === 0) {
      console.log('✅ Empty result is fine - should return empty array');
      console.log('🎯 The issue is NOT with the main query');
      
      // Test if the backend can handle empty results
      const maskedKeys = apiKeysData.map(key => ({
        ...key,
        api_key: key.api_key ? key.api_key.substring(0, 8) + '•'.repeat(16) : ''
      }));
      
      console.log('✅ Masking empty array works fine');
      
      // Test Promise.all with empty array
      const keysWithUserDetails = await Promise.all(maskedKeys.map(async (key) => {
        console.log('This should not run with empty array');
        return key;
      }));
      
      console.log('✅ Promise.all with empty array works fine');
      console.log('🎯 The backend should work with empty API keys');
      
      return { keys: keysWithUserDetails };
    }

    // If we have API keys, test the user lookup
    console.log('\n2️⃣ Testing user lookup for first API key...');
    const firstKey = apiKeysData[0];
    
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('email')
      .eq('id', firstKey.user_id)
      .single();

    if (userError) {
      console.error('❌ User lookup failed:', userError);
      console.log('🎯 This is likely the source of the 500 error!');
      return;
    }

    console.log('✅ User lookup successful:', userData);
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the test
testExactQuery()
  .then((result) => {
    if (result) {
      console.log('\n✅ Query completed successfully');
      console.log('Result:', JSON.stringify(result, null, 2));
    }
  })
  .catch((error) => {
    console.error('\n❌ Test failed:', error);
  });
