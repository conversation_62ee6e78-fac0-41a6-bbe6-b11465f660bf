#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔨 Windows Build with Retry Logic');
console.log('=================================');

const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

async function runCommand(command, args = []) {
    return new Promise((resolve, reject) => {
        console.log(`🔧 Running: ${command} ${args.join(' ')}`);
        
        const child = spawn(command, args, {
            stdio: 'inherit',
            shell: true
        });

        child.on('close', (code) => {
            if (code === 0) {
                resolve();
            } else {
                reject(new Error(`Command failed with code ${code}`));
            }
        });

        child.on('error', reject);
    });
}

async function unlockFile(filePath) {
    try {
        // Try to access the file to see if it's locked
        const fd = fs.openSync(filePath, 'r+');
        fs.closeSync(fd);
        console.log(`✅ File ${filePath} is accessible`);
        return true;
    } catch (error) {
        console.log(`⚠️  File ${filePath} is locked: ${error.message}`);
        return false;
    }
}

async function waitForFileUnlock(filePath, maxRetries = 10) {
    for (let i = 0; i < maxRetries; i++) {
        if (await unlockFile(filePath)) {
            return true;
        }
        console.log(`⏳ Waiting for file to unlock... (${i + 1}/${maxRetries})`);
        await delay(2000); // Wait 2 seconds
    }
    return false;
}

async function buildWithRetry() {
    try {
        // Step 1: Build MCP executable
        console.log('📦 Step 1: Building MCP executable...');
        await runCommand('npm', ['run', 'build:mcp-exe']);
        
        // Step 2: Wait for file to be unlocked
        const mcpPath = path.join(__dirname, '..', 'src', 'python-protected', 'mcp_server.exe');
        console.log('⏳ Step 2: Waiting for MCP executable to be unlocked...');
        await delay(3000); // Initial delay
        
        if (!(await waitForFileUnlock(mcpPath))) {
            throw new Error('MCP executable is still locked after waiting');
        }

        // Step 3: Build frontend
        console.log('🎨 Step 3: Building frontend...');
        await runCommand('npm', ['run', 'build:frontend-simple']);

        // Step 4: Copy Python files
        console.log('🐍 Step 4: Copying Python files...');
        await runCommand('npm', ['run', 'build:python']);

        // Step 5: Another delay before electron-builder
        console.log('⏳ Step 5: Final preparation...');
        await delay(2000);

        // Step 6: Run electron-builder with retry
        console.log('⚡ Step 6: Running electron-builder...');
        let retries = 3;
        while (retries > 0) {
            try {
                await runCommand('npx', ['electron-builder', '--win']);
                console.log('✅ Build completed successfully!');
                return;
            } catch (error) {
                retries--;
                if (retries > 0) {
                    console.log(`❌ Build failed, retrying... (${retries} attempts left)`);
                    await delay(5000);
                    
                    // Try to unlock the file again
                    await waitForFileUnlock(mcpPath, 3);
                } else {
                    throw error;
                }
            }
        }

    } catch (error) {
        console.error('❌ Build failed:', error.message);
        process.exit(1);
    }
}

buildWithRetry(); 