# DigitalOcean App Platform Deployment
name: unreal-mcp-server
services:
- name: mcp-server
  source_dir: /
  github:
    repo: AlexKissiJr/AiWebplatform
    branch: cc_plugin
    deploy_on_push: true
  dockerfile_path: UnrealGenAISupport_with_server/server/Dockerfile
  
  # Environment variables
  envs:
  - key: UNREAL_HOST
    value: "your-unreal-server.com"  # Replace with your Unreal Engine server
  - key: UNREAL_PORT
    value: "9877"
  - key: MCP_PORT
    value: "8000"
    
  # Health check
  health_check:
    http_path: /health
    
  # HTTP configuration
  http_port: 8000
  
  # Instance configuration
  instance_count: 1
  instance_size_slug: basic-xxs  # $5/month
  
  # Routes for external access
  routes:
  - path: /
    preserve_path_prefix: true

# Alternative: Docker Compose for VPS deployment
---
# docker-compose.cloud.yml
version: '3.8'

services:
  unreal-mcp-server:
    build: .
    container_name: unreal-mcp-server-cloud
    ports:
      - "80:8000"   # HTTP access
      - "443:8000"  # HTTPS access (with reverse proxy)
    environment:
      - UNREAL_HOST=${UNREAL_HOST:-your-unreal-server.com}
      - UNREAL_PORT=${UNREAL_PORT:-9877}
      - MCP_PORT=8000
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
    networks:
      - mcp-network

  # Optional: Nginx reverse proxy for HTTPS
  nginx:
    image: nginx:alpine
    ports:
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl/certs
    depends_on:
      - unreal-mcp-server
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge 