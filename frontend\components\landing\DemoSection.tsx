'use client';

import React, { useRef, useEffect, useState } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

const DemoSection: React.FC = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const videoRef = useRef<HTMLDivElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Animate section elements
    gsap.fromTo(
      '.demo-title',
      { opacity: 0, y: 50 },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        scrollTrigger: {
          trigger: sectionRef.current,
          start: 'top 80%',
        },
      }
    );

    gsap.fromTo(
      '.demo-description',
      { opacity: 0, y: 30 },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        delay: 0.2,
        scrollTrigger: {
          trigger: sectionRef.current,
          start: 'top 80%',
        },
      }
    );

    gsap.fromTo(
      videoRef.current,
      { opacity: 0, scale: 0.9 },
      {
        opacity: 1,
        scale: 1,
        duration: 1,
        delay: 0.4,
        scrollTrigger: {
          trigger: sectionRef.current,
          start: 'top 70%',
        },
      }
    );
  }, []);

  const handlePlayVideo = () => {
    setIsPlaying(true);
    // In a real implementation, you would play the actual video
  };

  // Demo chat messages showing the AI assistant in action
  const demoMessages = [
    { sender: 'user', text: 'Create a red metallic sphere at the center of the scene' },
    { sender: 'ai', text: 'Creating a sphere with metallic red material at coordinates (0,0,0)...' },
    { sender: 'ai', text: 'Sphere created successfully! The material has been set to metallic with a red base color and 0.8 roughness.' },
    { sender: 'user', text: 'Make it bigger and add a blue glow effect' },
    { sender: 'ai', text: 'Scaling the sphere to 2x size and adding a blue emissive glow effect...' },
    { sender: 'ai', text: 'Done! The sphere now has a blue glow with an intensity of 5.0.' },
    { sender: 'user', text: 'Create a blueprint that makes the sphere pulse' },
    { sender: 'ai', text: 'Generating blueprint for pulsing effect...' },
    { sender: 'ai', text: 'Blueprint created! It uses a Timeline node with a float curve to animate the scale between 1.0 and 1.2 with a smooth sine wave pattern.' },
  ];

  return (
    <section
      ref={sectionRef}
      id="demo"
      className="py-20 bg-gray-800 text-white relative overflow-hidden"
    >
      {/* Background image */}
      <div className="absolute inset-0 z-0 opacity-20 bg-cover bg-center"
           style={{
             backgroundImage: 'url(/images/backgrounds/unreal-editor.jpg)',
             backgroundBlendMode: 'overlay',
           }}
      />

      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-blue-900/50 to-gray-900 z-0" />
      <div className="container mx-auto px-4 relative z-10">
        <div className="inline-block mx-auto mb-4 px-4 py-1 border border-blue-400 rounded-full bg-blue-900/30 text-center">
          <span className="text-blue-300 font-medium">Demo</span>
        </div>

        <h2 className="demo-title text-4xl md:text-5xl font-bold text-center mb-6">
          See It <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-300">In Action</span>
        </h2>

        <p className="demo-description text-xl text-center mb-16 max-w-3xl mx-auto text-blue-100">
          Watch how easy it is to control Unreal Engine using natural language commands.
          No complex UI to learn, just tell the AI what you want to create.
        </p>

        <div className="flex flex-col lg:flex-row gap-8 items-center">
          {/* Video/Demo Preview */}
          <div
            ref={videoRef}
            className="w-full lg:w-1/2 bg-gray-800/70 backdrop-blur-sm rounded-xl overflow-hidden shadow-2xl border border-gray-700 hover:border-blue-500 transition-colors duration-300"
          >
            {isPlaying ? (
              <div className="aspect-video bg-black">
                {/* In a real implementation, this would be an actual video player */}
                <div className="flex items-center justify-center h-full text-gray-400">
                  Video player would be here
                </div>
              </div>
            ) : (
              <div className="aspect-video bg-gray-900 relative group cursor-pointer" onClick={handlePlayVideo}>
                {/* Video thumbnail with play button */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-20 h-20 bg-blue-500 rounded-full flex items-center justify-center group-hover:bg-blue-600 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent">
                  <div className="absolute bottom-4 left-4 text-white font-bold">
                    AI Unreal Engine Assistant Demo
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Chat Demo */}
          <div className="w-full lg:w-1/2 bg-gray-800/70 backdrop-blur-sm rounded-xl overflow-hidden shadow-2xl border border-gray-700 hover:border-blue-500 transition-colors duration-300">
            <div className="p-4 bg-gray-900/80 border-b border-gray-700">
              <h3 className="font-bold text-blue-300">AI Assistant Chat Demo</h3>
            </div>

            <div className="p-4 h-[400px] overflow-y-auto">
              {demoMessages.map((message, index) => (
                <div
                  key={index}
                  className={`mb-4 ${message.sender === 'user' ? 'text-right' : ''}`}
                >
                  <div
                    className={`inline-block rounded-lg px-4 py-2 max-w-[80%] ${
                      message.sender === 'user'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-700 text-gray-100'
                    }`}
                  >
                    {message.text}
                  </div>
                </div>
              ))}
            </div>

            <div className="p-4 bg-gray-900/80 border-t border-gray-700 flex">
              <input
                type="text"
                placeholder="Type a command..."
                className="flex-1 bg-gray-800 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                readOnly
              />
              <button className="ml-2 bg-gradient-to-r from-blue-600 to-blue-400 text-white px-4 py-2 rounded-lg hover:from-blue-500 hover:to-blue-300 transition-all shadow-lg hover:shadow-blue-500/50">
                Send
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DemoSection;
