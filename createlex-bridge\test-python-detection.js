#!/usr/bin/env node

const { execSync } = require('child_process');
const os = require('os');

console.log('🐍 Python Version Detection Test');
console.log('================================');

const platform = os.platform();
const isMac = platform === 'darwin';
const isLinux = platform === 'linux';
const isWindows = platform === 'win32';

console.log(`Platform: ${platform}`);
console.log('');

// Same logic as in build-mcp-exe.js
let pythonCmd = 'python';

if (isMac || isLinux) {
  const pythonVersions = [
    'python3.12',
    'python3.11', 
    'python3.10',
    'python3',
    'python'
  ];
  
  console.log('🔍 Scanning for Python versions...');
  
  for (const cmd of pythonVersions) {
    try {
      const versionOutput = execSync(`${cmd} --version`, { stdio: 'pipe' }).toString();
      const versionMatch = versionOutput.match(/Python (\d+)\.(\d+)/);
      
      if (versionMatch) {
        const major = parseInt(versionMatch[1]);
        const minor = parseInt(versionMatch[2]);
        
        console.log(`🐍 Found ${cmd}: Python ${major}.${minor}`);
        
        // For macOS, prefer Python 3.10+ for FastMCP compatibility
        if (isMac && major === 3 && minor >= 10) {
          pythonCmd = cmd;
          console.log(`✅ Selected ${cmd} for FastMCP compatibility (requires Python 3.10+)`);
          break;
        }
        // For Linux, Python 3.8+ is usually fine
        else if (isLinux && major === 3 && minor >= 8) {
          pythonCmd = cmd;
          console.log(`✅ Selected ${cmd} for Linux build`);
          break;
        }
        // Fallback for older versions
        else if (major === 3) {
          pythonCmd = cmd;
          console.log(`⚠️  Selected ${cmd} (Python ${major}.${minor}) - may have compatibility issues`);
          if (isMac) {
            console.log(`💡 Consider installing Python 3.10+ for better FastMCP support:`);
            console.log(`   brew install python@3.11`);
          }
          break;
        }
      }
    } catch (e) {
      // Command not found, continue to next version
      console.log(`❌ ${cmd}: Not found`);
      continue;
    }
  }
} else {
  // Windows logic
  console.log('🔍 Checking Windows Python...');
  try {
    const versionOutput = execSync('python --version', { stdio: 'pipe' }).toString();
    console.log(`✅ Found python: ${versionOutput.trim()}`);
    pythonCmd = 'python';
  } catch (e) {
    try {
      const versionOutput = execSync('python3 --version', { stdio: 'pipe' }).toString();
      console.log(`✅ Found python3: ${versionOutput.trim()}`);
      pythonCmd = 'python3';
    } catch (e2) {
      console.log('⚠️  No Python found, using default "python" command');
    }
  }
}

console.log('');
console.log('🎯 Final Result:');
console.log(`Selected Python command: ${pythonCmd}`);

// Test FastMCP compatibility
if (pythonCmd) {
  try {
    console.log('');
    console.log('🧪 Testing FastMCP compatibility...');
    execSync(`${pythonCmd} -c "import sys; print(f'Python version: {sys.version}'); major, minor = sys.version_info[:2]; print(f'FastMCP compatible: {major == 3 and minor >= 10}')"`, { stdio: 'inherit' });
  } catch (e) {
    console.log('❌ Failed to test Python compatibility');
  }
} 