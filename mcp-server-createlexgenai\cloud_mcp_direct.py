#!/usr/bin/env python3
"""
Direct Cloud MCP Server for Unreal Engine
This exposes the local MCP server functionality directly through HTTP
"""

import subprocess
import sys
import json
import asyncio
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Unreal Engine Direct Cloud MCP Server")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "Unreal Engine Direct Cloud MCP Server"
    }

@app.post("/mcp")
async def mcp_proxy(request: Request):
    """Proxy MCP requests to the local mcp_server.py"""
    try:
        # Get the request body
        body = await request.body()
        
        logger.info(f"Received MCP request: {body.decode('utf-8')[:200]}...")
        
        # Start the MCP server process
        process = subprocess.Popen(
            [sys.executable, "mcp_server.py"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Send the request to the MCP server
        stdout, stderr = process.communicate(input=body.decode('utf-8'))
        
        if process.returncode != 0:
            logger.error(f"MCP server error: {stderr}")
            raise HTTPException(status_code=500, detail=f"MCP server error: {stderr}")
        
        # Try to parse the response as JSON
        try:
            response_data = json.loads(stdout)
            return JSONResponse(content=response_data)
        except json.JSONDecodeError:
            # If not valid JSON, return as text
            return {"response": stdout, "stderr": stderr}
        
    except Exception as e:
        logger.error(f"Error processing MCP request: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    print("🌐 Starting Direct Cloud MCP Server...")
    print("📡 MCP endpoint: http://localhost:8000/mcp")
    print("📊 Health check: http://localhost:8000/health")
    
    uvicorn.run(app, host="0.0.0.0", port=8000) 