import unreal
import json
import socket
import os
import time
from utils import logging as log

def send_to_bridge_mcp(command):
    """Send commands to the CreateLex Bridge MCP server for Blueprint processing."""
    try:
        import socket
        import json
        
        # Bridge server configuration
        BRIDGE_HOST = "localhost"
        BRIDGE_PORT = 9877
        TIMEOUT = 15.0  # 15 second timeout
        
        command_type = command.get("type", "") or command.get("command", "")
        log.log_info(f"[BRIDGE] Sending command to MCP Bridge: {command_type}")
        
        # Map command types to Bridge API format
        bridge_command = None
        bridge_data = {}
        
        if command_type == "get_blueprint_context_detailed":
            # This doesn't need Bridge - return local context
            local_context = command.get("local_context", {})
            return {
                "success": True,
                "context": local_context,
                "message": "Blueprint context retrieved locally"
            }
        elif command_type == "analyze_level_blueprint":
            # This is now handled directly by C++ Asset Registry API
            return {
                "success": True,
                "message": "Blueprint analysis handled by C++ Asset Registry API",
                "method": "cpp_asset_registry"
            }
        elif command_type == "generate_smart_blueprint_function":
            bridge_command = "generate_blueprint_function"
            bridge_data = {
                "level_file": command.get("level_file", ""),
                "function_spec": {
                    "name": command.get("function_name", "AIGeneratedFunction"),
                    "description": command.get("description", ""),
                    "inputs": command.get("inputs", []),
                    "outputs": command.get("outputs", []),
                    "complexity": command.get("complexity", "medium"),
                    "nodes": command.get("suggested_nodes", [])
                }
            }
        elif command_type == "get_uasset_status":
            # UAssetAPI status no longer needed - using C++ Asset Registry only
            return {
                "success": True,
                "message": "UAssetAPI replaced by C++ Asset Registry API",
                "method": "cpp_asset_registry"
            }
        else:
            # Fallback for unknown commands
            log.log_info(f"[BRIDGE] Unknown command type, using fallback: {command_type}")
            return {
                "success": False,
                "error": f"Unknown command type: {command_type}",
                "fallback": True
            }
        
        if not bridge_command:
            return {"success": False, "error": "No bridge command mapped"}
        
        log.log_info(f"[BRIDGE] Connecting to Bridge server at {BRIDGE_HOST}:{BRIDGE_PORT}")
        
        # Connect to Bridge server (try both IPv4 and IPv6)
        try:
            sock = None
            connection_error = None
            
            # Try IPv4 first
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(TIMEOUT)
                sock.connect((BRIDGE_HOST, BRIDGE_PORT))
                log.log_info(f"[BRIDGE] Connected via IPv4")
            except Exception as ipv4_error:
                connection_error = ipv4_error
                log.log_info(f"[BRIDGE] IPv4 connection failed: {ipv4_error}, trying IPv6...")
                
                # Try IPv6
                try:
                    if sock:
                        sock.close()
                    sock = socket.socket(socket.AF_INET6, socket.SOCK_STREAM)
                    sock.settimeout(TIMEOUT)
                    sock.connect(("::1", BRIDGE_PORT))
                    log.log_info(f"[BRIDGE] Connected via IPv6")
                except Exception as ipv6_error:
                    log.log_error(f"[BRIDGE] IPv6 connection also failed: {ipv6_error}")
                    raise connection_error  # Raise the original IPv4 error
            
            # Send request to Bridge
            request = {
                "type": "blueprint_request",
                "command": bridge_command,
                "data": bridge_data,
                "timestamp": time.time()
            }
            
            request_json = json.dumps(request) + "\n"
            sock.send(request_json.encode('utf-8'))
            
            log.log_info(f"[BRIDGE] Sent request: {bridge_command}")
            
            # Receive response from Bridge
            response_data = ""
            while True:
                chunk = sock.recv(4096).decode('utf-8')
                if not chunk:
                    break
                response_data += chunk
                if '\n' in response_data:
                    break
            
            sock.close()
            
            # Parse response
            response_line = response_data.strip().split('\n')[0]
            response = json.loads(response_line)
            
            log.log_info(f"[BRIDGE] Received response: {response.get('success', False)}")
            return response
            
        except socket.timeout:
            log.log_error("[BRIDGE] Connection timeout - Bridge server may not be running")
            return {
                "success": False,
                "error": "Bridge server connection timeout",
                "suggestion": "Ensure CreateLex Bridge is running"
            }
        except ConnectionRefusedError:
            log.log_error("[BRIDGE] Connection refused - Bridge server not running")
            return {
                "success": False,
                "error": "Bridge server not running",
                "suggestion": "Start CreateLex Bridge application"
            }
        except Exception as conn_e:
            log.log_error(f"[BRIDGE] Connection error: {str(conn_e)}")
            return {
                "success": False,
                "error": f"Bridge connection failed: {str(conn_e)}"
            }
            
    except Exception as e:
        log.log_error(f"[BRIDGE] Error sending command to bridge: {str(e)}")
        return {
            "success": False,
            "error": f"Bridge communication failed: {str(e)}"
        }

class BlueprintContextHandler:
    """Proxy handler that forwards requests to the CreateLex Bridge MCP server."""
    
    @staticmethod
    def get_current_blueprint_context():
        """Proxy to Bridge MCP server for Blueprint context detection."""
        # Extract minimal context data locally for the Bridge to use
        local_context = BlueprintContextHandler._extract_local_context()
        
        # Send to Bridge MCP server for processing
        return send_to_bridge_mcp({
            "type": "get_blueprint_context_detailed",
            "local_context": local_context
        })
    
    @staticmethod
    def generate_blueprint_function(data):
        """Proxy to Bridge MCP server for function generation."""
        # Add local Blueprint data if needed
        local_context = BlueprintContextHandler._extract_local_context()
        
        # Enhanced AI generation with comprehensive context
        enhanced_request = {
            "type": "generate_smart_blueprint_function",
            "function_name": data.get("function_name", ""),
            "description": data.get("description", ""),
            "inputs": data.get("inputs", []),
            "outputs": data.get("outputs", []),
            "complexity": data.get("complexity", "medium"),
            "local_context": local_context,
            # Add comprehensive Blueprint analysis for better AI generation
            "blueprint_analysis": BlueprintContextHandler._analyze_blueprint_for_ai(local_context),
            "generation_mode": "kibibyte_style",  # Similar to Kibibyte Labs functionality
            "context_aware": True
        }
        
        return send_to_bridge_mcp(enhanced_request)
    
    @staticmethod
    def analyze_blueprint_graph(data):
        """Proxy to Bridge MCP server for graph analysis."""
        # Extract Blueprint graph data locally
        local_graph_data = BlueprintContextHandler._extract_graph_data(data)
        
        return send_to_bridge_mcp({
            "type": "analyze_blueprint_graph_advanced",
            "graph_name": data.get("graph_name", "EventGraph"),
            "analysis_type": data.get("analysis_type", "comprehensive"),
            "include_ai_insights": data.get("include_ai_insights", True),
            "local_graph_data": local_graph_data
        })
    
    @staticmethod
    def generate_context_aware_blueprint_function(data):
        """Generate context-aware Blueprint function using C++ Asset Registry context."""
        log.log_info("[BLUEPRINT] 🎯 Generating context-aware Blueprint function...")
        
        # Get current Blueprint context using our C++ Asset Registry API
        context_response = BlueprintContextHandler.get_current_blueprint_context()
        
        if not context_response or not context_response.get("success", False):
            return {
                "success": False,
                "error": "Failed to get Blueprint context. Make sure a Blueprint is open in UE5.",
                "context_response": context_response
            }
        
        # Extract context information
        blueprints = context_response.get("blueprints", [])
        if not blueprints:
            return {
                "success": False,
                "error": "No Blueprint information found in context"
            }
        
        blueprint_info = blueprints[0]
        function_description = data.get("function_description", "")
        function_name = data.get("function_name", "")
        complexity = data.get("complexity", "medium")
        
        # Generate function name if not provided
        if not function_name:
            function_name = BlueprintContextHandler._generate_function_name_from_description(function_description)
        
        log.log_info(f"[BLUEPRINT] Creating function '{function_name}' with description: {function_description}")
        
        # ACTUALLY CREATE THE BLUEPRINT FUNCTION using blueprint_commands
        try:
            from handlers import blueprint_commands
            
            # Get the Blueprint path
            blueprint_path = blueprint_info.get("path", "")
            if not blueprint_path:
                # For Level Blueprints, use dynamic detection
                detected_path = BlueprintContextHandler._get_current_level_blueprint_path()
                if detected_path:
                    blueprint_path = detected_path
                    log.log_info(f"[BLUEPRINT] ✅ Using detected level blueprint: {blueprint_path}")
                else:
                    # Last resort: try to find any existing blueprint
                    try:
                        asset_registry = unreal.AssetRegistryHelpers.get_asset_registry()
                        blueprint_assets = asset_registry.get_assets_by_class(unreal.Blueprint)
                        if blueprint_assets:
                            first_bp = blueprint_assets[0]
                            blueprint_path = first_bp.get_full_name().split(' ')[1]
                            log.log_info(f"[BLUEPRINT] ✅ Using first available blueprint: {blueprint_path}")
                        else:
                            blueprint_path = "/Game/Blueprints/BP_Default"
                            log.log_warning(f"[BLUEPRINT] No blueprints found, using fallback: {blueprint_path}")
                    except Exception as e:
                        log.log_error(f"[BLUEPRINT] Error finding fallback blueprint: {e}")
                        blueprint_path = "/Game/Blueprints/BP_Default"
            
            log.log_info(f"[BLUEPRINT] Target Blueprint path: {blueprint_path}")
            
            # Step 1: Create the function in the Blueprint
            function_inputs = []
            function_outputs = [{"name": "GeneratedPassword", "type": "string"}]
            
            # Add inputs based on the function description
            if "password" in function_description.lower():
                function_inputs = [
                    {"name": "Length", "type": "int"},
                    {"name": "IncludeNumbers", "type": "bool"},
                    {"name": "IncludeSymbols", "type": "bool"}
                ]
            
            # Create the function
            add_function_result = blueprint_commands.handle_add_function({
                "blueprint_path": blueprint_path,
                "function_name": function_name,
                "inputs": function_inputs,
                "outputs": function_outputs
            })
            
            if not add_function_result.get("success", False):
                log.log_error(f"[BLUEPRINT] Failed to create function: {add_function_result}")
                return {
                    "success": False,
                    "error": f"Failed to create Blueprint function: {add_function_result.get('error', 'Unknown error')}",
                    "blueprint_path": blueprint_path
                }
            
            function_id = add_function_result.get("function_id")
            log.log_info(f"[BLUEPRINT] ✅ Function created with ID: {function_id}")
            
            # Step 2: Add nodes to implement the password generation logic
            nodes_to_add = []
            
            if "password" in function_description.lower():
                # Add nodes for password generation
                nodes_to_add = [
                    {
                        "id": "start_node",
                        "node_type": "K2Node_FunctionEntry",
                        "node_position": [100, 100],
                        "node_properties": {}
                    },
                    {
                        "id": "branch_node", 
                        "node_type": "K2Node_IfThenElse",
                        "node_position": [300, 100],
                        "node_properties": {}
                    },
                    {
                        "id": "make_string_node",
                        "node_type": "K2Node_CallFunction",
                        "node_position": [500, 100],
                        "node_properties": {
                            "function_name": "BuildString_Int",
                            "target_class": "KismetStringLibrary"
                        }
                    },
                    {
                        "id": "return_node",
                        "node_type": "K2Node_FunctionResult", 
                        "node_position": [700, 100],
                        "node_properties": {}
                    }
                ]
            else:
                # Generic function nodes
                nodes_to_add = [
                    {
                        "id": "start_node",
                        "node_type": "K2Node_FunctionEntry",
                        "node_position": [100, 100],
                        "node_properties": {}
                    },
                    {
                        "id": "print_node",
                        "node_type": "K2Node_CallFunction",
                        "node_position": [300, 100],
                        "node_properties": {
                            "function_name": "PrintString",
                            "target_class": "KismetSystemLibrary"
                        }
                    },
                    {
                        "id": "return_node",
                        "node_type": "K2Node_FunctionResult",
                        "node_position": [500, 100], 
                        "node_properties": {}
                    }
                ]
            
            # Add all nodes
            add_nodes_result = blueprint_commands.handle_add_nodes_bulk({
                "blueprint_path": blueprint_path,
                "function_id": function_id,
                "nodes": nodes_to_add
            })
            
            if not add_nodes_result.get("success", False):
                log.log_error(f"[BLUEPRINT] Failed to add nodes: {add_nodes_result}")
                return {
                    "success": False,
                    "error": f"Failed to add nodes to function: {add_nodes_result.get('error', 'Unknown error')}",
                    "function_created": True,
                    "function_id": function_id
                }
            
            node_mapping = add_nodes_result.get("nodes", {})
            log.log_info(f"[BLUEPRINT] ✅ Nodes added: {list(node_mapping.keys())}")
            
            # Step 3: Connect the nodes
            connections = []
            node_ids = list(node_mapping.keys())
            
            # Connect nodes in sequence
            for i in range(len(node_ids) - 1):
                connections.append({
                    "source_node_id": node_mapping[node_ids[i]],
                    "source_pin": "exec",
                    "target_node_id": node_mapping[node_ids[i + 1]],
                    "target_pin": "exec"
                })
            
            if connections:
                connect_result = blueprint_commands.handle_connect_nodes_bulk({
                    "blueprint_path": blueprint_path,
                    "function_id": function_id,
                    "connections": connections
                })
                
                if connect_result.get("success", False):
                    log.log_info(f"[BLUEPRINT] ✅ Nodes connected successfully")
                else:
                    log.log_warning(f"[BLUEPRINT] ⚠️ Node connections failed: {connect_result}")
            
            # Step 4: Compile the Blueprint
            compile_result = blueprint_commands.handle_compile_blueprint({
                "blueprint_path": blueprint_path
            })
            
            if compile_result.get("success", False):
                log.log_info(f"[BLUEPRINT] ✅ Blueprint compiled successfully")
            else:
                log.log_warning(f"[BLUEPRINT] ⚠️ Blueprint compilation failed: {compile_result}")
            
            # Return success with detailed information
            return {
                "success": True,
                "message": f"Context-aware Blueprint function '{function_name}' created successfully!",
                "function_name": function_name,
                "function_id": function_id,
                "description": function_description,
                "complexity": complexity,
                "blueprint_context": {
                    "blueprint_name": blueprint_info.get("name", "Unknown"),
                    "blueprint_type": blueprint_info.get("type", "Unknown"),
                    "blueprint_path": blueprint_path,
                    "existing_variables": [var.get("name", "") for var in blueprint_info.get("variables", [])],
                    "existing_functions": [func.get("name", "") for func in blueprint_info.get("functions", [])],
                    "event_graph_nodes": blueprint_info.get("event_graph", {}).get("node_count", 0),
                    "total_nodes": blueprint_info.get("total_nodes", 0)
                },
                "generation_result": {
                    "nodes_created": len(nodes_to_add),
                    "connections_made": len(connections),
                    "compiled": compile_result.get("success", False),
                    "inputs": function_inputs,
                    "outputs": function_outputs
                }
            }
            
        except Exception as e:
            log.log_error(f"[BLUEPRINT] ❌ Error creating Blueprint function: {str(e)}")
            return {
                "success": False,
                "error": f"Blueprint function creation failed: {str(e)}",
                "function_name": function_name,
                "blueprint_path": blueprint_path if 'blueprint_path' in locals() else "Unknown"
            }

    @staticmethod
    def _generate_function_name_from_description(description):
        """Generate a function name from the description."""
        import re
        
        # Extract key words and convert to PascalCase
        words = re.findall(r'\b\w+\b', description.lower())
        
        # Remove common words
        stop_words = {'a', 'an', 'the', 'and', 'or', 'but', 'for', 'to', 'of', 'in', 'on', 'at', 'by', 'with'}
        meaningful_words = [word for word in words if word not in stop_words]
        
        # Take first 3-4 meaningful words and capitalize
        function_words = meaningful_words[:4]
        function_name = ''.join(word.capitalize() for word in function_words)
        
        # Ensure it starts with uppercase and has reasonable length
        if len(function_name) < 3:
            function_name = "GeneratedFunction"
        elif len(function_name) > 50:
            function_name = function_name[:50]
        
        return function_name

    @staticmethod
    def _get_current_level_blueprint_path():
        """Dynamically detect the current level blueprint path."""
        try:
            world = unreal.EditorLevelLibrary.get_editor_world()
            if not world:
                log.log_warning("[BLUEPRINT] No world found for level blueprint detection")
                return None
            
            world_name = world.get_name()
            log.log_info(f"[BLUEPRINT] Current world: {world_name}")
            
            if not world_name or world_name == "None":
                log.log_warning("[BLUEPRINT] Invalid world name")
                return None
            
            # Try multiple level blueprint path patterns
            possible_paths = [
                f"/Game/{world_name}.{world_name}",  # Standard format
                f"/Game/ThirdPerson/{world_name}.{world_name}",  # ThirdPerson template
                f"/Game/ThirdPerson/{world_name}.{world_name}:PersistentLevel.{world_name}",  # Level Script Blueprint
                f"/Game/Maps/{world_name}.{world_name}",  # Maps folder
                f"/Game/Maps/{world_name}.{world_name}:PersistentLevel.{world_name}",  # Maps folder Level Script
                f"/Game/Levels/{world_name}.{world_name}",  # Levels folder
                f"/Game/Levels/{world_name}.{world_name}:PersistentLevel.{world_name}",  # Levels folder Level Script
            ]
            
            # Try to find which path actually exists
            for path in possible_paths:
                try:
                    # Try to load the asset to verify it exists
                    asset = unreal.EditorAssetLibrary.load_asset(path)
                    if asset:
                        log.log_info(f"[BLUEPRINT] ✅ Found level blueprint at: {path}")
                        return path
                except:
                    continue
            
            # If none found, try to find level script blueprint via asset registry
            try:
                asset_registry = unreal.AssetRegistryHelpers.get_asset_registry()
                all_assets = asset_registry.get_assets_by_path("/Game", recursive=True)
                
                for asset in all_assets:
                    asset_name = str(asset.asset_name) if hasattr(asset, 'asset_name') else str(asset)
                    if world_name in asset_name and ("Script" in asset_name or "Level" in asset_name):
                        try:
                            asset_path = asset.get_full_name()
                            if asset_path:
                                # Extract the actual path part
                                path_part = asset_path.split(' ')[1] if ' ' in asset_path else asset_path
                                log.log_info(f"[BLUEPRINT] ✅ Found level blueprint via registry: {path_part}")
                                return path_part
                        except:
                            continue
            except Exception as e:
                log.log_warning(f"[BLUEPRINT] Asset registry search failed: {e}")
            
            log.log_warning(f"[BLUEPRINT] Could not find level blueprint for world: {world_name}")
            return None
            
        except Exception as e:
            log.log_error(f"[BLUEPRINT] Error in level blueprint detection: {e}")
            return None

    @staticmethod
    def _extract_local_context():
        """Extract minimal Blueprint context data locally for UE5.6."""
        log.log_info("[SYNC DEBUG] Starting UE5.6 blueprint context extraction...")
        
        # NEW: Try C++ Asset Registry API first (most efficient)
        try:
            log.log_info("[SYNC DEBUG] 🚀 Method 0A: C++ Asset Registry API (PRIORITY)")
            from asset_registry_bridge import get_blueprint_context_via_asset_registry
            
            asset_registry_result = get_blueprint_context_via_asset_registry()
            
            if asset_registry_result.get("success", False):
                log.log_info("[SYNC DEBUG] ✅ SUCCESS! C++ Asset Registry API found Level Script Blueprint!")
                
                # Convert to the expected format for compatibility
                level_blueprint = asset_registry_result.get("level_blueprint", {})
                event_graph = asset_registry_result.get("event_graph", {})
                
                return {
                    "success": True,
                    "blueprints_found": 1,
                    "detection_method": "cpp_asset_registry",
                    "performance": "high",
                    "lightweight": True,
                    "detection_logs": [
                        "✅ C++ Asset Registry API successful",
                        f"📁 Blueprint: {level_blueprint.get('blueprintName', 'Unknown')}",
                        f"🎯 Level: {level_blueprint.get('levelName', 'Unknown')}",
                        f"📊 Event Graph Nodes: {event_graph.get('eventGraphNodes', 0)}",
                        f"📈 Total Nodes: {event_graph.get('totalNodes', 0)}"
                    ],
                    "blueprints": [{
                        "name": level_blueprint.get("blueprintName", "Level_Blueprint"),
                        "path": level_blueprint.get("blueprintPath", ""),
                        "type": "LevelScriptBlueprint",
                        "class": "Blueprint",
                        "detection_method": "cpp_asset_registry",
                        "statistics": level_blueprint.get("statistics", {}),
                        "event_graph": {
                            "name": "EventGraph",
                            "nodes": event_graph.get("nodes", []),
                            "node_count": event_graph.get("eventGraphNodes", 0)
                        },
                        "variables": level_blueprint.get("variables", []),
                        "functions": level_blueprint.get("functions", []),
                        "total_nodes": event_graph.get("totalNodes", 0)
                    }]
                }
            elif asset_registry_result.get("fallback_required", False):
                log.log_info("[SYNC DEBUG] ⚠️ C++ Asset Registry API requires fallback - continuing with Python methods")
            else:
                log.log_info(f"[SYNC DEBUG] ❌ C++ Asset Registry API failed: {asset_registry_result.get('error', 'Unknown error')}")
                
        except ImportError:
            log.log_info("[SYNC DEBUG] ⚠️ Asset Registry Bridge not available - continuing with Python methods")
        except Exception as e:
            log.log_info(f"[SYNC DEBUG] ⚠️ C++ Asset Registry API error: {e} - continuing with Python methods")
        
        # EXISTING: Fallback to the original complex detection methods
        try:
            # Get basic editor info
            asset_subsystem = unreal.get_editor_subsystem(unreal.AssetEditorSubsystem)
            
            open_blueprints = []
            detection_logs = []
            
            # Use the proven approach from the other model
            open_blueprints_set = set()
            
            # Method 0: Selection-based detection (Editor Utility) - BULLET-PROOF #1
            try:
                log.log_info(f"[SYNC DEBUG]   ✅ Method 0: Selection-based detection via Content Browser")
                from unreal import EditorUtilityLibrary
                selected = EditorUtilityLibrary.get_selected_assets()
                log.log_info(f"[SYNC DEBUG]   Found {len(selected)} selected assets")
                for asset in selected:
                    log.log_info(f"[SYNC DEBUG]   Selected asset: {asset.get_name()} (type: {type(asset).__name__})")
                    if isinstance(asset, unreal.Blueprint):
                        open_blueprints_set.add(asset)
                        log.log_info(f"[SYNC DEBUG]   🎉 SUCCESS! Found Blueprint via selection: {asset.get_name()}")
                        break
            except Exception as e:
                log.log_info(f"[SYNC DEBUG]   Method 0 failed: {e}")
            
            # First, let's discover what methods are actually available
            try:
                log.log_info(f"[SYNC DEBUG] 🔍 Discovering available AssetEditorSubsystem methods...")
                aes_methods = [method for method in dir(asset_subsystem) if not method.startswith('_')]
                log.log_info(f"[SYNC DEBUG]   AssetEditorSubsystem methods: {', '.join(aes_methods[:20])}...")  # Show first 20
                
                log.log_info(f"[SYNC DEBUG] 🔍 Discovering available EditorLevelLibrary methods...")
                ell_methods = [method for method in dir(unreal.EditorLevelLibrary) if not method.startswith('_')]
                log.log_info(f"[SYNC DEBUG]   EditorLevelLibrary methods: {', '.join(ell_methods[:20])}...")  # Show first 20
            except Exception as e:
                log.log_info(f"[SYNC DEBUG] Method discovery failed: {e}")
            
            # Method A: High-level AssetEditorSubsystem API with C++ fallback
            if not open_blueprints_set:
                try:
                    # A1: Direct call if Python wrapper exposes it
                    if hasattr(asset_subsystem, "get_all_edited_assets"):
                        log.log_info(f"[SYNC DEBUG]   ✅ Method A1: get_all_edited_assets available")
                        edited_assets = asset_subsystem.get_all_edited_assets()
                        log.log_info(f"[SYNC DEBUG]   Found {len(edited_assets)} edited assets")
                        for asset in edited_assets:
                            log.log_info(f"[SYNC DEBUG]   Edited asset: {asset.get_name()} (type: {type(asset).__name__})")
                            if isinstance(asset, unreal.Blueprint):
                                open_blueprints_set.add(asset)
                                log.log_info(f"[SYNC DEBUG]   🎉 SUCCESS! Found Blueprint: {asset.get_name()}")
                    else:
                        log.log_info(f"[SYNC DEBUG]   ❌ Method A1: get_all_edited_assets NOT available")
                    
                    # A2: Fall back to calling the underlying C++ API directly - BULLET-PROOF #2
                    if not open_blueprints_set and hasattr(asset_subsystem, "call_method"):
                        try:
                            log.log_info(f"[SYNC DEBUG]   ✅ Method A2: Direct C++ call via call_method")
                            edited_assets = asset_subsystem.call_method("GetAllEditedAssets")
                            log.log_info(f"[SYNC DEBUG]   GetAllEditedAssets via call_method found {len(edited_assets)} assets")
                            for asset in edited_assets:
                                log.log_info(f"[SYNC DEBUG]   C++ Edited asset: {asset.get_name()} (type: {type(asset).__name__})")
                                if isinstance(asset, unreal.Blueprint):
                                    open_blueprints_set.add(asset)
                                    log.log_info(f"[SYNC DEBUG]   🎉 SUCCESS! Found Blueprint via call_method: {asset.get_name()}")
                        except Exception as e:
                            log.log_info(f"[SYNC DEBUG]   Method A2 failed: {e}")
                    elif not hasattr(asset_subsystem, "call_method"):
                        log.log_info(f"[SYNC DEBUG]   ❌ Method A2: call_method NOT available")
                except Exception as e:
                    log.log_info(f"[SYNC DEBUG]   Method A failed: {e}")
            
            # Method B: Fallback via "all editors for asset class"
            if not open_blueprints_set:
                try:
                    if hasattr(asset_subsystem, "get_all_editors_for_asset_class"):
                        log.log_info(f"[SYNC DEBUG]   ✅ Method B: get_all_editors_for_asset_class available")
                        for editor in asset_subsystem.get_all_editors_for_asset_class(unreal.Blueprint):
                            if hasattr(editor, "get_edited_objects"):
                                for obj in editor.get_edited_objects():
                                    if isinstance(obj, unreal.Blueprint):
                                        open_blueprints_set.add(obj)
                                        log.log_info(f"[SYNC DEBUG]     ✅ Found Blueprint: {obj.get_name()}")
                    else:
                        log.log_info(f"[SYNC DEBUG]   ❌ Method B: get_all_editors_for_asset_class NOT available")
                except Exception as e:
                    log.log_info(f"[SYNC DEBUG]   Method B failed: {e}")
            
            # Method C: Level Blueprint (always available via LevelLibrary)
            if not open_blueprints_set:
                try:
                    if hasattr(unreal.EditorLevelLibrary, "get_level_script_blueprint"):
                        log.log_info(f"[SYNC DEBUG]   ✅ Method C: get_level_script_blueprint available")
                        lvl_bp = unreal.EditorLevelLibrary.get_level_script_blueprint()
                        if lvl_bp:
                            open_blueprints_set.add(lvl_bp)
                            log.log_info(f"[SYNC DEBUG]     ✅ Found Level Blueprint: {lvl_bp.get_name()}")
                        else:
                            log.log_info(f"[SYNC DEBUG]     ❌ Level Blueprint not found")
                    else:
                        log.log_info(f"[SYNC DEBUG]   ❌ Method C: get_level_script_blueprint NOT available")
                        # Try alternative Level Blueprint access methods
                        alt_level_methods = [
                            'get_current_level_script_blueprint',
                            'get_level_blueprint', 
                            'get_persistent_level_script_blueprint'
                        ]
                        
                        for alt_method in alt_level_methods:
                            if hasattr(unreal.EditorLevelLibrary, alt_method):
                                try:
                                    method = getattr(unreal.EditorLevelLibrary, alt_method)
                                    lvl_bp = method()
                                    if lvl_bp:
                                        open_blueprints_set.add(lvl_bp)
                                        log.log_info(f"[SYNC DEBUG]     ✅ Found Level Blueprint via {alt_method}: {lvl_bp.get_name()}")
                                        break
                                except Exception as e:
                                    log.log_info(f"[SYNC DEBUG]   Alternative level method {alt_method} failed: {e}")
                        
                        # Also try via world access
                        try:
                            world = unreal.EditorLevelLibrary.get_editor_world()
                            if world:
                                log.log_info(f"[SYNC DEBUG]   World found: {world.get_name()}")
                                # Try multiple world-based approaches
                                world_methods = ['get_level_script_blueprint', 'get_level_blueprint']
                                for world_method in world_methods:
                                    if hasattr(world, world_method):
                                        try:
                                            method = getattr(world, world_method)
                                            lvl_bp = method()
                                            if lvl_bp:
                                                open_blueprints_set.add(lvl_bp)
                                                log.log_info(f"[SYNC DEBUG]     ✅ Found Level Blueprint via world.{world_method}: {lvl_bp.get_name()}")
                                                break
                                        except Exception as e:
                                            log.log_info(f"[SYNC DEBUG]   World method {world_method} failed: {e}")
                        except Exception as e:
                            log.log_info(f"[SYNC DEBUG]   World-based Level Blueprint access failed: {e}")
                except Exception as e:
                    log.log_info(f"[SYNC DEBUG]   Method C failed: {e}")
            
            # Method D: Direct Level Blueprint lookup using known information
            if not open_blueprints_set:
                try:
                    log.log_info(f"[SYNC DEBUG]   ✅ Method D: Direct Level Blueprint lookup")
                    # Access Level Blueprint through the World we already found
                    world = unreal.EditorLevelLibrary.get_editor_world()
                    if world:
                        log.log_info(f"[SYNC DEBUG]   World found: {world.get_name()}")
                        
                        # Try accessing the ULevel object via editor properties
                        persistent_level = None
                        if hasattr(world, 'get_editor_property'):
                            try:
                                persistent_level = world.get_editor_property("persistent_level")
                                if persistent_level:
                                    log.log_info(f"[SYNC DEBUG]   Persistent level (via get_editor_property) found: {persistent_level.get_name()}")
                            except Exception as e:
                                log.log_info(f"[SYNC DEBUG]   Could not get persistent_level via get_editor_property: {e}")

                        # if that didn't work, fall back to attribute lookup
                        if not persistent_level:
                            if hasattr(world, 'persistent_level'):
                                persistent_level = world.persistent_level
                                if persistent_level:
                                    log.log_info(f"[SYNC DEBUG]   Persistent level (via attribute) found: {persistent_level.get_name()}")

                        # Now try to grab the LevelScriptActor from the Level
                        if persistent_level:
                            try:
                                script_actor = None
                                # first via editor property
                                if hasattr(persistent_level, 'get_editor_property'):
                                    script_actor = persistent_level.get_editor_property("level_script_actor")
                                # then as a plain attribute
                                if not script_actor and hasattr(persistent_level, 'level_script_actor'):
                                    script_actor = persistent_level.level_script_actor

                                if script_actor:
                                    log.log_info(f"[SYNC DEBUG]   LevelScriptActor found: {script_actor.get_name()}")
                                    
                                    # now pull the Blueprint off of it
                                    level_bp = None
                                    if hasattr(script_actor, 'get_editor_property'):
                                        level_bp = script_actor.get_editor_property("level_script_blueprint")
                                    if not level_bp and hasattr(script_actor, 'get_level_script_blueprint'):
                                        level_bp = script_actor.get_level_script_blueprint()

                                    if level_bp:
                                        open_blueprints_set.add(level_bp)
                                        log.log_info(f"[SYNC DEBUG]     🎉 SUCCESS! Found Level Blueprint: {level_bp.get_name()}")
                                else:
                                    log.log_info(f"[SYNC DEBUG]   No LevelScriptActor found in persistent level")
                            except Exception as e:
                                log.log_info(f"[SYNC DEBUG]   Error getting LevelScriptActor/Blueprint: {e}")
                        else:
                            log.log_info(f"[SYNC DEBUG]   No persistent level found")
                except Exception as e:
                    log.log_info(f"[SYNC DEBUG]   Method D failed: {e}")
            
            # Method E: Asset Registry approach to find Level Blueprint
            if not open_blueprints_set:
                try:
                    log.log_info(f"[SYNC DEBUG]   ✅ Method E: Asset Registry Level Blueprint search")
                    asset_registry = unreal.AssetRegistryHelpers.get_asset_registry()
                    
                    # Search for LevelScriptBlueprint assets - use constructor approach for UE5.6
                    try:
                        # Method E1: Try constructor approach
                        filter = unreal.ARFilter(
                            class_paths=[unreal.TopLevelAssetPath("/Script/Engine", "LevelScriptBlueprint")]
                        )
                        asset_data_list = asset_registry.get_assets(filter)
                        log.log_info(f"[SYNC DEBUG]   Found {len(asset_data_list)} LevelScriptBlueprint assets via constructor")
                    except Exception as e:
                        log.log_info(f"[SYNC DEBUG]   Constructor approach failed: {e}")
                        
                        # Method E2: Fallback - get all assets and filter manually
                        try:
                            all_assets = asset_registry.get_assets_by_path("/Game", recursive=True)
                            asset_data_list = [asset for asset in all_assets if "LevelScript" in str(asset.asset_class)]
                            log.log_info(f"[SYNC DEBUG]   Found {len(asset_data_list)} LevelScript assets via manual filter")
                        except Exception as e2:
                            log.log_info(f"[SYNC DEBUG]   Manual filter approach failed: {e2}")
                            asset_data_list = []
                    
                    for asset_data in asset_data_list:
                        try:
                            # Debug AssetData structure first
                            asset_properties = [prop for prop in dir(asset_data) if not prop.startswith('_')]
                            log.log_info(f"[SYNC DEBUG]   AssetData properties: {asset_properties[:10]}...")
                            
                            # Try different property names for asset path
                            asset_path = None
                            path_properties = ['object_path', 'asset_name', 'package_name', 'package_path', 'path_name']
                            for path_prop in path_properties:
                                if hasattr(asset_data, path_prop):
                                    try:
                                        asset_path = str(getattr(asset_data, path_prop))
                                        log.log_info(f"[SYNC DEBUG]   Asset path via {path_prop}: {asset_path}")
                                        break
                                    except Exception as e:
                                        log.log_info(f"[SYNC DEBUG]   Failed to get {path_prop}: {e}")
                            
                            if not asset_path:
                                log.log_info(f"[SYNC DEBUG]   Could not get asset path, trying string conversion: {asset_data}")
                                asset_path = str(asset_data)
                            
                            if "Lvl_ThirdPerson" in asset_path:
                                try:
                                    lvl_bp = asset_data.get_asset()
                                    if lvl_bp:
                                        open_blueprints_set.add(lvl_bp)
                                        log.log_info(f"[SYNC DEBUG]     ✅ Found Level Blueprint via Asset Registry ({class_name}): {lvl_bp.get_name()}")
                                        break
                                except Exception as e:
                                    log.log_info(f"[SYNC DEBUG]   Failed to load asset: {e}")
                        except Exception as e:
                            log.log_info(f"[SYNC DEBUG]   Asset Registry asset load failed: {e}")
                except Exception as e:
                    log.log_info(f"[SYNC DEBUG]   Method E failed: {e}")
            
            # Method F: Simple world/level exploration
            if not open_blueprints_set:
                try:
                    log.log_info(f"[SYNC DEBUG]   ✅ Method F: Simple world/level exploration")
                    world = unreal.EditorLevelLibrary.get_editor_world()
                    if world:
                        # Method F1: Explore world properties
                        world_properties = [attr for attr in dir(world) if not attr.startswith('_')]
                        level_related = [prop for prop in world_properties if 'level' in prop.lower()]
                        log.log_info(f"[SYNC DEBUG]   World level-related properties: {level_related}")
                        
                        # Method F1b: Show ALL world properties for debugging
                        log.log_info(f"[SYNC DEBUG]   All world properties: {world_properties[:30]}...")  # Show first 30
                        
                        # Method F1c: Check world type and class
                        log.log_info(f"[SYNC DEBUG]   World type: {type(world)}")
                        log.log_info(f"[SYNC DEBUG]   World class: {world.get_class().get_name() if hasattr(world, 'get_class') else 'Unknown'}")
                        
                        # Method F2: Try different level access patterns
                        level_access_methods = [
                            'get_persistent_level',
                            'persistent_level', 
                            'get_current_level',
                            'current_level',
                            'get_level',
                            'level',
                            'levels',
                            'get_levels',
                            'streaming_levels',
                            'get_streaming_levels'
                        ]
                        
                        for method_name in level_access_methods:
                            if hasattr(world, method_name):
                                log.log_info(f"[SYNC DEBUG]   Found world method/property: {method_name}")
                                try:
                                    method_or_prop = getattr(world, method_name)
                                    if callable(method_or_prop):
                                        level_obj = method_or_prop()
                                    else:
                                        level_obj = method_or_prop
                                        
                                    if level_obj:
                                        log.log_info(f"[SYNC DEBUG]   Found level via {method_name}: {level_obj}")
                                        
                                        # Try to get Level Script Actor from this level
                                        script_methods = ['get_level_script_actor', 'level_script_actor']
                                        for script_method in script_methods:
                                            if hasattr(level_obj, script_method):
                                                try:
                                                    script_method_or_prop = getattr(level_obj, script_method)
                                                    if callable(script_method_or_prop):
                                                        script_actor = script_method_or_prop()
                                                    else:
                                                        script_actor = script_method_or_prop
                                                        
                                                    if script_actor:
                                                        log.log_info(f"[SYNC DEBUG]   Found script actor via {script_method}: {script_actor}")
                                                        
                                                        # Try to get Blueprint from script actor
                                                        if hasattr(script_actor, 'get_level_script_blueprint'):
                                                            lvl_bp = script_actor.get_level_script_blueprint()
                                                            if lvl_bp:
                                                                open_blueprints_set.add(lvl_bp)
                                                                log.log_info(f"[SYNC DEBUG]     ✅ Found Level Blueprint via {method_name}.{script_method}: {lvl_bp.get_name()}")
                                                                break
                                                except Exception as e:
                                                    log.log_info(f"[SYNC DEBUG]   Script method {script_method} failed: {e}")
                                                
                                                # If we found a blueprint, stop searching
                                                if open_blueprints_set:
                                                    break
                                    else:
                                        log.log_info(f"[SYNC DEBUG]   Method {method_name} returned None/empty")
                                except Exception as e:
                                    log.log_info(f"[SYNC DEBUG]   Level access method {method_name} failed: {e}")
                            else:
                                log.log_info(f"[SYNC DEBUG]   World does not have method/property: {method_name}")
                except Exception as e:
                    log.log_info(f"[SYNC DEBUG]   Method F failed: {e}")
            
            # Method G: Asset Registry comprehensive search
            if not open_blueprints_set:
                try:
                    log.log_info(f"[SYNC DEBUG]   ✅ Method G: Comprehensive Asset Registry search")
                    asset_registry = unreal.AssetRegistryHelpers.get_asset_registry()
                    
                    # Method G1: Try different class names for Level Blueprints
                    level_bp_classes = [
                        "LevelScriptBlueprint",
                        "Blueprint", 
                        "LevelBlueprint",
                        "LevelScript"
                    ]
                    
                    for class_name in level_bp_classes:
                        try:
                            log.log_info(f"[SYNC DEBUG]   Searching for class: {class_name}")
                            
                            # Try constructor approach
                            filter = unreal.ARFilter(
                                class_paths=[unreal.TopLevelAssetPath("/Script/Engine", class_name)]
                            )
                            asset_data_list = asset_registry.get_assets(filter)
                            log.log_info(f"[SYNC DEBUG]   Found {len(asset_data_list)} {class_name} assets")
                            
                            if asset_data_list:
                                for asset_data in asset_data_list:
                                    # Debug AssetData structure first
                                    asset_properties = [prop for prop in dir(asset_data) if not prop.startswith('_')]
                                    log.log_info(f"[SYNC DEBUG]   AssetData properties: {asset_properties[:10]}...")
                                    
                                    # Try different property names for asset path
                                    asset_path = None
                                    path_properties = ['object_path', 'asset_name', 'package_name', 'package_path', 'path_name']
                                    for path_prop in path_properties:
                                        if hasattr(asset_data, path_prop):
                                            try:
                                                asset_path = str(getattr(asset_data, path_prop))
                                                log.log_info(f"[SYNC DEBUG]   Asset path via {path_prop}: {asset_path}")
                                                break
                                            except Exception as e:
                                                log.log_info(f"[SYNC DEBUG]   Failed to get {path_prop}: {e}")
                                    
                                    if not asset_path:
                                        log.log_info(f"[SYNC DEBUG]   Could not get asset path, trying string conversion: {asset_data}")
                                        asset_path = str(asset_data)
                                    
                                    if "Lvl_ThirdPerson" in asset_path:
                                        try:
                                            lvl_bp = asset_data.get_asset()
                                            if lvl_bp:
                                                open_blueprints_set.add(lvl_bp)
                                                log.log_info(f"[SYNC DEBUG]     ✅ Found Level Blueprint via Asset Registry ({class_name}): {lvl_bp.get_name()}")
                                                break
                                        except Exception as e:
                                            log.log_info(f"[SYNC DEBUG]   Failed to load asset: {e}")
                                
                                # If we found something, stop searching
                                if open_blueprints_set:
                                    break
                        except Exception as e:
                            log.log_info(f"[SYNC DEBUG]   Class {class_name} search failed: {e}")
                    
                    # Method G2: Search all assets and filter by name/path
                    if not open_blueprints_set:
                        try:
                            log.log_info(f"[SYNC DEBUG]   Method G2: Search all assets for Lvl_ThirdPerson")
                            all_assets = asset_registry.get_assets_by_path("/Game", recursive=True)
                            log.log_info(f"[SYNC DEBUG]   Found {len(all_assets)} total assets")
                            
                            matching_assets = []
                            for asset in all_assets:
                                # Try to get asset identifier
                                asset_identifier = None
                                identifier_props = ['object_path', 'asset_name', 'package_name', 'package_path']
                                for id_prop in identifier_props:
                                    if hasattr(asset, id_prop):
                                        try:
                                            asset_identifier = str(getattr(asset, id_prop))
                                            break
                                        except:
                                            continue
                                
                                if not asset_identifier:
                                    asset_identifier = str(asset)
                                
                                if "Lvl_ThirdPerson" in asset_identifier:
                                    matching_assets.append(asset)
                            
                            log.log_info(f"[SYNC DEBUG]   Found {len(matching_assets)} assets with 'Lvl_ThirdPerson'")
                            
                            for asset in matching_assets:
                                # Get asset class safely
                                asset_class = "Unknown"
                                if hasattr(asset, 'asset_class'):
                                    asset_class = str(asset.asset_class)
                                elif hasattr(asset, 'asset_class_path'):
                                    asset_class = str(asset.asset_class_path)
                                
                                log.log_info(f"[SYNC DEBUG]   Matching asset: {asset} (class: {asset_class})")
                                
                                # Try to load any Blueprint-related asset
                                if "Blueprint" in asset_class or "Script" in asset_class:
                                    try:
                                        potential_bp = asset.get_asset()
                                        if potential_bp:
                                            open_blueprints_set.add(potential_bp)
                                            log.log_info(f"[SYNC DEBUG]     ✅ Found potential Level Blueprint via name match: {potential_bp.get_name()}")
                                            break
                                    except Exception as e:
                                        log.log_info(f"[SYNC DEBUG]   Failed to load potential blueprint: {e}")
                        except Exception as e:
                            log.log_info(f"[SYNC DEBUG]   Method G2 failed: {e}")
                except Exception as e:
                    log.log_info(f"[SYNC DEBUG]   Method G failed: {e}")
            
            # Method H: Official UE5.6 API approach using World Settings
            if not open_blueprints_set:
                try:
                    log.log_info(f"[SYNC DEBUG]   ✅ Method H: UE5.6 API World Settings approach")
                    world = unreal.EditorLevelLibrary.get_editor_world()
                    if world:
                        # Try getting world settings - we know this exists from the logs
                        try:
                            world_settings = world.get_world_settings()
                            if world_settings:
                                log.log_info(f"[SYNC DEBUG]   World settings found: {world_settings}")
                                
                                # Explore world settings properties
                                settings_props = [prop for prop in dir(world_settings) if not prop.startswith('_')]
                                level_related = [prop for prop in settings_props if 'level' in prop.lower() or 'script' in prop.lower()]
                                log.log_info(f"[SYNC DEBUG]   World settings level/script properties: {level_related}")
                        except Exception as e:
                            log.log_info(f"[SYNC DEBUG]   World settings access failed: {e}")
                            
                            # Try alternative: Use unreal.LevelEditorSubsystem
                            try:
                                level_editor_subsystem = unreal.get_editor_subsystem(unreal.LevelEditorSubsystem)
                                if level_editor_subsystem:
                                    log.log_info(f"[SYNC DEBUG]   Found LevelEditorSubsystem")
                                    
                                    # Explore LevelEditorSubsystem methods
                                    les_methods = [method for method in dir(level_editor_subsystem) if not method.startswith('_')]
                                    level_methods = [method for method in les_methods if 'level' in method.lower() or 'script' in method.lower() or 'blueprint' in method.lower()]
                                    log.log_info(f"[SYNC DEBUG]   LevelEditorSubsystem level/script/blueprint methods: {level_methods}")
                                    
                                    # Try common level editor methods
                                    level_access_methods = [
                                        'get_current_level',
                                        'get_editor_world_context',
                                        'get_world_context',
                                        'get_level_viewport_camera_info'
                                    ]
                                    
                                    for method_name in level_access_methods:
                                        if hasattr(level_editor_subsystem, method_name):
                                            try:
                                                method = getattr(level_editor_subsystem, method_name)
                                                result = method()
                                                log.log_info(f"[SYNC DEBUG]   {method_name} result: {result}")
                                            except Exception as e:
                                                log.log_info(f"[SYNC DEBUG]   {method_name} failed: {e}")
                            except Exception as e:
                                log.log_info(f"[SYNC DEBUG]   LevelEditorSubsystem access failed: {e}")
                            
                            # Try using unreal.Blueprint.get_level_script_blueprint() static method
                            try:
                                log.log_info(f"[SYNC DEBUG]   Trying Blueprint static methods...")
                                blueprint_methods = [method for method in dir(unreal.Blueprint) if not method.startswith('_')]
                                level_bp_methods = [method for method in blueprint_methods if 'level' in method.lower()]
                                log.log_info(f"[SYNC DEBUG]   Blueprint level-related methods: {level_bp_methods}")
                                
                                # Try static method approach
                                if hasattr(unreal.Blueprint, 'get_level_script_blueprint'):
                                    lvl_bp = unreal.Blueprint.get_level_script_blueprint(world)
                                    if lvl_bp:
                                        open_blueprints_set.add(lvl_bp)
                                        log.log_info(f"[SYNC DEBUG]     ✅ Found Level Blueprint via Blueprint.get_level_script_blueprint: {lvl_bp.get_name()}")
                            except Exception as e:
                                log.log_info(f"[SYNC DEBUG]   Blueprint static method failed: {e}")
                            
                            # Try direct level access via game mode or world context
                            try:
                                log.log_info(f"[SYNC DEBUG]   Trying GameMode/WorldContext approach...")
                                
                                # Check if world has game_mode or similar
                                if hasattr(world, 'get_game_instance'):
                                    game_instance = world.get_game_instance()
                                    if game_instance:
                                        log.log_info(f"[SYNC DEBUG]   Game instance: {game_instance}")
                                
                                # Try finding Level via World's internal structure
                                world_props = [prop for prop in dir(world) if 'level' in prop.lower() or 'persistent' in prop.lower()]
                                log.log_info(f"[SYNC DEBUG]   World level/persistent properties (full): {world_props}")
                                
                            except Exception as e:
                                log.log_info(f"[SYNC DEBUG]   GameMode/WorldContext approach failed: {e}")
                except Exception as e:
                    log.log_info(f"[SYNC DEBUG]   Method H failed: {e}")
            
            # Method I: SUCCESS! Use the Level object we found
            if not open_blueprints_set:
                try:
                    log.log_info(f"[SYNC DEBUG]   ✅ Method I: Direct Level object access (FINAL SOLUTION)")
                    
                    # Use LevelEditorSubsystem.get_current_level() - we know this works!
                    level_editor_subsystem = unreal.get_editor_subsystem(unreal.LevelEditorSubsystem)
                    if level_editor_subsystem:
                        current_level = level_editor_subsystem.get_current_level()
                        if current_level:
                            log.log_info(f"[SYNC DEBUG]   Current level found: {current_level}")
                            
                            # Explore the Level object properties
                            level_props = [prop for prop in dir(current_level) if not prop.startswith('_')]
                            script_props = [prop for prop in level_props if 'script' in prop.lower() or 'blueprint' in prop.lower()]
                            log.log_info(f"[SYNC DEBUG]   Level script/blueprint properties: {script_props}")
                            log.log_info(f"[SYNC DEBUG]   Level all properties: {level_props[:30]}...")
                            
                            # Try common Level Script Blueprint access methods
                            level_script_methods = [
                                'get_level_script_blueprint',
                                'level_script_blueprint',
                                'get_level_script_actor',
                                'level_script_actor',
                                'get_blueprint',
                                'blueprint',
                                'get_level_script',
                                'level_script'
                            ]
                            
                            for method_name in level_script_methods:
                                if hasattr(current_level, method_name):
                                    try:
                                        log.log_info(f"[SYNC DEBUG]   Trying Level.{method_name}()...")
                                        method_or_prop = getattr(current_level, method_name)
                                        
                                        if callable(method_or_prop):
                                            result = method_or_prop()
                                        else:
                                            result = method_or_prop
                                            
                                        if result:
                                            log.log_info(f"[SYNC DEBUG]   {method_name} returned: {result}")
                                            
                                            # If it's a Blueprint, add it directly
                                            if isinstance(result, unreal.Blueprint):
                                                open_blueprints_set.add(result)
                                                log.log_info(f"[SYNC DEBUG]     🎉 FOUND LEVEL BLUEPRINT via Level.{method_name}: {result.get_name()}")
                                                break
                                            
                                            # If it's an Actor, try to get its Blueprint
                                            elif hasattr(result, 'get_level_script_blueprint'):
                                                lvl_bp = result.get_level_script_blueprint()
                                                if lvl_bp:
                                                    open_blueprints_set.add(lvl_bp)
                                                    log.log_info(f"[SYNC DEBUG]     🎉 FOUND LEVEL BLUEPRINT via Level.{method_name}.get_level_script_blueprint(): {lvl_bp.get_name()}")
                                                    break
                                            
                                            # If it's an Actor, try to get its Blueprint via get_blueprint
                                            elif hasattr(result, 'get_blueprint'):
                                                lvl_bp = result.get_blueprint()
                                                if lvl_bp:
                                                    open_blueprints_set.add(lvl_bp)
                                                    log.log_info(f"[SYNC DEBUG]     🎉 FOUND LEVEL BLUEPRINT via Level.{method_name}.get_blueprint(): {lvl_bp.get_name()}")
                                                    break
                                            
                                            # If it's something else, explore its properties
                                            else:
                                                result_props = [prop for prop in dir(result) if not prop.startswith('_')]
                                                bp_props = [prop for prop in result_props if 'blueprint' in prop.lower()]
                                                log.log_info(f"[SYNC DEBUG]   {method_name} result blueprint properties: {bp_props}")
                                                
                                                # Try blueprint-related properties
                                                for bp_prop in bp_props:
                                                    if hasattr(result, bp_prop):
                                                        try:
                                                            bp_result = getattr(result, bp_prop)
                                                            if isinstance(bp_result, unreal.Blueprint):
                                                                open_blueprints_set.add(bp_result)
                                                                log.log_info(f"[SYNC DEBUG]     🎉 FOUND LEVEL BLUEPRINT via Level.{method_name}.{bp_prop}: {bp_result.get_name()}")
                                                                break
                                                        except Exception as e:
                                                            log.log_info(f"[SYNC DEBUG]   {bp_prop} access failed: {e}")
                                                
                                                # If we found a blueprint, stop searching
                                                if open_blueprints_set:
                                                    break
                                    except Exception as e:
                                        log.log_info(f"[SYNC DEBUG]   Level.{method_name} failed: {e}")
                                else:
                                    log.log_info(f"[SYNC DEBUG]   Level does not have {method_name}")
                            
                            # Alternative: World Settings get_level() approach
                            if not open_blueprints_set:
                                try:
                                    world = unreal.EditorLevelLibrary.get_editor_world()
                                    world_settings = world.get_world_settings()
                                    if world_settings and hasattr(world_settings, 'get_level'):
                                        level_via_settings = world_settings.get_level()
                                        if level_via_settings:
                                            log.log_info(f"[SYNC DEBUG]   Level via world settings: {level_via_settings}")
                                            # Repeat the same blueprint search on this level
                                            for method_name in ['get_level_script_blueprint', 'level_script_blueprint']:
                                                if hasattr(level_via_settings, method_name):
                                                    try:
                                                        method_or_prop = getattr(level_via_settings, method_name)
                                                        result = method_or_prop() if callable(method_or_prop) else method_or_prop
                                                        if result and isinstance(result, unreal.Blueprint):
                                                            open_blueprints_set.add(result)
                                                            log.log_info(f"[SYNC DEBUG]     🎉 FOUND LEVEL BLUEPRINT via WorldSettings.get_level().{method_name}: {result.get_name()}")
                                                            break
                                                    except Exception as e:
                                                        log.log_info(f"[SYNC DEBUG]   WorldSettings level {method_name} failed: {e}")
                                except Exception as e:
                                    log.log_info(f"[SYNC DEBUG]   World settings level access failed: {e}")
                                    
                            else:
                                log.log_info(f"[SYNC DEBUG]   No current level found from LevelEditorSubsystem")
                        else:
                            log.log_info(f"[SYNC DEBUG]   LevelEditorSubsystem not found")
                except Exception as e:
                    log.log_info(f"[SYNC DEBUG]   Method I failed: {e}")
            
            # Method J: Direct Level Script Blueprint asset loading (ULTIMATE SOLUTION)
            if not open_blueprints_set:
                try:
                    log.log_info(f"[SYNC DEBUG]   ✅ Method J: Direct Level Script Blueprint asset loading")
                    
                    # We know from AssetEditorSubsystem log that the Level Blueprint is:
                    # "LevelScriptBlueprint /Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson"
                    
                    # Try loading the Level Script Blueprint directly
                    level_script_path = "/Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson"
                    
                    try:
                        # Method J1: Load as sub-object
                        level_script_bp = unreal.EditorAssetLibrary.load_asset(level_script_path)
                        if level_script_bp and isinstance(level_script_bp, unreal.Blueprint):
                            open_blueprints_set.add(level_script_bp)
                            log.log_info(f"[SYNC DEBUG]   🎉 SUCCESS! Found Level Script Blueprint via direct loading: {level_script_bp.get_name()}")
                        else:
                            log.log_info(f"[SYNC DEBUG]   Direct loading failed - not a Blueprint: {type(level_script_bp) if level_script_bp else 'None'}")
                    except Exception as e:
                        log.log_info(f"[SYNC DEBUG]   Direct loading failed: {e}")
                    
                    # Method J2: Load World asset and access its Level Script Blueprint
                    if not open_blueprints_set:
                        try:
                            world_asset_path = "/Game/ThirdPerson/Lvl_ThirdPerson"
                            world_asset = unreal.EditorAssetLibrary.load_asset(world_asset_path)
                            if world_asset:
                                log.log_info(f"[SYNC DEBUG]   Loaded World asset: {world_asset.get_name()} (type: {type(world_asset)})")
                                
                                # Explore World asset properties
                                world_props = [prop for prop in dir(world_asset) if not prop.startswith('_')]
                                level_related_props = [prop for prop in world_props if any(keyword in prop.lower() for keyword in ['level', 'script', 'blueprint', 'persistent'])]
                                log.log_info(f"[SYNC DEBUG]   World asset level-related properties: {level_related_props}")
                                
                                # Try common Level Script Blueprint access patterns
                                for method_name in ['get_level_script_blueprint', 'level_script_blueprint', 'get_persistent_level', 'persistent_level']:
                                    if hasattr(world_asset, method_name):
                                        try:
                                            if callable(getattr(world_asset, method_name)):
                                                result = getattr(world_asset, method_name)()
                                            else:
                                                result = getattr(world_asset, method_name)
                                                
                                            if result:
                                                log.log_info(f"[SYNC DEBUG]   Found via {method_name}: {result} (type: {type(result)})")
                                                
                                                # If it's a Level, try to get its script blueprint
                                                if hasattr(result, 'get_level_script_blueprint'):
                                                    script_bp = result.get_level_script_blueprint()
                                                    if script_bp and isinstance(script_bp, unreal.Blueprint):
                                                        open_blueprints_set.add(script_bp)
                                                        log.log_info(f"[SYNC DEBUG]   🎉 SUCCESS! Found Level Script Blueprint: {script_bp.get_name()}")
                                                        break
                                                elif isinstance(result, unreal.Blueprint):
                                                    open_blueprints_set.add(result)
                                                    log.log_info(f"[SYNC DEBUG]   🎉 SUCCESS! Found Blueprint: {result.get_name()}")
                                                    break
                                        except Exception as e:
                                            log.log_info(f"[SYNC DEBUG]   {method_name} failed: {e}")
                            else:
                                log.log_info(f"[SYNC DEBUG]   Failed to load World asset: {world_asset_path}")
                        except Exception as e:
                            log.log_info(f"[SYNC DEBUG]   World asset loading failed: {e}")
                    
                    # Method J3: Use the Level object we found to access its script blueprint
                    if not open_blueprints_set:
                        try:
                            level_editor_subsystem = unreal.get_editor_subsystem(unreal.LevelEditorSubsystem)
                            current_level = level_editor_subsystem.get_current_level()
                            if current_level:
                                log.log_info(f"[SYNC DEBUG]   Using current level: {current_level.get_name()}")
                                
                                # Try to find the Level Script Actor in the level
                                try:
                                    # Get all actors in the level
                                    all_actors = unreal.EditorLevelLibrary.get_all_level_actors()
                                    log.log_info(f"[SYNC DEBUG]   Found {len(all_actors)} actors in level")
                                    
                                    # Look for LevelScriptActor and inspect all actors
                                    level_script_actors = []
                                    actor_types = {}
                                    
                                    for actor in all_actors:
                                        actor_type = type(actor).__name__
                                        if actor_type not in actor_types:
                                            actor_types[actor_type] = 0
                                        actor_types[actor_type] += 1
                                        
                                        # Check for LevelScriptActor or similar
                                        if isinstance(actor, unreal.LevelScriptActor):
                                            level_script_actors.append(actor)
                                            log.log_info(f"[SYNC DEBUG]   Found LevelScriptActor: {actor.get_name()}")
                                            
                                            # Try to get the blueprint from the actor
                                            if hasattr(actor, 'get_level_script_blueprint'):
                                                script_bp = actor.get_level_script_blueprint()
                                                if script_bp and isinstance(script_bp, unreal.Blueprint):
                                                    open_blueprints_set.add(script_bp)
                                                    log.log_info(f"[SYNC DEBUG]   🎉 SUCCESS! Found Level Script Blueprint from actor: {script_bp.get_name()}")
                                                    break
                                                elif hasattr(actor, 'level_script_blueprint'):
                                                    script_bp = actor.level_script_blueprint
                                                    if script_bp and isinstance(script_bp, unreal.Blueprint):
                                                        open_blueprints_set.add(script_bp)
                                                        log.log_info(f"[SYNC DEBUG]   🎉 SUCCESS! Found Level Script Blueprint from actor property: {script_bp.get_name()}")
                                                        break
                                        
                                        # Also check for actors with "script" or "level" in their name/type
                                        elif any(keyword in actor_type.lower() for keyword in ['script', 'level']) or any(keyword in actor.get_name().lower() for keyword in ['script', 'level']):
                                            log.log_info(f"[SYNC DEBUG]   Found potential script actor: {actor.get_name()} (type: {actor_type})")
                                            
                                            # Explore this actor's properties
                                            actor_props = [prop for prop in dir(actor) if not prop.startswith('_')]
                                            script_props = [prop for prop in actor_props if any(keyword in prop.lower() for keyword in ['script', 'blueprint', 'level'])]
                                            if script_props:
                                                log.log_info(f"[SYNC DEBUG]   Actor script-related properties: {script_props}")
                                                
                                                # Try to access blueprint-related properties
                                                for prop in script_props:
                                                    try:
                                                        if callable(getattr(actor, prop)):
                                                            result = getattr(actor, prop)()
                                                        else:
                                                            result = getattr(actor, prop)
                                                            
                                                        if result and isinstance(result, unreal.Blueprint):
                                                            open_blueprints_set.add(result)
                                                            log.log_info(f"[SYNC DEBUG]   🎉 SUCCESS! Found Level Script Blueprint via {prop}: {result.get_name()}")
                                                            break
                                                    except Exception as e:
                                                        log.log_info(f"[SYNC DEBUG]   Failed to access {prop}: {e}")
                                    
                                    # Log actor type summary
                                    log.log_info(f"[SYNC DEBUG]   Actor types found: {dict(sorted(actor_types.items()))}")
                                    log.log_info(f"[SYNC DEBUG]   LevelScriptActors found: {len(level_script_actors)}")
                                    
                                    # If no LevelScriptActor found, try alternative approaches
                                    if not level_script_actors and not open_blueprints_set:
                                        log.log_info(f"[SYNC DEBUG]   No LevelScriptActor found, trying alternative actor inspection...")
                                        
                                        # Look for any actor that might have blueprint properties
                                        for actor in all_actors[:10]:  # Check first 10 actors
                                            actor_props = [prop for prop in dir(actor) if not prop.startswith('_')]
                                            blueprint_props = [prop for prop in actor_props if 'blueprint' in prop.lower()]
                                            if blueprint_props:
                                                log.log_info(f"[SYNC DEBUG]   Actor {actor.get_name()} ({type(actor).__name__}) has blueprint properties: {blueprint_props}")
                                except Exception as e:
                                    log.log_info(f"[SYNC DEBUG]   Level actors search failed: {e}")
                        except Exception as e:
                            log.log_info(f"[SYNC DEBUG]   Level Script Actor search failed: {e}")
                                    
                except Exception as e:
                    log.log_info(f"[SYNC DEBUG]   Method J failed: {e}")
            
            # Method L: Project file exploration (informational only)
            if not open_blueprints_set:
                try:
                    log.log_info(f"[SYNC DEBUG]   ✅ Method L: Project file exploration")
                    
                    # Basic project file discovery for context
                    try:
                        import os
                        project_path = unreal.Paths.project_dir()
                        content_path = os.path.join(project_path, "Content")
                        
                        # Look for level files for context
                        level_assets = []
                        if os.path.exists(content_path):
                            for root, dirs, files in os.walk(content_path):
                                for file in files:
                                    if file.endswith('.umap'):
                                        level_path = os.path.join(root, file)
                                        level_assets.append(level_path)
                        
                        log.log_info(f"[SYNC DEBUG]   Found {len(level_assets)} level files in project")
                        
                        # Check if current level matches any known level files
                        current_world = unreal.EditorLevelLibrary.get_editor_world()
                        if current_world:
                            world_name = current_world.get_name()
                            matching_levels = [l for l in level_assets if world_name in l]
                            if matching_levels:
                                log.log_info(f"[SYNC DEBUG]   Current level file: {matching_levels[0]}")
                                log.log_info(f"[SYNC DEBUG]   📁 Level file context available for C++ Asset Registry API")
                                
                    except Exception as e:
                        log.log_info(f"[SYNC DEBUG]   File exploration failed: {e}")
                        
                except Exception as e:
                    log.log_info(f"[SYNC DEBUG]   Method L failed: {e}")
            
            # Final Result: If C++ Asset Registry fails, no other methods are needed
            # The C++ API is the only supported path now
            if not open_blueprints_set:
                log.log_info(f"[SYNC DEBUG]   📋 C++ Asset Registry API is the only supported method")
            
            # Convert set to list for further processing
            open_blueprints = list(open_blueprints_set)
            
            if open_blueprints:
                for bp in open_blueprints:
                    detection_logs.append(f"✅ Found open Blueprint: {bp.get_name()}")
                log.log_info(f"[SYNC DEBUG]   🎉 Total Blueprints found: {len(open_blueprints)}")
            else:
                log.log_info(f"[SYNC DEBUG]   ❌ No Blueprints found with any method")
            
            return {
                "success": True,
                "blueprints_found": len(open_blueprints),
                "detection_method": "UE5.6 Compatible",
                "detection_logs": detection_logs,
                "blueprints": []
            }
        except Exception as e:
            detection_logs.append(f"Universal detection error: {str(e)}")
            log.log_error(f"[SYNC DEBUG] Universal detection failed: {e}")
        
        current_timestamp = time.time()
        
        # Final result compilation
        if open_blueprints:
            log.log_info(f"[SYNC DEBUG] ✅ Found {len(open_blueprints)} Blueprint(s) for analysis!")
            
            result = {
                "success": True,
                "blueprints_found": len(open_blueprints),
                "detection_method": "UE5.6 Compatible",
                "detection_logs": detection_logs,
                "blueprints": []
            }
            
            # Process each found Blueprint
            for i, blueprint in enumerate(open_blueprints):
                blueprint_data = BlueprintContextHandler._extract_blueprint_data(blueprint)
                if blueprint_data:
                    result["blueprints"].append(blueprint_data)
                    log.log_info(f"[SYNC DEBUG]   Blueprint {i+1}: {blueprint_data.get('name', 'Unknown')} ({blueprint_data.get('total_nodes', 0)} nodes)")
            
            return result
        else:
            log.log_info("[SYNC DEBUG] 📋 No specific Blueprint detected - providing project context")
            
            # Even without specific Blueprint, provide useful project context (Kibibyte Labs style)
            try:
                asset_registry = unreal.AssetRegistryHelpers.get_asset_registry()
                project_blueprints = []
                
                if asset_registry:
                    # UE5.6 compatible asset registry call
                    # Use path-based approach since ARFilter class_paths is read-only
                    try:
                        all_assets = asset_registry.get_assets_by_path("/Game", recursive=True)
                        blueprint_assets = [asset for asset in all_assets if asset.asset_class_path.asset_name == "Blueprint"]
                    except:
                        try:
                            all_assets = asset_registry.get_all_assets()
                            blueprint_assets = [asset for asset in all_assets if "Blueprint" in str(asset.asset_class)]
                        except:
                            blueprint_assets = []
                    project_blueprints = [asset.asset_name for asset in blueprint_assets[:10]]  # Get first 10 for context
                
                # NEW: Auto-fallback to Level Blueprint when nothing is selected
                log.log_info("[SYNC DEBUG] 🎯 No specific Blueprint selected - attempting Level Blueprint fallback...")
                level_blueprint_result = BlueprintContextHandler._get_level_blueprint_as_fallback()

                if level_blueprint_result.get("success", False):
                    log.log_info("[SYNC DEBUG] ✅ Successfully using Level Blueprint as fallback context!")
                    return level_blueprint_result

                # If Level Blueprint fallback also fails, provide project context
                return {
                    "success": True,
                    "message": "No specific Blueprint open - providing project context",
                    "detection_logs": detection_logs,
                    "project_context": {
                        "total_blueprints": len(blueprint_assets) if 'blueprint_assets' in locals() else 0,
                        "available_blueprints": project_blueprints,
                        "suggestions": [
                            "Open any Blueprint in the editor to get full context",
                            "Level Blueprint is always available for scripting",
                            "Create a new Blueprint to start fresh",
                            "Select a Blueprint in Content Browser for analysis"
                        ]
                    },
                    "editor_info": {
                        "world_available": unreal.EditorLevelLibrary.get_editor_world() is not None,
                        "editor_active": True
                    },
                    "level_blueprint_fallback_attempted": True,
                    "level_blueprint_fallback_result": level_blueprint_result
                }
            except Exception as e:
                log.log_error(f"[SYNC DEBUG] Error creating project context: {e}")
            
            return {
                "success": True,
                "message": "Ready for Blueprint context - awaiting specific Blueprint",
                "detection_logs": detection_logs,
                "guidance": [
                    "🎯 Open any Blueprint to get full node-level context",
                    "📝 Level Blueprint scripting is always available", 
                    "🔧 Create new Blueprint for fresh AI generation",
                    "📋 Select Blueprint in Content Browser for analysis"
                ]
            }

    @staticmethod
    def _extract_graph_data(request_data):
        """Extract Blueprint graph data for analysis."""
        try:
            graph_name = request_data.get("graph_name", "EventGraph")
            use_current_context = request_data.get("use_current_context", True)
            blueprint_path = request_data.get("blueprint_path", "")
            
            # Get Blueprint reference
            if use_current_context:
                asset_subsystem = unreal.get_editor_subsystem(unreal.AssetEditorSubsystem)
                
                # Use the same multi-method approach as _extract_local_context
                open_blueprints = []
                
                # Try multiple methods to find open blueprints
                if hasattr(asset_subsystem, 'get_all_edited_objects'):
                    try:
                        all_edited_objects = asset_subsystem.get_all_edited_objects()
                        open_blueprints = [obj for obj in all_edited_objects if isinstance(obj, unreal.Blueprint)]
                    except:
                        pass
                
                if not open_blueprints and hasattr(asset_subsystem, 'get_open_assets'):
                    try:
                        all_open_assets = asset_subsystem.get_open_assets()
                        open_blueprints = [asset for asset in all_open_assets if isinstance(asset, unreal.Blueprint)]
                    except:
                        pass
                
                if open_blueprints:
                    blueprint = open_blueprints[0]
                    blueprint_path = blueprint.get_path_name()
                else:
                    return {"error": "No Blueprint currently open"}
            else:
                blueprint = unreal.EditorAssetLibrary.load_asset(blueprint_path)
            
            if not blueprint:
                return {"error": f"Could not load Blueprint: {blueprint_path}"}
            
            # Extract basic graph structure (nodes, connections, etc.)
            # This is just metadata - the complex analysis happens in Bridge server
            graph_data = {
                "blueprint_path": blueprint_path,
                "blueprint_name": blueprint.get_name(),
                "graph_name": graph_name,
                "blueprint_type": blueprint.get_class().get_name(),
                "has_graphs": True,
                "extraction_timestamp": time.time()
            }
            
            # Try to get basic node count if possible
            try:
                blueprint_obj = blueprint.get_blueprint_obj()
                if blueprint_obj and blueprint_obj.ubergraph_pages:
                    for graph in blueprint_obj.ubergraph_pages:
                        if graph.get_name() == graph_name:
                            # Get basic node information
                            nodes = graph.nodes
                            graph_data.update({
                                "node_count": len(nodes) if nodes else 0,
                                "has_nodes": bool(nodes)
                            })
                            break
            except Exception as e:
                log.log_debug(f"Could not extract detailed graph data: {str(e)}")
                # This is fine - the Bridge will handle the complex analysis
            
            return graph_data
            
        except Exception as e:
            log.log_error(f"Error extracting graph data: {str(e)}")
            return {"error": str(e)}

    @staticmethod
    def _extract_detailed_node_info(node):
        """Extract comprehensive node information including pins, connections, and function references."""
        try:
            node_info = {
                "name": node.get_name() if hasattr(node, 'get_name') else str(node),
                "class": node.get_class().get_name() if hasattr(node, 'get_class') else type(node).__name__,
                "type": "unknown"
            }
            
            # Basic node properties
            if hasattr(node, 'node_title'):
                node_info["title"] = str(node.node_title)
            
            if hasattr(node, 'node_pos_x') and hasattr(node, 'node_pos_y'):
                node_info["position"] = {"x": float(node.node_pos_x), "y": float(node.node_pos_y)}
            
            if hasattr(node, 'node_comment'):
                node_info["comment"] = str(node.node_comment)
                
            if hasattr(node, 'node_guid'):
                node_info["guid"] = str(node.node_guid)
            
            # Identify node type based on class
            node_class = node_info["class"]
            if "Event" in node_class:
                node_info["type"] = "event"
                # Extract event reference for Event nodes
                if hasattr(node, 'event_reference'):
                    node_info["event_reference"] = {
                        "member_parent": str(node.event_reference.member_parent) if hasattr(node.event_reference, 'member_parent') else "",
                        "member_name": str(node.event_reference.member_name) if hasattr(node.event_reference, 'member_name') else ""
                    }
            elif "CallFunction" in node_class:
                node_info["type"] = "function_call"
                # Extract function reference for Function Call nodes
                if hasattr(node, 'function_reference'):
                    node_info["function_reference"] = {
                        "member_parent": str(node.function_reference.member_parent) if hasattr(node.function_reference, 'member_parent') else "",
                        "member_name": str(node.function_reference.member_name) if hasattr(node.function_reference, 'member_name') else ""
                    }
            elif "Variable" in node_class:
                node_info["type"] = "variable"
                if hasattr(node, 'variable_reference'):
                    node_info["variable_reference"] = {
                        "member_parent": str(node.variable_reference.member_parent) if hasattr(node.variable_reference, 'member_parent') else "",
                        "member_name": str(node.variable_reference.member_name) if hasattr(node.variable_reference, 'member_name') else ""
                    }
            elif "Comment" in node_class:
                node_info["type"] = "comment"
            else:
                node_info["type"] = "other"
            
            # Extract all pins (inputs/outputs)
            pins = []
            if hasattr(node, 'pins') and node.pins:
                for pin in node.pins:
                    if pin:
                        pin_info = BlueprintContextHandler._extract_pin_info(pin)
                        if pin_info:
                            pins.append(pin_info)
            
            node_info["pins"] = pins
            node_info["pin_count"] = len(pins)
            
            # Additional node-specific properties
            if hasattr(node, 'enabled_state'):
                node_info["enabled_state"] = str(node.enabled_state)
                
            if hasattr(node, 'advanced_pin_display'):
                node_info["advanced_pin_display"] = str(node.advanced_pin_display)
            
            return node_info
            
        except Exception as e:
            log.log_error(f"Error extracting node info: {str(e)}")
            return {
                "name": "ErrorNode",
                "class": "Unknown", 
                "type": "error",
                "error": str(e)
            }

    @staticmethod
    def _extract_pin_info(pin):
        """Extract detailed pin information including connections and types."""
        try:
            pin_info = {
                "id": str(pin.pin_id) if hasattr(pin, 'pin_id') else "",
                "name": str(pin.pin_name) if hasattr(pin, 'pin_name') else "",
                "direction": str(pin.direction) if hasattr(pin, 'direction') else "unknown"
            }
            
            # Pin type information
            if hasattr(pin, 'pin_type'):
                pin_type = pin.pin_type
                pin_info["type"] = {
                    "category": str(pin_type.pin_category) if hasattr(pin_type, 'pin_category') else "",
                    "sub_category": str(pin_type.pin_sub_category) if hasattr(pin_type, 'pin_sub_category') else "",
                    "is_reference": bool(pin_type.b_is_reference) if hasattr(pin_type, 'b_is_reference') else False,
                    "is_const": bool(pin_type.b_is_const) if hasattr(pin_type, 'b_is_const') else False,
                    "container_type": str(pin_type.container_type) if hasattr(pin_type, 'container_type') else ""
                }
                
                # Sub-category object (for specific types like structs, classes)
                if hasattr(pin_type, 'pin_sub_category_object') and pin_type.pin_sub_category_object:
                    pin_info["type"]["sub_category_object"] = str(pin_type.pin_sub_category_object)
            
            # Default value
            if hasattr(pin, 'default_value'):
                pin_info["default_value"] = str(pin.default_value)
                
            # Pin properties
            if hasattr(pin, 'b_hidden'):
                pin_info["hidden"] = bool(pin.b_hidden)
            if hasattr(pin, 'b_advanced_view'):
                pin_info["advanced_view"] = bool(pin.b_advanced_view)
            if hasattr(pin, 'b_not_connectable'):
                pin_info["not_connectable"] = bool(pin.b_not_connectable)
            
            # Connection information
            connections = []
            if hasattr(pin, 'linked_to') and pin.linked_to:
                for linked_pin in pin.linked_to:
                    if linked_pin:
                        connection = {
                            "pin_id": str(linked_pin.pin_id) if hasattr(linked_pin, 'pin_id') else "",
                            "node_name": str(linked_pin.get_owning_node().get_name()) if hasattr(linked_pin, 'get_owning_node') else ""
                        }
                        connections.append(connection)
            
            pin_info["connections"] = connections
            pin_info["connection_count"] = len(connections)
            
            return pin_info
            
        except Exception as e:
            return {
                "name": "ErrorPin",
                "error": str(e)
            }

    @staticmethod
    def _analyze_blueprint_for_ai(local_context):
        """Analyze Blueprint context for AI-powered generation (Kibibyte Labs style)."""
        try:
            if not local_context.get("success", False):
                return {"error": "No Blueprint context available"}
            
            analysis = {
                "blueprint_info": {
                    "name": local_context.get("blueprint_name", ""),
                    "path": local_context.get("blueprint_path", ""),
                    "class": local_context.get("blueprint_class", ""),
                    "total_nodes": local_context.get("total_nodes", 0)
                },
                "existing_patterns": [],
                "suggested_connections": [],
                "variable_context": [],
                "function_opportunities": [],
                "optimization_suggestions": []
            }
            
            # Analyze existing variables for context
            variables = local_context.get("variables", [])
            for var in variables:
                analysis["variable_context"].append({
                    "name": var.get("name", ""),
                    "type": var.get("type", ""),
                    "usage_potential": "high"  # AI can determine usage patterns
                })
            
            # Analyze event graph for patterns
            event_graph = local_context.get("event_graph", {})
            if event_graph and event_graph.get("nodes"):
                nodes = event_graph["nodes"]
                
                # Pattern detection (like Kibibyte Labs)
                event_nodes = [n for n in nodes if n.get("type") == "event"]
                function_nodes = [n for n in nodes if n.get("type") == "function_call"]
                
                analysis["existing_patterns"] = [
                    {
                        "pattern_type": "event_to_function",
                        "events": len(event_nodes),
                        "functions": len(function_nodes),
                        "complexity": "medium" if len(nodes) > 5 else "simple"
                    }
                ]
                
                # Suggest potential new connections
                for node in nodes:
                    if node.get("type") == "event" and node.get("event_reference"):
                        event_ref = node["event_reference"]
                        if "BeginPlay" in event_ref.get("member_name", ""):
                            analysis["function_opportunities"].append({
                                "type": "initialization",
                                "suggestion": "Add initialization logic after BeginPlay",
                                "priority": "high",
                                "nodes": ["Set variables", "Initialize components", "Setup bindings"]
                            })
                        elif "Tick" in event_ref.get("member_name", ""):
                            analysis["function_opportunities"].append({
                                "type": "update_loop", 
                                "suggestion": "Add per-frame update logic",
                                "priority": "medium",
                                "nodes": ["Update position", "Check conditions", "Handle input"]
                            })
                
                # Analyze function calls for optimization
                for node in function_nodes:
                    if node.get("function_reference"):
                        func_ref = node["function_reference"]
                        if "PrintString" in func_ref.get("member_name", ""):
                            analysis["optimization_suggestions"].append({
                                "type": "debug_cleanup",
                                "suggestion": "Consider removing debug PrintString nodes for production",
                                "severity": "low"
                            })
            
            # Generate AI-friendly summary
            analysis["ai_summary"] = {
                "blueprint_complexity": "simple" if local_context.get("total_nodes", 0) < 10 else "complex",
                "generation_context": f"Blueprint with {len(variables)} variables and {local_context.get('total_nodes', 0)} nodes",
                "recommended_approach": "context_aware_generation",
                "kibibyte_compatibility": True
            }
            
            return analysis
            
        except Exception as e:
            log.log_error(f"Error in Blueprint AI analysis: {str(e)}")
            return {"error": str(e), "fallback_mode": True}

    @staticmethod
    def _get_level_blueprint_as_fallback():
        """
        Get Level Blueprint as fallback context when no specific Blueprint is selected.
        This provides comprehensive context from the current level's Blueprint.
        """
        try:
            log.log_info("[LEVEL FALLBACK] 🎯 Attempting to get Level Blueprint as context fallback...")

            # Try multiple methods to get the Level Blueprint
            level_blueprint = None
            detection_method = "unknown"

            # Method 1: Direct Level Script Blueprint access
            try:
                if hasattr(unreal.EditorLevelLibrary, "get_level_script_blueprint"):
                    level_blueprint = unreal.EditorLevelLibrary.get_level_script_blueprint()
                    if level_blueprint:
                        detection_method = "EditorLevelLibrary.get_level_script_blueprint"
                        log.log_info(f"[LEVEL FALLBACK] ✅ Method 1 success: {level_blueprint.get_name()}")
            except Exception as e:
                log.log_info(f"[LEVEL FALLBACK] Method 1 failed: {e}")

            # Method 2: Via current world
            if not level_blueprint:
                try:
                    world = unreal.EditorLevelLibrary.get_editor_world()
                    if world and hasattr(world, "get_level_script_blueprint"):
                        level_blueprint = world.get_level_script_blueprint()
                        if level_blueprint:
                            detection_method = "World.get_level_script_blueprint"
                            log.log_info(f"[LEVEL FALLBACK] ✅ Method 2 success: {level_blueprint.get_name()}")
                except Exception as e:
                    log.log_info(f"[LEVEL FALLBACK] Method 2 failed: {e}")

            # Method 3: Asset Registry search for Level Script Blueprint
            if not level_blueprint:
                try:
                    asset_registry = unreal.AssetRegistryHelpers.get_asset_registry()
                    filter = unreal.ARFilter()
                    filter.class_names = ["LevelScriptBlueprint"]
                    assets = asset_registry.get_assets(filter)

                    for asset in assets:
                        # Load the asset to get the Blueprint object
                        blueprint_asset = unreal.EditorAssetLibrary.load_asset(asset.object_path)
                        if blueprint_asset:
                            level_blueprint = blueprint_asset
                            detection_method = "AssetRegistry.LevelScriptBlueprint"
                            log.log_info(f"[LEVEL FALLBACK] ✅ Method 3 success: {level_blueprint.get_name()}")
                            break
                except Exception as e:
                    log.log_info(f"[LEVEL FALLBACK] Method 3 failed: {e}")

            if not level_blueprint:
                log.log_warning("[LEVEL FALLBACK] ❌ Could not find Level Blueprint using any method")
                return {
                    "success": False,
                    "error": "Level Blueprint not found",
                    "methods_attempted": ["EditorLevelLibrary", "World", "AssetRegistry"]
                }

            # Extract comprehensive data from the Level Blueprint
            blueprint_data = BlueprintContextHandler._extract_blueprint_data(level_blueprint)
            if not blueprint_data:
                return {
                    "success": False,
                    "error": "Failed to extract Level Blueprint data"
                }

            # Mark this as a fallback context
            blueprint_data["is_fallback_context"] = True
            blueprint_data["fallback_reason"] = "No specific Blueprint selected - using Level Blueprint"
            blueprint_data["detection_method"] = detection_method

            log.log_info(f"[LEVEL FALLBACK] ✅ Successfully extracted Level Blueprint context: {blueprint_data.get('name', 'Unknown')}")

            return {
                "success": True,
                "message": "Using Level Blueprint as fallback context",
                "blueprints_found": 1,
                "detection_method": f"level_blueprint_fallback_{detection_method}",
                "is_fallback": True,
                "fallback_type": "level_blueprint",
                "blueprints": [blueprint_data],
                "current_blueprint": blueprint_data,
                "context_source": "level_blueprint_window"
            }

        except Exception as e:
            log.log_error(f"[LEVEL FALLBACK] Error getting Level Blueprint fallback: {e}")
            return {
                "success": False,
                "error": f"Level Blueprint fallback failed: {str(e)}"
            }

    @staticmethod
    def _extract_blueprint_data(blueprint):
        """Extract comprehensive Blueprint data including all nodes and context."""
        try:
            if not blueprint:
                return None

            blueprint_name = blueprint.get_name()
            blueprint_path = blueprint.get_path_name()
            blueprint_class = blueprint.get_class().get_name()

            log.log_info(f"[SYNC DEBUG] Extracting data from Blueprint: {blueprint_name}")

            blueprint_data = {
                "name": blueprint_name,
                "path": blueprint_path,
                "class": blueprint_class,
                "type": "Level Blueprint" if "Level" in blueprint_name or "Lvl_" in blueprint_name else "Blueprint Class"
            }
            
            # Extract variables
            variables = []
            if hasattr(blueprint, 'new_variables'):
                for var in blueprint.new_variables:
                    variables.append({
                        "name": var.var_name,
                        "type": str(var.var_type.pin_category),
                        "category": getattr(var.var_type, 'pin_subcategory', ""),
                        "is_array": getattr(var.var_type, 'container_type', "") == "Array"
                    })
            blueprint_data["variables"] = variables
            
            # Extract comprehensive graph information
            graphs = []
            event_graph_details = None
            total_nodes = 0
            all_nodes = []
            
            # Use the correct approach to get ALL graphs from Blueprint
            try:
                # Method 1: Event Graphs (Ubergraph Pages) - Main Event Graph with BeginPlay, etc.
                if hasattr(blueprint, 'ubergraph_pages'):
                    ubergraph_pages = blueprint.get_editor_property("ubergraph_pages")
                    log.log_info(f"[SYNC DEBUG]   Found {len(ubergraph_pages)} ubergraph pages")
                    for graph in ubergraph_pages:
                        graph_name = graph.get_name()
                        graph_info = {
                            "name": graph_name,
                            "type": "EventGraph",
                            "category": "Ubergraph",
                            "nodes": []
                        }
                        
                        # Extract nodes from this graph
                        if hasattr(graph, 'nodes') and graph.nodes:
                            log.log_info(f"[SYNC DEBUG]   EventGraph '{graph_name}' has {len(graph.nodes)} nodes")
                            for node in graph.nodes:
                                if node:
                                    # Extract detailed node information
                                    node_info = BlueprintContextHandler._extract_detailed_node_info(node)
                                    if node_info:
                                        graph_info["nodes"].append(node_info)
                                        all_nodes.append(node_info)
                        
                            graph_info["node_count"] = len(graph_info["nodes"])
                            total_nodes += len(graph_info["nodes"])
                            
                            # This is the main EventGraph with BeginPlay, etc.
                            event_graph_details = {
                                "name": graph_name,
                                "nodes": graph_info["nodes"],
                                "node_count": len(graph_info["nodes"])
                            }
                            log.log_info(f"[SYNC DEBUG]   ✅ EventGraph '{graph_name}' extracted with {len(graph_info['nodes'])} nodes")
                        
                        graphs.append(graph_info)
                
                # Method 2: Function Graphs - Custom Blueprint functions
                if hasattr(blueprint, 'function_graphs'):
                    function_graphs = blueprint.get_editor_property("function_graphs")
                    log.log_info(f"[SYNC DEBUG]   Found {len(function_graphs)} function graphs")
                    for graph in function_graphs:
                        graph_name = graph.get_name()
                        graph_info = {
                            "name": graph_name,
                            "type": "Function",
                            "category": "FunctionGraph",
                            "nodes": []
                        }
                        
                        if hasattr(graph, 'nodes') and graph.nodes:
                            for node in graph.nodes:
                                if node:
                                    node_info = BlueprintContextHandler._extract_detailed_node_info(node)
                                    if node_info:
                                        graph_info["nodes"].append(node_info)
                                        all_nodes.append(node_info)
                        
                            graph_info["node_count"] = len(graph_info["nodes"])
                            total_nodes += len(graph_info["nodes"])
                            log.log_info(f"[SYNC DEBUG]   Function '{graph_name}' has {len(graph_info['nodes'])} nodes")
                        
                        graphs.append(graph_info)
                
                # Method 3: Macro Graphs - Blueprint macros
                if hasattr(blueprint, 'macro_graphs'):
                    macro_graphs = blueprint.get_editor_property("macro_graphs")
                    log.log_info(f"[SYNC DEBUG]   Found {len(macro_graphs)} macro graphs")
                    for graph in macro_graphs:
                        graph_name = graph.get_name()
                        graph_info = {
                            "name": graph_name,
                            "type": "Macro",
                            "category": "MacroGraph",
                            "nodes": []
                        }
                        
                        if hasattr(graph, 'nodes') and graph.nodes:
                            for node in graph.nodes:
                                if node:
                                    node_info = BlueprintContextHandler._extract_detailed_node_info(node)
                                    if node_info:
                                        graph_info["nodes"].append(node_info)
                                        all_nodes.append(node_info)
                        
                            graph_info["node_count"] = len(graph_info["nodes"])
                            total_nodes += len(graph_info["nodes"])
                            log.log_info(f"[SYNC DEBUG]   Macro '{graph_name}' has {len(graph_info['nodes'])} nodes")
                        
                        graphs.append(graph_info)
                    
            except Exception as graph_e:
                log.log_error(f"[SYNC DEBUG] Error extracting graph information: {graph_e}")
                # Fallback to old method if new approach fails
                if hasattr(blueprint, 'ubergraph_pages'):
                    for graph in blueprint.ubergraph_pages:
                        graph_info = {
                            "name": graph.get_name(),
                            "type": "EventGraph",
                            "category": "Fallback",
                            "nodes": [],
                            "node_count": 0
                        }
                        graphs.append(graph_info)
            
            blueprint_data["graphs"] = graphs
            blueprint_data["event_graph"] = event_graph_details
            blueprint_data["total_nodes"] = total_nodes
            blueprint_data["all_nodes"] = all_nodes
            
            # Extract node patterns for AI context
            node_patterns = BlueprintContextHandler._analyze_node_patterns(all_nodes)
            blueprint_data["node_patterns"] = node_patterns
            
            log.log_info(f"[SYNC DEBUG]   Extracted: {len(variables)} variables, {len(graphs)} graphs, {total_nodes} nodes")
            
            return blueprint_data
            
        except Exception as e:
            log.log_error(f"[SYNC DEBUG] Error extracting Blueprint data: {e}")
            return {"name": "Error", "error": str(e)}

    @staticmethod
    def _analyze_node_patterns(nodes):
        """Analyze node patterns for AI context (Kibibyte Labs style)."""
        try:
            patterns = {
                "event_nodes": [],
                "function_calls": [],
                "variable_operations": [],
                "flow_control": [],
                "common_patterns": []
            }
            
            for node in nodes:
                node_type = node.get("type", "")
                node_name = node.get("name", "")
                node_class = node.get("class", "")
                
                # Categorize nodes for pattern analysis
                if "Event" in node_class:
                    patterns["event_nodes"].append(node_name)
                elif "CallFunction" in node_class:
                    func_ref = node.get("function_reference", {})
                    if func_ref.get("member_name"):
                        patterns["function_calls"].append(func_ref["member_name"])
                elif "Variable" in node_class:
                    patterns["variable_operations"].append(node_name)
                elif any(control in node_class for control in ["Branch", "Loop", "Gate", "Switch"]):
                    patterns["flow_control"].append(node_class)
            
            # Identify common patterns
            if "ReceiveBeginPlay" in patterns["event_nodes"]:
                patterns["common_patterns"].append("Initialization Logic")
            if "ReceiveTick" in patterns["event_nodes"]:
                patterns["common_patterns"].append("Update Loop")
            if len(patterns["function_calls"]) > 0:
                patterns["common_patterns"].append("Function-Based Logic")
            if len(patterns["variable_operations"]) > 0:
                patterns["common_patterns"].append("Data Management")
            
            return patterns
            
        except Exception as e:
            return {"error": str(e)}

# Register the handler
def handle_blueprint_context_request(request_type, data):
    """Main entry point for Blueprint context requests."""
    log.log_info(f"[SYNC DEBUG] Handling Blueprint context request: {request_type}")
    
    if request_type == "get_current_blueprint_context":
        return BlueprintContextHandler.get_current_blueprint_context()
    elif request_type == "generate_blueprint_function":
        return BlueprintContextHandler.generate_blueprint_function(data)
    elif request_type == "generate_context_aware_blueprint_function":
        return BlueprintContextHandler.generate_context_aware_blueprint_function(data)
    elif request_type == "analyze_blueprint_graph":
        return BlueprintContextHandler.analyze_blueprint_graph(data)
    else:
        return {
            "success": False,
            "error": f"Unknown Blueprint context request type: {request_type}"
        } 