const express = require('express');
const router = express.Router();
const { authenticateJWT } = require('../../middleware/auth');
const subscriptionMiddleware = require('../../middleware/subscription');

// Get optimal MCP server configuration for the user
router.get('/config', authenticateJWT, async function(req, res) {
  try {
    const { user } = req;
    const { device_id, platform, app_version } = req.query;
    
    // Get user's subscription info
    const subscriptionStatus = await checkUserSubscription(user.id);
    
    // Determine the best MCP server for this user
    const mcpConfig = await determineMCPServerConfig({
      user,
      subscriptionStatus,
      deviceId: device_id,
      platform,
      appVersion: app_version
    });
    
    res.json({
      success: true,
      config: mcpConfig
    });
    
  } catch (error) {
    console.error('Error getting MCP config:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get MCP configuration',
      details: error.message
    });
  }
});

// Determine optimal MCP server configuration
async function determineMCPServerConfig({ user, subscriptionStatus, deviceId, platform, appVersion }) {
  const config = {
    serverType: 'url', // 'url', 'local', or 'embedded'
    serverUrl: null,
    version: null,
    features: [],
    updateFrequency: 3600000, // 1 hour in ms
    cacheEnabled: true,
    fallbackUrls: []
  };
  
  // Local backend file serving
  const baseUrl = process.env.API_BASE_URL || 'http://localhost:5001/api';
  
  // Determine server version and file based on subscription tier
  if (subscriptionStatus.tier === 'pro' || subscriptionStatus.tier === 'enterprise') {
    // Pro/Enterprise users get the premium server with all features
    config.serverUrl = `${baseUrl}/mcp/servers/premium/latest/mcp_server_protected.py`;
    config.version = 'premium-latest';
    config.features = [
      'advanced_blueprint_analysis',
      'ai_powered_generation', 
      'smart_node_planning',
      'performance_optimization',
      'custom_templates',
      'priority_support'
    ];
    config.updateFrequency = 1800000; // 30 minutes for faster updates
  } else if (subscriptionStatus.tier === 'basic') {
    // Basic users get standard features
    config.serverUrl = `${baseUrl}/mcp/servers/standard/latest/mcp_server_protected.py`;
    config.version = 'standard-latest';
    config.features = [
      'basic_blueprint_tools',
      'standard_generation',
      'basic_analysis'
    ];
  } else {
    // Free tier users get limited features
    config.serverUrl = `${baseUrl}/mcp/servers/free/latest/mcp_server_protected.py`;
    config.version = 'free-latest';
    config.features = [
      'basic_tools_only',
      'limited_generation'
    ];
    config.updateFrequency = 7200000; // 2 hours for free tier
  }
  
  // Platform-specific optimizations (disabled for now - using latest for all platforms)
  // if (platform === 'darwin') {
  //   config.serverUrl = config.serverUrl.replace('/latest/', '/macos/');
  // } else if (platform === 'linux') {
  //   config.serverUrl = config.serverUrl.replace('/latest/', '/linux/');
  // } else if (platform === 'win32') {
  //   config.serverUrl = config.serverUrl.replace('/latest/', '/windows/');
  // }
  
  // A/B testing for beta features
  if (await isUserInBetaGroup(user.id)) {
    config.serverUrl = config.serverUrl.replace('/latest/', '/beta/');
    config.version += '-beta';
    config.features.push('experimental_features');
  }
  
  // Add fallback URLs - all served by backend API
  config.fallbackUrls = [
    `${baseUrl}/mcp/servers/stable/latest/mcp_server_protected.py`, // Stable fallback
    `${baseUrl}/mcp/servers/minimal/latest/mcp_server_protected.py`, // Minimal fallback
    `${baseUrl}/mcp/servers/basic/latest/mcp_server_stdio.py` // Basic stdio fallback
  ];
  
  // Custom server for specific users (admin override)
  const customServer = await getCustomServerForUser(user.id);
  if (customServer) {
    config.serverUrl = customServer.url;
    config.version = customServer.version;
    config.features = customServer.features;
  }
  
  return config;
}

// Check if user is in beta testing group
async function isUserInBetaGroup(userId) {
  // Check database for beta group membership
  // For now, return false, but you can implement this based on your needs
  return false;
}

// Get custom server configuration for specific users
async function getCustomServerForUser(userId) {
  // Check if admin has assigned a custom server to this user
  // This could be stored in a database table
  return null;
}

// Check user subscription status
async function checkUserSubscription(userId) {
  // This should integrate with your existing subscription system
  // For now, return a mock response
  return {
    tier: 'pro', // 'free', 'basic', 'pro', 'enterprise'
    status: 'active',
    features: ['advanced_blueprint_tools']
  };
}

module.exports = router; 