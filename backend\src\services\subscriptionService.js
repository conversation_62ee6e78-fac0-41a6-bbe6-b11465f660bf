const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const authService = require('./authService');

class SubscriptionService {
  constructor() {
    // Price IDs for different tiers
    this.PRICE_ID_BASIC = process.env.STRIPE_PRICE_ID_BASIC; // $20/month tier
    this.PRICE_ID_PRO = process.env.STRIPE_PRICE_ID_PRO; // $30/month tier
    this.PRICE_ID = process.env.STRIPE_PRICE_ID; // Default price ID (for backward compatibility)

    this.PRODUCT_NAME = 'AI Unreal Engine Assistant';
    this.CURRENCY = 'usd';

    // Plan details
    this.PLANS = {
      basic: {
        name: 'Basic',
        priceId: this.PRICE_ID_BASIC,
        amount: 2000, // $20.00
        features: ['Basic AI assistance', 'Limited API calls', 'Standard support']
      },
      pro: {
        name: 'Pro',
        priceId: this.PRICE_ID_PRO,
        amount: 3000, // $30.00
        features: ['Advanced AI assistance', 'Unlimited API calls', 'Priority support', 'Custom blueprints']
      }
    };

    // Initialize Stripe
    console.log('Stripe initialized with secret key:', process.env.STRIPE_SECRET_KEY ? process.env.STRIPE_SECRET_KEY.substring(0, 10) + '...' : 'Not set');
    console.log('Subscription service initialized with plans:', {
      basic: {
        priceId: this.PLANS.basic.priceId,
        amount: this.PLANS.basic.amount
      },
      pro: {
        priceId: this.PLANS.pro.priceId,
        amount: this.PLANS.pro.amount
      },
      default: this.PRICE_ID
    });
  }

  // Find a Stripe customer by email
  async findCustomerByEmail(email) {
    try {
      if (!email) {
        console.log('No email provided to find customer');
        return null;
      }

      console.log(`Searching for Stripe customer with email: ${email}`);

      const customers = await stripe.customers.list({
        email: email,
        limit: 1
      });

      if (customers.data.length > 0) {
        console.log(`Found Stripe customer with ID: ${customers.data[0].id}`);
        return customers.data[0];
      }

      console.log(`No Stripe customer found with email: ${email}`);
      return null;
    } catch (error) {
      console.error('Error finding Stripe customer by email:', error);
      return null;
    }
  }

  // Find active subscriptions for a customer
  async findActiveSubscriptions(customerId) {
    try {
      if (!customerId) {
        console.log('No customer ID provided to find subscriptions');
        return [];
      }

      console.log(`Searching for active subscriptions for customer: ${customerId}`);

      const subscriptions = await stripe.subscriptions.list({
        customer: customerId,
        status: 'active',
        limit: 10
      });

      console.log(`Found ${subscriptions.data.length} active subscriptions for customer ${customerId}`);
      return subscriptions.data;
    } catch (error) {
      console.error('Error finding active subscriptions:', error);
      return [];
    }
  }

  // Create a Stripe customer for a user
  async createCustomer(user) {
    try {
      // Always use the actual user information from authentication
      // with fallbacks only if the data is missing
      const email = user.email || '<EMAIL>';
      const name = user.name || 'Unknown User';

      console.log(`Creating Stripe customer for user:`, {
        id: user.id,
        email: email,
        name: name
      });

      const customer = await stripe.customers.create({
        email: email,
        name: name,
        metadata: {
          userId: user.id
        }
      });

      console.log(`Stripe customer created successfully:`, {
        customerId: customer.id,
        email: customer.email,
        name: customer.name
      });

      return customer;
    } catch (error) {
      console.error('Error creating Stripe customer:', error);
      throw new Error(`Failed to create customer: ${error.message}`);
    }
  }

  // Create a checkout session for subscription
  async createCheckoutSession(userId, successUrl, cancelUrl, planType = 'pro') {
    try {
      console.log(`Creating checkout session for user ${userId} with plan: ${planType}`);
      console.log(`Success URL: ${successUrl}`);
      console.log(`Cancel URL: ${cancelUrl}`);

      const user = authService.getUserById(userId);

      if (!user) {
        console.error(`User not found with ID: ${userId}`);
        throw new Error('User not found');
      }

      console.log(`Found user:`, user);

      // Always check for existing customer in Stripe by email first
      let customerId = null;
      let stripeCustomer = null;

      if (user.email) {
        console.log(`Checking for existing Stripe customer with email: ${user.email}`);
        stripeCustomer = await this.findCustomerByEmail(user.email);

        if (stripeCustomer) {
          console.log(`Found existing Stripe customer with ID: ${stripeCustomer.id} for email: ${user.email}`);
          customerId = stripeCustomer.id;

          // Update user with the found Stripe customer ID
          user.stripeCustomerId = customerId;
          authService.saveUsersToFile();
        }
      }

      // If no customer found by email, create a new one
      if (!customerId) {
        console.log(`No existing Stripe customer found, creating new customer for user ${userId}`);
        stripeCustomer = await this.createCustomer(user);
        customerId = stripeCustomer.id;

        console.log(`Created new Stripe customer with ID: ${customerId}`);

        // Update user with Stripe customer ID
        user.stripeCustomerId = customerId;
        authService.saveUsersToFile();
      } else {
        console.log(`Using existing Stripe customer with ID: ${customerId}`);
      }

      // Make sure to save the user data with the updated Stripe customer ID
      authService.saveUsersToFile();

      // Check for any existing subscriptions for this customer
      try {
        console.log(`Checking for existing subscriptions for customer ${customerId}`);
        const subscriptions = await stripe.subscriptions.list({
          customer: customerId,
          limit: 10
        });

        // If there are any subscriptions, check their status
        if (subscriptions.data.length > 0) {
          console.log(`Found ${subscriptions.data.length} subscriptions for customer ${customerId}`);

          // Check if any of them are active
          const activeSubscription = subscriptions.data.find(sub => sub.status === 'active');
          if (activeSubscription) {
            console.log(`Customer ${customerId} has an active subscription ${activeSubscription.id}`);
            user.subscriptionId = activeSubscription.id;
            user.subscriptionStatus = 'active';
            user.subscriptionUpdatedAt = new Date().toISOString();
            authService.saveUsersToFile();

            // Return the active subscription details
            return {
              url: successUrl,
              alreadySubscribed: true,
              message: 'User already has an active subscription'
            };
          }

          // If there are canceled subscriptions, we can proceed with creating a new one
          console.log(`No active subscriptions found for customer ${customerId}, proceeding with checkout`);
        } else {
          console.log(`No subscriptions found for customer ${customerId}, proceeding with checkout`);
        }
      } catch (error) {
        console.error(`Error checking existing subscriptions:`, error);
        // Continue with creating a new checkout session
      }

      // Determine which plan to use
      const plan = planType === 'basic' ? this.PLANS.basic : this.PLANS.pro;
      const priceId = plan.priceId;

      if (!priceId) {
        console.error(`No price ID found for plan type: ${planType}`);
        throw new Error(`Invalid plan type: ${planType}`);
      }

      // Create checkout session
      console.log(`Creating Stripe checkout session with:`, {
        customer: customerId,
        mode: 'subscription',
        plan: planType,
        priceId: priceId,
        amount: plan.amount,
        currency: this.CURRENCY,
        productName: this.PRODUCT_NAME
      });

      const session = await stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        line_items: [
          {
            price: priceId,
            quantity: 1
          }
        ],
        mode: 'subscription',
        success_url: successUrl,
        cancel_url: cancelUrl,
        customer: customerId,
        client_reference_id: userId,
        metadata: {
          planType: planType
        }
      });

      console.log(`Checkout session created successfully:`, {
        sessionId: session.id,
        url: session.url,
        planType: planType
      });

      // Store the checkout session ID and plan type for reference
      user.lastCheckoutSessionId = session.id;
      user.selectedPlanType = planType;
      authService.saveUsersToFile();

      return session;
    } catch (error) {
      console.error('Error creating checkout session:', error);
      throw new Error(`Failed to create checkout session: ${error.message}`);
    }
  }

  // Handle webhook events from Stripe
  async handleWebhookEvent(event) {
    try {
      console.log(`Received Stripe webhook event: ${event.type}`);

      switch (event.type) {
        case 'checkout.session.completed':
          return await this.handleCheckoutCompleted(event.data.object);

        case 'customer.subscription.created':
          return await this.handleSubscriptionCreated(event.data.object);

        case 'customer.subscription.updated':
          return await this.handleSubscriptionUpdated(event.data.object);

        case 'customer.subscription.deleted':
          return await this.handleSubscriptionDeleted(event.data.object);

        case 'customer.subscription.paused':
          return await this.handleSubscriptionPaused(event.data.object);

        case 'customer.subscription.resumed':
          return await this.handleSubscriptionResumed(event.data.object);

        case 'customer.subscription.trial_will_end':
          return await this.handleSubscriptionTrialWillEnd(event.data.object);

        case 'invoice.payment_succeeded':
          return await this.handleInvoicePaymentSucceeded(event.data.object);

        case 'invoice.payment_failed':
          return await this.handleInvoicePaymentFailed(event.data.object);

        default:
          console.log(`Unhandled event type: ${event.type}`);
          return { status: 'ignored', event: event.type };
      }
    } catch (error) {
      console.error('Error handling webhook event:', error);
      throw new Error('Failed to process webhook event');
    }
  }

  // Handle checkout.session.completed event
  async handleCheckoutCompleted(session) {
    const userId = session.client_reference_id;
    const subscriptionId = session.subscription;
    const customerId = session.customer;

    console.log(`Handling checkout completed for user ${userId}:`, {
      subscriptionId,
      customerId
    });

    // Get the user
    const user = authService.getUserById(userId);

    if (!user) {
      console.error(`User not found with ID: ${userId}`);
      return {
        status: 'failed',
        reason: 'user_not_found',
        userId
      };
    }

    // Update user's Stripe customer ID if it's different
    if (customerId && user.stripeCustomerId !== customerId) {
      console.log(`Updating Stripe customer ID for user ${userId} from ${user.stripeCustomerId || 'none'} to ${customerId}`);
      user.stripeCustomerId = customerId;
      authService.saveUsersToFile();
    }

    // Update user subscription status
    const updated = authService.updateSubscription(userId, 'active', subscriptionId);

    return {
      status: updated ? 'success' : 'failed',
      userId,
      subscriptionId,
      customerId
    };
  }

  // Handle customer.subscription.updated event
  async handleSubscriptionUpdated(subscription) {
    const customerId = subscription.customer;
    const subscriptionId = subscription.id;

    console.log(`Handling subscription updated:`, {
      subscriptionId,
      customerId,
      status: subscription.status
    });

    // First try to find user with this subscription ID
    const users = Object.values(authService.users);
    let user = users.find(u => u.subscriptionId === subscriptionId);

    // If no user found by subscription ID, try to find by customer ID
    if (!user && customerId) {
      user = users.find(u => u.stripeCustomerId === customerId);

      // If found by customer ID but subscription ID doesn't match, update it
      if (user && user.subscriptionId !== subscriptionId) {
        console.log(`Found user ${user.id} by customer ID ${customerId}, updating subscription ID to ${subscriptionId}`);
        user.subscriptionId = subscriptionId;
        authService.saveUsersToFile();
      }
    }

    // If still no user found, try to find by email using Stripe customer
    if (!user && customerId) {
      try {
        const customer = await stripe.customers.retrieve(customerId);
        if (customer && customer.email) {
          user = users.find(u => u.email === customer.email);

          if (user) {
            console.log(`Found user ${user.id} by email ${customer.email}, updating customer ID to ${customerId}`);
            user.stripeCustomerId = customerId;

            if (user.subscriptionId !== subscriptionId) {
              console.log(`Updating subscription ID to ${subscriptionId}`);
              user.subscriptionId = subscriptionId;
            }

            authService.saveUsersToFile();
          }
        }
      } catch (error) {
        console.error(`Error retrieving customer ${customerId}:`, error);
      }
    }

    if (!user) {
      console.log(`No user found for subscription ${subscriptionId} with customer ${customerId}`);
      return { status: 'ignored', reason: 'user_not_found' };
    }

    // Update subscription status
    const status = subscription.status === 'active' ? 'active' : 'inactive';
    const updated = authService.updateSubscription(user.id, status, subscription.id);

    return {
      status: updated ? 'success' : 'failed',
      userId: user.id,
      subscriptionStatus: status,
      customerId
    };
  }

  // Helper method to find user by subscription or customer
  async findUserBySubscriptionOrCustomer(subscriptionId, customerId) {
    // First try to find user with this subscription ID
    const users = Object.values(authService.users);
    let user = users.find(u => u.subscriptionId === subscriptionId);

    // If no user found by subscription ID, try to find by customer ID
    if (!user && customerId) {
      user = users.find(u => u.stripeCustomerId === customerId);
    }

    // If still no user found, try to find by email using Stripe customer
    if (!user && customerId) {
      try {
        const customer = await stripe.customers.retrieve(customerId);
        if (customer && customer.email) {
          user = users.find(u => u.email === customer.email);

          if (user) {
            console.log(`Found user ${user.id} by email ${customer.email}, updating customer ID to ${customerId}`);
            user.stripeCustomerId = customerId;
            authService.saveUsersToFile();
          }
        }
      } catch (error) {
        console.error(`Error retrieving customer ${customerId}:`, error);
      }
    }

    return user;
  }

  // Handle customer.subscription.created event
  async handleSubscriptionCreated(subscription) {
    const customerId = subscription.customer;
    const subscriptionId = subscription.id;
    const status = subscription.status;

    console.log(`Handling subscription created:`, {
      subscriptionId,
      customerId,
      status
    });

    const user = await this.findUserBySubscriptionOrCustomer(subscriptionId, customerId);

    if (!user) {
      console.log(`No user found for subscription ${subscriptionId} with customer ${customerId}`);
      return { status: 'ignored', reason: 'user_not_found' };
    }

    // Update user subscription status
    const subscriptionStatus = status === 'active' ? 'active' : 'inactive';
    console.log(`Updating subscription status for user ${user.id} to ${subscriptionStatus}`);
    const updated = authService.updateSubscription(user.id, subscriptionStatus, subscriptionId);

    return {
      status: updated ? 'success' : 'failed',
      userId: user.id,
      subscriptionId,
      customerId,
      subscriptionStatus
    };
  }

  // Handle customer.subscription.deleted event
  async handleSubscriptionDeleted(subscription) {
    const customerId = subscription.customer;
    const subscriptionId = subscription.id;

    console.log(`Handling subscription deleted:`, {
      subscriptionId,
      customerId
    });

    const user = await this.findUserBySubscriptionOrCustomer(subscriptionId, customerId);

    if (!user) {
      console.log(`No user found for subscription ${subscriptionId} with customer ${customerId}`);
      return { status: 'ignored', reason: 'user_not_found' };
    }

    // Update subscription status
    const updated = authService.updateSubscription(user.id, 'canceled', null);

    return {
      status: updated ? 'success' : 'failed',
      userId: user.id,
      subscriptionStatus: 'canceled',
      customerId
    };
  }

  // Handle customer.subscription.paused event
  async handleSubscriptionPaused(subscription) {
    const customerId = subscription.customer;
    const subscriptionId = subscription.id;

    console.log(`Handling subscription paused:`, {
      subscriptionId,
      customerId
    });

    const user = await this.findUserBySubscriptionOrCustomer(subscriptionId, customerId);

    if (!user) {
      console.log(`No user found for subscription ${subscriptionId} with customer ${customerId}`);
      return { status: 'ignored', reason: 'user_not_found' };
    }

    // Update user subscription status
    console.log(`Updating subscription status for user ${user.id} to paused`);
    // We'll use 'inactive' for paused subscriptions in our system
    const updated = authService.updateSubscription(user.id, 'inactive', subscriptionId);

    return {
      status: updated ? 'success' : 'failed',
      userId: user.id,
      subscriptionId,
      customerId
    };
  }

  // Handle customer.subscription.resumed event
  async handleSubscriptionResumed(subscription) {
    const customerId = subscription.customer;
    const subscriptionId = subscription.id;

    console.log(`Handling subscription resumed:`, {
      subscriptionId,
      customerId
    });

    const user = await this.findUserBySubscriptionOrCustomer(subscriptionId, customerId);

    if (!user) {
      console.log(`No user found for subscription ${subscriptionId} with customer ${customerId}`);
      return { status: 'ignored', reason: 'user_not_found' };
    }

    // Update user subscription status
    console.log(`Updating subscription status for user ${user.id} to active`);
    const updated = authService.updateSubscription(user.id, 'active', subscriptionId);

    return {
      status: updated ? 'success' : 'failed',
      userId: user.id,
      subscriptionId,
      customerId
    };
  }

  // Handle customer.subscription.trial_will_end event
  async handleSubscriptionTrialWillEnd(subscription) {
    const customerId = subscription.customer;
    const subscriptionId = subscription.id;
    const trialEnd = subscription.trial_end;

    console.log(`Handling subscription trial will end:`, {
      subscriptionId,
      customerId,
      trialEnd: new Date(trialEnd * 1000).toISOString()
    });

    const user = await this.findUserBySubscriptionOrCustomer(subscriptionId, customerId);

    if (!user) {
      console.log(`No user found for subscription ${subscriptionId} with customer ${customerId}`);
      return { status: 'ignored', reason: 'user_not_found' };
    }

    // We could send an email notification here
    // For now, just log it
    console.log(`Trial ending soon for user ${user.id}, subscription ${subscriptionId}`);

    return {
      status: 'success',
      userId: user.id,
      subscriptionId,
      customerId,
      trialEnd: new Date(trialEnd * 1000).toISOString()
    };
  }

  // Handle invoice.payment_succeeded event
  async handleInvoicePaymentSucceeded(invoice) {
    const customerId = invoice.customer;
    const subscriptionId = invoice.subscription;

    if (!subscriptionId) {
      console.log(`Invoice ${invoice.id} is not for a subscription`);
      return { status: 'ignored', reason: 'not_subscription_invoice' };
    }

    console.log(`Handling invoice payment succeeded:`, {
      invoiceId: invoice.id,
      customerId,
      subscriptionId,
      amount: invoice.amount_paid
    });

    const user = await this.findUserBySubscriptionOrCustomer(subscriptionId, customerId);

    if (!user) {
      console.log(`No user found for subscription ${subscriptionId} with customer ${customerId}`);
      return { status: 'ignored', reason: 'user_not_found' };
    }

    // Update user subscription status to active
    console.log(`Updating subscription status for user ${user.id} to active`);
    const updated = authService.updateSubscription(user.id, 'active', subscriptionId);

    return {
      status: updated ? 'success' : 'failed',
      userId: user.id,
      subscriptionId,
      customerId,
      invoiceId: invoice.id
    };
  }

  // Handle invoice.payment_failed event
  async handleInvoicePaymentFailed(invoice) {
    const customerId = invoice.customer;
    const subscriptionId = invoice.subscription;

    if (!subscriptionId) {
      console.log(`Invoice ${invoice.id} is not for a subscription`);
      return { status: 'ignored', reason: 'not_subscription_invoice' };
    }

    console.log(`Handling invoice payment failed:`, {
      invoiceId: invoice.id,
      customerId,
      subscriptionId,
      attemptCount: invoice.attempt_count
    });

    const user = await this.findUserBySubscriptionOrCustomer(subscriptionId, customerId);

    if (!user) {
      console.log(`No user found for subscription ${subscriptionId} with customer ${customerId}`);
      return { status: 'ignored', reason: 'user_not_found' };
    }

    // We don't immediately mark the subscription as inactive because Stripe will retry the payment
    // But we could send an email notification here
    console.log(`Payment failed for user ${user.id}, subscription ${subscriptionId}`);

    // If this is the final attempt, mark the subscription as inactive
    if (invoice.attempt_count >= 3) {
      console.log(`Final payment attempt failed for user ${user.id}, marking subscription as inactive`);
      const updated = authService.updateSubscription(user.id, 'inactive', subscriptionId);

      return {
        status: updated ? 'success' : 'failed',
        userId: user.id,
        subscriptionId,
        customerId,
        invoiceId: invoice.id,
        action: 'marked_inactive'
      };
    }

    return {
      status: 'success',
      userId: user.id,
      subscriptionId,
      customerId,
      invoiceId: invoice.id,
      attemptCount: invoice.attempt_count,
      action: 'notification_only'
    };
  }

  // Cancel a subscription in Stripe
  async cancelSubscription(subscriptionId) {
    try {
      if (!subscriptionId) {
        console.error('No subscription ID provided to cancel');
        throw new Error('Subscription ID is required');
      }

      console.log(`Canceling Stripe subscription: ${subscriptionId}`);

      // Cancel the subscription at period end to avoid prorated refunds
      const subscription = await stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: true
      });

      console.log(`Subscription ${subscriptionId} will be canceled at period end`);

      // For immediate cancellation, uncomment this:
      // const subscription = await stripe.subscriptions.cancel(subscriptionId);
      // console.log(`Subscription ${subscriptionId} canceled immediately`);

      return {
        success: true,
        status: subscription.status,
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        currentPeriodEnd: new Date(subscription.current_period_end * 1000).toISOString()
      };
    } catch (error) {
      console.error(`Error canceling subscription ${subscriptionId}:`, error);
      throw error;
    }
  }

  // Check if a user has an active subscription
  async checkSubscription(userId) {
    try {
      const user = authService.getUserById(userId);

      if (!user) {
        console.error(`User not found with ID: ${userId}`);
        return false;
      }

      // First check: If user has a subscription ID, check that subscription in Stripe
      if (user.subscriptionId) {
        console.log(`Checking Stripe subscription status for user ${userId} with subscription ID ${user.subscriptionId}`);

        try {
          const subscription = await stripe.subscriptions.retrieve(user.subscriptionId);

          // Update local subscription status if it's different
          const stripeStatus = subscription.status === 'active' ? 'active' : 'inactive';
          if (user.subscriptionStatus !== stripeStatus) {
            console.log(`Updating subscription status for user ${userId} from ${user.subscriptionStatus} to ${stripeStatus}`);
            authService.updateSubscription(userId, stripeStatus, user.subscriptionId);
          }

          return subscription.status === 'active';
        } catch (stripeError) {
          console.error(`Error retrieving subscription from Stripe:`, stripeError);
          // Continue to next check if subscription not found
        }
      }

      // Second check: If user has a Stripe customer ID, check for active subscriptions
      if (user.stripeCustomerId) {
        console.log(`Checking for active subscriptions for customer ${user.stripeCustomerId}`);

        try {
          const subscriptions = await this.findActiveSubscriptions(user.stripeCustomerId);

          if (subscriptions.length > 0) {
            // Use the first active subscription
            const subscription = subscriptions[0];
            console.log(`Found active subscription ${subscription.id} for customer ${user.stripeCustomerId}`);

            // Update user with subscription ID and status
            authService.updateSubscription(userId, 'active', subscription.id);
            return true;
          }
        } catch (stripeError) {
          console.error(`Error checking subscriptions for customer:`, stripeError);
          // Continue to next check
        }
      }

      // Third check: Try to find customer by email
      if (user.email) {
        console.log(`Checking for Stripe customer by email: ${user.email}`);

        try {
          const customer = await this.findCustomerByEmail(user.email);

          if (customer) {
            // Update user with Stripe customer ID
            user.stripeCustomerId = customer.id;
            authService.saveUsersToFile();

            // Check for active subscriptions
            const subscriptions = await this.findActiveSubscriptions(customer.id);

            if (subscriptions.length > 0) {
              // Use the first active subscription
              const subscription = subscriptions[0];
              console.log(`Found active subscription ${subscription.id} for customer ${customer.id}`);

              // Update user with subscription ID and status
              authService.updateSubscription(userId, 'active', subscription.id);
              return true;
            }
          }
        } catch (stripeError) {
          console.error(`Error checking customer by email:`, stripeError);
          // Fall back to local status
        }
      }

      // If all checks fail, fall back to local status
      console.log(`No active subscription found in Stripe for user ${userId}`);
      console.log(`Falling back to local subscription status: ${user.subscriptionStatus}`);
      return authService.hasActiveSubscription(userId);
    } catch (error) {
      console.error('Error checking subscription status:', error);
      return false;
    }
  }

  // Get subscription details from Stripe
  async getSubscriptionDetails(subscriptionId) {
    try {
      console.log(`Getting subscription details for subscription ${subscriptionId}`);

      if (!subscriptionId) {
        console.log('No subscription ID provided');
        return null;
      }

      // Retrieve the subscription from Stripe
      const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
        expand: ['items.data.price']
      });

      console.log(`Retrieved subscription details for ${subscriptionId}:`, {
        status: subscription.status,
        priceId: subscription.items.data[0]?.price?.id || 'unknown'
      });

      return subscription;
    } catch (error) {
      console.error(`Error getting subscription details for ${subscriptionId}:`, error);
      return null;
    }
  }
}

module.exports = new SubscriptionService();
