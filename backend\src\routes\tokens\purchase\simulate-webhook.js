const express = require('express');
const router = express.Router();
const tokenPurchaseService = require('../../../services/tokenPurchaseService');
const authService = require('../../../services/authService');

// Generate a unique ID for this webhook request
const generateWebhookId = () => `simulate-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;

// Simulate a webhook event for token purchase
router.post('/', async (req, res) => {
  const webhookId = generateWebhookId();

  try {
    console.log(`[${webhookId}] Simulating webhook for token purchase`);

    // Get the user ID from the request
    const userId = req.headers['x-user-id'] || req.body.userId;

    if (!userId) {
      console.error(`[${webhookId}] Missing user ID in request`);
      return res.status(400).json({
        error: 'Missing user ID',
        webhookId
      });
    }

    // Get the package ID from the request
    const packageId = req.body.packageId;

    if (!packageId) {
      console.error(`[${webhookId}] Missing package ID in request`);
      return res.status(400).json({
        error: 'Missing package ID',
        webhookId
      });
    }

    console.log(`[${webhookId}] Processing token purchase for user ${userId}, package ${packageId}`);

    // Get the token amount for the package
    let tokens = 0;

    switch (packageId) {
      case 'small':
        tokens = 100000;
        break;
      case 'medium':
        tokens = 500000;
        break;
      case 'large':
        tokens = 1000000;
        break;
      default:
        tokens = 100000;
    }

    // First, get the current token balance
    try {
      const currentBalance = await tokenPurchaseService.getUserTokenBalance(userId);
      console.log(`[${webhookId}] Current token balance for user ${userId}: ${currentBalance?.balance || 0}`);

      console.log(`[${webhookId}] Adding ${tokens} tokens to user ${userId}`);

      // Generate a mock transaction ID
      const transactionId = `simulate-${webhookId}`;

      // Add tokens to user's account
      const result = await tokenPurchaseService.addTokensToUser(userId, tokens, transactionId);

      console.log(`[${webhookId}] Token purchase processed successfully:`, result);

      // Get the updated token balance
      const updatedBalance = await tokenPurchaseService.getUserTokenBalance(userId);
      console.log(`[${webhookId}] Updated token balance for user ${userId}: ${updatedBalance?.balance || 0}`);

      // Return success response
      res.json({
        success: true,
        message: 'Simulated webhook processed successfully',
        result,
        previousBalance: currentBalance?.balance || 0,
        newBalance: updatedBalance?.balance || 0,
        tokensAdded: tokens,
        webhookId
      });
    } catch (tokenError) {
      console.error(`[${webhookId}] Error adding tokens to user:`, tokenError);

      // Check if this transaction has already been processed
      if (tokenError.message && tokenError.message.includes('already processed')) {
        console.log(`[${webhookId}] Transaction already processed, returning success response`);

        // Get the current token balance
        const balance = await tokenPurchaseService.getUserTokenBalance(userId);

        return res.json({
          success: true,
          message: 'Token purchase already processed',
          balance: balance.balance,
          alreadyProcessed: true,
          webhookId
        });
      }

      throw tokenError; // Re-throw to be caught by the outer catch block
    }
  } catch (error) {
    console.error(`[${webhookId}] Error processing simulated webhook:`, error);
    res.status(500).json({
      error: error.message || 'Failed to process simulated webhook',
      details: error.stack,
      webhookId
    });
  }
});

module.exports = router;
