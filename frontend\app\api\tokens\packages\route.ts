import { NextRequest, NextResponse } from 'next/server';
import { apiRequest } from '../../../../lib/api-client';

// Completely bypass Supabase during build time
const isBuildTime = process.env.NODE_ENV === 'production' && typeof window === 'undefined';
let supabase: any = null;

// Only import and initialize Supabase if we're not in build time
if (!isBuildTime) {
  try {
    // Dynamic import to prevent build-time evaluation
    const { createClient } = require('@supabase/supabase-js');
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

    if (supabaseUrl && supabaseAnonKey) {
      supabase = createClient(supabaseUrl, supabaseAnonKey);
      console.log('[Supabase] Client initialized successfully');
    } else {
      console.log('[Supabase] Missing URL or key, client not initialized');
    }
  } catch (error) {
    console.error('[Supabase] Error initializing client:', error);
  }
}

export async function GET(req: NextRequest) {
  // Default token packages
  const defaultPackages = {
    small: { tokens: 100000, price: 5 },
    medium: { tokens: 500000, price: 20 },
    large: { tokens: 1000000, price: 35 }
  };

  try {
    console.log('[API] Token packages endpoint called');

    // During build time or if Supabase is not initialized, return default packages
    if (isBuildTime || !supabase) {
      console.log('[API] Build-time environment or Supabase not initialized, returning default packages');
      return NextResponse.json(defaultPackages, { status: 200 });
    }

    // Get the authorization header
    const authHeader = req.headers.get('authorization');

    if (!authHeader) {
      console.log('[API] No authorization header provided');
      return NextResponse.json(defaultPackages, { status: 200 });
    }

    // Extract the token
    const token = authHeader.replace('Bearer ', '');

    try {
      // Get user from token
      const { data: { user }, error: userError } = await supabase.auth.getUser(token);

      if (userError || !user) {
        console.log('[API] Error getting user from token:', userError?.message);
        return NextResponse.json(defaultPackages, { status: 200 });
      }

      console.log(`[API] User found: ${user.id}`);

      try {
        // Forward the request to the backend
        const backendResponse = await apiRequest('/api/tokens/packages', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'x-user-id': user.id
          }
        }, token, user.id);

        console.log('[API] Backend response received:', backendResponse);

        // Return the backend response
        return NextResponse.json(backendResponse);
      } catch (backendError: any) {
        console.error('[API] Error from backend:', backendError.message);

        // Return a default response if the backend is unavailable
        return NextResponse.json(defaultPackages);
      }
    } catch (supabaseError: any) {
      console.error('[API] Supabase error:', supabaseError.message);
      return NextResponse.json(defaultPackages, { status: 200 });
    }
  } catch (error: any) {
    console.error('[API] Token packages error:', error.message);

    // Return a default response in case of error
    return NextResponse.json(defaultPackages);
  }
}
