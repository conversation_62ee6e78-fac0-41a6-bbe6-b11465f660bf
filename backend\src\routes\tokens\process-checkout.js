const express = require('express');
const router = express.Router();
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const tokenPurchaseService = require('../../services/tokenPurchaseService');
const authService = require('../../services/authService');
const { authenticateJWTWithFallback } = require('../../middleware/auth');

// Generate a unique ID for this request
const generateRequestId = () => `process-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;

/**
 * Process a checkout session manually
 * This is used when the webhook fails to process the checkout session
 * POST /api/tokens/process-checkout
 * Body: { userId: 'string', sessionId: 'string' }
 */
router.post('/', authenticateJWTWithFallback, async (req, res) => {
  const requestId = generateRequestId();

  try {
    console.log(`[${requestId}] Processing checkout session manually`);

    // Get the user ID from the request
    const userId = req.headers['x-user-id'] || req.body.userId || req.user?.id;

    if (!userId) {
      console.error(`[${requestId}] Missing user ID in request`);
      return res.status(400).json({
        error: 'Missing user ID',
        requestId
      });
    }

    // Get the session ID from the request
    const sessionId = req.body.sessionId;

    if (!sessionId) {
      console.error(`[${requestId}] Missing session ID in request`);
      return res.status(400).json({
        error: 'Missing session ID',
        requestId
      });
    }

    console.log(`[${requestId}] Processing checkout session ${sessionId} for user ${userId}`);

    // Retrieve the checkout session from Stripe
    try {
      const session = await stripe.checkout.sessions.retrieve(sessionId);

      if (!session) {
        console.error(`[${requestId}] Session not found: ${sessionId}`);
        return res.status(404).json({
          error: 'Checkout session not found',
          requestId
        });
      }

      console.log(`[${requestId}] Retrieved session from Stripe:`, {
        id: session.id,
        status: session.status,
        mode: session.mode,
        metadata: session.metadata
      });

      // Check if this is a token purchase
      if (session.metadata && session.metadata.type === 'token_purchase') {
        console.log(`[${requestId}] Processing token purchase for session ${sessionId}`);

        // Get the token purchase details from metadata
        const sessionUserId = session.metadata.userId;
        const tokens = parseInt(session.metadata.tokens, 10);
        const packageId = session.metadata.packageId;
        const transactionId = session.payment_intent;

        // Verify that the user ID in the session matches the requested user ID
        if (sessionUserId && sessionUserId !== userId) {
          console.error(`[${requestId}] User ID mismatch: ${sessionUserId} (session) vs ${userId} (request)`);
          return res.status(403).json({
            error: 'User ID mismatch',
            requestId
          });
        }

        if (!tokens) {
          console.error(`[${requestId}] Missing tokens in session metadata`);
          return res.status(400).json({
            error: 'Missing tokens in session metadata',
            requestId
          });
        }

        console.log(`[${requestId}] Adding ${tokens} tokens to user ${userId}`);

        try {
          // Add tokens to user's account
          const result = await tokenPurchaseService.addTokensToUser(userId, tokens, transactionId);

          console.log(`[${requestId}] Token purchase processed successfully:`, result);

          // Return success response
          return res.json({
            success: true,
            message: `Successfully processed checkout session ${sessionId}`,
            tokens: tokens,
            balance: result.balance,
            transaction: result.transaction,
            requestId
          });
        } catch (tokenError) {
          // Check if this is a duplicate transaction
          if (tokenError.message && tokenError.message.includes('already processed')) {
            console.log(`[${requestId}] Transaction already processed, returning success`);

            // Get the current token balance
            const balance = await tokenPurchaseService.getUserTokenBalance(userId);

            return res.json({
              success: true,
              message: 'Transaction already processed',
              tokens: tokens,
              balance: balance.balance,
              alreadyProcessed: true,
              requestId
            });
          }

          console.error(`[${requestId}] Error adding tokens to user:`, tokenError);
          return res.status(500).json({
            error: tokenError.message || 'Failed to add tokens to user',
            operationId: tokenError.operationId,
            requestId
          });
        }
      } else {
        console.error(`[${requestId}] Not a token purchase session: ${sessionId}`);
        return res.status(400).json({
          error: 'Not a token purchase session',
          requestId
        });
      }
    } catch (stripeError) {
      console.error(`[${requestId}] Error retrieving session from Stripe:`, stripeError);

      // If we can't retrieve the session from Stripe, try to use the simulate-webhook endpoint
      console.log(`[${requestId}] Falling back to simulate-webhook`);

      try {
        // Determine the package ID based on the session ID
        // This is a fallback mechanism, so we'll just use 'small' as the default
        const packageId = 'small';

        // Import the simulate-webhook router
        const simulateWebhookRouter = require('./purchase/simulate-webhook');

        // Call the simulate-webhook endpoint directly
        const simulateResult = await fetch(`${process.env.BACKEND_URL || 'http://localhost:5001'}/api/tokens/purchase/simulate-webhook`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-user-id': userId
          },
          body: JSON.stringify({
            userId: userId,
            packageId: packageId
          })
        }).then(res => res.json());

        console.log(`[${requestId}] Simulate webhook response:`, simulateResult);

        // Return the simulate webhook response
        return res.json({
          ...simulateResult,
          note: 'Used simulate-webhook as fallback',
          requestId
        });
      } catch (simulateError) {
        console.error(`[${requestId}] Error with simulate-webhook fallback:`, simulateError);

        // Try one more approach - add tokens directly
        try {
          console.log(`[${requestId}] Trying direct token addition as final fallback`);

          // Determine token amount based on package ID
          let tokenAmount = 100000; // Default to small package (100,000 tokens)

          // Create a transaction ID for this purchase
          const transactionId = `direct-${requestId}`;

          // Add tokens to the user's account
          const result = await tokenPurchaseService.addTokensToUser(userId, tokenAmount, transactionId);

          console.log(`[${requestId}] Direct token addition successful:`, result);

          // Return success response
          return res.json({
            success: true,
            message: 'Direct token addition successful',
            tokens: tokenAmount,
            balance: result.balance,
            transaction: result.transaction,
            note: 'Used direct token addition as final fallback',
            requestId
          });
        } catch (directError) {
          console.error(`[${requestId}] All fallback mechanisms failed:`, directError);

          // Return error response
          return res.status(500).json({
            error: 'All fallback mechanisms failed',
            details: directError.message,
            stripeError: stripeError.message,
            requestId
          });
        }
      }
    }
  } catch (error) {
    console.error(`[${requestId}] Error processing checkout session:`, error);
    return res.status(500).json({
      error: 'Failed to process checkout session',
      details: error.message,
      requestId
    });
  }
});

module.exports = router;
