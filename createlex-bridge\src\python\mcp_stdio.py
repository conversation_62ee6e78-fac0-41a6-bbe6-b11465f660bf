#!/usr/bin/env python3
"""
stdio wrapper for MCP server - allows <PERSON> to communicate with HTTP MCP server
"""

import json
import sys
import requests
import os

# MCP server HTTP endpoint - FastMCP streamable-http transport
MCP_SERVER_URL = "http://localhost:8000/mcp/"

# Global session ID storage
session_id = None
stateless_mode = False

def parse_sse_response(response_text):
    """Parse Server-Sent Events format to extract JSON data"""
    lines = response_text.strip().split('\n')
    for line in lines:
        if line.startswith('data: '):
            json_data = line[6:]  # Remove 'data: ' prefix
            try:
                return json.loads(json_data)
            except json.JSONDecodeError:
                continue
    return None

def send_mcp_request(method, params=None, request_id=1):
    """Send a request to the FastMCP SSE server"""
    global session_id, stateless_mode
    
    payload = {
        "jsonrpc": "2.0",
        "method": method,
        "id": request_id
    }
    if params:
        payload["params"] = params
    
    # Headers for streamable-http transport
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json, text/event-stream"
    }
    
    # Add session ID to headers for non-initialize requests (if we have one and not in stateless mode)
    if session_id and method != "initialize" and not stateless_mode:
        headers["X-Session-ID"] = session_id
        print(f"DEBUG: Using session ID: {session_id}", file=sys.stderr)
    elif method != "initialize" and not stateless_mode:
        print(f"DEBUG: No session ID available for method: {method}", file=sys.stderr)
    
    try:
        response = requests.post(MCP_SERVER_URL, json=payload, headers=headers, timeout=10)
        
        print(f"DEBUG: Status Code: {response.status_code}", file=sys.stderr)
        print(f"DEBUG: Response Text: {response.text[:500]}", file=sys.stderr)
        
        # Handle initialize response
        if method == "initialize":
            if response.status_code == 200:
                # Try to extract session ID from headers
                session_id = (response.headers.get('X-Session-ID') or 
                             response.headers.get('x-session-id') or
                             response.headers.get('mcp-session-id') or
                             response.headers.get('MCP-Session-ID'))
                
                if session_id:
                    print(f"DEBUG: Extracted session ID: {session_id}", file=sys.stderr)
                    stateless_mode = False
                else:
                    print("DEBUG: No session ID found - assuming stateless mode", file=sys.stderr)
                    stateless_mode = True
                    print(f"DEBUG: Response headers: {dict(response.headers)}", file=sys.stderr)
            else:
                print(f"DEBUG: Initialize failed with status {response.status_code}", file=sys.stderr)
        
        # Handle 400 errors for missing session ID by trying stateless mode
        if response.status_code == 400 and "Missing session ID" in response.text and not stateless_mode:
            print("DEBUG: Got 'Missing session ID' error, switching to stateless mode", file=sys.stderr)
            stateless_mode = True
            # Retry the request without session ID
            headers_no_session = {
                "Content-Type": "application/json",
                "Accept": "application/json, text/event-stream"
            }
            response = requests.post(MCP_SERVER_URL, json=payload, headers=headers_no_session, timeout=10)
            print(f"DEBUG: Retry Status Code: {response.status_code}", file=sys.stderr)
            print(f"DEBUG: Retry Response Text: {response.text[:500]}", file=sys.stderr)
        
        if response.status_code == 200:
            # Check if response is SSE format
            if response.headers.get('content-type') == 'text/event-stream':
                parsed_json = parse_sse_response(response.text)
                if parsed_json:
                    return parsed_json
                else:
                    return {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "error": {
                            "code": -32603,
                            "message": "Failed to parse SSE response"
                        }
                    }
            else:
                return response.json()
        else:
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "error": {
                    "code": -32603,
                    "message": f"HTTP {response.status_code}: {response.text}"
                }
            }
    except Exception as e:
        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "error": {
                "code": -32603,
                "message": f"Internal error: {str(e)}"
            }
        }

def main():
    """Main stdio loop for Claude Desktop"""
    for line in sys.stdin:
        try:
            request = json.loads(line.strip())
            method = request.get("method")
            params = request.get("params", {})
            request_id = request.get("id", 1)
            
            # Ensure request_id is never null
            if request_id is None:
                request_id = 1
            
            print(f"DEBUG: Received request: {method}", file=sys.stderr)
            
            # Handle notifications (no response expected)
            if method in ["initialized", "notifications/initialized"]:
                # Send to FastMCP server but don't send response to Claude
                send_mcp_request(method, params, request_id)
                continue
            
            # Send request to FastMCP server and get response
            response = send_mcp_request(method, params, request_id)
            
            # Send response back to Claude Desktop
            print(json.dumps(response), flush=True)
            
        except json.JSONDecodeError as e:
            error_response = {
                "jsonrpc": "2.0",
                "id": 1,
                "error": {
                    "code": -32700,
                    "message": f"Parse error: {str(e)}"
                }
            }
            print(json.dumps(error_response), flush=True)
        except Exception as e:
            error_response = {
                "jsonrpc": "2.0",
                "id": 1,
                "error": {
                    "code": -32603,
                    "message": f"Internal error: {str(e)}"
                }
            }
            print(json.dumps(error_response), flush=True)

if __name__ == "__main__":
    main() 