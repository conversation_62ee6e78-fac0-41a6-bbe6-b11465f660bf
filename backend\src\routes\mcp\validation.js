const express = require('express');
const router = express.Router();
const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Generate a secure token for MCP server validation
function generateMCPToken(userId, expiresIn = 3600) {
  const payload = {
    userId,
    type: 'mcp_validation',
    exp: Date.now() + (expiresIn * 1000),
    nonce: crypto.randomBytes(16).toString('hex')
  };
  
  const secret = process.env.MCP_SECRET || 'your-secret-key';
  const token = crypto
    .createHmac('sha256', secret)
    .update(JSON.stringify(payload))
    .digest('hex');
    
  return {
    token,
    payload: Buffer.from(JSON.stringify(payload)).toString('base64')
  };
}

// Validate MCP token
function validateMCPToken(token, payload) {
  try {
    const decoded = JSON.parse(Buffer.from(payload, 'base64').toString());
    
    // Check expiration
    if (decoded.exp < Date.now()) {
      return { valid: false, reason: 'Token expired' };
    }
    
    // Verify signature
    const secret = process.env.MCP_SECRET || 'your-secret-key';
    const expectedToken = crypto
      .createHmac('sha256', secret)
      .update(JSON.stringify(decoded))
      .digest('hex');
      
    if (token !== expectedToken) {
      return { valid: false, reason: 'Invalid token' };
    }
    
    return { valid: true, userId: decoded.userId };
  } catch (error) {
    return { valid: false, reason: 'Invalid payload' };
  }
}

// Endpoint to validate MCP server startup
router.post('/validate-startup', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ 
        valid: false, 
        error: 'No authorization token provided' 
      });
    }

    const token = authHeader.substring(7);
    
    // Decode and verify the JWT token
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      return res.status(401).json({ 
        valid: false, 
        error: 'Invalid authentication token' 
      });
    }

    // Check subscription status
    const { data: subscription, error: subError } = await supabase
      .from('subscriptions')
      .select('status, current_period_end')
      .eq('user_id', user.id)
      .single();

    if (subError || !subscription) {
      return res.status(403).json({ 
        valid: false, 
        error: 'No active subscription found' 
      });
    }

    // Check if subscription is active
    const isActive = subscription.status === 'active' || subscription.status === 'trialing';
    const isValid = isActive && new Date(subscription.current_period_end) > new Date();

    if (!isValid) {
      return res.status(403).json({ 
        valid: false, 
        error: 'Subscription expired or inactive' 
      });
    }

    // Generate MCP validation token
    const mcpAuth = generateMCPToken(user.id, 86400); // 24 hour validity

    // Log the validation
    await supabase
      .from('mcp_validations')
      .insert({
        user_id: user.id,
        validated_at: new Date().toISOString(),
        ip_address: req.ip,
        user_agent: req.headers['user-agent']
      });

    res.json({
      valid: true,
      mcpToken: mcpAuth.token,
      mcpPayload: mcpAuth.payload,
      expiresIn: 86400
    });

  } catch (error) {
    console.error('MCP validation error:', error);
    res.status(500).json({ 
      valid: false, 
      error: 'Internal server error' 
    });
  }
});

// Endpoint to validate MCP token (called periodically by MCP server)
router.post('/validate-token', async (req, res) => {
  try {
    const { token, payload } = req.body;
    
    if (!token || !payload) {
      return res.status(400).json({ 
        valid: false, 
        error: 'Missing token or payload' 
      });
    }

    const validation = validateMCPToken(token, payload);
    
    if (!validation.valid) {
      return res.status(401).json({ 
        valid: false, 
        error: validation.reason 
      });
    }

    // Optional: Check if user still has active subscription
    const { data: subscription } = await supabase
      .from('subscriptions')
      .select('status')
      .eq('user_id', validation.userId)
      .single();

    const isStillActive = subscription?.status === 'active' || subscription?.status === 'trialing';

    res.json({
      valid: isStillActive,
      userId: validation.userId
    });

  } catch (error) {
    console.error('Token validation error:', error);
    res.status(500).json({ 
      valid: false, 
      error: 'Internal server error' 
    });
  }
});

module.exports = router; 