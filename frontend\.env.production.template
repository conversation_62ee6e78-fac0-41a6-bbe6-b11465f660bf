# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Application Configuration
PORT=3000
NEXT_PUBLIC_APP_URL=https://your-production-domain.com

# API Configuration
NEXT_PUBLIC_API_URL=https://your-api-domain.com

# Authentication
# Note: Authentication and subscription checks are now controlled directly in the code
# In middleware.ts and contexts/AuthContext.tsx, set bypassAuth and bypassSubscription to false for production

# JWT Configuration
JWT_SECRET=your_production_jwt_secret
JWT_EXPIRES_IN=7d

# Subscription/Billing
STRIPE_SECRET_KEY=your_production_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=your_production_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=your_production_stripe_webhook_secret

# Other Configuration
NODE_ENV=production
