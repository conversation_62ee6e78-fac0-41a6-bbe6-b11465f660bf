'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '../../../components/ui/card';
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger
} from '../../../components/ui/tabs';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import { Skeleton } from '../../../components/ui/skeleton';
import {
  ArrowLeft,
  Database,
  Download,
  RefreshCw,
  Save,
  Settings,
  Upload
} from 'lucide-react';
import { fetchWithAuth, getApiUrl } from '../../../lib/auth-utils';
import { format } from 'date-fns';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '../../../components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '../../../components/ui/dialog';
import { toast } from 'sonner';

interface SystemSettings {
  [key: string]: string | boolean | number;
}

interface Backup {
  id: string;
  created_at: string;
  created_by: string;
  status: string;
  file_name: string;
  file_size: number;
  last_restored_at?: string;
}

export default function SettingsPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [settings, setSettings] = useState<SystemSettings>({});
  const [editedSettings, setEditedSettings] = useState<SystemSettings>({});
  const [backups, setBackups] = useState<Backup[]>([]);
  const [backupsLoading, setBackupsLoading] = useState(true);
  const [backupsError, setBackupsError] = useState<string | null>(null);
  const [creatingBackup, setCreatingBackup] = useState(false);
  const [restoringBackup, setRestoringBackup] = useState(false);
  const [selectedBackupId, setSelectedBackupId] = useState<string | null>(null);
  const [savingSettings, setSavingSettings] = useState(false);

  useEffect(() => {
    fetchSettings();
    fetchBackups();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);

      const apiUrl = getApiUrl();
      const response = await fetchWithAuth(`${apiUrl}/api/admin/settings`);

      if (!response.ok) {
        throw new Error(`Failed to fetch settings: ${response.status}`);
      }

      const data = await response.json();
      setSettings(data);
      setEditedSettings({});
    } catch (error) {
      console.error('Error fetching settings:', error);
      setError('Failed to load settings');

      // Use mock data for development
      const mockData = {
        'api_rate_limit': '100',
        'max_credit_limit': '100000',
        'default_model': 'claude-3-sonnet',
        'enable_logging': true,
        'maintenance_mode': false,
        'environment': 'development',
        'apiUrl': 'http://localhost:5001'
      };

      setSettings(mockData);
    } finally {
      setLoading(false);
    }
  };

  const fetchBackups = async () => {
    try {
      setBackupsLoading(true);

      const apiUrl = getApiUrl();
      const response = await fetchWithAuth(`${apiUrl}/api/admin/settings/backups`);

      if (!response.ok) {
        throw new Error(`Failed to fetch backups: ${response.status}`);
      }

      const data = await response.json();
      setBackups(data.backups || []);

      // If no backups returned, use mock data for development
      if (!data.backups || data.backups.length === 0) {
        const mockData = generateMockBackups();
        setBackups(mockData);
      }
    } catch (error) {
      console.error('Error fetching backups:', error);
      setBackupsError('Failed to load backups');

      // Use mock data for development
      const mockData = generateMockBackups();
      setBackups(mockData);
    } finally {
      setBackupsLoading(false);
    }
  };

  const generateMockBackups = (): Backup[] => {
    const mockBackups: Backup[] = [];

    for (let i = 0; i < 5; i++) {
      const createdAt = new Date();
      createdAt.setDate(createdAt.getDate() - i);

      mockBackups.push({
        id: `backup_${Date.now() - i * 86400000}`,
        created_at: createdAt.toISOString(),
        created_by: 'admin',
        status: 'completed',
        file_name: `backup_${format(createdAt, 'yyyy-MM-dd-HH-mm-ss')}.sql`,
        file_size: Math.floor(Math.random() * 1000000) + 500000, // Random size between 500KB and 1.5MB
        last_restored_at: i === 0 ? new Date().toISOString() : undefined
      });
    }

    return mockBackups;
  };

  const handleInputChange = (key: string, value: string | boolean | number) => {
    setEditedSettings({
      ...editedSettings,
      [key]: value
    });
  };

  const handleSaveSettings = async () => {
    try {
      setSavingSettings(true);

      const apiUrl = getApiUrl();
      const response = await fetchWithAuth(`${apiUrl}/api/admin/settings`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(editedSettings)
      });

      if (!response.ok) {
        throw new Error(`Failed to update settings: ${response.status}`);
      }

      const updatedSettings = await response.json();
      setSettings(updatedSettings);
      setEditedSettings({});

      toast.success('Settings updated successfully');
    } catch (error) {
      console.error('Error updating settings:', error);
      toast.error('Failed to update settings');
    } finally {
      setSavingSettings(false);
    }
  };

  const handleCreateBackup = async () => {
    try {
      setCreatingBackup(true);

      const apiUrl = getApiUrl();
      const response = await fetchWithAuth(`${apiUrl}/api/admin/settings/backup`, {
        method: 'POST'
      });

      if (!response.ok) {
        throw new Error(`Failed to create backup: ${response.status}`);
      }

      const data = await response.json();

      toast.success('Backup created successfully');
      fetchBackups();
    } catch (error) {
      console.error('Error creating backup:', error);
      toast.error('Failed to create backup');
    } finally {
      setCreatingBackup(false);
    }
  };

  const handleRestoreBackup = async () => {
    if (!selectedBackupId) return;

    try {
      setRestoringBackup(true);

      const apiUrl = getApiUrl();
      const response = await fetchWithAuth(`${apiUrl}/api/admin/settings/restore`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ backupId: selectedBackupId })
      });

      if (!response.ok) {
        throw new Error(`Failed to restore backup: ${response.status}`);
      }

      const data = await response.json();

      toast.success('Database restored successfully');
      fetchBackups();
      setSelectedBackupId(null);
    } catch (error) {
      console.error('Error restoring backup:', error);
      toast.error('Failed to restore backup');
    } finally {
      setRestoringBackup(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) {
      return `${bytes} B`;
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(2)} KB`;
    } else {
      return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" onClick={() => router.push('/admin')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <Skeleton className="h-8 w-64" />
        </div>

        <Skeleton className="h-96" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" onClick={() => router.push('/admin')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">System Settings</h1>
        </div>

        <Card>
          <CardContent className="p-6">
            <div className="text-red-500">{error}</div>
            <Button className="mt-4" onClick={fetchSettings}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <Button variant="outline" size="icon" onClick={() => router.push('/admin')}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-2xl font-bold">System Settings</h1>
      </div>

      <Tabs defaultValue="general">
        <TabsList>
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="billing">Billing</TabsTrigger>
          <TabsTrigger value="api">API</TabsTrigger>
          <TabsTrigger value="backup">Backup & Restore</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
              <CardDescription>
                Configure system-wide settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="default_model">Default Model</Label>
                  <Input
                    id="default_model"
                    defaultValue={settings.default_model as string}
                    onChange={(e) => handleInputChange('default_model', e.target.value)}
                  />
                  <p className="text-sm text-gray-500">
                    Default AI model to use
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="enable_logging">Enable Logging</Label>
                    <input
                      type="checkbox"
                      id="enable_logging"
                      checked={editedSettings.enable_logging !== undefined ? editedSettings.enable_logging as boolean : settings.enable_logging as boolean}
                      onChange={(e) => handleInputChange('enable_logging', e.target.checked)}
                    />
                  </div>
                  <p className="text-sm text-gray-500">
                    Enable detailed logging for debugging
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="maintenance_mode">Maintenance Mode</Label>
                    <input
                      type="checkbox"
                      id="maintenance_mode"
                      checked={editedSettings.maintenance_mode !== undefined ? editedSettings.maintenance_mode as boolean : settings.maintenance_mode as boolean}
                      onChange={(e) => handleInputChange('maintenance_mode', e.target.checked)}
                    />
                  </div>
                  <p className="text-sm text-gray-500">
                    Put the system in maintenance mode
                  </p>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={handleSaveSettings} disabled={Object.keys(editedSettings).length === 0 || savingSettings}>
                <Save className="h-4 w-4 mr-2" />
                {savingSettings ? 'Saving...' : 'Save Settings'}
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Environment Information</CardTitle>
              <CardDescription>
                Current system environment
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="font-medium">Environment:</span>
                  <span>{settings.environment}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">API URL:</span>
                  <span>{settings.apiUrl}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="billing" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Billing Settings</CardTitle>
              <CardDescription>
                Configure billing rates and settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="basic_plan_price">Basic Plan Price ($)</Label>
                    <Input
                      id="basic_plan_price"
                      type="number"
                      defaultValue="20"
                      onChange={(e) => handleInputChange('basic_plan_price', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="pro_plan_price">Pro Plan Price ($)</Label>
                    <Input
                      id="pro_plan_price"
                      type="number"
                      defaultValue="30"
                      onChange={(e) => handleInputChange('pro_plan_price', e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="token_package_price">Credit Package Price ($)</Label>
                    <Input
                      id="token_package_price"
                      type="number"
                      defaultValue="5"
                      onChange={(e) => handleInputChange('token_package_price', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="token_package_amount">Credits per Package</Label>
                    <Input
                      id="token_package_amount"
                      type="number"
                      defaultValue="100000"
                      onChange={(e) => handleInputChange('token_package_amount', e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={handleSaveSettings} disabled={Object.keys(editedSettings).length === 0 || savingSettings}>
                <Save className="h-4 w-4 mr-2" />
                {savingSettings ? 'Saving...' : 'Save Settings'}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="api" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>API Settings</CardTitle>
              <CardDescription>
                Configure API settings and rate limits
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="api_rate_limit">API Rate Limit</Label>
                  <Input
                    id="api_rate_limit"
                    type="number"
                    defaultValue={settings.api_rate_limit as string}
                    onChange={(e) => handleInputChange('api_rate_limit', e.target.value)}
                  />
                  <p className="text-sm text-gray-500">
                    Maximum number of API requests per minute
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="max_token_limit">Max Credit Limit</Label>
                  <Input
                    id="max_token_limit"
                    type="number"
                    defaultValue={settings.max_token_limit as string}
                    onChange={(e) => handleInputChange('max_token_limit', e.target.value)}
                  />
                  <p className="text-sm text-gray-500">
                    Maximum number of credits per request
                  </p>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={handleSaveSettings} disabled={Object.keys(editedSettings).length === 0 || savingSettings}>
                <Save className="h-4 w-4 mr-2" />
                {savingSettings ? 'Saving...' : 'Save Settings'}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="backup" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Database Backup</CardTitle>
              <CardDescription>
                Create and manage database backups
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button onClick={handleCreateBackup} disabled={creatingBackup}>
                <Database className="h-4 w-4 mr-2" />
                {creatingBackup ? 'Creating Backup...' : 'Create Backup'}
              </Button>

              {backupsLoading ? (
                <div className="space-y-2">
                  {[...Array(3)].map((_, i) => (
                    <Skeleton key={i} className="h-12 w-full" />
                  ))}
                </div>
              ) : backupsError ? (
                <div className="text-red-500 p-4">{backupsError}</div>
              ) : backups.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No backups found
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Filename</TableHead>
                        <TableHead>Created</TableHead>
                        <TableHead>Size</TableHead>
                        <TableHead>Last Restored</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {backups.map((backup) => (
                        <TableRow key={backup.id}>
                          <TableCell className="font-medium">{backup.file_name}</TableCell>
                          <TableCell>{format(new Date(backup.created_at), 'MMM dd, yyyy HH:mm')}</TableCell>
                          <TableCell>{formatFileSize(backup.file_size)}</TableCell>
                          <TableCell>
                            {backup.last_restored_at ? format(new Date(backup.last_restored_at), 'MMM dd, yyyy HH:mm') : '-'}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end space-x-2">
                              <Button variant="outline" size="sm" asChild>
                                <a href="#" onClick={(e) => e.preventDefault()}>
                                  <Download className="h-4 w-4 mr-2" />
                                  Download
                                </a>
                              </Button>
                              <Dialog>
                                <DialogTrigger asChild>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setSelectedBackupId(backup.id)}
                                  >
                                    <Upload className="h-4 w-4 mr-2" />
                                    Restore
                                  </Button>
                                </DialogTrigger>
                                <DialogContent>
                                  <DialogHeader>
                                    <DialogTitle>Restore Database</DialogTitle>
                                    <DialogDescription>
                                      Are you sure you want to restore the database from this backup? This will overwrite all current data.
                                    </DialogDescription>
                                  </DialogHeader>
                                  <DialogFooter>
                                    <Button variant="outline" onClick={() => setSelectedBackupId(null)}>
                                      Cancel
                                    </Button>
                                    <Button
                                      variant="destructive"
                                      onClick={handleRestoreBackup}
                                      disabled={restoringBackup}
                                    >
                                      {restoringBackup ? 'Restoring...' : 'Restore'}
                                    </Button>
                                  </DialogFooter>
                                </DialogContent>
                              </Dialog>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
