'use client';

import { useState } from 'react';

export default function TestUnrealPage() {
  const [status, setStatus] = useState('');
  const [loading, setLoading] = useState(false);

  const testUnrealUIUpdate = async (testType: string) => {
    setLoading(true);
    setStatus(`Testing ${testType}...`);

    try {
      let response;
      
      if (testType === 'auth-true') {
        // Test authenticated user with subscription
        response = await fetch('http://localhost:9878/update-auth', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            user_email: '<EMAIL>',
            is_authenticated: true,
            has_subscription: true
          }),
        });
      } else if (testType === 'auth-false') {
        // Test unauthenticated user
        response = await fetch('http://localhost:9878/update-auth', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            user_email: '',
            is_authenticated: false,
            has_subscription: false
          }),
        });
      } else if (testType === 'mcp-running') {
        // Test MCP server running
        response = await fetch('http://localhost:9878/update-mcp', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            is_running: true
          }),
        });
      } else if (testType === 'mcp-stopped') {
        // Test MCP server stopped
        response = await fetch('http://localhost:9878/update-mcp', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            is_running: false
          }),
        });
      }

      if (response && response.ok) {
        const result = await response.json();
        setStatus(`✅ Success: ${result.message || 'UI update sent to Unreal Engine'}`);
      } else {
        setStatus(`❌ Failed: ${response?.status || 'No response'} - ${response?.statusText || 'Unknown error'}`);
      }
    } catch (error: any) {
      setStatus(`❌ Error: ${error.message || 'Failed to connect to Unreal Engine'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-6 text-center">
          🎮 Test Unreal Engine UI Updates
        </h1>
        
        <div className="space-y-4">
          <div>
            <h2 className="text-lg font-semibold text-gray-700 mb-2">Authentication Tests</h2>
            <div className="space-y-2">
              <button
                onClick={() => testUnrealUIUpdate('auth-true')}
                disabled={loading}
                className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-md transition-colors"
              >
                🟢 Set Authenticated + Subscription
              </button>
              
              <button
                onClick={() => testUnrealUIUpdate('auth-false')}
                disabled={loading}
                className="w-full bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-md transition-colors"
              >
                🔴 Set Not Authenticated
              </button>
            </div>
          </div>

          <div>
            <h2 className="text-lg font-semibold text-gray-700 mb-2">MCP Server Tests</h2>
            <div className="space-y-2">
              <button
                onClick={() => testUnrealUIUpdate('mcp-running')}
                disabled={loading}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-md transition-colors"
              >
                🟢 Set MCP Server Running
              </button>
              
              <button
                onClick={() => testUnrealUIUpdate('mcp-stopped')}
                disabled={loading}
                className="w-full bg-orange-600 hover:bg-orange-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-md transition-colors"
              >
                🔴 Set MCP Server Stopped
              </button>
            </div>
          </div>
        </div>

        {status && (
          <div className="mt-6 p-4 bg-gray-50 rounded-md">
            <h3 className="text-sm font-medium text-gray-700 mb-1">Status:</h3>
            <p className="text-sm text-gray-600">{status}</p>
          </div>
        )}

        <div className="mt-6 text-xs text-gray-500">
          <p><strong>Instructions:</strong></p>
          <ol className="list-decimal list-inside space-y-1">
            <li>Make sure Unreal Engine is running with the plugin loaded</li>
            <li>Open the Python Socket Control Panel in Unreal Engine</li>
            <li>Click the buttons above to test UI updates</li>
            <li>Watch the emoji circles in Unreal Engine change in real-time</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
