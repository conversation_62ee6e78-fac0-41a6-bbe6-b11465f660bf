'use client';

import MCPDirectAuth from '@/components/MCPDirectAuth';

export default function TestMCPAuthPage() {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🧪 MCP Authentication Test
          </h1>
          <p className="text-gray-600 mb-8">
            Test the direct authentication flow for AI editors
          </p>
        </div>

        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-lg font-medium text-gray-900 mb-4">
                Direct AI Editor Authentication
              </h2>
              <p className="text-sm text-gray-500 mb-6">
                Click the button below to authenticate directly for AI editors (<PERSON>, <PERSON>urs<PERSON>, Windsurf, etc.)
              </p>
              
              <MCPDirectAuth />
            </div>

            <div className="mt-8 pt-6 border-t border-gray-200">
              <h3 className="text-sm font-medium text-gray-900 mb-3">How it works:</h3>
              <ol className="text-sm text-gray-500 space-y-2">
                <li>1. Click "Authenticate for AI Editor"</li>
                <li>2. Sign in with Google via Supabase</li>
                <li>3. Get redirected to MCP callback</li>
                <li>4. MCP server receives authentication</li>
                <li>5. Window closes automatically</li>
              </ol>
            </div>

            <div className="mt-6 pt-6 border-t border-gray-200">
              <h3 className="text-sm font-medium text-gray-900 mb-3">Test Status:</h3>
              <div className="space-y-2">
                <div className="flex items-center text-xs">
                  <div className="h-2 w-2 bg-green-400 rounded-full mr-2"></div>
                  <span>Frontend: Running on localhost:3000</span>
                </div>
                <div className="flex items-center text-xs">
                  <div className="h-2 w-2 bg-yellow-400 rounded-full mr-2"></div>
                  <span>MCP Server: Should be running on localhost:8080</span>
                </div>
                <div className="flex items-center text-xs">
                  <div className="h-2 w-2 bg-blue-400 rounded-full mr-2"></div>
                  <span>Supabase: Callback URL configured</span>
                </div>
              </div>
            </div>

            <div className="mt-6 pt-6 border-t border-gray-200">
              <p className="text-xs text-gray-400 text-center">
                After successful authentication, you can use CreateLex MCP tools in your AI editor
              </p>
            </div>
          </div>
        </div>

        <div className="mt-8 text-center">
          <a 
            href="/dashboard" 
            className="text-blue-600 hover:text-blue-500 text-sm font-medium"
          >
            ← Back to Dashboard
          </a>
        </div>
      </div>
    </div>
  );
} 