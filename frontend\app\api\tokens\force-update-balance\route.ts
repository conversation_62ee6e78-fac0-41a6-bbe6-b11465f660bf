import { NextRequest, NextResponse } from 'next/server';
import { apiRequest } from '../../../../lib/api-client';

export const dynamic = 'force-dynamic'; // Completely disable caching for this route
export const fetchCache = 'force-no-store'; // Ensure fetch requests are not cached

/**
 * Force update token balance for a user
 * @route POST /api/tokens/force-update-balance
 */
export async function POST(req: NextRequest) {
  try {
    // Get the request body
    const body = await req.json();
    const { userId, balance } = body;

    if (!userId) {
      console.error('[API] No user ID provided');
      return NextResponse.json({ error: 'No user ID provided' }, { status: 400 });
    }

    if (balance === undefined || balance === null) {
      console.error('[API] No balance provided');
      return NextResponse.json({ error: 'No balance provided' }, { status: 400 });
    }

    console.log(`[API] Force updating token balance for user ${userId} to ${balance}`);

    // Get the API URL from environment variables
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';

    // Forward the request to the backend
    const backendUrl = `${apiUrl}/api/tokens/force-update-balance`;

    console.log(`[API] Making request to ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-user-id': userId,
        'Authorization': 'Bearer dummy-token',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      },
      body: JSON.stringify({ userId, balance }),
      cache: 'no-store'
    });

    if (!response.ok) {
      console.error(`[API] Error from backend: ${response.status}`);
      const errorText = await response.text();
      console.error(`[API] Error response: ${errorText}`);
      return NextResponse.json({ error: 'Failed to update token balance' }, { status: response.status });
    }

    const data = await response.json();
    console.log(`[API] Successfully updated token balance: ${JSON.stringify(data)}`);

    return NextResponse.json(data);
  } catch (error) {
    console.error('[API] Error in force-update-balance route:', error);
    return NextResponse.json({ error: 'Server error' }, { status: 500 });
  }
}
