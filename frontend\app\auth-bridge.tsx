'use client';

import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { setAuthToken } from '@/lib/auth-utils';
import { updateUserId } from '@/lib/user-id';
import { getSupabaseClient } from '@/lib/supabase-singleton';
import { fetchAuthTokenFromMainApp } from '@/lib/fetch-auth-token';
import withSearchParamsProvider from '@/components/utils/withSearchParamsProvider';

/**
 * AuthBridge component that handles authentication between the main application and MCP-Chat
 * It extracts auth tokens from URL parameters and sets them in localStorage
 * It also checks for existing Supabase sessions and ensures the user ID is consistent
 */
function AuthBridge() {
  const searchParams = useSearchParams();

  useEffect(() => {
    // Function to migrate chats from browser-generated IDs to authenticated user ID
    const migrateChatsToAuthenticatedUser = async (userId: string) => {
      console.log('AUTH BRIDGE: Migrating chats to authenticated user ID:', userId);

      // Get the current user ID from localStorage
      const currentUserId = localStorage.getItem('ai-chat-user-id');

      // If the current user ID is different from the authenticated user ID,
      // we need to migrate chats from the current user ID to the authenticated user ID
      if (currentUserId && currentUserId !== userId) {
        console.log(`AUTH BRIDGE: Current user ID (${currentUserId}) differs from authenticated user ID (${userId})`);

        try {
          // Get the Supabase client
          const supabase = getSupabaseClient();

          // Call the Supabase API directly to update all chats
          const { error } = await supabase
            .from('chats')
            .update({ user_id: userId })
            .eq('user_id', currentUserId);

          if (error) {
            console.error('AUTH BRIDGE: Error migrating chats:', error);
          } else {
            console.log('AUTH BRIDGE: Successfully migrated chats to authenticated user ID');
          }
        } catch (error) {
          console.error('AUTH BRIDGE: Error migrating chats:', error);
        }
      } else {
        console.log('AUTH BRIDGE: No migration needed, user IDs match or no current user ID');
      }
    };

    const handleAuth = async () => {
      // Check if searchParams exists before trying to access it
      if (!searchParams) {
        console.log('AUTH BRIDGE: No search parameters available');
        return;
      }

      // Get auth token from URL parameters
      const authToken = searchParams.get('auth_token');
      const refreshToken = searchParams.get('refresh_token');
      const userId = searchParams.get('user_id');

      console.log('AUTH BRIDGE: Checking for auth tokens in URL', {
        hasAuthToken: !!authToken,
        hasRefreshToken: !!refreshToken,
        hasUserId: !!userId
      });

      // Check if we have a user ID directly in the URL
      if (userId) {
        console.log('AUTH BRIDGE: User ID found in URL parameters:', userId);

        // Set the user ID in localStorage
        updateUserId(userId, 'supabase');

        // Migrate any existing chats to this authenticated user ID
        try {
          await migrateChatsToAuthenticatedUser(userId);
        } catch (migrationError) {
          console.error('AUTH BRIDGE: Error migrating chats:', migrationError);
        }

        // If we also have auth tokens, try to set the session
        if (authToken && refreshToken) {
          try {
            const supabase = getSupabaseClient();
            await supabase.auth.setSession({
              access_token: authToken,
              refresh_token: refreshToken
            });
            console.log('AUTH BRIDGE: Session set successfully with tokens from URL');
          } catch (sessionError) {
            console.error('AUTH BRIDGE: Error setting session:', sessionError);
          }
        }

        // Remove the auth parameters from the URL to avoid exposing them
        if (typeof window !== 'undefined') {
          const url = new URL(window.location.href);
          url.searchParams.delete('auth_token');
          url.searchParams.delete('refresh_token');
          url.searchParams.delete('user_id');
          window.history.replaceState({}, '', url.toString());
        }

        return;
      }

      // If no user ID in URL, check if we have auth tokens
      if (authToken) {
        console.log('AUTH BRIDGE: Auth token found in URL parameters');

        try {
          // Set the auth token in localStorage
          setAuthToken(authToken);

          // Try to get the user ID from the token
          const supabase = getSupabaseClient();
          const { data, error } = await supabase.auth.getUser(authToken);

          if (error) {
            console.error('AUTH BRIDGE: Error getting user from token:', error);
          } else if (data.user) {
            console.log('AUTH BRIDGE: User found from token:', data.user.id);

            // Set the user ID in localStorage
            updateUserId(data.user.id, 'supabase');

            // If we have a refresh token, try to set the session
            if (refreshToken) {
              try {
                const supabase = getSupabaseClient();
                await supabase.auth.setSession({
                  access_token: authToken,
                  refresh_token: refreshToken
                });
                console.log('AUTH BRIDGE: Session set successfully');
              } catch (sessionError) {
                console.error('AUTH BRIDGE: Error setting session:', sessionError);
              }
            }

            // Migrate any existing chats to this authenticated user ID
            try {
              await migrateChatsToAuthenticatedUser(data.user.id);
            } catch (migrationError) {
              console.error('AUTH BRIDGE: Error migrating chats:', migrationError);
            }
          }
        } catch (error) {
          console.error('AUTH BRIDGE: Error processing auth token:', error);
        }

        // Remove the auth parameters from the URL to avoid exposing them
        // This is a client-side only operation and doesn't affect the server
        if (typeof window !== 'undefined') {
          const url = new URL(window.location.href);
          url.searchParams.delete('auth_token');
          url.searchParams.delete('refresh_token');
          url.searchParams.delete('user_id');
          window.history.replaceState({}, '', url.toString());
        }
      } else {
        // Since there's no separate main app anymore, just check for existing Supabase session
        console.log('AUTH BRIDGE: No auth tokens in URL, checking for existing session');

        try {
          const supabase = getSupabaseClient();
          const { data, error } = await supabase.auth.getSession();

          if (error) {
            console.error('AUTH BRIDGE: Error getting session:', error);
          } else if (data.session?.user) {
            console.log('AUTH BRIDGE: Found existing session for user:', data.session.user.id);

            // Set the user ID in localStorage
            updateUserId(data.session.user.id, 'supabase');

            // Migrate any existing chats to this authenticated user ID
            try {
              await migrateChatsToAuthenticatedUser(data.session.user.id);
            } catch (migrationError) {
              console.error('AUTH BRIDGE: Error migrating chats:', migrationError);
            }
          } else {
            console.log('AUTH BRIDGE: No active session found');
          }
        } catch (error) {
          console.error('AUTH BRIDGE: Error checking for existing session:', error);
        }
      }
    };



    handleAuth();
  }, [searchParams]);

  // This component doesn't render anything
  return null;
}

// Export the wrapped component
export default withSearchParamsProvider(AuthBridge);
