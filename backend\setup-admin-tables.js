require('dotenv').config();
const supabase = require('./src/services/supabaseClient');

async function setupAdminTables() {
  console.log('Setting up admin dashboard tables...');
  
  try {
    // 1. Create usage_logs table
    console.log('Creating usage_logs table...');
    const { error: usageLogsError } = await supabase.rpc('create_table_if_not_exists', {
      table_name: 'usage_logs',
      table_definition: `
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES users(id),
        request_id TEXT,
        endpoint TEXT NOT NULL,
        token_count INTEGER NOT NULL,
        model TEXT NOT NULL,
        cost DECIMAL(10, 6) NOT NULL,
        timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        request_data JSONB,
        response_data JSONB,
        status TEXT,
        ip_address TEXT,
        user_agent TEXT
      `
    });
    
    if (usageLogsError) {
      console.error('Error creating usage_logs table with RPC:', usageLogsError);
      console.log('Attempting to create usage_logs table with SQL query...');
      
      // Alternative approach using SQL query
      const { error: sqlError } = await supabase.from('usage_logs').insert({
        id: '00000000-0000-0000-0000-000000000000',
        user_id: null,
        endpoint: 'table_creation',
        token_count: 0,
        model: 'none',
        cost: 0,
        timestamp: new Date().toISOString(),
        status: 'setup'
      });
      
      if (sqlError && sqlError.code === '42P01') {
        console.log('Table does not exist, need to create it first');
      } else if (sqlError) {
        console.error('Error with SQL approach:', sqlError);
      } else {
        console.log('Successfully created usage_logs table');
      }
    } else {
      console.log('Successfully created usage_logs table');
    }
    
    // 2. Create api_keys table
    console.log('Creating api_keys table...');
    const { error: apiKeysError } = await supabase.rpc('create_table_if_not_exists', {
      table_name: 'api_keys',
      table_definition: `
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES users(id),
        key_name TEXT NOT NULL,
        api_key TEXT NOT NULL UNIQUE,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        last_used_at TIMESTAMPTZ,
        is_active BOOLEAN NOT NULL DEFAULT TRUE,
        rate_limit INTEGER DEFAULT 100,
        permissions JSONB
      `
    });
    
    if (apiKeysError) {
      console.error('Error creating api_keys table:', apiKeysError);
    } else {
      console.log('Successfully created api_keys table');
    }
    
    // 3. Create billing_records table
    console.log('Creating billing_records table...');
    const { error: billingError } = await supabase.rpc('create_table_if_not_exists', {
      table_name: 'billing_records',
      table_definition: `
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES users(id),
        stripe_customer_id TEXT,
        stripe_invoice_id TEXT,
        amount DECIMAL(10, 2) NOT NULL,
        currency TEXT NOT NULL DEFAULT 'usd',
        status TEXT NOT NULL,
        billing_period_start TIMESTAMPTZ,
        billing_period_end TIMESTAMPTZ,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        metadata JSONB
      `
    });
    
    if (billingError) {
      console.error('Error creating billing_records table:', billingError);
    } else {
      console.log('Successfully created billing_records table');
    }
    
    // 4. Add is_admin column to users table if it doesn't exist
    console.log('Adding is_admin column to users table...');
    const { error: alterError } = await supabase.rpc('add_column_if_not_exists', {
      table_name: 'users',
      column_name: 'is_admin',
      column_definition: 'BOOLEAN NOT NULL DEFAULT FALSE'
    });
    
    if (alterError) {
      console.error('Error adding is_admin column:', alterError);
      console.log('Attempting alternative approach...');
      
      // Try to update a user with is_admin field to see if it exists
      const { error: updateError } = await supabase
        .from('users')
        .update({ is_admin: true })
        .eq('email', '<EMAIL>');
        
      if (updateError && updateError.message.includes('column "is_admin" does not exist')) {
        console.log('is_admin column does not exist');
      } else if (updateError) {
        console.error('Error checking is_admin column:', updateError);
      } else {
        console.log('is_admin column exists and updated admin user');
      }
    } else {
      console.log('Successfully added is_admin column');
    }
    
    // 5. Set admin privileges for specified users
    console.log('Setting admin privileges for specified users...');
    const { error: adminError } = await supabase
      .from('users')
      .update({ is_admin: true })
      .or('<EMAIL>,<EMAIL>');
      
    if (adminError) {
      console.error('Error setting admin privileges:', adminError);
    } else {
      console.log('Successfully set admin privileges');
    }
    
  } catch (error) {
    console.error('Exception in setup:', error);
  }
}

// Run the setup
setupAdminTables()
  .then(() => {
    console.log('Setup completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
