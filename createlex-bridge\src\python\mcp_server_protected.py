# Protected MCP Server with Subscription Validation
import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from subscription_validator import check_subscription

# Validate subscription on startup
if not check_subscription():
    print("Invalid subscription. Exiting.")
    sys.exit(1)

# Import original server after validation
from mcp_server_stdio import mcp  # use existing FastMCP instance

if __name__ == "__main__":
    print("Protected UnrealGenAI MCP Server")
    print("Subscription validated")
    # Start the FastMCP server in stdio mode (expected by the Electron bridge)
    mcp.run("stdio")
