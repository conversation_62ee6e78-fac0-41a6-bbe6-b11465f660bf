# Integrated CreateLex AI Platform

This application combines the frontend landing pages and the MCP Chat functionality into a single application running on port 3000.

## Setup Instructions

### 1. Install Dependencies

```bash
npm install
```

### 2. Configure Environment Variables

Copy the `.env.example` file to `.env` and update the values:

```bash
cp .env.example .env
```

Make sure to set the following variables:
- `PORT=3000` - The port to run the application on
- `API_URL` - URL of your backend API (e.g., `http://localhost:5001`)
- `MCP_SERVER_URL` - URL of your MCP server (e.g., `ws://127.0.0.1:9877`)
- `OPENAI_API_KEY` - Your OpenAI API key
- `ANTHROPIC_API_KEY` - Your Anthropic API key
- `GOOGLE_API_KEY` - Your Google API key
- `SUPABASE_URL` - Your Supabase URL
- `SUPABASE_SERVICE_KEY` - Your Supabase service key

### 3. Run the Integration Test

```bash
./test-integration.sh
```

Fix any issues reported by the test before proceeding.

### 4. Run the Application

```bash
npm run dev
```

The application will be available at http://localhost:3000

## Application Structure

- `/` - Landing page
- `/chat` - Chat interface
- `/dashboard` - User dashboard
- `/login` - Login page
- `/signup` - Signup page
- `/subscription` - Subscription management

## Docker Deployment

You can also run the application using Docker:

```bash
docker-compose up -d
```

## Troubleshooting

### Port Conflicts

If port 3000 is already in use, you can change the port by:

1. Updating the PORT environment variable in .env
2. Updating the port in next.config.ts
3. Updating the port in server.js
4. Updating the port in docker-compose.yml
5. Updating the port in Dockerfile

### Authentication Issues

If you're having trouble with authentication:

1. Make sure your Supabase configuration is correct
2. Check that the auth-bridge.tsx component is working correctly
3. Verify that the user ID is being set correctly in localStorage

### MCP Server Connection Issues

If the chat cannot connect to the MCP server:

1. Check that the MCP server is running
2. Verify that the MCP_SERVER_URL environment variable is set correctly
3. Check for any network restrictions or firewall settings
