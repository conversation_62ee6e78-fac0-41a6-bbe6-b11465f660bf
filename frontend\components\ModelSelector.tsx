'use client';

import { useState, useEffect } from 'react';

interface Model {
  id: string;
  name: string;
  description: string;
  requiresConfig: boolean;
}

interface ModelSelectorProps {
  onModelChange: (modelId: string) => void;
}

const ModelSelector = ({ onModelChange }: ModelSelectorProps) => {
  const [models, setModels] = useState<Model[]>([]);
  const [selectedModel, setSelectedModel] = useState<string>('gemini-flash');
  const [loading, setLoading] = useState<boolean>(true);
  const [showConfig, setShowConfig] = useState<boolean>(false);
  const [apiKey, setApiKey] = useState<string>('');
  const [saving, setSaving] = useState<boolean>(false);

  // Default endpoints that will be used
  const defaultEndpoints = {
    'deepseek-r1': 'https://api.together.xyz/v1/completions',
    'gemini-flash': 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent',
    'llama3': 'https://api.together.xyz/v1/chat/completions',
    'local-ollama': 'http://localhost:11434/api/chat'
  };

  useEffect(() => {
    // Fetch available models
    const fetchModels = async () => {
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001'}/api/models`);
        if (response.ok) {
          const data = await response.json();
          setModels(data.models);
          setSelectedModel(data.currentModel);
          setLoading(false);
        } else {
          console.error('Failed to fetch models');
          // Fallback for development
          setModels([
            { id: 'rule-based', name: 'Rule-based (Default)', description: 'Simple command parsing without AI', requiresConfig: false },
            { id: 'deepseek-r1', name: 'DeepSeek-R1', description: 'Powerful AI model for Unreal Engine tasks', requiresConfig: true },
            { id: 'llama3', name: 'Llama 3', description: 'Open source AI assistant', requiresConfig: true },
            { id: 'local-ollama', name: 'Local Ollama', description: 'Use locally hosted models', requiresConfig: true }
          ]);
          setLoading(false);
        }
      } catch (error) {
        console.error('Error fetching models:', error);
        setLoading(false);
      }
    };

    fetchModels();
  }, []);

  const handleModelChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const modelId = e.target.value;
    setSelectedModel(modelId);
    onModelChange(modelId);

    // Show config panel only if the model requires configuration
    const model = models.find(m => m.id === modelId);
    if (model?.requiresConfig && modelId !== 'rule-based') {
      setShowConfig(true);
      // Fetch existing config if available
      fetchModelConfig(modelId);
    } else {
      setShowConfig(false);
    }
  };

  const fetchModelConfig = async (modelId: string) => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001'}/api/models/${modelId}/config`);
      if (response.ok) {
        const data = await response.json();
        setApiKey(data.apiKey || '');
      }
    } catch (error) {
      console.error('Error fetching model config:', error);
    }
  };

  const saveModelConfig = async () => {
    setSaving(true);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001'}/api/models/${selectedModel}/config`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          apiKey,
          endpoint: defaultEndpoints[selectedModel as keyof typeof defaultEndpoints] || ''
        }),
      });

      if (response.ok) {
        setShowConfig(false);
      } else {
        console.error('Failed to save config');
      }
    } catch (error) {
      console.error('Error saving config:', error);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="model-selector">
      <div className="flex items-center space-x-2">
        <select
          id="model-select"
          value={selectedModel}
          onChange={handleModelChange}
          className="bg-gray-800 text-gray-300 text-sm border border-gray-700 rounded-full px-3 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
          disabled={loading}
        >
          {loading ? (
            <option value="">Loading models...</option>
          ) : (
            models.map(model => (
              <option key={model.id} value={model.id}>
                {model.name}
              </option>
            ))
          )}
        </select>

        {selectedModel !== 'rule-based' && (
          <button
            onClick={() => setShowConfig(!showConfig)}
            className="text-sm px-3 py-1 rounded-full border border-gray-700 bg-gray-800 text-gray-300 hover:bg-gray-700"
          >
            {showConfig ? 'Hide Settings' : 'Settings'}
          </button>
        )}
      </div>

      {showConfig && (
        <div className="absolute mt-2 p-4 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-10 w-80 right-4">
          <div className="mb-3">
            <p className="text-sm text-gray-400 mb-2">Using default endpoint: {defaultEndpoints[selectedModel as keyof typeof defaultEndpoints]}</p>

            <label htmlFor="api-key" className="block text-sm font-medium mb-1 text-gray-300">
              API Key {selectedModel === 'local-ollama' ? '(Optional)' : '(Required)'}:
            </label>
            <input
              id="api-key"
              type="password"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white focus:outline-none focus:ring-1 focus:ring-blue-500"
              placeholder={selectedModel === 'local-ollama' ? 'Optional for local models' : 'Enter API key'}
            />
          </div>

          <button
            onClick={saveModelConfig}
            disabled={saving || (selectedModel !== 'local-ollama' && !apiKey)}
            className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded hover:from-blue-600 hover:to-purple-600 disabled:opacity-50"
          >
            {saving ? 'Saving...' : 'Save Settings'}
          </button>
        </div>
      )}
    </div>
  );
};

export default ModelSelector;