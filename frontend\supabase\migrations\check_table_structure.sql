-- Function to check if a table has the required columns
CREATE OR R<PERSON>LACE FUNCTION check_table_structure(table_name text, required_columns text[])
RETURNS boolean
LANGUAGE plpgsql
AS $$
DECLARE
  column_exists boolean;
  column_name text;
BEGIN
  FOREACH column_name IN ARRAY required_columns
  LOOP
    SELECT EXISTS (
      SELECT 1
      FROM information_schema.columns
      WHERE table_name = check_table_structure.table_name
      AND column_name = check_table_structure.column_name
    ) INTO column_exists;
    
    IF NOT column_exists THEN
      RETURN false;
    END IF;
  END LOOP;
  
  RETURN true;
END;
$$;

-- Function to check if a column has the expected type
CREATE OR REPLACE FUNCTION check_column_type(table_name text, column_name text, expected_type text)
RETURNS boolean
LANGUAGE plpgsql
AS $$
DECLARE
  column_type text;
BEGIN
  SELECT data_type
  FROM information_schema.columns
  WHERE table_name = check_column_type.table_name
  AND column_name = check_column_type.column_name
  INTO column_type;
  
  IF column_type IS NULL THEN
    RETURN false;
  END IF;
  
  RETURN column_type = expected_type;
END;
$$;

-- Function to create the check_table_structure function
CREATE OR REPLACE FUNCTION create_check_table_structure_function()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  -- Function already created above
  NULL;
END;
$$;

-- Function to create the check_column_type function
CREATE OR REPLACE FUNCTION create_check_column_type_function()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  -- Function already created above
  NULL;
END;
$$;

-- Check if the tables exist and have the correct structure
SELECT EXISTS (
  SELECT 1
  FROM information_schema.tables
  WHERE table_name = 'chats'
) AS chats_table_exists;

SELECT EXISTS (
  SELECT 1
  FROM information_schema.tables
  WHERE table_name = 'messages'
) AS messages_table_exists;

-- Check if the content column in the messages table is JSONB
SELECT data_type
FROM information_schema.columns
WHERE table_name = 'messages'
AND column_name = 'content';
