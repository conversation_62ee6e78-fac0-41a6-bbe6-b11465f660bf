#!/usr/bin/env python3
"""
Cloud MCP Server for Unreal Engine
Handles multiple Unreal Engine instances and routes commands appropriately
"""

import asyncio
import json
import time
from typing import Dict, List, Optional
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
from mcp.server.fastmcp import FastMCP
import requests
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastMCP instance
mcp = FastMCP("Unreal Engine Cloud MCP Server")

# In-memory registry of connected Unreal Engine instances
unreal_instances: Dict[str, dict] = {}

class UnrealInstanceRegistration(BaseModel):
    tunnel_url: str
    local_port: int
    instance_id: str
    capabilities: List[str]

class CommandRequest(BaseModel):
    instance_id: Optional[str] = None
    command_type: str
    command_data: dict

# FastAPI app for registration endpoints
app = FastAPI(title="Unreal Engine Cloud MCP Server")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.post("/register")
async def register_unreal_instance(registration: UnrealInstanceRegistration):
    """Register a new Unreal Engine instance"""
    logger.info(f"Registering Unreal Engine instance: {registration.instance_id}")
    
    # Store instance info
    unreal_instances[registration.instance_id] = {
        "tunnel_url": registration.tunnel_url,
        "local_port": registration.local_port,
        "capabilities": registration.capabilities,
        "last_seen": time.time(),
        "status": "connected"
    }
    
    logger.info(f"✅ Registered instance {registration.instance_id} at {registration.tunnel_url}")
    
    return {
        "success": True,
        "instance_id": registration.instance_id,
        "message": "Instance registered successfully"
    }

@app.get("/instances")
async def list_instances():
    """List all registered Unreal Engine instances"""
    # Clean up stale instances (older than 1 hour)
    current_time = time.time()
    stale_instances = [
        instance_id for instance_id, info in unreal_instances.items()
        if current_time - info["last_seen"] > 3600
    ]
    
    for instance_id in stale_instances:
        del unreal_instances[instance_id]
        logger.info(f"Removed stale instance: {instance_id}")
    
    return {
        "instances": unreal_instances,
        "count": len(unreal_instances)
    }

@app.post("/command")
async def execute_command(command: CommandRequest):
    """Execute a command on a specific Unreal Engine instance"""
    if command.instance_id and command.instance_id not in unreal_instances:
        raise HTTPException(status_code=404, detail="Unreal Engine instance not found")
    
    # If no instance specified, use the first available one
    if not command.instance_id:
        if not unreal_instances:
            raise HTTPException(status_code=503, detail="No Unreal Engine instances available")
        command.instance_id = next(iter(unreal_instances.keys()))
    
    instance_info = unreal_instances[command.instance_id]
    
    try:
        # Forward command to the Unreal Engine instance
        response = await forward_command_to_unreal(instance_info, command.command_data)
        
        # Update last seen
        instance_info["last_seen"] = time.time()
        
        return response
    
    except Exception as e:
        logger.error(f"Failed to execute command on instance {command.instance_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def forward_command_to_unreal(instance_info: dict, command_data: dict) -> dict:
    """Forward a command to a specific Unreal Engine instance"""
    tunnel_url = instance_info["tunnel_url"]
    
    # Convert HTTPS tunnel URL to the appropriate format for socket communication
    # This is a simplified example - you might need to adjust based on your tunnel setup
    if tunnel_url.startswith("https://"):
        # For cloudflare tunnels, we need to use the tunnel URL directly
        # This would require modifying the Unreal Engine socket server to accept HTTP requests
        # For now, we'll simulate the response
        logger.info(f"Forwarding command to {tunnel_url}: {command_data}")
        
        # In a real implementation, you would:
        # 1. Convert the command to the format expected by Unreal Engine
        # 2. Send it via HTTP/WebSocket to the tunnel URL
        # 3. Return the response
        
        # Simulated response for now
        return {
            "success": True,
            "message": f"Command forwarded to {tunnel_url}",
            "command": command_data
        }
    
    else:
        raise Exception(f"Unsupported tunnel URL format: {tunnel_url}")

# MCP Tools that route to registered Unreal Engine instances

@mcp.tool()
def handshake_test(instance_id: str = None) -> str:
    """Test connection to a specific Unreal Engine instance"""
    if not unreal_instances:
        return "No Unreal Engine instances registered"
    
    if instance_id and instance_id not in unreal_instances:
        return f"Instance {instance_id} not found"
    
    target_instance = instance_id or next(iter(unreal_instances.keys()))
    instance_info = unreal_instances[target_instance]
    
    return f"Handshake successful with instance {target_instance} at {instance_info['tunnel_url']}"

@mcp.tool()
def spawn_actor(actor_class: str, x: float = 0.0, y: float = 0.0, z: float = 0.0, instance_id: str = None) -> str:
    """Spawn an actor in a specific Unreal Engine instance"""
    if not unreal_instances:
        return "No Unreal Engine instances available"
    
    target_instance = instance_id or next(iter(unreal_instances.keys()))
    
    if target_instance not in unreal_instances:
        return f"Instance {target_instance} not found"
    
    # This would forward the command to the actual Unreal Engine instance
    command_data = {
        "type": "spawn",
        "actor_class": actor_class,
        "x": x, "y": y, "z": z
    }
    
    return f"Spawn command sent to instance {target_instance}: {command_data}"

@mcp.tool()
def create_material(material_name: str, base_color: List[float] = [1.0, 1.0, 1.0], 
                   metallic: float = 0.0, roughness: float = 0.5, instance_id: str = None) -> str:
    """Create a material in a specific Unreal Engine instance"""
    if not unreal_instances:
        return "No Unreal Engine instances available"
    
    target_instance = instance_id or next(iter(unreal_instances.keys()))
    
    if target_instance not in unreal_instances:
        return f"Instance {target_instance} not found"
    
    command_data = {
        "type": "create_material",
        "material_name": material_name,
        "base_color": base_color,
        "metallic": metallic,
        "roughness": roughness
    }
    
    return f"Create material command sent to instance {target_instance}: {command_data}"

@mcp.tool()
def execute_python(script: str, instance_id: str = None) -> str:
    """Execute Python script in a specific Unreal Engine instance"""
    if not unreal_instances:
        return "No Unreal Engine instances available"
    
    target_instance = instance_id or next(iter(unreal_instances.keys()))
    
    if target_instance not in unreal_instances:
        return f"Instance {target_instance} not found"
    
    command_data = {
        "type": "execute_python",
        "script": script
    }
    
    return f"Python execution command sent to instance {target_instance}"

@mcp.tool()
def list_unreal_instances() -> str:
    """List all registered Unreal Engine instances"""
    if not unreal_instances:
        return "No Unreal Engine instances registered"
    
    instances_info = []
    for instance_id, info in unreal_instances.items():
        instances_info.append(f"- {instance_id}: {info['tunnel_url']} (last seen: {time.time() - info['last_seen']:.0f}s ago)")
    
    return f"Registered instances ({len(unreal_instances)}):\n" + "\n".join(instances_info)

@mcp.tool()
def server_status() -> str:
    """Get cloud MCP server status"""
    return f"Cloud MCP Server running. {len(unreal_instances)} Unreal Engine instances connected."

# Health check endpoint
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "instances_count": len(unreal_instances),
        "uptime": time.time()
    }

# Mount FastMCP app
@app.post("/mcp")
async def mcp_endpoint(request: Request):
    """MCP protocol endpoint"""
    # This would handle MCP protocol requests
    # For now, return a simple response
    return {"message": "MCP endpoint - implement protocol handling here"}

if __name__ == "__main__":
    # Run both FastAPI and MCP server
    print("🌐 Starting Cloud MCP Server...")
    print("📡 Registration endpoint: http://localhost:8000/register")
    print("🔧 MCP endpoint: http://localhost:8000/mcp")
    print("📊 Health check: http://localhost:8000/health")
    
    # In production, you would run this with a proper ASGI server
    uvicorn.run(app, host="0.0.0.0", port=8000) 