import { useState } from 'react';
import { supabase } from '@/lib/supabase';

export default function MCPDirectAuth() {
  const [isAuthenticating, setIsAuthenticating] = useState(false);

  const handleMCPAuth = async () => {
    try {
      setIsAuthenticating(true);
      
      // Direct OAuth with MCP callback
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/api/mcp/auth/callback`,
          queryParams: {
            direct_mcp: 'true' // Flag to indicate this is for MCP
          }
        },
      });

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Error with MCP authentication:', error);
    } finally {
      setIsAuthenticating(false);
    }
  };

  return (
    <button
      onClick={handleMCPAuth}
      disabled={isAuthenticating}
      className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
    >
      <svg 
        className="w-4 h-4" 
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
      >
        <path 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          strokeWidth={2} 
          d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" 
        />
        <path 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          strokeWidth={2} 
          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" 
        />
      </svg>
      <span>{isAuthenticating ? 'Authenticating...' : 'Authenticate for AI Editor'}</span>
    </button>
  );
} 