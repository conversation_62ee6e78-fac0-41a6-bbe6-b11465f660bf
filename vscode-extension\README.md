# CreateLex MCP VSCode Extension

This VSCode extension allows you to connect to CreateLex MCP servers for Unreal Engine integration. It provides a user-friendly interface for selecting your Unreal Engine project and configuring the MCP server connection.

## Features

- 🔧 **MCP Server Management**: Connect to CreateLex MCP servers through an intuitive UI
- 🚀 **Unreal Engine Integration**: Seamless communication with Unreal Engine through MCP
- 🎯 **AI Model Access**: Access to powerful AI models for game development
- ⚡ **Server Health Monitoring**: Real-time monitoring of MCP server status and connections
- 🔄 **Automatic Connection Management**: Seamless handling of MCP server connections and reconnections

## Installation

1. Download the VSIX file from this directory (`createlex-mcp-0.0.41.vsix`)
2. Open VSCode
3. Go to Extensions view (Ctrl+Shift+X)
4. Click "..." in the top-right corner
5. Select "Install from VSIX..."
6. Choose the downloaded VSIX file

## Usage

1. Open the CreateLex MCP view from the VSCode activity bar
2. Select your Unreal Engine project folder in the "Unreal Engine" tab
3. The extension will automatically find the MCP server at `[Project]\Plugins\UnrealGenAISupport\Content\Python\mcp_server.py`
4. Connect to your Unreal Engine project through the MCP server

## Requirements

- VSCode 
- Unreal Engine 5.1 or later
- CreateLex account

## Support

For support, please contact us at [<EMAIL>](mailto:<EMAIL>) or visit our [website](https://createlex.com).
