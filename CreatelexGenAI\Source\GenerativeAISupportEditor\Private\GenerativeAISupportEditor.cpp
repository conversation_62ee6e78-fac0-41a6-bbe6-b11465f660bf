// Copyright (c) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
// Licensed under the MIT License. See LICENSE file in the root directory of this
// source tree or http://opensource.org/licenses/MIT.



#include "GenerativeAISupportEditor.h"
#include "ISettingsModule.h"
#include "Styling/AppStyle.h"
#include "GenerativeAISupportSettings.h"
#include "ISettingsSection.h"
#include "LevelEditor.h"
#include "WorkspaceMenuStructure.h"
#include "WorkspaceMenuStructureModule.h"
#include "Editor/GenEditorCommands.h"
#include "Editor/GenEditorWindow.h"
#include "Subsystems/AssetEditorSubsystem.h"
#include "ToolMenus.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"
#include "Styling/SlateStyle.h"
#include "Interfaces/IPluginManager.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"
#include "Engine/Engine.h"
#include "Framework/Notifications/NotificationManager.h"
#include "Widgets/Notifications/SNotificationList.h"
#include "Editor.h"

#define LOCTEXT_NAMESPACE "FGenerativeAISupportEditorModule"

// Define the tab ID for our editor window
static const FName GenEditorTabId("GenEditorWindow");

FGenerativeAISupportEditorModule::FGenerativeAISupportEditorModule()
    : bSettingsRegistered(false)
{
    // Constructor initialization ensures proper state
}

void FGenerativeAISupportEditorModule::StartupModule()
{
    // Module startup notification
    UE_LOG(LogTemp, Log, TEXT("🚀🚀🚀 CREATELEXGENAI MODULE STARTING UP! 🚀🚀🚀"));

    // Register project settings
    UE_LOG(LogTemp, Log, TEXT("🔧 Registering settings..."));
    RegisterSettings();

    // Register menu extension
    UE_LOG(LogTemp, Log, TEXT("🔧 Registering menu extension..."));
    RegisterMenuExtension();

    // Register universal asset editor toolbar button
    UE_LOG(LogTemp, Log, TEXT("🔧 Registering asset editor toolbar extension..."));
    RegisterAssetEditorToolbarExtension();

    // Register tab spawner
    UE_LOG(LogTemp, Log, TEXT("🔧 Registering tab spawner..."));
    FGlobalTabmanager::Get()->RegisterNomadTabSpawner(
                            GenEditorTabId,
                            FOnSpawnTab::CreateRaw(&FGenEditorWindowManager::Get(),
                                                   &FGenEditorWindowManager::SpawnEditorWindowTab))
                            .SetDisplayName(LOCTEXT("GenEditorWindowTitle", "Gen AI Support"))
                            .SetTooltipText(LOCTEXT("GenEditorWindowTooltip", "Open the Generative AI Support window"))
                            .SetIcon(FSlateIcon(FAppStyle::GetAppStyleSetName(), "LevelEditor.Tabs.Details"))
                            .SetGroup(WorkspaceMenu::GetMenuStructure().GetToolsCategory());
                            
    UE_LOG(LogTemp, Log, TEXT("✅✅✅ CREATELEXGENAI MODULE STARTUP COMPLETE! ✅✅✅"));
}

void FGenerativeAISupportEditorModule::ShutdownModule()
{
    // Unregister settings
    UnregisterSettings();

    // Unregister menu extension
    UnregisterMenuExtension();

    // Unregister asset editor toolbar extension
    UnregisterAssetEditorToolbarExtension();

    // Unregister tab spawner
    if (FSlateApplication::IsInitialized())
    {
        FGlobalTabmanager::Get()->UnregisterNomadTabSpawner(GenEditorTabId);
    }
}

void FGenerativeAISupportEditorModule::RegisterSettings()
{
    // Prevent duplicate registration
    if (bSettingsRegistered)
    {
        UE_LOG(LogTemp, Warning, TEXT("CreatelexGenAI settings already registered, skipping."));
        return;
    }

    if (ISettingsModule* SettingsModule = FModuleManager::GetModulePtr<ISettingsModule>("Settings"))
    {
        ISettingsSectionPtr SettingsSection = SettingsModule->RegisterSettings(
            "Project", "Plugins", "CreatelexGenAI",
            LOCTEXT("CreatelexGenAISettingsName", "CreateLex GenAI"),
            LOCTEXT("CreatelexGenAIDescription", "Configuration for the CreatelexGenAI plugin"),
            GetMutableDefault<UGenerativeAISupportSettings>()
        );

        if (SettingsSection.IsValid())
        {
            SettingsSection->OnModified().BindRaw(this, &FGenerativeAISupportEditorModule::HandleSettingsSaved);
            bSettingsRegistered = true;
            UE_LOG(LogTemp, Log, TEXT("CreatelexGenAI settings registered successfully."));
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("Failed to register CreatelexGenAI settings."));
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("Settings module not found."));
    }
}

void FGenerativeAISupportEditorModule::UnregisterSettings()
{
    if (bSettingsRegistered)
    {
        if (ISettingsModule* SettingsModule = FModuleManager::GetModulePtr<ISettingsModule>("Settings"))
        {
            SettingsModule->UnregisterSettings("Project", "Plugins", "CreatelexGenAI");
            bSettingsRegistered = false;
            UE_LOG(LogTemp, Log, TEXT("CreatelexGenAI settings unregistered successfully."));
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("Settings module not found during unregistration."));
        }
    }
}

bool FGenerativeAISupportEditorModule::HandleSettingsSaved()
{
    UGenerativeAISupportSettings* Settings = GetMutableDefault<UGenerativeAISupportSettings>();
    Settings->SaveConfig();
    
    return true;
}

void FGenerativeAISupportEditorModule::RegisterMenuExtension()
{
    // Register commands
    FGenEditorCommands::Register();
    PluginCommands = MakeShareable(new FUICommandList);
    
    // Map commands to actions
    PluginCommands->MapAction(
        FGenEditorCommands::Get().OpenGenEditorWindow,
        FExecuteAction::CreateRaw(this, &FGenerativeAISupportEditorModule::OnEditorWindowMenuClicked),
        FCanExecuteAction());
    
    // Register menu extension - add to the Window menu
    FLevelEditorModule& LevelEditorModule = FModuleManager::LoadModuleChecked<FLevelEditorModule>("LevelEditor");
    TSharedPtr<FExtender> MenuExtender = MakeShareable(new FExtender());
    
    MenuExtender->AddMenuExtension(
        "WindowLayout",
        EExtensionHook::After,
        PluginCommands,
        FMenuExtensionDelegate::CreateLambda([](FMenuBuilder& Builder) {
            Builder.AddMenuEntry(FGenEditorCommands::Get().OpenGenEditorWindow);
        }));
    
    LevelEditorModule.GetMenuExtensibilityManager()->AddExtender(MenuExtender);
}

void FGenerativeAISupportEditorModule::UnregisterMenuExtension()
{
    // Unregister commands
    FGenEditorCommands::Unregister();
}

void FGenerativeAISupportEditorModule::OnEditorWindowMenuClicked()
{
    FGlobalTabmanager::Get()->TryInvokeTab(GenEditorTabId);
}

void FGenerativeAISupportEditorModule::RegisterAssetEditorToolbarExtension()
{
    UE_LOG(LogTemp, Log, TEXT("🎯 Starting RegisterAssetEditorToolbarExtension..."));
    
    // Commands are already registered in RegisterMenuExtension, just ensure we have a command list
    if (!PluginCommands.IsValid())
    {
        PluginCommands = MakeShareable(new FUICommandList);
        UE_LOG(LogTemp, Log, TEXT("✅ Created new PluginCommands"));
    }

    // Map the sync command
    UE_LOG(LogTemp, Log, TEXT("🎯 Mapping SyncWithAI action..."));
    PluginCommands->MapAction(
        FGenEditorCommands::Get().SyncWithAI,
        FExecuteAction::CreateRaw(this, &FGenerativeAISupportEditorModule::OnSyncWithAIClicked),
        FCanExecuteAction::CreateRaw(this, &FGenerativeAISupportEditorModule::CanSyncWithAI));
    UE_LOG(LogTemp, Log, TEXT("✅ SyncWithAI action mapped"));

    // Register button only for Blueprint Editor to avoid duplicates
    if (UToolMenus* ToolMenus = UToolMenus::Get())
    {
        UE_LOG(LogTemp, Log, TEXT("✅ ToolMenus is valid"));
        
        // Register to extend Blueprint Editor toolbar specifically
        UToolMenu* BlueprintEditorToolbar = ToolMenus->ExtendMenu("AssetEditor.BlueprintEditor.ToolBar");
        if (BlueprintEditorToolbar)
        {
            UE_LOG(LogTemp, Log, TEXT("✅ Found BlueprintEditor toolbar"));
            
            // Create a new section for our button
            FToolMenuSection& Section = BlueprintEditorToolbar->AddSection("CreatelexGenAI", LOCTEXT("CreatelexGenAI", "AI Tools"));
            
            // Add a dynamic entry that provides the command list context
            Section.AddDynamicEntry("SyncWithAIDynamic", FNewToolMenuSectionDelegate::CreateLambda(
                [this](FToolMenuSection& InSection)
                {
                    InSection.AddEntry(FToolMenuEntry::InitToolBarButton(
                        FGenEditorCommands::Get().SyncWithAI,
                        LOCTEXT("SyncWithAI", "Sync with AI"),
                        LOCTEXT("SyncWithAITooltip", "Sync current asset context with AI Assistant"),
                        FSlateIcon(FAppStyle::GetAppStyleSetName(), "GenericCommands.Redo")
                    )).SetCommandList(PluginCommands);
                }
            ));
            
            UE_LOG(LogTemp, Log, TEXT("✅✅✅ SYNC WITH AI BUTTON ADDED TO BLUEPRINT TOOLBAR! ✅✅✅"));
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("❌ BlueprintEditor toolbar not found!"));
        }
        
        // CRITICAL: Refresh all menus to ensure our extensions are visible
        UE_LOG(LogTemp, Log, TEXT("🔄 Refreshing all tool menus..."));
        ToolMenus->RefreshAllWidgets();
        UE_LOG(LogTemp, Log, TEXT("✅ Tool menus refreshed"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("❌ ToolMenus is NULL! Cannot register toolbar extension."));
    }
    
    UE_LOG(LogTemp, Log, TEXT("🎯 RegisterAssetEditorToolbarExtension completed"));
}

void FGenerativeAISupportEditorModule::UnregisterAssetEditorToolbarExtension()
{
    if (UToolMenus* ToolMenus = UToolMenus::Get())
    {
        ToolMenus->RemoveSection("AssetEditor.DefaultToolBar", "GenAISync");
    }
}

void FGenerativeAISupportEditorModule::OnSyncWithAIClicked()
{
    UE_LOG(LogTemp, Warning, TEXT("🎯 Sync with AI button clicked!"));
    
    // Show a notification in the editor
    if (GEditor)
    {
        FNotificationInfo Info(FText::FromString(TEXT("Syncing Blueprint context with AI...")));
        Info.ExpireDuration = 3.0f;
        Info.bUseSuccessFailIcons = false;
        FSlateNotificationManager::Get().AddNotification(Info);
    }
    
    // Dynamically determine the plugin Python path
    FString PluginPythonPath;
    TSharedPtr<IPlugin> Plugin = IPluginManager::Get().FindPlugin(TEXT("CreatelexGenAI"));
    if (Plugin.IsValid())
    {
        PluginPythonPath = FPaths::Combine(Plugin->GetBaseDir(), TEXT("Content"), TEXT("Python"));
        PluginPythonPath = PluginPythonPath.Replace(TEXT("\\"), TEXT("/"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("Could not find CreatelexGenAI plugin! Falling back to default path."));
        PluginPythonPath = TEXT("./CreatelexGenAI/Content/Python");
    }

    FString PythonScript = FString::Printf(TEXT(
        "try:\n"
        "    import sys\n"
        "    import os\n"
        "    plugin_path = r'%s'\n"
        "    handlers_path = os.path.join(plugin_path, 'handlers')\n"
        "    if handlers_path not in sys.path:\n"
        "        sys.path.append(handlers_path)\n"
        "    print('[C++->Python] Importing blueprint_context_handler...')\n"
        "    import blueprint_context_handler\n"
        "    print('[C++->Python] Calling handle_blueprint_context_request...')\n"
        "    blueprint_context_handler.handle_blueprint_context_request('get_current_blueprint_context', {})\n"
        "    print('[C++->Python] Blueprint context sync completed successfully!')\n"
        "except Exception as e:\n"
        "    print(f'[C++->Python] Error in blueprint sync: {e}')\n"
        "    import traceback\n"
        "    traceback.print_exc()"
    ), *PluginPythonPath);
            
    UE_LOG(LogTemp, Warning, TEXT("🐍 Executing Blueprint context sync script..."));
    
    if (GEngine)
    {
        GEngine->Exec(nullptr, *FString::Printf(TEXT("py %s"), *PythonScript));
        UE_LOG(LogTemp, Log, TEXT("✅ Blueprint context sync script sent to Python"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("❌ GEngine is null, cannot execute Python"));
    }
}

bool FGenerativeAISupportEditorModule::CanSyncWithAI() const
{
    // Always enable the button - let Python handle the blueprint detection
    return true;
}

// Removed SendContextToMCP - now handled by Python directly

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FGenerativeAISupportEditorModule, GenerativeAISupportEditor)
