'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import HeroSection from '@/components/landing/HeroSection';
import FeaturesSection from '@/components/landing/FeaturesSection';
import ProductsSection from '@/components/landing/ProductsSection';
import PricingSection from '@/components/landing/PricingSection';
import DemoSection from '@/components/landing/DemoSection';
import TestimonialsSection from '@/components/landing/TestimonialsSection';
import CTASection from '@/components/landing/CTASection';
import Navbar from '@/components/landing/Navbar';
import Footer from '@/components/landing/Footer';

export default function HomePage() {
  const { isAuthenticated, hasActiveSubscription } = useAuth();
  const [isScrolled, setIsScrolled] = useState(false);

  // Handle scroll events for navbar styling
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Get the appropriate destination URL for CTA buttons
  const getStartedUrl = () => {
    if (isAuthenticated) {
      if (hasActiveSubscription) {
        return '/dashboard'; // Go to dashboard if subscribed
      } else {
        return '/subscription'; // Go to subscription page if authenticated but not subscribed
      }
    } else {
      return '/signup'; // Go to signup page if not authenticated
    }
  };

  return (
    <div className="landing-page">
      <Navbar isScrolled={isScrolled} isAuthenticated={isAuthenticated} />

      <main>
        <HeroSection onGetStarted={() => {}} getStartedUrl={getStartedUrl()} />
        <FeaturesSection />
        <ProductsSection />
        <DemoSection />
        <PricingSection onSubscribe={() => {}} getStartedUrl={getStartedUrl()} />
        <TestimonialsSection />
        <CTASection onGetStarted={() => {}} getStartedUrl={getStartedUrl()} />
      </main>

      <Footer />
    </div>
  );
}
