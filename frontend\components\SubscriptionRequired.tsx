import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useSubscription } from '../contexts/SubscriptionContext';

const SubscriptionRequired: React.FC = () => {
  const { user } = useAuth();
  const { createCheckoutSession, isLoading } = useSubscription();
  const [error, setError] = React.useState<string | null>(null);

  const handleSubscribe = async () => {
    setError(null);
    try {
      const checkoutUrl = await createCheckoutSession();
      if (checkoutUrl) {
        window.location.href = checkoutUrl;
      } else {
        setError('Failed to create checkout session');
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      setError('An error occurred while creating the checkout session');
    }
  };

  return (
    <div className="subscription-required p-8 bg-white rounded-lg shadow-md max-w-md mx-auto mt-10">
      <h2 className="text-2xl font-bold mb-4 text-gray-800">Subscription Required</h2>

      <div className="mb-6">
        <p className="text-gray-600 mb-4">
          To access the AI Unreal Engine Assistant, you need an active subscription.
        </p>

        <div className="bg-blue-50 p-4 rounded-md mb-6">
          <h3 className="font-semibold text-blue-800 mb-2">Premium Features:</h3>
          <ul className="list-disc pl-5 text-blue-700">
            <li>Natural language to Unreal Engine commands</li>
            <li>Real-time communication with your Unreal Engine projects</li>
            <li>Blueprint generation capabilities</li>
            <li>Access to Gemini Flash AI model</li>
          </ul>
        </div>

        <div className="bg-gray-50 p-4 rounded-md mb-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="font-semibold text-gray-800">Monthly Subscription</h3>
            <span className="text-xl font-bold text-gray-800">$20/month</span>
          </div>
          <p className="text-gray-600 text-sm">Cancel anytime. Unlimited access to all features.</p>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <button
        onClick={handleSubscribe}
        disabled={isLoading}
        className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isLoading ? 'Processing...' : 'Subscribe Now'}
      </button>
    </div>
  );
};

export default SubscriptionRequired;
