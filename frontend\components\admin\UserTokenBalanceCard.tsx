'use client';

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';

interface UserTokenBalanceCardProps {
  user: any;
  onResetTokenUsage: () => void;
}

export default function UserTokenBalanceCard({ user, onResetTokenUsage }: UserTokenBalanceCardProps) {
  if (!user) return null;
  
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    } else {
      return num.toString();
    }
  };
  
  const getDailyUsagePercentage = () => {
    if (!user.daily_token_limit) return 0;
    return Math.min(100, (user.daily_token_usage / user.daily_token_limit) * 100);
  };
  
  const getMonthlyUsagePercentage = () => {
    if (!user.monthly_token_limit) return 0;
    return Math.min(100, (user.monthly_token_usage / user.monthly_token_limit) * 100);
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-sm font-medium">Token Usage</CardTitle>
        <CardDescription>
          Current token usage and limits
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-sm font-medium">Daily Usage</span>
              <span className="text-sm">
                {formatNumber(user.daily_token_usage || 0)} / {formatNumber(user.daily_token_limit || 0)}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full" 
                style={{ width: `${getDailyUsagePercentage()}%` }}
              ></div>
            </div>
          </div>
          
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-sm font-medium">Monthly Usage</span>
              <span className="text-sm">
                {formatNumber(user.monthly_token_usage || 0)} / {formatNumber(user.monthly_token_limit || 0)}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-green-600 h-2 rounded-full" 
                style={{ width: `${getMonthlyUsagePercentage()}%` }}
              ></div>
            </div>
          </div>
          
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-sm font-medium">Additional Tokens</span>
              <span className="text-sm font-bold">
                {formatNumber(user.token_balance || 0)}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button 
          variant="outline" 
          size="sm" 
          className="w-full"
          onClick={onResetTokenUsage}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Reset Usage Counters
        </Button>
      </CardFooter>
    </Card>
  );
}
