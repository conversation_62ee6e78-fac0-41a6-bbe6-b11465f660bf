import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useSubscription } from '../contexts/SubscriptionContext';
import { FiAlertCircle, FiCheckCircle, FiInfo, FiShoppingCart } from 'react-icons/fi';
import { toast } from 'react-hot-toast';

const TokenUsage = ({ purchaseSuccess = false }) => {
  const { user } = useAuth();
  const { hasActiveSubscription, subscriptionPlan } = useSubscription();
  const [tokenUsage, setTokenUsage] = useState(null);
  const [tokenBalance, setTokenBalance] = useState(null);
  const [tokenPackages, setTokenPackages] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showPurchaseOptions, setShowPurchaseOptions] = useState(false);

  // Function to fetch token usage data
  const fetchTokenUsage = async () => {
    try {
      if (!user || !user.id) {
        console.error('No user ID available for token usage fetch');
        setLoading(false);
        return;
      }

      console.log('Fetching token usage for user:', user.id);

      // First try to fetch from our Next.js API proxy endpoint
      const testResponse = await fetch(`/api/token-usage?userId=${user.id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (testResponse.ok) {
        const testData = await testResponse.json();
        console.log('Token usage data from test endpoint:', testData);

        // Log the specific values we're interested in
        console.log('Daily usage:', testData.dailyUsage);
        console.log('Monthly usage:', testData.monthlyUsage);

        // Create a properly structured object
        const formattedData = {
          dailyUsage: {
            used: testData.dailyUsage?.used || 0,
            limit: testData.dailyUsage?.limit || 50000,
            percentage: testData.dailyUsage?.percentage || 0
          },
          monthlyUsage: {
            used: testData.monthlyUsage?.used || 0,
            limit: testData.monthlyUsage?.limit || 1000000,
            percentage: testData.monthlyUsage?.percentage || 0
          },
          plan: testData.plan || 'basic'
        };

        console.log('Formatted data:', formattedData);
        setTokenUsage(formattedData);
        setLoading(false);
        return;
      }

      // If test endpoint fails, fall back to the regular endpoint
      const response = await fetch('/api/subscription/check', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setTokenUsage(data);
      } else {
        console.error('Failed to fetch token usage');
        // Set default token usage data if API returns 404
        if (response.status === 404) {
          setTokenUsage({
            dailyUsage: { used: 0, limit: 50000, percentage: 0 },
            monthlyUsage: { used: 0, limit: 1000000, percentage: 0 },
            plan: 'basic'
          });
        }
      }
    } catch (error) {
      console.error('Error fetching token usage:', error);
      // Set default token usage data on error
      setTokenUsage({
        dailyUsage: { used: 0, limit: 50000, percentage: 0 },
        monthlyUsage: { used: 0, limit: 1000000, percentage: 0 },
        plan: 'basic'
      });
    } finally {
      setLoading(false);
    }
  };

  // Function to fetch token balance
  const fetchTokenBalance = async (isPurchase = false, forceRefresh = false) => {
    try {
      if (!user || !user.id) {
        console.error('No user ID available for token balance fetch');
        return;
      }

      console.log(`Fetching token balance${isPurchase ? ' after purchase' : ''}${forceRefresh ? ' (forced refresh)' : ''} for user:`, user.id);

      // Try direct backend call first
      try {
        const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
        console.log(`Fetching token balance directly from backend${isPurchase ? ' after purchase' : ''}`);

        // Add timestamp to prevent caching and forceRefresh=true to ensure we get the latest data
        const timestamp = Date.now();

        // If this is after a purchase, make multiple attempts to get the updated balance
        if (isPurchase) {
          console.log('This is a post-purchase balance check, making multiple attempts if needed');

          // Try up to 3 times with a delay between attempts
          for (let attempt = 1; attempt <= 3; attempt++) {
            console.log(`Post-purchase balance check attempt ${attempt}/3`);

            const directResponse = await fetch(`${apiUrl}/api/tokens/balance?userId=${user.id}&forceRefresh=true&nocache=${timestamp + attempt}`, {
              headers: {
                'Content-Type': 'application/json',
                'x-user-id': user.id,
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
              }
            });

            if (directResponse.ok) {
              const data = await directResponse.json();
              console.log(`Attempt ${attempt} - Direct token balance data:`, data);
              if (data && typeof data.balance === 'number') {
                setTokenBalance(data);
                return;
              }
            }

            // Wait 1 second before the next attempt
            if (attempt < 3) {
              console.log(`Waiting before attempt ${attempt + 1}...`);
              await new Promise(resolve => setTimeout(resolve, 1000));
            }
          }
        } else {
          // Regular balance check (not after purchase)
          const directResponse = await fetch(`${apiUrl}/api/tokens/balance?userId=${user.id}&forceRefresh=true&nocache=${timestamp}`, {
            headers: {
              'Content-Type': 'application/json',
              'x-user-id': user.id,
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache',
              'Expires': '0'
            }
          });

          if (directResponse.ok) {
            const data = await directResponse.json();
            console.log('Direct token balance data:', data);
            if (data && typeof data.balance === 'number') {
              setTokenBalance(data);
              return;
            }
          }
        }
      } catch (directError) {
        console.error('Error with direct token balance fetch:', directError);
      }

      // Fallback to Next.js API route
      console.log(`Falling back to Next.js API route for token balance${isPurchase ? ' after purchase' : ''}`);

      // Add timestamp to prevent caching and forceRefresh=true to ensure we get the latest data
      const timestamp = Date.now();
      const response = await fetch(`/api/tokens/balance?forceRefresh=true&nocache=${timestamp}`, {
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': user.id,
          'Authorization': 'Bearer dummy-token', // Add a dummy token to avoid "No authorization header provided" error
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
        cache: 'no-store' // Ensure we don't get a cached response
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Token balance data from Next.js API:', data);
        setTokenBalance(data);
      } else {
        console.error('Failed to fetch token balance, status:', response.status);

        // Set default token balance if API returns 404
        if (response.status === 404) {
          setTokenBalance({ balance: 0 });
        }
      }
    } catch (error) {
      console.error('Error fetching token balance:', error);
      // Set default token balance on error
      setTokenBalance({ balance: 0 });
    }
  };

  // Function to fetch token packages
  const fetchTokenPackages = async () => {
    try {
      const response = await fetch('/api/tokens/packages', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setTokenPackages(data);
      } else {
        console.error('Failed to fetch token packages');
        // Set default token packages if API returns 404
        if (response.status === 404) {
          setTokenPackages({
            small: { tokens: 100000, price: 5 },
            medium: { tokens: 500000, price: 20 },
            large: { tokens: 1000000, price: 35 }
          });
        }
      }
    } catch (error) {
      console.error('Error fetching token packages:', error);
      // Set default token packages on error
      setTokenPackages({
        small: { tokens: 100000, price: 5 },
        medium: { tokens: 500000, price: 20 },
        large: { tokens: 1000000, price: 35 }
      });
    }
  };

  // Function to refresh all data
  const refreshData = async (isPurchase = false, forceRefresh = false) => {
    setLoading(true);
    console.log(`Refreshing token usage data${isPurchase ? ' after purchase' : ''}${forceRefresh ? ' (forced refresh)' : ''}`);

    try {
      // Always refresh token balance first if this is after a purchase
      if (isPurchase && user?.id) {
        console.log('refreshData: Purchase detected, refreshing token balance first');
        await fetchTokenBalance(true, true);

        // Add a small delay to ensure UI updates
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      // Fetch token usage data
      await fetchTokenUsage();

      // Only fetch token balance if not already fetched for a purchase
      if (!isPurchase) {
        await fetchTokenBalance(false, forceRefresh);
      } else {
        // If this is after a purchase, fetch token balance again to ensure it's up to date
        console.log('refreshData: Fetching token balance again after purchase to ensure it\'s up to date');
        await fetchTokenBalance(true, true);
      }

      // Fetch token packages
      await fetchTokenPackages();
    } catch (error) {
      console.error('Error refreshing data:', error);
      toast.error('Failed to refresh token data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle purchase tokens
  const handlePurchaseTokens = async (packageId) => {
    try {
      if (!user || !user.id) {
        console.error('No user ID available for token purchase');
        toast.error('You must be logged in to purchase tokens');
        return;
      }

      const response = await fetch('/api/tokens/purchase', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          packageId,
          successUrl: `${window.location.origin}/dashboard?purchase=success&package=${packageId}&userId=${user.id}`,
          cancelUrl: `${window.location.origin}/dashboard?purchase=cancelled`,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        // Redirect to Stripe checkout
        window.location.href = data.url;
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to create checkout session');
      }
    } catch (error) {
      console.error('Error purchasing tokens:', error);
      toast.error('An error occurred while processing your request');
    }
  };

  // Removed forceUpdateBalance function as it's no longer needed

  // Effect to fetch data when user or purchaseSuccess changes
  useEffect(() => {
    if (user) {
      console.log('TokenUsage: User available, fetching data');
      console.log('TokenUsage: Purchase success:', purchaseSuccess);

      if (purchaseSuccess) {
        console.log('TokenUsage: Purchase success detected, refreshing data with priority on token balance');
        // Force a refresh with a small delay to ensure the webhook has processed
        setTimeout(() => {
          refreshData(true, true); // Use refreshData with isPurchase=true and forceRefresh=true
        }, 1000);
      } else {
        // Regular data fetch
        fetchTokenUsage();
        fetchTokenBalance(false);
        fetchTokenPackages();
      }
    }
  }, [user, purchaseSuccess]);

  // Add event listener for refreshTokenBalance event
  useEffect(() => {
    const handleRefreshTokenBalance = (event) => {
      console.log('TokenUsage: Received refreshTokenBalance event', event.detail);
      if (user) {
        const force = event.detail?.force || false;
        const isPurchase = event.detail?.isPurchase || false;
        console.log(`TokenUsage: Refreshing token balance (force=${force}, isPurchase=${isPurchase})`);

        // Use a more aggressive refresh strategy for purchases
        if (isPurchase || force) {
          // Make multiple attempts to refresh the balance
          const attemptRefresh = (attempt = 1, maxAttempts = 3) => {
            console.log(`TokenUsage: Refresh attempt ${attempt}/${maxAttempts}`);

            // Refresh with increasing delays
            setTimeout(async () => {
              try {
                await fetchTokenBalance(true, true);

                // If this is the last attempt, also refresh token usage
                if (attempt === maxAttempts) {
                  await fetchTokenUsage();
                }
              } catch (error) {
                console.error(`TokenUsage: Error in refresh attempt ${attempt}:`, error);

                // Try again if we haven't reached max attempts
                if (attempt < maxAttempts) {
                  attemptRefresh(attempt + 1, maxAttempts);
                }
              }
            }, attempt * 1000); // Increasing delay: 1s, 2s, 3s
          };

          // Start the refresh attempts
          attemptRefresh();
        } else {
          // Regular refresh
          fetchTokenBalance(false, force);
        }
      }
    };

    // Add event listener
    window.addEventListener('refreshTokenBalance', handleRefreshTokenBalance);

    // Clean up
    return () => {
      window.removeEventListener('refreshTokenBalance', handleRefreshTokenBalance);
    };
  }, [user]);

  // Add event listener for tokenBalanceUpdated event (from chat page)
  useEffect(() => {
    if (!user) return;

    const handleTokenBalanceUpdated = (event) => {
      console.log('TokenUsage: Received tokenBalanceUpdated event:', event.detail);

      // Only update if the event is for the current user
      if (event.detail.userId === user.id) {
        console.log('TokenUsage: Updating token balance from chat page event:', event.detail.balance);

        // Update the token balance directly and force a re-render
        setTokenBalance({
          balance: event.detail.balance,
          user_id: user.id,
          updated_at: new Date().toISOString() // Add a timestamp to ensure state change is detected
        });

        // Also force a re-render by briefly showing loading state
        setLoading(true);
        setTimeout(() => setLoading(false), 100);

        // Show a toast notification
        toast.success('Token balance updated from chat activity');
      }
    };

    window.addEventListener('tokenBalanceUpdated', handleTokenBalanceUpdated);

    return () => {
      window.removeEventListener('tokenBalanceUpdated', handleTokenBalanceUpdated);
    };
  }, [user]);

  // Set up a polling mechanism to regularly check for token balance updates
  useEffect(() => {
    if (!user) return;

    console.log('Setting up token balance polling for dashboard');

    // Track rate limiting to implement circuit breaker pattern
    let isRateLimited = false;
    let rateLimitResetTime = 0;
    const RATE_LIMIT_BACKOFF = 300000; // 5 minutes backoff when rate limited

    // Function to check if we're currently rate limited
    const checkRateLimit = () => {
      if (!isRateLimited) return false;

      const now = Date.now();
      if (now > rateLimitResetTime) {
        // Reset rate limit status after backoff period
        console.log('Rate limit backoff period ended, resuming normal operation');
        isRateLimited = false;
        return false;
      }

      // Still rate limited
      const remainingSeconds = Math.ceil((rateLimitResetTime - now) / 1000);
      console.log(`Still in rate limit backoff period. ${remainingSeconds}s remaining.`);
      return true;
    };

    // Function to set rate limited status
    const setRateLimited = () => {
      isRateLimited = true;
      rateLimitResetTime = Date.now() + RATE_LIMIT_BACKOFF;
      console.log(`Rate limited. Backing off for ${RATE_LIMIT_BACKOFF/1000}s until ${new Date(rateLimitResetTime).toLocaleTimeString()}`);

      // Show a toast notification
      toast.warning(`API rate limit reached. Reducing request frequency for 5 minutes.`);
    };

    // Wrapper for fetchTokenBalance that respects rate limiting
    const safeFetchTokenBalance = async (isPurchase = false, forceRefresh = false) => {
      // Skip if we're rate limited (unless it's a critical fetch like after purchase)
      if (checkRateLimit() && !isPurchase) {
        console.log('Skipping token balance check due to active rate limiting');
        return;
      }

      try {
        await fetchTokenBalance(isPurchase, forceRefresh);
      } catch (error) {
        if (error.message && error.message.includes('429')) {
          setRateLimited();
        }
        console.error('Error in safeFetchTokenBalance:', error);
      }
    };

    // Check token balance with a delay when component mounts
    // to avoid overwhelming the API
    const initialCheckTimeout = setTimeout(() => {
      console.log('Performing initial token balance check on dashboard load');
      safeFetchTokenBalance(false, true);
    }, 5000); // Wait 5 seconds before initial check

    // Check token balance less frequently to avoid rate limiting
    const balanceCheckIntervalId = setInterval(() => {
      console.log('Performing regular token balance check on dashboard');
      safeFetchTokenBalance(false, false); // Don't force refresh for regular checks
    }, 300000); // Check every 5 minutes (300000ms)

    // Also check when the page becomes visible again, but only if it's been at least
    // 120 seconds since the last check
    let lastCheckTime = Date.now();

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        const now = Date.now();
        const timeSinceLastCheck = now - lastCheckTime;

        // Only check if it's been at least 120 seconds since the last check
        if (timeSinceLastCheck > 120000) {
          console.log('Page became visible, checking token balance');
          safeFetchTokenBalance(false, false);
          lastCheckTime = now;
        } else {
          console.log('Page became visible, but skipping token balance check (too soon)');
        }
      }
    };

    // Add visibility change listener
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Clean up interval, timeout, and event listener on unmount
    return () => {
      clearInterval(balanceCheckIntervalId);
      clearTimeout(initialCheckTimeout);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [user]);

  // Debug log the current token usage state
  console.log('Current tokenUsage state:', tokenUsage);
  console.log('Current tokenBalance state:', tokenBalance);

  if (loading) {
    return (
      <div className="p-4 bg-white rounded-lg shadow-md">
        <h2 className="text-lg font-semibold mb-4">Token Usage</h2>
        <p>Loading usage data...</p>
      </div>
    );
  }

  return (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">Token Usage</h2>
        <button
          onClick={() => refreshData(false, true)}
          className="flex items-center space-x-1 px-3 py-1 rounded-md text-sm transition-colors bg-blue-50 text-blue-600 hover:bg-blue-100 mr-2"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          <span>Force Refresh</span>
        </button>

        {/* Only show the force update button if the user has purchased tokens */}
        {/* Removed Update Balance and Add 100K Tokens buttons */}
      </div>

      {tokenUsage && (
        <div className="mb-4">
          <div className="mb-2">
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium">Daily Usage</span>
              <span className="text-sm font-medium">
                {(tokenUsage.dailyUsage?.used || 0).toLocaleString()} / {(tokenUsage.dailyUsage?.limit || 50000).toLocaleString()} tokens
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className={`h-2.5 rounded-full ${
                  (tokenUsage.dailyUsage?.percentage || 0) > 90 ? 'bg-red-500' :
                  (tokenUsage.dailyUsage?.percentage || 0) > 75 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                style={{ width: `${Math.min(tokenUsage.dailyUsage?.percentage || 0, 100)}%` }}
              ></div>
            </div>
          </div>

          <div className="mb-2">
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium">Monthly Usage </span>
              <span className="text-sm font-medium">
                {(tokenUsage.monthlyUsage?.used || 0).toLocaleString()} / {(tokenUsage.monthlyUsage?.limit || 1000000).toLocaleString()} tokens
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className={`h-2.5 rounded-full ${
                  (tokenUsage.monthlyUsage?.percentage || 0) > 90 ? 'bg-red-500' :
                  (tokenUsage.monthlyUsage?.percentage || 0) > 75 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                style={{ width: `${Math.min(tokenUsage.monthlyUsage?.percentage || 0, 100)}%` }}
              ></div>
            </div>
          </div>

          <div className="mt-4 text-sm">
            <p className="font-medium">Subscription Plan: {tokenUsage.plan === 'pro' ? 'Pro' : 'Basic'}</p>
            {(tokenUsage.monthlyUsage?.percentage || 0) > 75 && (
              <div className="mt-2 flex items-start p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                <FiAlertCircle className="text-yellow-500 mr-2 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-yellow-700">
                    You're using your subscription at a high rate ({Math.round(tokenUsage.monthlyUsage?.percentage || 0)}% of monthly limit).
                  </p>
                  {tokenUsage.plan !== 'pro' && (
                    <p className="text-yellow-700 mt-1">
                      Consider upgrading to Pro for double the token limit.
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Always show the token balance section */}
      <div className="mt-4 p-3 bg-blue-50 border border-blue-100 rounded-md">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-md font-medium flex items-center">
            <FiInfo className="mr-2 text-blue-500" />
            Additional Tokens
          </h3>
          <button
            onClick={async () => {
              // Only refresh token balance
              setLoading(true);
              try {
                await fetchTokenBalance(false, true);
                toast.success('Token balance refreshed');
              } catch (error) {
                console.error('Error refreshing token balance:', error);
              } finally {
                setLoading(false);
              }
            }}
            className="text-xs px-2 py-1 bg-blue-100 text-blue-600 rounded hover:bg-blue-200 transition-colors"
            disabled={loading}
          >
            {loading ? 'Refreshing...' : 'Refresh Balance'}
          </button>
        </div>
        <p className="text-sm mb-2">
          You have <span className="font-bold">{tokenBalance && typeof tokenBalance.balance === 'number' ? tokenBalance.balance.toLocaleString() : '0'}</span> additional tokens available.
          {loading && <span className="ml-2 text-xs text-blue-500">(refreshing...)</span>}
        </p>
        <p className="text-xs text-gray-600">
          These tokens are used when you exceed your subscription limits.
        </p>
        {purchaseSuccess && (
          <div className="mt-2 p-2 bg-green-50 border border-green-100 rounded-md">
            <p className="text-xs text-green-700 flex items-center">
              <FiCheckCircle className="mr-1 text-green-500" />
              Your token purchase has been processed. The balance above includes your new tokens.
            </p>
            <p className="text-xs text-green-700 mt-1">
              If your balance doesn't look right, click the "Refresh Balance" button above.
            </p>
          </div>
        )}
      </div>

      <div className="mt-4">
        <button
          onClick={() => setShowPurchaseOptions(!showPurchaseOptions)}
          className="flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <FiShoppingCart className="mr-2" />
          {showPurchaseOptions ? 'Hide Options' : 'Buy More Tokens'}
        </button>
      </div>

      {showPurchaseOptions && tokenPackages && (
        <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-3">
          {Object.entries(tokenPackages).map(([id, pkg]) => (
            <div key={id} className="border rounded-md p-3 hover:shadow-md transition-shadow">
              <h3 className="font-medium">{id.charAt(0).toUpperCase() + id.slice(1)} Package</h3>
              <p className="text-2xl font-bold">${pkg.price}</p>
              <p className="text-sm text-gray-600">{(pkg.tokens / 1000).toLocaleString()}K tokens</p>
              <button
                onClick={() => handlePurchaseTokens(id)}
                className="mt-2 w-full px-3 py-1.5 text-sm text-white bg-green-600 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                Purchase
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default TokenUsage;
