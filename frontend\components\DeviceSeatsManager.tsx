'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Laptop, 
  Monitor, 
  Smartphone, 
  Trash2, 
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useAuth } from '../contexts/AuthContext';
import { useSupabaseAuth } from '../contexts/SupabaseAuthContext';

interface Device {
  id: string;
  device_id: string;
  device_name: string;
  platform: string;
  last_active: string;
  created_at: string;
  ip_address: string;
  is_active: boolean;
}

interface DeviceSeatsData {
  devices: Device[];
  seatLimit: number;
  activeCount: number;
}

export default function DeviceSeatsManager() {
  const [deviceSeats, setDeviceSeats] = useState<DeviceSeatsData>({ devices: [], seatLimit: 0, activeCount: 0 });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deletingDevice, setDeletingDevice] = useState<string | null>(null);
  
  // Use authentication contexts
  const { isAuthenticated, token, userId } = useAuth();
  const { user: supabaseUser } = useSupabaseAuth();

  // Get authentication token from multiple sources
  const getAuthToken = async () => {
    // First try the auth context token
    if (token) {
      return token;
    }

    // Try to get token from Supabase session
    try {
      const { createClient } = await import('@supabase/supabase-js');
      const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      );
      
      const { data: { session } } = await supabase.auth.getSession();
      if (session?.access_token) {
        return session.access_token;
      }
    } catch (error) {
      console.error('DeviceSeatsManager: Error getting Supabase session:', error);
    }

    // Fallback to localStorage tokens
    const tokenSources = [
      localStorage.getItem('token'),
      localStorage.getItem('authToken'),
      (() => {
        try {
          const supabaseAuthData = localStorage.getItem('sb-ujiakzkncbxisdatygpo-auth-token');
          if (supabaseAuthData) {
            const parsed = JSON.parse(supabaseAuthData);
            return parsed.access_token;
          }
        } catch (e) {
          return null;
        }
        return null;
      })()
    ].filter(Boolean);

    return tokenSources[0] || null;
  };

  // Get user ID from multiple sources
  const getUserId = () => {
    // First try the auth context
    if (userId) {
      return userId;
    }

    // Try Supabase user
    if (supabaseUser?.id) {
      return supabaseUser.id;
    }

    // For development mode fallback
    if (process.env.NODE_ENV === 'development') {
      return '077f1533-9f81-429c-b1b1-52d9c83f146c';
    }

    return null;
  };

  const fetchDeviceSeats = async () => {
    try {
      const currentUserId = getUserId();
      const authToken = await getAuthToken();

      if (!currentUserId) {
        throw new Error('No user ID available. Please log in again.');
      }

      if (!authToken) {
        throw new Error('No authentication token available. Please log in again.');
      }

      console.log('DeviceSeatsManager: Using user ID:', currentUserId);

      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
      const response = await fetch(`${apiUrl}/api/device/seats`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch device seats`);
      }

      const data = await response.json();
      console.log('DeviceSeatsManager: API response:', data);
      setDeviceSeats(data);
      setError(null);
    } catch (err: any) {
      console.error('DeviceSeatsManager: Error fetching device seats:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const deactivateDevice = async (deviceId: string) => {
    try {
      const currentUserId = getUserId();
      const authToken = await getAuthToken();

      if (!currentUserId) {
        throw new Error('No user ID available. Please log in again.');
      }

      if (!authToken) {
        throw new Error('No authentication token available. Please log in again.');
      }

      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
      const response = await fetch(`${apiUrl}/api/device/seats/${deviceId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Failed to deactivate device`);
      }

      // Refresh the device list
      await fetchDeviceSeats();
    } catch (err: any) {
      console.error('DeviceSeatsManager: Error deactivating device:', err);
      setError(err.message);
    }
  };

  const handleDeactivateDevice = async (deviceId: string) => {
    if (!confirm('Are you sure you want to deactivate this device? You will need to re-authenticate on that device to use it again.')) {
      return;
    }

    setDeletingDevice(deviceId);
    try {
      await deactivateDevice(deviceId);
    } catch (err) {
      // Error handling is already done in deactivateDevice
    } finally {
      setDeletingDevice(null);
    }
  };

  // Fetch device seats on component mount and every 30 seconds
  useEffect(() => {
    // Only fetch if user is authenticated
    if (isAuthenticated && (userId || supabaseUser?.id)) {
      fetchDeviceSeats();
      const interval = setInterval(fetchDeviceSeats, 30000);
      return () => clearInterval(interval);
    } else {
      setIsLoading(false);
      setError('Please log in to view device seats');
    }
  }, [isAuthenticated, userId, supabaseUser?.id]);

  const getDeviceIcon = (platform: string) => {
    const platformLower = platform.toLowerCase();
    if (platformLower.includes('darwin') || platformLower.includes('mac') || platformLower.includes('macos')) {
      return <Laptop className="h-5 w-5" />;
    } else if (platformLower.includes('win') || platformLower.includes('windows')) {
      return <Monitor className="h-5 w-5" />;
    } else if (platformLower.includes('linux')) {
      return <Monitor className="h-5 w-5" />;
    }
    return <Smartphone className="h-5 w-5" />;
  };

  const getPlatformName = (platform: string) => {
    if (platform.includes('darwin') || platform.includes('macOS')) return 'macOS';
    if (platform.includes('win') || platform.includes('Windows')) return 'Windows';
    if (platform.includes('linux') || platform.includes('Linux')) return 'Linux';
    return platform;
  };

  // Don't show anything if user is not authenticated
  if (!isAuthenticated) {
    return null;
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="py-8">
          <div className="text-center text-muted-foreground">
            Loading device information...
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-red-500" />
            Device Seats - Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-2">
                <p>Unable to load device seat information: {error}</p>
                <p className="text-xs">
                  This might be because:
                </p>
                <ul className="text-xs list-disc list-inside space-y-1 ml-2">
                  <li>Authentication token has expired</li>
                  <li>Backend server is not accessible</li>
                  <li>Network connectivity issues</li>
                </ul>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={fetchDeviceSeats}
                  className="mt-2"
                >
                  Retry
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (!deviceSeats.devices.length) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Device Seats</CardTitle>
          <CardDescription>
            Manage your active devices and installations
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center py-8 text-muted-foreground">
            No devices found. Devices will appear here when you use CreateLex Bridge.
          </div>
        </CardContent>
      </Card>
    );
  }

  const { devices, seatLimit, activeCount } = deviceSeats;
  const availableSeats = seatLimit - activeCount;
  const isAtLimit = activeCount >= seatLimit;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Device Seats</CardTitle>
        <CardDescription>
          Manage your active devices and installations
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Seat Usage Summary */}
        <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
          <div>
            <p className="text-sm font-medium">Active Devices</p>
            <p className="text-2xl font-bold">
              {activeCount} / {seatLimit}
            </p>
          </div>
          <div className="text-right">
            <p className="text-sm text-muted-foreground">
              {availableSeats} seat{availableSeats !== 1 ? 's' : ''} available
            </p>
            {isAtLimit && (
              <Badge variant="destructive" className="mt-1">
                Seat limit reached
              </Badge>
            )}
          </div>
        </div>

        {/* Warning Alert */}
        {isAtLimit && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              You've reached your device seat limit. To use CreateLex Bridge on a new device, 
              please deactivate one of your existing devices first.
            </AlertDescription>
          </Alert>
        )}

        {/* Device List */}
        <div className="space-y-3">
          {devices.map((device) => {
            const isCurrentDevice = device.device_id === localStorage.getItem('deviceId');
            
            return (
              <div
                key={device.id}
                className={`p-4 border rounded-lg ${
                  !device.is_active ? 'opacity-60' : ''
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className="mt-0.5">
                      {getDeviceIcon(device.platform)}
                    </div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <p className="font-medium">{device.device_name}</p>
                        {isCurrentDevice && (
                          <Badge variant="secondary" className="text-xs">
                            This device
                          </Badge>
                        )}
                        {device.is_active ? (
                          <Badge variant="default" className="text-xs">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Active
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="text-xs">
                            Inactive
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {getPlatformName(device.platform)}
                      </p>
                      <div className="flex items-center space-x-4 mt-1 text-xs text-muted-foreground">
                        <span className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          Last active {formatDistanceToNow(new Date(device.last_active), { addSuffix: true })}
                        </span>
                        {device.ip_address && (
                          <span>IP: {device.ip_address}</span>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {device.is_active && !isCurrentDevice && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeactivateDevice(device.device_id)}
                      disabled={deletingDevice === device.device_id}
                    >
                      {deletingDevice === device.device_id ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
                      ) : (
                        <Trash2 className="h-4 w-4" />
                      )}
                    </Button>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Help Text */}
        <div className="text-sm text-muted-foreground space-y-2 pt-4 border-t">
          <p>
            • Devices are automatically deactivated after 30 days of inactivity
          </p>
          <p>
            • Each subscription includes {seatLimit} concurrent device seat{seatLimit !== 1 ? 's' : ''}
          </p>
          <p>
            • Deactivating a device will require re-authentication on that device
          </p>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
} 