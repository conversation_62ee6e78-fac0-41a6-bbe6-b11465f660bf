import unreal
import os
from typing import Dict, Any, List, Optional


def create_material_with_color(material_name: str, color: List[float], save_path: str = "/Game/Materials") -> Dict[str, Any]:
    """
    Create a material with a specified base color in Unreal Engine.
    
    Args:
        material_name: Name for the new material
        color: [R, G, B] color values (0-1 range)
        save_path: Path where to save the material (default: "/Game/Materials")
        
    Returns:
        Dictionary with success status and material information
    """
    try:
        # Validate inputs
        if not material_name:
            return {"success": False, "error": "Material name cannot be empty"}
            
        if not isinstance(color, (list, tuple)) or len(color) < 3:
            return {"success": False, "error": "Color must be a list with at least 3 values [R, G, B]"}
            
        # Ensure color values are in valid range (0-1)
        color = [max(0.0, min(1.0, float(c))) for c in color[:3]]
        
        # Ensure save path exists
        if not unreal.EditorAssetLibrary.does_directory_exist(save_path):
            unreal.EditorAssetLibrary.make_directory(save_path)
            
        # Create full material path
        material_path = f"{save_path}/{material_name}"
        
        # Check if material already exists
        if unreal.EditorAssetLibrary.does_asset_exist(material_path):
            unreal.log_warning(f"Material already exists at {material_path}, will overwrite")
            
        # Create material using AssetTools
        asset_tools = unreal.AssetToolsHelpers.get_asset_tools()
        material_factory = unreal.MaterialFactoryNew()
        
        # Create the material asset
        material = asset_tools.create_asset(
            material_name,
            save_path,
            unreal.Material,
            material_factory
        )
        
        if not material:
            return {"success": False, "error": f"Failed to create material asset at {material_path}"}
            
        # Create base color expression node
        base_color_expr = unreal.MaterialEditingLibrary.create_material_expression(
            material, 
            unreal.MaterialExpressionConstant3Vector,
            -400, 0  # Position in material editor
        )
        
        if not base_color_expr:
            return {"success": False, "error": "Failed to create base color expression node"}
            
        # Set the color value
        linear_color = unreal.LinearColor(color[0], color[1], color[2], 1.0)
        base_color_expr.set_editor_property("constant", linear_color)
        
        # Connect to base color input
        success = unreal.MaterialEditingLibrary.connect_material_property(
            base_color_expr, "",  # Empty string for output pin
            unreal.MaterialProperty.MP_BASE_COLOR
        )
        
        if not success:
            unreal.log_warning("Failed to connect base color expression, but continuing...")
            
        # Recompile the material
        unreal.MaterialEditingLibrary.recompile_material(material)
        
        # Save the asset
        unreal.EditorAssetLibrary.save_asset(material_path)
        
        # Log success
        unreal.log(f"Successfully created material '{material_name}' at {material_path} with color {color}")
        
        return {
            "success": True,
            "material_path": material_path,
            "material_name": material_name,
            "color": color,
            "message": f"Successfully created material '{material_name}' with color {color}"
        }
        
    except Exception as e:
        error_msg = f"Error creating material: {str(e)}"
        unreal.log_error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "message": f"Failed to create material '{material_name}': {str(e)}"
        }


def create_pbr_material(material_name: str, properties: Dict[str, Any], save_path: str = "/Game/Materials") -> Dict[str, Any]:
    """
    Create a PBR (Physically Based Rendering) material with specified properties.
    
    Args:
        material_name: Name for the new material
        properties: Dictionary containing PBR properties:
            - base_color: [R, G, B] base color values (0-1)
            - metallic: Metallic value (0-1)
            - specular: Specular value (0-1)
            - roughness: Roughness value (0-1)
            - emissive_color: [R, G, B] emissive color values (0-1)
            - emissive_intensity: Emissive intensity multiplier (0+)
            - opacity: Opacity value (0-1)
        save_path: Path where to save the material
        
    Returns:
        Dictionary with success status and material information
    """
    try:
        # Extract properties with defaults
        base_color = properties.get('base_color', [0.5, 0.5, 0.5])
        metallic = properties.get('metallic', 0.0)
        specular = properties.get('specular', 0.5)
        roughness = properties.get('roughness', 0.5)
        emissive_color = properties.get('emissive_color', [0.0, 0.0, 0.0])
        emissive_intensity = properties.get('emissive_intensity', 0.0)
        opacity = properties.get('opacity', 1.0)
        
        # Ensure save path exists
        if not unreal.EditorAssetLibrary.does_directory_exist(save_path):
            unreal.EditorAssetLibrary.make_directory(save_path)
            
        # Create full material path
        material_path = f"{save_path}/{material_name}"
        
        # Create material
        asset_tools = unreal.AssetToolsHelpers.get_asset_tools()
        material_factory = unreal.MaterialFactoryNew()
        
        material = asset_tools.create_asset(
            material_name,
            save_path,
            unreal.Material,
            material_factory
        )
        
        if not material:
            return {"success": False, "error": f"Failed to create material asset at {material_path}"}
            
        # Create and connect base color
        if base_color:
            base_color_expr = unreal.MaterialEditingLibrary.create_material_expression(
                material, unreal.MaterialExpressionConstant3Vector, -400, 0
            )
            color_val = unreal.LinearColor(base_color[0], base_color[1], base_color[2], 1.0)
            base_color_expr.set_editor_property("constant", color_val)
            unreal.MaterialEditingLibrary.connect_material_property(
                base_color_expr, "", unreal.MaterialProperty.MP_BASE_COLOR
            )
            
        # Create and connect metallic
        metallic_expr = unreal.MaterialEditingLibrary.create_material_expression(
            material, unreal.MaterialExpressionConstant, -400, 100
        )
        metallic_expr.set_editor_property("R", float(metallic))
        unreal.MaterialEditingLibrary.connect_material_property(
            metallic_expr, "", unreal.MaterialProperty.MP_METALLIC
        )
        
        # Create and connect specular
        specular_expr = unreal.MaterialEditingLibrary.create_material_expression(
            material, unreal.MaterialExpressionConstant, -400, 200
        )
        specular_expr.set_editor_property("R", float(specular))
        unreal.MaterialEditingLibrary.connect_material_property(
            specular_expr, "", unreal.MaterialProperty.MP_SPECULAR
        )
        
        # Create and connect roughness
        roughness_expr = unreal.MaterialEditingLibrary.create_material_expression(
            material, unreal.MaterialExpressionConstant, -400, 300
        )
        roughness_expr.set_editor_property("R", float(roughness))
        unreal.MaterialEditingLibrary.connect_material_property(
            roughness_expr, "", unreal.MaterialProperty.MP_ROUGHNESS
        )
        
        # Create and connect emissive if needed
        if emissive_intensity > 0 and any(c > 0 for c in emissive_color):
            emissive_expr = unreal.MaterialEditingLibrary.create_material_expression(
                material, unreal.MaterialExpressionConstant3Vector, -400, 400
            )
            emissive_val = unreal.LinearColor(
                emissive_color[0] * emissive_intensity,
                emissive_color[1] * emissive_intensity,
                emissive_color[2] * emissive_intensity,
                1.0
            )
            emissive_expr.set_editor_property("constant", emissive_val)
            unreal.MaterialEditingLibrary.connect_material_property(
                emissive_expr, "", unreal.MaterialProperty.MP_EMISSIVE_COLOR
            )
            
        # Handle opacity if not fully opaque
        if opacity < 1.0:
            # Set blend mode to translucent
            material.set_editor_property("blend_mode", unreal.BlendMode.BLEND_TRANSLUCENT)
            
            # Create and connect opacity
            opacity_expr = unreal.MaterialEditingLibrary.create_material_expression(
                material, unreal.MaterialExpressionConstant, -400, 500
            )
            opacity_expr.set_editor_property("R", float(opacity))
            unreal.MaterialEditingLibrary.connect_material_property(
                opacity_expr, "", unreal.MaterialProperty.MP_OPACITY
            )
            
        # Recompile and save
        unreal.MaterialEditingLibrary.recompile_material(material)
        unreal.EditorAssetLibrary.save_asset(material_path)
        
        unreal.log(f"Successfully created PBR material '{material_name}' at {material_path}")
        
        return {
            "success": True,
            "material_path": material_path,
            "material_name": material_name,
            "message": f"Successfully created PBR material '{material_name}'"
        }
        
    except Exception as e:
        error_msg = f"Error creating PBR material: {str(e)}"
        unreal.log_error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "message": f"Failed to create PBR material '{material_name}': {str(e)}"
        } 