const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');
const path = require('path');
const fs = require('fs');

// Load environment variables from .env.production in production
if (process.env.NODE_ENV === 'production') {
  try {
    // First try to load dotenv if it's not already loaded
    try {
      require('dotenv').config({ path: path.join(__dirname, '.env.production') });
      console.log('Loaded environment variables from .env.production');
    } catch (loadError) {
      console.warn('Failed to load .env.production file:', loadError.message);
      console.log('Using environment variables from system');
    }

    // Check for required environment variables
    const requiredEnvVars = [
      'ANTHROPIC_API_KEY',
      'DEEPSEEK_API_KEY',
      'OPENAI_API_KEY',
      'GOOGLE_API_KEY',
      'SUPABASE_SERVICE_KEY'
    ];

    const missingEnvVars = requiredEnvVars.filter(key => !process.env[key]);

    if (missingEnvVars.length > 0) {
      console.warn('Missing required environment variables:', missingEnvVars.join(', '));
      console.warn('Some features may not work correctly without these variables');
    }

    // Log available environment variables for debugging (without revealing values)
    console.log('Environment variables status:');
    console.log('ANTHROPIC_API_KEY:', process.env.ANTHROPIC_API_KEY ? '[SET]' : '[NOT SET]');
    console.log('OPENAI_API_KEY:', process.env.OPENAI_API_KEY ? '[SET]' : '[NOT SET]');
    console.log('DEEPSEEK_API_KEY:', process.env.DEEPSEEK_API_KEY ? '[SET]' : '[NOT SET]');
    console.log('GOOGLE_API_KEY:', process.env.GOOGLE_API_KEY ? '[SET]' : '[NOT SET]');
    console.log('SUPABASE_SERVICE_KEY:', process.env.SUPABASE_SERVICE_KEY ? '[SET]' : '[NOT SET]');
  } catch (error) {
    console.warn('Error checking environment variables:', error.message);
  }
}

const dev = process.env.NODE_ENV !== 'production';
console.log(`Running in ${dev ? 'development' : 'production'} mode`);

// Check if we're using the standalone output
const isStandalone = fs.existsSync(path.join(__dirname, '.next/standalone'));
console.log(`Using ${isStandalone ? 'standalone' : 'regular'} Next.js output`);

// Determine the correct Next.js directory
let nextDir;
if (isStandalone) {
  nextDir = path.join(__dirname, '.next/standalone/.next');
  if (!fs.existsSync(nextDir)) {
    nextDir = path.join(__dirname, '.next');
  }
} else {
  nextDir = path.join(__dirname, '.next');
}

console.log(`Using Next.js directory: ${nextDir}`);

// Check if .next directory exists
if (!dev && !fs.existsSync(nextDir)) {
  console.log(`Creating .next directory at ${nextDir}`);
  fs.mkdirSync(nextDir, { recursive: true });
}

// Create required directories
const requiredDirs = [
  path.join(nextDir, 'server'),
  path.join(nextDir, 'static'),
  path.join(nextDir, 'cache')
];

for (const dir of requiredDirs) {
  if (!fs.existsSync(dir)) {
    console.log(`Creating directory: ${dir}`);
    fs.mkdirSync(dir, { recursive: true });
  }
}

// Create BUILD_ID file if it doesn't exist
const buildIdPath = path.join(nextDir, 'BUILD_ID');
if (!fs.existsSync(buildIdPath)) {
  console.log(`Creating BUILD_ID file at ${buildIdPath}`);
  fs.writeFileSync(buildIdPath, Date.now().toString());
}

// Create prerender-manifest.json if it doesn't exist
const prerenderManifestPath = path.join(nextDir, 'prerender-manifest.json');
if (!fs.existsSync(prerenderManifestPath)) {
  console.log(`Creating empty prerender-manifest.json at ${prerenderManifestPath}`);
  fs.writeFileSync(prerenderManifestPath, JSON.stringify({
    version: 4,
    routes: {},
    dynamicRoutes: {},
    preview: {
      previewModeId: "development-id",
      previewModeSigningKey: "development-signing-key",
      previewModeEncryptionKey: "development-encryption-key"
    },
    notFoundRoutes: []
  }));
}

// Create build-manifest.json if it doesn't exist
const buildManifestPath = path.join(nextDir, 'build-manifest.json');
if (!fs.existsSync(buildManifestPath)) {
  console.log(`Creating empty build-manifest.json at ${buildManifestPath}`);
  fs.writeFileSync(buildManifestPath, JSON.stringify({
    polyfillFiles: [],
    devFiles: [],
    ampDevFiles: [],
    lowPriorityFiles: [],
    rootMainFiles: [],
    pages: {
      "/_app": []
    },
    ampFirstPages: []
  }));
}

// Create routes-manifest.json if it doesn't exist
const routesManifestPath = path.join(nextDir, 'routes-manifest.json');
if (!fs.existsSync(routesManifestPath)) {
  console.log(`Creating routes-manifest.json at ${routesManifestPath}`);
  fs.writeFileSync(routesManifestPath, JSON.stringify({
    version: 3,
    pages404: true,
    basePath: "",
    redirects: [],
    headers: [],
    dynamicRoutes: [],
    staticRoutes: [],
    dataRoutes: [],
    rewrites: []
  }));
}

// Create required-server-files.json if it doesn't exist
const requiredServerFilesPath = path.join(nextDir, 'required-server-files.json');
if (!fs.existsSync(requiredServerFilesPath)) {
  console.log(`Creating required-server-files.json at ${requiredServerFilesPath}`);
  fs.writeFileSync(requiredServerFilesPath, JSON.stringify({
    version: 1,
    config: {
      distDir: ".next",
      buildId: fs.readFileSync(buildIdPath, 'utf8'),
      rewrites: { beforeFiles: [], afterFiles: [], fallback: [] }
    }
  }));
}

// Create pages-manifest.json if it doesn't exist
const serverDir = path.join(nextDir, 'server');
if (!fs.existsSync(serverDir)) {
  console.log(`Creating server directory at ${serverDir}`);
  fs.mkdirSync(serverDir, { recursive: true });
}

const pagesManifestPath = path.join(serverDir, 'pages-manifest.json');
if (!fs.existsSync(pagesManifestPath)) {
  console.log(`Creating pages-manifest.json at ${pagesManifestPath}`);
  fs.writeFileSync(pagesManifestPath, JSON.stringify({
    "/_app": "pages/_app.js",
    "/_error": "pages/_error.js",
    "/_document": "pages/_document.js"
  }));
}

// Configure Next.js app
const app = next({
  dev,
  dir: __dirname,
  conf: {
    // Force dynamic rendering for all pages
    staticPageGenerationTimeout: 0,
    // Disable static generation
    experimental: {
      workerThreads: false,
      cpus: 1
    },
    // Disable static exports
    output: 'standalone'
  }
});

const handle = app.getRequestHandler();
const PORT = process.env.PORT || 3000; // Use default port 3000

// Log environment variables (excluding sensitive ones)
console.log('Environment variables:');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('PORT:', PORT);
console.log('NEXT_PUBLIC_API_URL:', process.env.NEXT_PUBLIC_API_URL);
console.log('NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL ? '[SET]' : '[NOT SET]');
console.log('NEXT_PUBLIC_BYPASS_AUTH:', process.env.NEXT_PUBLIC_BYPASS_AUTH);
console.log('NEXT_PUBLIC_BYPASS_SUBSCRIPTION:', process.env.NEXT_PUBLIC_BYPASS_SUBSCRIPTION);

// Log API keys status (without revealing the actual keys)
console.log('API Keys Status:');
console.log('ANTHROPIC_API_KEY:', process.env.ANTHROPIC_API_KEY ? '[SET]' : '[NOT SET]');
console.log('OPENAI_API_KEY:', process.env.OPENAI_API_KEY ? '[SET]' : '[NOT SET]');
console.log('DEEPSEEK_API_KEY:', process.env.DEEPSEEK_API_KEY ? '[SET]' : '[NOT SET]');
console.log('GROQ_API_KEY:', process.env.GROQ_API_KEY ? '[SET]' : '[NOT SET]');
console.log('XAI_API_KEY:', process.env.XAI_API_KEY ? '[SET]' : '[NOT SET]');

// Create a fallback handler for when Next.js fails to initialize
const fallbackHandler = (req, res) => {
  res.statusCode = 200;
  res.setHeader('Content-Type', 'text/html');
  res.end(`
    <!DOCTYPE html>
    <html>
      <head>
        <title>CreateLex AI</title>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f5f5f5;
            color: #333;
          }
          .container {
            max-width: 600px;
            padding: 2rem;
            text-align: center;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }
          h1 {
            margin-top: 0;
            color: #2563eb;
          }
          p {
            line-height: 1.6;
          }
          .button {
            display: inline-block;
            background-color: #2563eb;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            text-decoration: none;
            margin-top: 1rem;
            font-weight: 500;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>CreateLex AI</h1>
          <p>Our application is currently starting up. Please check back in a moment.</p>
          <p>If you continue to see this message, please contact support.</p>
          <a href="/" class="button">Refresh Page</a>
        </div>
      </body>
    </html>
  `);
};

// Health check endpoint handler
const healthCheckHandler = (req, res) => {
  res.statusCode = 200;
  res.setHeader('Content-Type', 'application/json');
  res.end(JSON.stringify({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    version: process.env.APP_VERSION || 'unknown'
  }));
};

// Try to prepare the Next.js app, but fall back to a simple server if it fails
app.prepare()
  .then(() => {
    console.log('Next.js app prepared successfully');

    const server = createServer((req, res) => {
      const parsedUrl = parse(req.url, true);

      // Handle health check endpoint
      if (parsedUrl.pathname === '/health' || parsedUrl.pathname === '/api/health') {
        return healthCheckHandler(req, res);
      }

      // Add dynamic=true query parameter to force dynamic rendering
      parsedUrl.query.dynamic = 'true';

      // Add no-cache headers for API routes
      if (parsedUrl.pathname.startsWith('/api/')) {
        res.setHeader('Cache-Control', 'no-store, must-revalidate');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');
      }

      // Handle the request
      handle(req, res, parsedUrl);
    });

    // Add graceful shutdown
    process.on('SIGTERM', () => {
      console.log('SIGTERM received, shutting down gracefully');
      server.close(() => {
        console.log('Server closed');
        process.exit(0);
      });
    });

    process.on('SIGINT', () => {
      console.log('SIGINT received, shutting down gracefully');
      server.close(() => {
        console.log('Server closed');
        process.exit(0);
      });
    });

    server.listen(PORT, (err) => {
      if (err) throw err;
      console.log(`> Ready on http://localhost:${PORT}`);
    });

    // Handle server errors
    server.on('error', (err) => {
      console.error('Server error:', err);
      if (err.code === 'EADDRINUSE') {
        console.error(`Port ${PORT} is already in use. Try using a different port.`);
      }
      process.exit(1);
    });
  })
  .catch(err => {
    console.error('Error preparing Next.js app:', err);
    console.log('Starting fallback server...');

    // Start a simple fallback server
    const server = createServer((req, res) => {
      const parsedUrl = parse(req.url, true);

      // Handle health check endpoint
      if (parsedUrl.pathname === '/health' || parsedUrl.pathname === '/api/health') {
        return healthCheckHandler(req, res);
      }

      // Fallback handler for all other routes
      fallbackHandler(req, res);
    });

    server.listen(PORT, (err) => {
      if (err) {
        console.error('Failed to start fallback server:', err);
        process.exit(1);
      }
      console.log(`> Fallback server ready on http://localhost:${PORT}`);
    });
  });
