const express = require('express');
const router = express.Router();
const { supabase } = require('../../services/supabaseService');
const { authenticateJWTWithFallback } = require('../../middleware/auth');

/**
 * @route POST /api/test/token-fallback
 * @description Test endpoint to verify token fallback mechanism
 * @access Private
 */
router.post('/', authenticateJWTWithFallback, async (req, res) => {
  try {
    // Get user ID from the authenticated user object
    const userId = req.user.id;

    // Generate a unique operation ID for tracking
    const operationId = `token-fallback-test-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;

    console.log(`[${operationId}] Starting token fallback test for user ${userId}`);

    // Step 1: Get current token usage and balance
    const tokenUsageService = require('../../services/tokenUsageService');
    const tokenPurchaseService = require('../../services/tokenPurchaseService');

    // Get current usage status
    const initialUsageStatus = await tokenUsageService.checkUsageLimits(userId);

    // Get current token balance
    const initialTokenBalance = await tokenPurchaseService.getUserTokenBalance(userId);

    console.log(`[${operationId}] Initial state:`);
    console.log(`[${operationId}] - Daily usage: ${initialUsageStatus.dailyUsage.used}/${initialUsageStatus.dailyUsage.limit} (${initialUsageStatus.dailyUsage.percentage}%)`);
    console.log(`[${operationId}] - Monthly usage: ${initialUsageStatus.monthlyUsage.used}/${initialUsageStatus.monthlyUsage.limit} (${initialUsageStatus.monthlyUsage.percentage}%)`);
    console.log(`[${operationId}] - Additional token balance: ${initialTokenBalance.balance}`);

    // Step 2: Set daily usage to max limit (simulate used up daily tokens)
    // Get the current date
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    console.log(`[${operationId}] Setting daily usage to limit for user ${userId}`);

    // Get the daily limit from the usage status
    const dailyLimit = initialUsageStatus.dailyUsage.limit;
    console.log(`[${operationId}] Daily limit: ${dailyLimit}`);

    // Since daily_token_usage is a VIEW, we need to insert directly into token_usage
    // We'll insert a record that will make the total daily usage equal to the limit

    // First, get the current daily usage
    const currentDailyUsage = initialUsageStatus.dailyUsage.used;
    console.log(`[${operationId}] Current daily usage: ${currentDailyUsage}`);

    // Calculate how many tokens we need to add to reach the limit
    const tokensToAdd = dailyLimit - currentDailyUsage;
    console.log(`[${operationId}] Tokens to add to reach limit: ${tokensToAdd}`);

    if (tokensToAdd <= 0) {
      console.log(`[${operationId}] Daily usage already at or above limit, no need to add more`);
    } else {
      // Insert a record into token_usage to simulate usage up to the limit
      const { data: insertData, error: insertError } = await supabase
        .from('token_usage')
        .insert({
          user_id: userId,
          model_id: 'test-model',
          prompt_tokens: Math.floor(tokensToAdd * 0.2), // 20% prompt tokens
          completion_tokens: Math.floor(tokensToAdd * 0.8), // 80% completion tokens
          total_tokens: tokensToAdd,
          request_type: 'test',
          subscription_plan: initialUsageStatus.plan,
          timestamp: new Date().toISOString()
        })
        .select();

      if (insertError) {
        console.error(`[${operationId}] Error inserting token usage:`, insertError);
        return res.status(500).json({
          error: 'Failed to insert token usage',
          details: insertError.message,
          operationId
        });
      }

      console.log(`[${operationId}] Successfully inserted token usage record to reach daily limit`);
    }

    // Step 3: Verify that daily limit is reached by checking usage again
    const afterSetUsageStatus = await tokenUsageService.checkUsageLimits(userId);
    console.log(`[${operationId}] After setting daily usage:`);
    console.log(`[${operationId}] - Daily usage: ${afterSetUsageStatus.dailyUsage.used}/${afterSetUsageStatus.dailyUsage.limit} (${afterSetUsageStatus.dailyUsage.percentage}%)`);
    console.log(`[${operationId}] - Daily limit reached: ${afterSetUsageStatus.dailyUsage.exceeded}`);

    // Step 4: Make a test request that should use additional tokens
    // Define the number of tokens to use for the test
    const tokensToUse = 1000; // Use 1000 tokens for the test
    console.log(`[${operationId}] Using ${tokensToUse} tokens, which should come from additional token balance`);

    // Use the token purchase service to deduct tokens
    const useTokensResult = await tokenPurchaseService.useTokens(
      userId,
      tokensToUse,
      operationId
    );

    console.log(`[${operationId}] Token usage result:`, useTokensResult);

    // Emit a token balance updated event
    if (useTokensResult && useTokensResult.success && global.eventEmitter) {
      global.eventEmitter.emit('tokenBalanceUpdated', {
        userId,
        balance: useTokensResult.balance
      });
      console.log(`[${operationId}] Emitted tokenBalanceUpdated event with balance: ${useTokensResult.balance}`);
    }

    // Step 4: Get updated token balance
    const updatedTokenBalance = await tokenPurchaseService.getUserTokenBalance(userId);

    // Step 5: Get updated usage status
    const updatedUsageStatus = await tokenUsageService.checkUsageLimits(userId);

    console.log(`[${operationId}] Updated state:`);
    console.log(`[${operationId}] - Daily usage: ${updatedUsageStatus.dailyUsage.used}/${updatedUsageStatus.dailyUsage.limit} (${updatedUsageStatus.dailyUsage.percentage}%)`);
    console.log(`[${operationId}] - Monthly usage: ${updatedUsageStatus.monthlyUsage.used}/${updatedUsageStatus.monthlyUsage.limit} (${updatedUsageStatus.monthlyUsage.percentage}%)`);
    console.log(`[${operationId}] - Additional token balance: ${updatedTokenBalance.balance}`);

    // Return the results
    return res.json({
      success: true,
      operationId,
      initialState: {
        dailyUsage: {
          used: initialUsageStatus.dailyUsage.used,
          limit: initialUsageStatus.dailyUsage.limit,
          percentage: initialUsageStatus.dailyUsage.percentage,
          exceeded: initialUsageStatus.dailyUsage.exceeded
        },
        monthlyUsage: {
          used: initialUsageStatus.monthlyUsage.used,
          limit: initialUsageStatus.monthlyUsage.limit,
          percentage: initialUsageStatus.monthlyUsage.percentage,
          exceeded: initialUsageStatus.monthlyUsage.exceeded
        },
        tokenBalance: initialTokenBalance.balance
      },
      afterSetLimit: {
        dailyUsage: {
          used: afterSetUsageStatus.dailyUsage.used,
          limit: afterSetUsageStatus.dailyUsage.limit,
          percentage: afterSetUsageStatus.dailyUsage.percentage,
          exceeded: afterSetUsageStatus.dailyUsage.exceeded
        }
      },
      action: {
        setDailyUsageToLimit: dailyLimit,
        tokensAdded: tokensToAdd > 0 ? tokensToAdd : 0,
        tokensUsed: tokensToUse
      },
      updatedState: {
        dailyUsage: {
          used: updatedUsageStatus.dailyUsage.used,
          limit: updatedUsageStatus.dailyUsage.limit,
          percentage: updatedUsageStatus.dailyUsage.percentage,
          exceeded: updatedUsageStatus.dailyUsage.exceeded
        },
        monthlyUsage: {
          used: updatedUsageStatus.monthlyUsage.used,
          limit: updatedUsageStatus.monthlyUsage.limit,
          percentage: updatedUsageStatus.monthlyUsage.percentage,
          exceeded: updatedUsageStatus.monthlyUsage.exceeded
        },
        tokenBalance: updatedTokenBalance.balance,
        tokenBalanceChange: initialTokenBalance.balance - updatedTokenBalance.balance
      },
      useTokensResult
    });
  } catch (error) {
    console.error('Error in token fallback test:', error);
    return res.status(500).json({
      error: 'Failed to run token fallback test',
      details: error.message
    });
  }
});

module.exports = router;
