require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: SUPABASE_URL and SUPABASE_SERVICE_KEY must be set in .env file');
  process.exit(1);
}

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key (first 10 chars):', supabaseKey.substring(0, 10) + '...');

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function retrieveUser() {
  try {
    const userId = '8075c290-0943-4d3e-94a7-dcdf42bda6c6';
    
    console.log(`Retrieving user with ID: ${userId}`);
    
    // Try different query approaches
    
    // Approach 1: Using .single()
    console.log('\nApproach 1: Using .single()');
    const { data: user1, error: error1 } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();
    
    console.log('Result:', user1 ? 'User found' : 'User not found');
    console.log('Error:', error1);
    
    // Approach 2: Without .single()
    console.log('\nApproach 2: Without .single()');
    const { data: users2, error: error2 } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId);
    
    console.log('Result:', users2 && users2.length > 0 ? `${users2.length} users found` : 'No users found');
    if (users2 && users2.length > 0) {
      console.log('First user:', users2[0]);
    }
    console.log('Error:', error2);
    
    // Approach 3: Using a different column
    console.log('\nApproach 3: Using email instead of ID');
    const { data: users3, error: error3 } = await supabase
      .from('users')
      .select('*')
      .eq('email', '<EMAIL>');
    
    console.log('Result:', users3 && users3.length > 0 ? `${users3.length} users found` : 'No users found');
    if (users3 && users3.length > 0) {
      console.log('First user:', users3[0]);
    }
    console.log('Error:', error3);
    
    // Approach 4: List all users
    console.log('\nApproach 4: List all users');
    const { data: allUsers, error: errorAll } = await supabase
      .from('users')
      .select('*')
      .limit(10);
    
    console.log('Result:', allUsers && allUsers.length > 0 ? `${allUsers.length} users found` : 'No users found');
    if (allUsers && allUsers.length > 0) {
      console.log('Users:', allUsers);
    }
    console.log('Error:', errorAll);
    
    // Approach 5: Check if the table exists
    console.log('\nApproach 5: Check if the table exists');
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public');
    
    console.log('Tables:', tables);
    console.log('Error:', tablesError);
    
    process.exit(0);
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

retrieveUser();
