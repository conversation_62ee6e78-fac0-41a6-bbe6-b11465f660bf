@echo off
setlocal

REM =============================================
REM   START MCP BRIDGE WITH BYPASS MODE - SIMPLE
REM =============================================

title MCP Bridge Bypass Mode - Simple

echo ========================================
echo   MCP BRIDGE BYPASS MODE - SIMPLE
echo ========================================

set BRIDGE_DIR=C:\Dev\AiWebplatform\createlex-bridge

REM 1. THOROUGH CLEANUP - Kill ALL bridge and MCP processes
echo ^> Performing thorough cleanup of all MCP/Bridge processes...
call "%~dp0kill_all_mcp.bat"

REM 2. Check if bridge directory exists
if not exist "%BRIDGE_DIR%" (
    echo ^> ❌ ERROR: Bridge directory not found!
    echo ^>    Expected: %BRIDGE_DIR%
    pause
    exit /b 1
)

REM 3. Start bridge using the bypass npm script
echo ^> Starting bridge with bypass environment variables...
cd /d "%BRIDGE_DIR%"

echo ^> Setting bypass environment variables...
set NODE_ENV=development
set DEV_MODE=true
set BYPASS_SUBSCRIPTION=true
set BYPASS_LOGIN=true
set AUTO_START_MCP=true
set SKIP_AUTH=true
set CREATELEX_BASE_URL=http://localhost:3000
set API_BASE_URL=http://localhost:5001/api

echo ^> Running: npm run start:bypass
start "MCP-Bridge-Bypass-Window" cmd /k "title MCP-Bridge-Bypass-Active && npm run start:bypass"

REM 4. Wait for startup and check status
echo ^> Waiting for bridge to start...
timeout /t 20 /nobreak >nul

echo ^> Checking MCP server status...
netstat -an | find ":9877" >nul
if %errorlevel% equ 0 (
    echo ^> ✅ MCP Server is running on port 9877!
    goto :SUCCESS
) else (
    echo ^> ⚠️  MCP Server may still be starting...
    timeout /t 10 /nobreak >nul
    
    netstat -an | find ":9877" >nul
    if %errorlevel% equ 0 (
        echo ^> ✅ MCP Server started successfully!
        goto :SUCCESS
    ) else (
        echo ^> ⚠️  MCP Server startup may need verification
        echo ^>    Check the bridge window for status
        goto :PARTIAL
    )
)

:SUCCESS
echo.
echo ========================================
echo   MCP BRIDGE BYPASS SUCCESS!
echo ========================================
echo.
echo Status:
echo - Bridge App: RUNNING in bypass mode
echo - Authentication: BYPASSED
echo - MCP Server: AUTO-STARTED on port 9877
echo - Mode: Development/Testing
echo.
goto :END

:PARTIAL
echo.
echo ========================================
echo   BRIDGE STARTED - VERIFY MCP STATUS
echo ========================================
echo.
echo The bridge window should be open.
echo Check if MCP auto-started within the app.
echo.

:END
timeout /t 3 /nobreak >nul
exit /b 0