# Sync with AI Button Improvements

## Overview
Enhanced the "Sync with AI" button functionality to automatically use Level Blueprint as fallback context when no specific Blueprint is selected. This provides comprehensive context for AI-assisted Blueprint development even when nothing is explicitly selected.

## Key Improvements

### 1. Automatic Level Blueprint Fallback
- **Previous Behavior**: When no Blueprint was selected, the sync would return project context with suggestions
- **New Behavior**: Automatically detects and uses Level Blueprint as fallback context
- **Benefit**: Always provides actionable Blueprint context for AI assistance

### 2. Enhanced Context Detection
The improved system uses multiple detection methods:

1. **Primary Detection**: Selected/open Blueprint in editor
2. **Fallback Method 1**: `EditorLevelLibrary.get_level_script_blueprint()`
3. **Fallback Method 2**: Via current world's `get_level_script_blueprint()`
4. **Fallback Method 3**: Asset Registry search for `LevelScriptBlueprint`

### 3. New MCP Tool Function
Added `get_context_window_reference()` tool that:
- Intelligently determines the best context source
- Provides comprehensive Blueprint analysis
- Shows nodes, variables, functions, and statistics
- Clearly indicates when using fallback context

## Technical Implementation

### Modified Files
1. `CreatelexGenAI/Content/Python/handlers/blueprint_context_handler.py`
   - Added `_get_level_blueprint_as_fallback()` method
   - Enhanced fallback logic in `_extract_local_context()`

2. MCP Server Files (all variants):
   - `createlex-bridge/src/python/mcp_stdio_simple.py`
   - `mcp-server-createlexgenai/mcp_stdio_simple.py`
   - `storage/mcp-servers/free/latest/mcp_server_protected.py`
   - `backend/storage/mcp-servers/basic/latest/mcp_server_stdio.py`

### New MCP Tool Function
```python
@mcp.tool()
def get_context_window_reference() -> str:
    """Get context window reference from the current Blueprint window or Level Blueprint as fallback."""
```

## Usage Examples

### When Blueprint is Selected
```
🎯 **Context Window Reference (Active Blueprint)**

**Blueprint:** BP_ThirdPersonCharacter
**Type:** Blueprint Class
**Path:** /Game/ThirdPerson/Blueprints/BP_ThirdPersonCharacter
**Event Graph Nodes:** 15
**Variables (3):**
  - Health: Float
  - MaxSpeed: Float
  - IsJumping: Boolean
**Functions (2):**
  - TakeDamage()
  - ResetHealth()
**Total Nodes:** 15
```

### When Nothing is Selected (Level Blueprint Fallback)
```
🎯 **Context Window Reference (Level Blueprint Fallback)**

Since no specific Blueprint was selected, automatically using Level Blueprint context:

**Blueprint:** Lvl_ThirdPerson
**Type:** Level Blueprint
**Path:** /Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson
**Event Graph Nodes:** 8
**Key Nodes:**
  - Event BeginPlay (Event)
  - Print String (CallFunction)
  - Delay (CallFunction)
**Total Nodes:** 8
**Fallback Reason:** No specific Blueprint selected - using Level Blueprint
```

## Benefits

1. **Always Available Context**: Users always get meaningful Blueprint context, even when nothing is selected
2. **Seamless Experience**: No need to manually open/select a Blueprint to get AI assistance
3. **Level Blueprint Focus**: Encourages use of Level Blueprint for game logic, which is a common pattern
4. **Comprehensive Analysis**: Provides detailed information about nodes, variables, and functions
5. **Clear Feedback**: Users understand when fallback context is being used

## Testing

To test the improvements:

1. **Test with Selected Blueprint**:
   - Open any Blueprint in the editor
   - Click "Sync with AI" button
   - Should show active Blueprint context

2. **Test with Level Blueprint Fallback**:
   - Close all Blueprint editors
   - Click "Sync with AI" button
   - Should automatically use Level Blueprint context

3. **Test MCP Tool**:
   - Use `get_context_window_reference()` in Claude Desktop
   - Should return formatted context information

## Future Enhancements

1. **Smart Context Prioritization**: Could prioritize recently edited Blueprints
2. **Multi-Blueprint Context**: Support for analyzing multiple open Blueprints
3. **Context Caching**: Cache context for faster subsequent requests
4. **Visual Indicators**: Show in UI when fallback context is being used
