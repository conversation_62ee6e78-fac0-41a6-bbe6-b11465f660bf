#!/usr/bin/env python3
"""
Cloud MCP Bridge Server
This creates a proper MCP server that <PERSON> can connect to via HTTP
"""

import asyncio
import json
import sys
import logging
import threading
import time
from typing import Any, Dict, List, Optional
from fastmcp import FastMCP

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastMCP server
mcp = FastMCP("Unreal Engine Cloud MCP Server")

# Configuration
UNREAL_ENGINE_HOST = "localhost"  # Changed from host.docker.internal for cloud
UNREAL_ENGINE_PORT = 9877

def sync_send_command_to_unreal(command_data: dict) -> dict:
    """Send command to Unreal Engine socket server (synchronous version)"""
    try:
        import socket
        
        # Create socket connection
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10.0)  # 10 second timeout
        
        # Connect to Unreal Engine
        sock.connect((UNREAL_ENGINE_HOST, UNREAL_ENGINE_PORT))
        
        # Send command as JSON
        command_json = json.dumps(command_data) + '\n'
        sock.send(command_json.encode())
        
        # Read response
        response_data = sock.recv(4096)
        sock.close()
        
        if response_data:
            return json.loads(response_data.decode().strip())
        else:
            return {"success": False, "error": "No response from Unreal Engine"}
            
    except Exception as e:
        logger.error(f"Error communicating with Unreal Engine: {e}")
        return {"success": False, "error": str(e)}

# MCP Tools - All key tools that work with Unreal Engine

@mcp.tool()
def spawn_actor(actor_class: str, x: float = 0, y: float = 0, z: float = 0) -> str:
    """Spawn an actor in Unreal Engine at specified coordinates"""
    command = {
        "id": f"spawn_{int(time.time() * 1000)}",
        "type": "spawn",
        "actor_class": actor_class,
        "x": x, "y": y, "z": z
    }
    
    result = sync_send_command_to_unreal(command)
    return json.dumps(result)

@mcp.tool()
def create_material(material_name: str, base_color: List[float] = [1.0, 1.0, 1.0], 
                   metallic: float = 0.0, roughness: float = 0.5) -> str:
    """Create a material in Unreal Engine with specified properties"""
    command = {
        "id": f"create_material_{int(time.time() * 1000)}",
        "type": "create_material",
        "material_name": material_name,
        "base_color": base_color,
        "metallic": metallic,
        "roughness": roughness
    }
    
    result = sync_send_command_to_unreal(command)
    return json.dumps(result)

@mcp.tool()
def modify_object_property(object_path: str, property_name: str, property_value: Any) -> str:
    """Modify an object property in Unreal Engine"""
    command = {
        "id": f"modify_prop_{int(time.time() * 1000)}",
        "type": "modify_object",
        "object_path": object_path,
        "property_name": property_name,
        "property_value": property_value
    }
    
    result = sync_send_command_to_unreal(command)
    return json.dumps(result)

@mcp.tool()
def execute_python(script: str) -> str:
    """Execute Python script in Unreal Engine"""
    command = {
        "id": f"python_{int(time.time() * 1000)}",
        "type": "execute_python",
        "script": script
    }
    
    result = sync_send_command_to_unreal(command)
    return json.dumps(result)

@mcp.tool()
def create_blueprint(blueprint_name: str, parent_class: str = "Actor") -> str:
    """Create a new Blueprint in Unreal Engine"""
    command = {
        "id": f"create_bp_{int(time.time() * 1000)}",
        "type": "create_blueprint",
        "blueprint_name": blueprint_name,
        "parent_class": parent_class
    }
    
    result = sync_send_command_to_unreal(command)
    return json.dumps(result)

@mcp.tool()
def add_component(blueprint_path: str, component_class: str, component_name: str) -> str:
    """Add a component to a Blueprint"""
    command = {
        "id": f"add_comp_{int(time.time() * 1000)}",
        "type": "add_component",
        "blueprint_path": blueprint_path,
        "component_class": component_class,
        "component_name": component_name
    }
    
    result = sync_send_command_to_unreal(command)
    return json.dumps(result)

@mcp.tool()
def handshake_test(message: str = "Cloud MCP Connection Test") -> str:
    """Test connection to Unreal Engine"""
    command = {
        "id": f"handshake_{int(time.time() * 1000)}",
        "type": "handshake",
        "message": message
    }
    
    result = sync_send_command_to_unreal(command)
    return json.dumps(result)

@mcp.tool()
def create_project_folder(folder_name: str) -> str:
    """Create a project folder in Unreal Engine"""
    command = {
        "id": f"folder_{int(time.time() * 1000)}",
        "type": "create_project_folder", 
        "folder_name": folder_name
    }
    
    result = sync_send_command_to_unreal(command)
    return json.dumps(result)

@mcp.tool()
def get_files_in_folder(folder_path: str) -> str:
    """Get list of files in a folder"""
    command = {
        "id": f"get_files_{int(time.time() * 1000)}",
        "type": "get_files_in_folder",
        "folder_path": folder_path
    }
    
    result = sync_send_command_to_unreal(command)
    return json.dumps(result)

@mcp.tool()
def add_input_binding(action_name: str, key: str) -> str:
    """Add input binding to project"""
    command = {
        "id": f"input_{int(time.time() * 1000)}",
        "type": "add_input_binding",
        "action_name": action_name,
        "key": key
    }
    
    result = sync_send_command_to_unreal(command)
    return json.dumps(result)

@mcp.tool()
def add_variable(blueprint_path: str, variable_name: str, variable_type: str) -> str:
    """Add a variable to a Blueprint"""
    command = {
        "id": f"add_var_{int(time.time() * 1000)}",
        "type": "add_variable",
        "blueprint_path": blueprint_path,
        "variable_name": variable_name,
        "variable_type": variable_type
    }
    
    result = sync_send_command_to_unreal(command)
    return json.dumps(result)

@mcp.tool()
def add_function(blueprint_path: str, function_name: str, return_type: str = "void") -> str:
    """Add a function to a Blueprint"""
    command = {
        "id": f"add_func_{int(time.time() * 1000)}",
        "type": "add_function",
        "blueprint_path": blueprint_path,
        "function_name": function_name,
        "return_type": return_type
    }
    
    result = sync_send_command_to_unreal(command)
    return json.dumps(result)

@mcp.tool()
def compile_blueprint(blueprint_path: str) -> str:
    """Compile a Blueprint"""
    command = {
        "id": f"compile_{int(time.time() * 1000)}",
        "type": "compile_blueprint",
        "blueprint_path": blueprint_path
    }
    
    result = sync_send_command_to_unreal(command)
    return json.dumps(result)

@mcp.tool()
def spawn_blueprint(blueprint_path: str, x: float = 0, y: float = 0, z: float = 0) -> str:
    """Spawn a Blueprint in the level"""
    command = {
        "id": f"spawn_bp_{int(time.time() * 1000)}",
        "type": "spawn_blueprint", 
        "blueprint_path": blueprint_path,
        "x": x, "y": y, "z": z
    }
    
    result = sync_send_command_to_unreal(command)
    return json.dumps(result)

@mcp.tool()
def execute_unreal_command(command_string: str) -> str:
    """Execute a native Unreal Engine console command"""
    command = {
        "id": f"unreal_cmd_{int(time.time() * 1000)}",
        "type": "execute_unreal_command",
        "command": command_string
    }
    
    result = sync_send_command_to_unreal(command)
    return json.dumps(result)

# Run the MCP server
if __name__ == "__main__":
    print("🌐 Starting Cloud MCP Bridge Server...")
    print(f"📡 Connecting to Unreal Engine at {UNREAL_ENGINE_HOST}:{UNREAL_ENGINE_PORT}")
    print(f"🔧 Available tools: {len(mcp.list_tools())}")
    
    # Test connection
    try:
        test_result = sync_send_command_to_unreal({"type": "handshake", "message": "Cloud bridge startup test"})
        print(f"✅ Connection test: {test_result}")
    except Exception as e:
        print(f"⚠️ Connection test failed: {e}")
    
    # Run the FastMCP server with stdio transport for Claude Desktop
    mcp.run(transport="stdio") 