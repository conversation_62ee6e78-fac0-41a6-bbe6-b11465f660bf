# Unreal Engine Integration

This document explains how to integrate your Unreal Engine project with the AiWebplatform SaaS application.

## Overview

The integration consists of three main components:

1. **Unreal Engine Plugin**: A plugin installed in Unreal Engine that exposes a socket server for commands
2. **Python Adapter**: A Python script that acts as a bridge between Node.js and Unreal Engine
3. **Node.js API**: Backend API endpoints that allow the web frontend to communicate with Unreal Engine

## Architecture

```
[Web Frontend] ➡️ [Node.js Backend] ➡️ [Python Adapter] ➡️ [Unreal Socket Server] ➡️ [Unreal Engine]
```

## Setup Steps

### 1. Install the UnrealGenAISupport Plugin

1. Copy the `UnrealGenAISupport` plugin to your Unreal Engine project's `Plugins` directory
2. Start or restart your Unreal Engine project
3. Enable the plugin in Unreal Engine (Edit > Plugins > AI Tools > UnrealGenAISupport)

### 2. Configure the Socket Server

The Unreal Engine socket server listens on two ports:
- Port 9877: For direct MCP/Python commands
- Port 9878: For web bridge connections

You can customize these ports by setting environment variables before starting your Unreal Engine project:

```
# Windows
set UNREAL_PORT=9877
set BRIDGE_PORT=9878
set UNREAL_API_KEY=your_secret_key

# Linux/macOS
export UNREAL_PORT=9877
export BRIDGE_PORT=9878
export UNREAL_API_KEY=your_secret_key
```

### 3. Set Up the Backend

1. Ensure your backend Node.js server has Python installed (3.8+ recommended)
2. Configure your backend with the same API key and connection details:

```
# .env file
UNREAL_HOST=localhost
UNREAL_PORT=9877
BRIDGE_PORT=9878
UNREAL_API_KEY=your_secret_key
```

3. Include the Unreal routes in your Express app:

```javascript
// In app.js or index.js
const unrealRoutes = require('./routes/unreal');
app.use('/api/unreal', unrealRoutes);
```

### 4. Test the Connection

1. Start your Unreal Engine project
2. Start your backend server
3. Make a request to check the connection status:

```
GET /api/unreal/status
```

If everything is set up correctly, you should receive:

```json
{
  "success": true,
  "connected": true
}
```

## Usage Examples

### Spawn a Cube

```javascript
// From your frontend
async function spawnCube() {
  const response = await fetch('/api/unreal/spawn', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer your_auth_token'
    },
    body: JSON.stringify({
      actorClass: 'Cube',
      location: [0, 0, 100],
      scale: [2, 2, 2],
      actorLabel: 'MyCube'
    })
  });
  
  const data = await response.json();
  console.log(data);
}
```

### Create a Material

```javascript
// From your frontend
async function createMaterial() {
  const response = await fetch('/api/unreal/material', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer your_auth_token'
    },
    body: JSON.stringify({
      materialName: 'RedMaterial',
      color: [1, 0, 0]  // RGB values (0-1)
    })
  });
  
  const data = await response.json();
  console.log(data);
}
```

### Apply Material to Object

```javascript
// From your frontend
async function applyMaterial() {
  const response = await fetch('/api/unreal/set-material', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer your_auth_token'
    },
    body: JSON.stringify({
      actorName: 'MyCube',
      materialPath: '/Game/Materials/RedMaterial'
    })
  });
  
  const data = await response.json();
  console.log(data);
}
```

## Events from Unreal Engine

The integration supports real-time events from Unreal Engine. You can listen for these events using WebSockets from your frontend.

### Setting Up WebSocket in Frontend

```javascript
// In your frontend code
const socket = new WebSocket('ws://your-backend-url/ws');

socket.onopen = () => {
  console.log('Connected to backend WebSocket');
};

socket.onmessage = (event) => {
  const data = JSON.parse(event.data);
  
  if (data.type === 'unreal_event') {
    // Handle Unreal Engine events
    console.log('Unreal event:', data.event_type, data.data);
    
    // Example: If an object was spawned
    if (data.event_type === 'object_spawned') {
      updateSceneObjects(data.data);
    }
  }
};
```

### Supported Event Types

- `object_spawned`: When a new object is spawned in the scene
- `object_deleted`: When an object is deleted
- `editor_state_changed`: When the Unreal Editor state changes
- Custom events can be added by extending the Unreal plugin

## Troubleshooting

### Connection Issues

1. Verify Unreal Engine is running and the plugin is enabled
2. Check that the socket server is running in Unreal (look for log messages)
3. Ensure the API keys match between Unreal and your backend
4. Verify no firewall is blocking the ports (9877 and 9878)
5. Check Python is properly installed and can run the adapter script

### Command Failures

1. Enable verbose logging in the Unreal plugin
2. Check the backend logs for error messages
3. Verify the command syntax matches what the plugin expects

## Security Considerations

- The API key should be kept secret and not exposed in client-side code
- Only authenticated users should be allowed to send commands to Unreal
- Consider implementing additional access controls for sensitive operations
- In production, consider using HTTPS and WSS for all communication

## Next Steps

- Set up event forwarding to receive real-time updates from Unreal Engine
- Create custom UI components for interacting with Unreal objects
- Implement more specific commands for your use cases
- Add user permissions for different Unreal operations