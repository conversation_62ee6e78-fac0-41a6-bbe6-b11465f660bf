version: '3.8'

services:
  unreal-mcp-server-protected:
    build:
      context: .
      dockerfile: Dockerfile.protected
    container_name: unreal-mcp-server-protected
    ports:
      - "8000:8000"  # MCP server
      - "9877:9877"  # Unreal Engine port
    environment:
      - SUBSCRIPTION_ENDPOINT=http://host.docker.internal:3000/api/verify
      - DASHBOARD_URL=http://host.docker.internal:3000/dashboard
      - UNREAL_HOST=${UNREAL_HOST:-host.docker.internal}
      - UNREAL_PORT=${UNREAL_PORT:-9877}
      - ALWAYS_SUBSCRIBED=${ALWAYS_SUBSCRIBED:-true}
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs  # Optional: for log persistence
      - ./cache:/app/cache  # Cache for subscription status