import { NextRequest, NextResponse } from 'next/server';

// Completely bypass Supabase during build time
const isBuildTime = process.env.NODE_ENV === 'production' && typeof window === 'undefined';

export async function GET(request: NextRequest) {
  try {
    // Get all cookies for debugging
    const cookieStore = request.cookies;
    const allCookies = cookieStore.getAll().map(c => ({ name: c.name, value: c.name.includes('token') ? '***' : c.value }));

    // Check for Supabase session cookie in various formats
    let token = null;
    const sbAccessTokenCookie = cookieStore.get('sb-access-token');
    const sbTokenCookie = cookieStore.get('sb:token');
    const sbAuthTokenCookie = cookieStore.get('supabase-auth-token');
    const sbRefreshTokenCookie = cookieStore.get('sb-refresh-token');

    // Try to parse the sb-access-token cookie
    if (sbAccessTokenCookie?.value) {
      try {
        const parsedCookie = JSON.parse(sbAccessTokenCookie.value);
        if (parsedCookie?.access_token) {
          token = parsedCookie.access_token;
        }
      } catch (e) {
        // If it's not JSON, use the value directly
        token = sbAccessTokenCookie.value;
      }
    }

    // If we didn't find a token yet, try other cookie names
    if (!token) {
      token = sbTokenCookie?.value || sbAuthTokenCookie?.value;
    }

    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    const tokenFromHeader = authHeader ? authHeader.replace('Bearer ', '') : null;

    // Use token from header if available
    if (tokenFromHeader) {
      token = tokenFromHeader;
    }

    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

    let userData = null;
    let sessionData = null;

    // During build time, skip Supabase client creation
    if (isBuildTime) {
      console.log('[API] Build-time environment detected, skipping Supabase client creation');
    } else if (supabaseUrl && supabaseAnonKey) {
      try {
        // Dynamic import to prevent build-time evaluation
        const { createClient } = require('@supabase/supabase-js');
        const supabase = createClient(supabaseUrl, supabaseAnonKey, {
          auth: {
            autoRefreshToken: false,
            persistSession: false
          }
        });
        console.log('[Supabase] Client initialized successfully');

        // Try to get the current session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (session) {
          sessionData = {
            accessToken: '***',
            refreshToken: '***',
            expiresAt: session.expires_at,
            userId: session.user.id,
            email: session.user.email
          };
        }

        // If we have a token, verify it
        if (token) {
          const { data, error } = await supabase.auth.getUser(token);
          if (!error && data.user) {
            userData = {
              id: data.user.id,
              email: data.user.email,
              metadata: data.user.user_metadata
            };
          }
        }
      } catch (error) {
        console.error('[Supabase] Error initializing client:', error);
      }
    }

    // Return debug information
    return NextResponse.json({
      environment: {
        NEXT_PUBLIC_BYPASS_AUTH: process.env.NEXT_PUBLIC_BYPASS_AUTH,
        NEXT_PUBLIC_BYPASS_SUBSCRIPTION: process.env.NEXT_PUBLIC_BYPASS_SUBSCRIPTION,
        NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
        NODE_ENV: process.env.NODE_ENV
      },
      auth: {
        hasToken: !!token,
        hasAuthHeader: !!authHeader,
        hasSbAccessTokenCookie: !!sbAccessTokenCookie,
        hasSbTokenCookie: !!sbTokenCookie,
        hasSbAuthTokenCookie: !!sbAuthTokenCookie,
        hasSbRefreshTokenCookie: !!sbRefreshTokenCookie,
        allCookies,
        user: userData,
        session: sessionData
      }
    });
  } catch (error: any) {
    return NextResponse.json({
      error: 'Error in debug route',
      message: error.message
    }, { status: 500 });
  }
}
