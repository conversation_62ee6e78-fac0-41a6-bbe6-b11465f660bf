#!/usr/bin/env python3
"""
JWT Token Extractor Service
Monitors backend for active user sessions and extracts tokens for MCP server
"""

import os
import time
import json
import requests
import logging
from pathlib import Path
from typing import Optional, Dict, Any
import threading
import signal

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TokenExtractor:
    def __init__(self):
        self.backend_url = os.getenv('BACKEND_URL', 'http://localhost:5001')
        self.token_output_path = Path(os.getenv('TOKEN_OUTPUT_PATH', '/app/config/auth_token.jwt'))
        self.extraction_interval = int(os.getenv('EXTRACTION_INTERVAL', '300'))  # 5 minutes
        self.admin_token = os.getenv('ADMIN_TOKEN')  # Optional admin token for backend access
        
        self.running = True
        self.current_token = None
        self.current_user_id = None
        
        # Ensure output directory exists
        self.token_output_path.parent.mkdir(parents=True, exist_ok=True)
        
    def get_active_sessions(self) -> list:
        """Get active user sessions from backend"""
        try:
            headers = {}
            if self.admin_token:
                headers['Authorization'] = f'Bearer {self.admin_token}'
            
            # Try to get active sessions (you may need to implement this endpoint)
            response = requests.get(
                f'{self.backend_url}/api/admin/active-sessions',
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json().get('sessions', [])
            else:
                logger.warning(f"Failed to get active sessions: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"Error getting active sessions: {e}")
            return []
    
    def validate_token_with_backend(self, token: str) -> Optional[Dict[str, Any]]:
        """Validate a token with the backend and get user info"""
        try:
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
            
            # Test the token with a subscription check
            response = requests.get(
                f'{self.backend_url}/api/subscription/check',
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"Token validation successful for user: {data.get('userId')}")
                return data
            else:
                logger.warning(f"Token validation failed: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error validating token: {e}")
            return None
    
    def extract_token_from_dashboard(self) -> Optional[str]:
        """Extract token from dashboard session (placeholder - implement based on your setup)"""
        # This is a placeholder implementation
        # You would implement this based on how your dashboard stores/manages tokens
        
        # Option 1: If tokens are stored in a shared session store (Redis, etc.)
        # Option 2: If tokens are available through an admin endpoint
        # Option 3: If tokens are stored in a database
        
        # For now, this is a placeholder that would need to be customized
        # based on your specific session management system
        
        try:
            # Example: Check for environment variable containing a demo token
            demo_token = os.getenv('DEMO_JWT_TOKEN')
            if demo_token:
                logger.info("Using demo JWT token from environment")
                return demo_token
            
            # Example: Check for a shared token file (if dashboard writes tokens there)
            shared_token_file = Path('/shared/tokens/current_user.jwt')
            if shared_token_file.exists():
                token = shared_token_file.read_text().strip()
                logger.info("Extracted token from shared file")
                return token
            
            # Example: Query your backend for the most recent active user token
            # This would require implementing an admin endpoint in your backend
            try:
                admin_headers = {}
                if self.admin_token:
                    admin_headers['Authorization'] = f'Bearer {self.admin_token}'
                
                response = requests.get(
                    f'{self.backend_url}/api/admin/latest-active-token',
                    headers=admin_headers,
                    timeout=10
                )
                
                if response.status_code == 200:
                    data = response.json()
                    token = data.get('token')
                    if token:
                        logger.info(f"Extracted token for user: {data.get('userId')}")
                        return token
                        
            except Exception as e:
                logger.debug(f"Admin token extraction failed: {e}")
            
            return None
            
        except Exception as e:
            logger.error(f"Error extracting token from dashboard: {e}")
            return None
    
    def save_token(self, token: str, user_data: Dict[str, Any]):
        """Save the extracted token to file for MCP server"""
        try:
            # Save the raw token
            self.token_output_path.write_text(token)
            
            # Also save metadata for debugging
            metadata_file = self.token_output_path.with_suffix('.meta.json')
            metadata = {
                'extracted_at': time.time(),
                'user_id': user_data.get('userId'),
                'user_email': user_data.get('userEmail', 'unknown'),
                'has_subscription': user_data.get('hasActiveSubscription', False),
                'plan': user_data.get('plan', 'unknown'),
                'source': 'token_extractor'
            }
            
            metadata_file.write_text(json.dumps(metadata, indent=2))
            
            logger.info(f"Token saved for user {metadata['user_id']} with subscription: {metadata['has_subscription']}")
            
        except Exception as e:
            logger.error(f"Error saving token: {e}")
    
    def monitor_and_extract(self):
        """Main monitoring loop"""
        logger.info("Starting token extraction monitoring...")
        
        while self.running:
            try:
                # Try to extract a token
                token = self.extract_token_from_dashboard()
                
                if token:
                    # Validate the token
                    user_data = self.validate_token_with_backend(token)
                    
                    if user_data:
                        # Check if this is a new token or user
                        new_user_id = user_data.get('userId')
                        
                        if new_user_id != self.current_user_id or token != self.current_token:
                            logger.info(f"New token detected for user: {new_user_id}")
                            self.save_token(token, user_data)
                            self.current_token = token
                            self.current_user_id = new_user_id
                        else:
                            logger.debug("Token unchanged, no update needed")
                    else:
                        logger.warning("Token validation failed, not saving")
                else:
                    logger.debug("No token extracted from dashboard")
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
            
            # Sleep for the configured interval
            time.sleep(self.extraction_interval)
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down...")
        self.running = False
    
    def main(self):
        """Main entry point"""
        signal.signal(signal.SIGTERM, self.signal_handler)
        signal.signal(signal.SIGINT, self.signal_handler)
        
        logger.info("JWT Token Extractor Service Starting")
        logger.info(f"Backend URL: {self.backend_url}")
        logger.info(f"Token output path: {self.token_output_path}")
        logger.info(f"Extraction interval: {self.extraction_interval}s")
        
        # Start monitoring in a separate thread
        monitor_thread = threading.Thread(target=self.monitor_and_extract, daemon=True)
        monitor_thread.start()
        
        # Keep main thread alive
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("Shutting down...")
            self.running = False

if __name__ == "__main__":
    extractor = TokenExtractor()
    extractor.main() 