'use client';

import { useEffect, useState } from 'react';
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useRouter } from 'next/navigation';
import { fetchWithAuth, getApiUrl } from '@/lib/auth-utils';

export default function HighUsageUsersTable() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [users, setUsers] = useState<any[]>([]);
  const router = useRouter();

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('Fetching high usage users...');
        const apiUrl = getApiUrl();
        const requestUrl = `${apiUrl}/api/admin/token-usage/high-usage-users?limit=10`;

        console.log('Making request to:', requestUrl);

        const response = await fetchWithAuth(requestUrl);

        console.log('Response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Error response:', errorText);
          throw new Error(`Failed to fetch high usage users: ${response.status} ${errorText}`);
        }

        const data = await response.json();
        console.log('High usage users data received:', JSON.stringify(data, null, 2));
        setUsers(data);
      } catch (error) {
        console.error('Error fetching high usage users:', error);
        setError('Failed to load user data: ' + (error instanceof Error ? error.message : String(error)));
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const viewUserDetails = (userId: string) => {
    router.push(`/admin/users/${userId}`);
  };

  if (loading) {
    return (
      <div className="space-y-2">
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-8 w-full" />
      </div>
    );
  }

  if (error) {
    return <div className="text-red-500">{error}</div>;
  }

  if (users.length === 0) {
    return <div className="text-center py-4">No usage data available yet.</div>;
  }

  return (
    <Table>
      <TableCaption>Top 10 users by token usage this month</TableCaption>
      <TableHeader>
        <TableRow>
          <TableHead>User</TableHead>
          <TableHead>Plan</TableHead>
          <TableHead className="text-right">Tokens</TableHead>
          <TableHead className="text-right">Cost</TableHead>
          <TableHead className="text-right">Profit</TableHead>
          <TableHead></TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {users.map((user) => (
          <TableRow key={user.userId}>
            <TableCell>
              <div className="font-medium">{user.name}</div>
              <div className="text-sm text-muted-foreground">{user.email}</div>
            </TableCell>
            <TableCell>
              <Badge variant={user.plan === 'pro' ? 'default' : 'secondary'}>
                {user.plan.toUpperCase()}
              </Badge>
            </TableCell>
            <TableCell className="text-right">{user.totalTokens.toLocaleString()}</TableCell>
            <TableCell className="text-right">${user.cost}</TableCell>
            <TableCell className="text-right">${user.profit}</TableCell>
            <TableCell>
              <Button
                variant="outline"
                size="sm"
                onClick={() => viewUserDetails(user.userId)}
              >
                Details
              </Button>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
