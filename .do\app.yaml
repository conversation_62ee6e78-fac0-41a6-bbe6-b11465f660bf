name: createlex-app
region: nyc
services:
  - name: frontend
    github:
      repo: AlexKissiJr/AiWebplatform
      branch: windows-working
      deploy_on_push: true
    source_dir: frontend
    build_command: npm run build
    run_command: npm start
    http_port: 3000
    instance_size_slug: basic-xs
    instance_count: 1
    routes:
      - path: /
    envs:
      - key: NEXT_PUBLIC_SUPABASE_URL
        scope: RUN_AND_BUILD_TIME
        value: ${supabase_url}
      - key: NEXT_PUBLIC_SUPABASE_ANON_KEY
        scope: RUN_AND_BUILD_TIME
        value: ${supabase_anon_key}
      - key: NEXT_PUBLIC_API_URL
        scope: RUN_AND_BUILD_TIME
        value: https://api.createlex.com
      - key: NODE_ENV
        scope: RUN_AND_BUILD_TIME
        value: production

  - name: backend
    github:
      repo: AlexKissiJr/AiWebplatform
      branch: windows-working
      deploy_on_push: true
    source_dir: backend
    build_command: npm install
    run_command: npm run start:production
    http_port: 5001
    instance_size_slug: basic-xs
    instance_count: 1
    routes:
      - path: /api
    health_check:
      http_path: /health
    envs:
      - key: PORT
        scope: RUN_AND_BUILD_TIME
        value: "5001"
      - key: FRONTEND_URL
        scope: RUN_AND_BUILD_TIME
        value: ${frontend_url}
      - key: SUPABASE_URL
        scope: RUN_AND_BUILD_TIME
        value: ${supabase_url}
      - key: SUPABASE_SERVICE_KEY
        scope: RUN_AND_BUILD_TIME
        value: ${supabase_service_key}
      - key: STRIPE_SECRET_KEY
        scope: RUN_AND_BUILD_TIME
        value: ${stripe_secret_key}
      - key: STRIPE_PUBLISHABLE_KEY
        scope: RUN_AND_BUILD_TIME
        value: ${stripe_publishable_key}
      - key: STRIPE_WEBHOOK_SECRET
        scope: RUN_AND_BUILD_TIME
        value: ${stripe_webhook_secret}
      - key: STRIPE_PRICE_ID
        scope: RUN_AND_BUILD_TIME
        value: ${stripe_price_id}
      - key: STRIPE_PRICE_ID_PRO
        scope: RUN_AND_BUILD_TIME
        value: ${stripe_price_id_pro}
      - key: PS_ANTHROPICAPIKEY
        scope: RUN_AND_BUILD_TIME
        value: ${anthropic_api_key}
      - key: EMAIL_HOST
        scope: RUN_AND_BUILD_TIME
        value: smtp.hostinger.com
      - key: EMAIL_PORT
        scope: RUN_AND_BUILD_TIME
        value: "465"
      - key: EMAIL_USER
        scope: RUN_AND_BUILD_TIME
        value: ${email_user}
      - key: EMAIL_PASS
        scope: RUN_AND_BUILD_TIME
        value: ${email_pass}
      - key: EMAIL_FROM
        scope: RUN_AND_BUILD_TIME
        value: ${email_from}
      - key: EMAIL_TO
        scope: RUN_AND_BUILD_TIME
        value: <EMAIL>
      - key: JWT_SECRET
        scope: RUN_AND_BUILD_TIME
        value: ${jwt_secret}
      - key: NODE_ENV
        scope: RUN_AND_BUILD_TIME
        value: production

  - name: mcp_server
    github:
      repo: AlexKissiJr/AiWebplatform
      branch: vscode-ext
      deploy_on_push: true
    source_dir: mcp_server
    build_command: pip install -r requirements.txt
    run_command: python mcp_server.py
    http_port: 9877
    instance_size_slug: basic-xs
    instance_count: 1
    routes:
      - path: /mcp
    envs:
      - key: MCP_PORT
        scope: RUN_AND_BUILD_TIME
        value: "9877"
      - key: MCP_HOST
        scope: RUN_AND_BUILD_TIME
        value: "0.0.0.0"

  - name: mcp-server
    source_dir: /UnrealGenAISupport_with_server/server
    github:
      repo: AlexKissiJr/AiWebplatform
      branch: cc_plugin
      deploy_on_push: true
    dockerfile_path: UnrealGenAISupport_with_server/server/Dockerfile.cloud
    
    # Environment variables
    envs:
    - key: PORT
      value: "8000"
    - key: ENVIRONMENT
      value: "production"
    - key: LOG_LEVEL
      value: "info"
    
    # Health check
    health_check:
      http_path: /health
      initial_delay_seconds: 10
      period_seconds: 10
      timeout_seconds: 5
      success_threshold: 1
      failure_threshold: 3
    
    # HTTP configuration
    http_port: 8000
    
    # Instance configuration with auto-scaling
    instance_size_slug: basic-xxs  # $5/month
    
    # Auto-scaling configuration
    autoscaling:
      min_instance_count: 1
      max_instance_count: 3
      metrics:
        cpu:
          percent: 80
    
    # Routes for external access
    routes:
    - path: /
      preserve_path_prefix: true