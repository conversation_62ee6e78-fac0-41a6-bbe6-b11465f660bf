'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

interface ProductCardProps {
  title: string;
  description: string;
  imageUrl: string;
  primaryLink: {
    text: string;
    href: string;
    external?: boolean;
  };
  secondaryLink?: {
    text: string;
    href: string;
    external?: boolean;
  };
  tags?: string[];
}

const ProductCard: React.FC<ProductCardProps> = ({
  title,
  description,
  imageUrl,
  primaryLink,
  secondaryLink,
  tags
}) => {
  return (
    <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-blue-500/20 hover:border-blue-500 transform hover:-translate-y-1 group">
      <div className="relative h-56 w-full bg-gradient-to-br from-gray-700 to-gray-900 flex items-center justify-center overflow-hidden">
        {/* Fallback content in case image doesn't load */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-3xl font-bold text-gray-600">{title.split(' ')[0]}</div>
        </div>

        {/* Image with error handling */}
        <Image
          src={imageUrl}
          alt={title}
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          priority
          className="object-cover transition-transform duration-700 group-hover:scale-110"
          onError={(e) => {
            // Hide the image on error
            console.error(`Failed to load image: ${imageUrl}`);
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
          }}
        />

        {/* Overlay gradient */}
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900 via-transparent to-transparent opacity-70"></div>

        {/* Product title overlay at bottom */}
        <div className="absolute bottom-0 left-0 right-0 p-4">
          <h3 className="text-xl font-bold text-white drop-shadow-lg">{title}</h3>
        </div>
      </div>
      <div className="p-6">
        {tags && tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {tags.map((tag, index) => (
              <span
                key={index}
                className="text-xs font-medium px-2.5 py-0.5 rounded-full bg-blue-900/50 text-blue-200 border border-blue-700 group-hover:bg-blue-800/50 transition-colors"
              >
                {tag}
              </span>
            ))}
          </div>
        )}
        <p className="text-gray-300 mb-6 min-h-[80px]">{description}</p>
        <div className="flex flex-wrap gap-3">
          {primaryLink.external ? (
            <a
              href={primaryLink.href}
              target="_blank"
              rel="noopener noreferrer"
              className="px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-500 text-white rounded-md hover:from-blue-700 hover:to-blue-600 transition-colors shadow-md hover:shadow-blue-500/50"
            >
              {primaryLink.text}
            </a>
          ) : (
            <Link
              href={primaryLink.href}
              className="px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-500 text-white rounded-md hover:from-blue-700 hover:to-blue-600 transition-colors shadow-md hover:shadow-blue-500/50"
            >
              {primaryLink.text}
            </Link>
          )}

          {secondaryLink && (
            secondaryLink.external ? (
              <a
                href={secondaryLink.href}
                target="_blank"
                rel="noopener noreferrer"
                className="px-4 py-2 bg-gray-700 text-gray-200 rounded-md hover:bg-gray-600 transition-colors border border-gray-600 shadow-md"
              >
                {secondaryLink.text}
              </a>
            ) : (
              <Link
                href={secondaryLink.href}
                className="px-4 py-2 bg-gray-700 text-gray-200 rounded-md hover:bg-gray-600 transition-colors border border-gray-600 shadow-md"
              >
                {secondaryLink.text}
              </Link>
            )
          )}
        </div>
      </div>
    </div>
  );
};

const ProductsSection: React.FC = () => {
  const products: ProductCardProps[] = [
    {
                  title: "CreatelexGenAI Plugin",
      description: "Integrate AI-powered natural language commands into your Unreal Engine projects. Generate blueprints, materials, and manipulate scenes with simple text prompts.",
      imageUrl: "/images/products/unreal-plugin.jpg",
      primaryLink: {
        text: "Download",
        href: "/download",
      },
      secondaryLink: {
        text: "Learn More",
        href: "/docs/unreal-plugin",
      },
      tags: ["Unreal Engine", "Plugin", "AI"]
    },
    {
      title: "AI Webplatform",
      description: "Our cloud-based platform for AI-powered Unreal Engine development. Connect to your projects remotely and collaborate with team members.",
      imageUrl: "/images/products/ai-webplatform.jpg",
      primaryLink: {
        text: "Get Started",
        href: "/dashboard",
      },
      secondaryLink: {
        text: "View Demo",
        href: "#demo",
      },
      tags: ["SaaS", "Cloud", "Collaboration"]
    },
    {
      title: "Unreal Engine AI Assistant",
      description: "Standalone chat application with a powerful AI assistant designed specifically for Unreal Engine developers. Generate blueprints, materials, and solve complex development challenges.",
      imageUrl: "/images/products/chat.jpg",
      primaryLink: {
        text: "Open App",
        href: "/chat",
        external: false
      },
      secondaryLink: {
        text: "Documentation",
        href: "/docs/chat",
      },
      tags: ["Chat", "MCP", "AI"]
    }
  ];

  return (
    <section id="products" className="py-20 bg-gray-900 relative overflow-hidden diagonal-top diagonal-bottom">
      {/* Background elements */}
      <div className="absolute top-0 left-0 w-full h-full opacity-10 z-0">
        <div className="absolute top-1/3 right-1/4 w-96 h-96 rounded-full bg-purple-600 filter blur-3xl"></div>
        <div className="absolute bottom-1/3 left-1/4 w-64 h-64 rounded-full bg-blue-600 filter blur-3xl"></div>
      </div>

      {/* Unreal-style grid lines */}
      <div className="absolute inset-0 z-0 opacity-5">
        {[...Array(20)].map((_, i) => (
          <div key={i} className="absolute left-0 w-full h-px bg-blue-400" style={{ top: `${i * 5}%` }}></div>
        ))}
        {[...Array(20)].map((_, i) => (
          <div key={i} className="absolute top-0 h-full w-px bg-blue-400" style={{ left: `${i * 5}%` }}></div>
        ))}
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="inline-block mx-auto mb-4 px-4 py-1 border border-purple-400 rounded-full bg-purple-900/30 text-center">
          <span className="text-purple-300 font-medium">Products</span>
        </div>
        <h2 className="text-4xl md:text-5xl font-bold text-center mb-8 text-white">
          Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-blue-400">Products</span>
        </h2>
        <p className="text-xl text-gray-300 max-w-3xl mx-auto text-center mb-16">
          Powerful tools to enhance your Unreal Engine development workflow with AI
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {products.map((product, index) => (
            <ProductCard key={index} {...product} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ProductsSection;
