import { NextApiRequest, NextApiResponse } from 'next';
import { getSupabaseClient } from '@/lib/supabase-singleton';

/**
 * API endpoint to set the primary user ID for all devices
 * This endpoint allows setting a specific user ID as the primary ID for all devices
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { userId, currentUserId, deviceId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    console.log(`API: Setting primary user ID to ${userId} for all devices`);

    // First, store the mapping for the current device
    if (deviceId) {
      const supabase = getSupabaseClient();
      const { error } = await supabase
        .from('user_id_mappings')
        .upsert({
          device_id: deviceId,
          primary_user_id: userId,
          last_seen_at: new Date().toISOString()
        }, {
          onConflict: 'device_id'
        });

      if (error) {
        console.error('Error storing user ID mapping:', error);
      }
    }

    // If we have a current user ID, we need to migrate all chats from that ID to the new one
    if (currentUserId && currentUserId !== userId) {
      console.log(`API: Migrating chats from ${currentUserId} to ${userId}`);

      // Get Supabase client
      const supabase = getSupabaseClient();

      // Get all chats for the current user ID
      const { data: chats, error: chatsError } = await supabase
        .from('chats')
        .select('id')
        .eq('user_id', currentUserId);

      if (chatsError) {
        console.error('Error getting chats for migration:', chatsError);
      } else if (chats && chats.length > 0) {
        console.log(`API: Found ${chats.length} chats to migrate`);

        // Update all chats to use the new user ID
        const { error: updateError } = await supabase
          .from('chats')
          .update({ user_id: userId })
          .eq('user_id', currentUserId);

        if (updateError) {
          console.error('Error updating chats:', updateError);
        } else {
          console.log(`API: Successfully migrated ${chats.length} chats`);
        }
      } else {
        console.log(`API: No chats found for user ID ${currentUserId}`);
      }

      // Also update any mappings that use the current user ID
      const { error: mappingError } = await supabase
        .from('user_id_mappings')
        .update({ primary_user_id: userId })
        .eq('primary_user_id', currentUserId);

      if (mappingError) {
        console.error('Error updating user ID mappings:', mappingError);
      }
    }

    // Check if there are any chats for the user ID
    const supabase = getSupabaseClient();
    const { data: userChats, error: userChatsError } = await supabase
      .from('chats')
      .select('id')
      .eq('user_id', userId)
      .limit(1);

    if (userChatsError) {
      console.error('Error checking for user chats:', userChatsError);
    } else if (!userChats || userChats.length === 0) {
      // If there are no chats for the user ID, create a test chat
      console.log(`API: No existing chats found for user ID ${userId}, creating a test chat`);

      const { error: insertError } = await supabase
        .from('chats')
        .insert({
          user_id: userId,
          title: 'Test Chat',
          messages: [
            {
              role: 'system',
              content: 'This is a test chat created to ensure consistent user IDs across devices.',
            },
            {
              role: 'user',
              content: 'Hello, this is a test message.',
            },
            {
              role: 'assistant',
              content: 'Hello! I am here to help ensure your chat history is consistent across all your devices.',
            },
          ],
        });

      if (insertError) {
        console.error('API: Error creating test chat:', insertError);
      } else {
        console.log(`API: Successfully created test chat for user ID ${userId}`);
      }
    }

    return res.status(200).json({
      success: true,
      primaryUserId: userId,
      message: 'Primary user ID set successfully'
    });
  } catch (error) {
    console.error('Error in set-primary-user-id API:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
