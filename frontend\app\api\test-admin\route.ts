import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/supabase';

export async function GET(req: NextRequest) {
  try {
    console.log('🧪 Test admin route called');
    
    // Get current user
    const user = await getCurrentUser();
    console.log('👤 Current user:', user?.email);
    
    // Check admin status
    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    const isAdmin = user?.email && adminEmails.includes(user.email.toLowerCase());
    
    console.log('👑 Is admin:', isAdmin);
    
    return NextResponse.json({
      success: true,
      message: 'Test admin route working',
      user: {
        id: user?.id,
        email: user?.email,
        isAdmin: isAdmin
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Test admin route error:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
