#!/bin/bash

# This script initializes Let's Encrypt SSL certificates

# Exit on error
set -e

# Read domain from user input
read -p "Enter your domain name (without www): " DOMAIN_NAME
DOMAINS=($DOMAIN_NAME www.$DOMAIN_NAME)
RSA_KEY_SIZE=4096
EMAIL="<EMAIL>" # Change to your email
STAGING=0 # Set to 1 if you're testing your setup to avoid hitting request limits

# Create a temporary nginx config
cat > ./nginx/conf/app.conf.template <<EOF
server {
    listen 80;
    server_name $DOMAIN_NAME www.$DOMAIN_NAME;
    server_tokens off;

    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    location / {
        return 301 https://\$host\$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name $DOMAIN_NAME www.$DOMAIN_NAME;
    server_tokens off;

    ssl_certificate /etc/letsencrypt/live/$DOMAIN_NAME/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN_NAME/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    client_max_body_size 20M;

    # Frontend
    location / {
        proxy_pass http://frontend:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # Backend API
    location /api {
        proxy_pass http://backend:5001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # MCP WebSocket Server
    location /mcp {
        proxy_pass http://mcp_server:9877;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

# Create a temporary nginx config for HTTP only
cat > ./nginx/conf/app.conf <<EOF
server {
    listen 80;
    server_name $DOMAIN_NAME www.$DOMAIN_NAME;
    server_tokens off;

    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    location / {
        return 200 'OK';
        add_header Content-Type text/plain;
    }
}
EOF

# Start nginx
docker compose -f docker-compose.prod.yml up -d nginx

# Wait for nginx to start
echo "Waiting for nginx to start..."
sleep 5

# Create dummy certificates
for domain in "${DOMAINS[@]}"; do
  mkdir -p ./nginx/certbot/conf/live/$domain
  docker compose -f docker-compose.prod.yml run --rm --entrypoint "\
    openssl req -x509 -nodes -newkey rsa:$RSA_KEY_SIZE -days 1\
      -keyout '/etc/letsencrypt/live/$domain/privkey.pem' \
      -out '/etc/letsencrypt/live/$domain/fullchain.pem' \
      -subj '/CN=localhost'" certbot
done

# Reload nginx
docker compose -f docker-compose.prod.yml exec nginx nginx -s reload

# Get real certificates
for domain in "${DOMAINS[@]}"; do
  echo "Getting certificate for $domain..."
  
  docker compose -f docker-compose.prod.yml run --rm --entrypoint "\
    certbot certonly --webroot -w /var/www/certbot \
      --email $EMAIL \
      -d $domain \
      --rsa-key-size $RSA_KEY_SIZE \
      --agree-tos \
      --force-renewal \
      --non-interactive \
      $([ $STAGING -eq 1 ] && echo '--staging')" certbot
done

# Generate strong DH parameters
docker compose -f docker-compose.prod.yml run --rm --entrypoint "\
  openssl dhparam -out /etc/letsencrypt/ssl-dhparams.pem 2048" certbot

# Copy the full nginx config
cp ./nginx/conf/app.conf.template ./nginx/conf/app.conf

# Reload nginx
docker compose -f docker-compose.prod.yml exec nginx nginx -s reload

echo "SSL certificates have been successfully set up!"
