require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key (first 10 chars):', supabaseKey.substring(0, 10) + '...');

const supabase = createClient(supabaseUrl, supabaseKey);

async function retrieveSpecificUser() {
  try {
    const userId = '8075c290-0943-4d3e-94a7-dcdf42bda6c6';
    
    console.log(`Retrieving user with ID: ${userId}`);
    
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId);
    
    if (error) {
      console.error('Error:', error);
    } else if (data && data.length > 0) {
      console.log('User found:', data[0]);
    } else {
      console.log('User not found');
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

retrieveSpecificUser();
