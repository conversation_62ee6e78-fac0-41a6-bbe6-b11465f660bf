# SQL Functions for MCP-Chat

This directory contains SQL functions that need to be added to your Supabase project to enable certain features.

## Required SQL Functions

### 1. Force Delete Chat Function (Recommended)

The `force_delete_chat_function.sql` file contains a comprehensive function that:
- Deletes all messages associated with a chat
- Deletes all topics associated with a chat (if the table exists)
- Deletes the chat itself
- Handles errors gracefully

This is the recommended function to use for chat deletion as it handles everything in one transaction.

### 2. Delete Chat Function (Legacy)

The `delete_chat_function.sql` file contains a simpler function that allows for direct deletion of chats and their messages from the database. This is used as a fallback mechanism when the regular deletion process fails.

## How to Add These Functions to Supabase

1. Go to your Supabase project dashboard
2. Click on "SQL Editor" in the left sidebar
3. Click "New Query"
4. Copy and paste the contents of each SQL file into the query editor
5. Click "Run" to create the functions

### Step 1: Create the Force Delete Chat Function (Recommended)

```sql
-- Function to completely delete a chat and all related data
CREATE OR REPLACE FUNCTION force_delete_chat(chat_id TEXT, user_id TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    success BOOLEAN := FALSE;
    rows_deleted INTEGER;
BEGIN
    -- Delete messages first
    BEGIN
        DELETE FROM messages WHERE chat_id = $1;
        GET DIAGNOSTICS rows_deleted = ROW_COUNT;
        RAISE NOTICE 'Deleted % messages for chat %', rows_deleted, $1;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error deleting messages: %', SQLERRM;
    END;

    -- Delete topics if the table exists
    BEGIN
        IF EXISTS (
            SELECT FROM pg_tables
            WHERE schemaname = 'public'
            AND tablename = 'topics'
        ) THEN
            DELETE FROM topics WHERE chat_id = $1;
            GET DIAGNOSTICS rows_deleted = ROW_COUNT;
            RAISE NOTICE 'Deleted % topics for chat %', rows_deleted, $1;
        END IF;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error deleting topics: %', SQLERRM;
    END;

    -- Delete the chat
    BEGIN
        DELETE FROM chats WHERE id = $1;
        GET DIAGNOSTICS rows_deleted = ROW_COUNT;
        RAISE NOTICE 'Deleted % chats with ID %', rows_deleted, $1;

        IF rows_deleted > 0 THEN
            success := TRUE;
        END IF;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error deleting chat: %', SQLERRM;
    END;

    RETURN success;
END;
$$ LANGUAGE plpgsql;
```

### Step 2: Create the Delete Chat Function (Legacy)

```sql
-- Function to delete a chat by ID
CREATE OR REPLACE FUNCTION delete_chat_by_id(chat_id TEXT)
RETURNS VOID AS $$
BEGIN
    -- Delete messages first
    DELETE FROM messages WHERE chat_id = $1;

    -- Delete topics associated with this chat
    DELETE FROM topics WHERE chat_id = $1;

    -- Then delete the chat
    DELETE FROM chats WHERE id = $1;
END;
$$ LANGUAGE plpgsql;
```

## Usage

These functions are called automatically by the application when needed:

```sql
-- To completely delete a chat and all related data (recommended)
SELECT force_delete_chat('your-chat-id-here', 'user-id-here');

-- To delete a chat and its messages (legacy)
SELECT delete_chat_by_id('your-chat-id-here');
```

## Troubleshooting

If you encounter issues with chat deletion, make sure:

1. The SQL functions are properly installed in your Supabase project
2. Your Supabase user has the necessary permissions to execute the functions
3. The chat ID being passed to the functions is valid
4. The database connection is working properly
