# MCP Server Update API Specification

This document describes the API endpoints needed on `createlex.com` to support automatic MCP server updates in the CreateLex Bridge app.

## Base URL
```
https://createlex.com/api/mcp-updates
```

## Authentication
All endpoints should require valid subscription authentication (same as existing API).

## Endpoints

### 1. Check for Updates

**Endpoint:** `GET /api/mcp-updates/check`

**Parameters:**
- `current_version` (query string) - The current MCP server version (e.g., "1.0.0")

**Response:**
```json
{
  "hasUpdate": true,
  "latestVersion": "1.1.0",
  "updateInfo": {
    "size": 1048576,
    "description": "CreateLex MCP Server v1.1.0",
    "releaseNotes": "Added new Blueprint tools and improved performance",
    "critical": false,
    "minAppVersion": "1.0.0"
  }
}
```

**Response (No Update):**
```json
{
  "hasUpdate": false,
  "currentVersion": "1.0.0"
}
```

### 2. Download Update Package

**Endpoint:** `GET /api/mcp-updates/download/{version}`

**Parameters:**
- `version` (path parameter) - The version to download (e.g., "1.1.0")

**Response:**
- **Content-Type:** `application/zip`
- **Headers:**
  - `x-checksum: {sha256_hash}` - SHA256 checksum of the ZIP file
  - `content-length: {file_size}`
- **Body:** ZIP file containing MCP server files

**Error Response (404):**
```json
{
  "error": "Version not found",
  "version": "1.1.0"
}
```

## Database Schema

### MCP Versions Table
```sql
CREATE TABLE mcp_versions (
  id SERIAL PRIMARY KEY,
  version VARCHAR(20) NOT NULL UNIQUE,
  description TEXT,
  release_notes TEXT,
  file_path VARCHAR(255) NOT NULL,
  file_size INTEGER NOT NULL,
  checksum VARCHAR(64) NOT NULL,
  critical BOOLEAN DEFAULT FALSE,
  min_app_version VARCHAR(20),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  published_at TIMESTAMP,
  is_active BOOLEAN DEFAULT TRUE
);
```

### Example Data
```sql
INSERT INTO mcp_versions (
  version, 
  description, 
  release_notes, 
  file_path, 
  file_size, 
  checksum,
  min_app_version,
  published_at
) VALUES (
  '1.1.0',
  'CreateLex MCP Server v1.1.0',
  'Added new Blueprint tools and improved performance',
  '/updates/mcp-server-v1.1.0.zip',
  1048576,
  'a1b2c3d4e5f6...',
  '1.0.0',
  NOW()
);
```

## File Storage

### Directory Structure
```
/var/www/createlex.com/storage/mcp-updates/
├── mcp-server-v1.0.0.zip
├── mcp-server-v1.1.0.zip
├── mcp-server-v1.2.0.zip
└── metadata/
    ├── v1.0.0.json
    ├── v1.1.0.json
    └── v1.2.0.json
```

### Update Package Format
Each ZIP file should contain:
- `mcp_server_protected.py` - Main server entry point
- `mcp_server_stdio.py` - STDIO server implementation
- `subscription_validator.py` - Subscription validation
- `fastmcp.py` - MCP framework
- `version.json` - Version metadata
- `requirements.txt` - Python dependencies
- All tool files (`*.py`)

### Version Metadata Format
```json
{
  "version": "1.1.0",
  "build_date": "2024-12-19",
  "build_number": 1734624000000,
  "description": "CreateLex MCP Server v1.1.0",
  "features": [
    "Unreal Engine project management",
    "Asset creation and import",
    "Blueprint creation and editing"
  ],
  "compatibility": {
    "unreal_engine": "5.0+",
    "python": "3.8+",
    "platforms": ["Windows", "macOS", "Linux"]
  }
}
```

## Security Considerations

1. **Authentication:** All endpoints require valid subscription
2. **File Integrity:** SHA256 checksums prevent tampering
3. **Version Validation:** Semantic versioning prevents downgrades
4. **Rate Limiting:** Prevent abuse of download endpoints
5. **Access Control:** Only active subscribers can download updates

## Error Handling

### Common Error Responses
```json
{
  "error": "Unauthorized",
  "message": "Valid subscription required"
}
```

```json
{
  "error": "Version not found",
  "version": "1.5.0",
  "available_versions": ["1.0.0", "1.1.0", "1.2.0"]
}
```

```json
{
  "error": "Update check failed",
  "message": "Invalid version format"
}
```

## Implementation Notes

### Backend Implementation (Node.js/Express)
```javascript
// Check for updates
app.get('/api/mcp-updates/check', authenticateSubscription, async (req, res) => {
  const { current_version } = req.query;
  
  // Query database for latest version
  const latest = await db.query(
    'SELECT * FROM mcp_versions WHERE is_active = true ORDER BY created_at DESC LIMIT 1'
  );
  
  if (semver.gt(latest.version, current_version)) {
    res.json({
      hasUpdate: true,
      latestVersion: latest.version,
      updateInfo: {
        size: latest.file_size,
        description: latest.description,
        releaseNotes: latest.release_notes,
        critical: latest.critical
      }
    });
  } else {
    res.json({ hasUpdate: false, currentVersion: current_version });
  }
});

// Download update
app.get('/api/mcp-updates/download/:version', authenticateSubscription, async (req, res) => {
  const { version } = req.params;
  
  const versionData = await db.query(
    'SELECT * FROM mcp_versions WHERE version = ? AND is_active = true',
    [version]
  );
  
  if (!versionData) {
    return res.status(404).json({ error: 'Version not found', version });
  }
  
  const filePath = path.join(UPDATES_DIR, versionData.file_path);
  
  res.set({
    'Content-Type': 'application/zip',
    'x-checksum': versionData.checksum,
    'Content-Length': versionData.file_size
  });
  
  res.sendFile(filePath);
});
```

## Testing

### Manual Testing
```bash
# Check for updates
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "https://createlex.com/api/mcp-updates/check?current_version=1.0.0"

# Download update
curl -H "Authorization: Bearer YOUR_TOKEN" \
  -o "mcp-server-v1.1.0.zip" \
  "https://createlex.com/api/mcp-updates/download/1.1.0"
```

### Automated Testing
The CreateLex Bridge app includes test commands:
```bash
# Test update check
npm run update-check

# Create test update package
npm run create-mcp-update 1.1.0
```

## Deployment Checklist

- [ ] Database table created
- [ ] API endpoints implemented
- [ ] File storage directory created with proper permissions
- [ ] Authentication middleware integrated
- [ ] Error handling implemented
- [ ] Rate limiting configured
- [ ] Logging added for monitoring
- [ ] SSL certificate configured
- [ ] Backup strategy for update files
- [ ] Monitoring alerts configured

## Monitoring

### Metrics to Track
- Update check requests per day
- Download requests per version
- Failed update attempts
- Average download time
- Storage usage for update files

### Log Examples
```
[INFO] MCP Update Check: user=user123, current=1.0.0, latest=1.1.0, hasUpdate=true
[INFO] MCP Download: user=user123, version=1.1.0, size=1048576, duration=2.3s
[ERROR] MCP Download Failed: user=user123, version=1.1.0, error=file_not_found
``` 