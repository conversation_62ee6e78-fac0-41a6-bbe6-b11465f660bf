const Store = require('electron-store');

class TokenManager {
  constructor() {
    this.store = new Store({ name: 'createlex-auth' });
  }

  async storeToken(token) {
    this.store.set('authToken', token);
  }

  async getToken() {
    return this.store.get('authToken');
  }

  async isAuthenticated() {
    return !!this.store.get('authToken');
  }

  async clearToken() {
    this.store.delete('authToken');
  }
}

module.exports = { TokenManager }; 