import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '@/lib/supabase';

/**
 * API endpoint to create a test chat with a specific user ID
 * This is used to ensure that the auto-sync process can find a chat with the Supabase UUID
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    console.log(`API: Creating test chat for user ID ${userId}`);
    
    // Create a test chat with the specified user ID
    const { data, error } = await supabase
      .from('chats')
      .insert({
        user_id: userId,
        title: 'Test Chat',
        messages: [
          {
            role: 'system',
            content: 'This is a test chat created to ensure consistent user IDs across devices.',
          },
          {
            role: 'user',
            content: 'Hello, this is a test message.',
          },
          {
            role: 'assistant',
            content: 'Hello! I am here to help ensure your chat history is consistent across all your devices.',
          },
        ],
      })
      .select();
      
    if (error) {
      console.error('Error creating test chat:', error);
      return res.status(500).json({ error: 'Error creating test chat' });
    }
    
    console.log(`API: Successfully created test chat for user ID ${userId}`);
    
    // Trigger the auto-sync process
    try {
      const syncResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001'}/api/auto-sync-user-ids`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!syncResponse.ok) {
        console.error('Error triggering auto-sync:', syncResponse.statusText);
      } else {
        const syncData = await syncResponse.json();
        console.log('Auto-sync completed:', syncData);
      }
    } catch (syncError) {
      console.error('Error triggering auto-sync:', syncError);
    }
    
    return res.status(200).json({
      success: true,
      chatId: data?.[0]?.id,
      message: 'Test chat created successfully',
    });
  } catch (error) {
    console.error('Error in create-test-chat API:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
