'use client';

import { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { updateUserId } from "@/lib/user-id";
import { useRouter } from "next/navigation";

export default function SetUserIdPage() {
  const [userId, setUserId] = useState('');
  const [currentUserId, setCurrentUserId] = useState('');
  const [currentUserIdSource, setCurrentUserIdSource] = useState('');
  const router = useRouter();

  // Get the current user ID from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedUserId = localStorage.getItem('ai-chat-user-id');
      const storedUserIdSource = localStorage.getItem('ai-chat-user-id-source') || 'unknown';
      
      setCurrentUserId(storedUserId || '');
      setCurrentUserIdSource(storedUserIdSource);
    }
  }, []);

  const handleSetUserId = () => {
    if (!userId.trim()) {
      toast.error("User ID cannot be empty");
      return;
    }

    const trimmedUserId = userId.trim();
    
    // Check if the user ID looks like a UUID (Supabase format)
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(trimmedUserId);
    
    // If it looks like a UUID, mark it as from Supabase
    if (isUUID) {
      console.log('New user ID looks like a Supabase UUID');
      updateUserId(trimmedUserId, 'supabase');
      toast.success("Supabase User ID set successfully");
    } else {
      // Otherwise, mark it as manually entered
      updateUserId(trimmedUserId, 'manual');
      toast.success("User ID set successfully");
    }
    
    // Redirect to the chat page
    setTimeout(() => {
      router.push('/chat');
      // Reload the page to ensure all components use the new user ID
      window.location.reload();
    }, 1000);
  };

  return (
    <div className="flex min-h-screen items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Set User ID</CardTitle>
          <CardDescription>
            Enter your Supabase user ID to sync your chats across devices
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="currentUserId">Current User ID</Label>
              <Input
                id="currentUserId"
                value={currentUserId}
                readOnly
                className="font-mono text-xs"
              />
              <p className="text-xs text-muted-foreground">
                Source: {currentUserIdSource}
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="userId">New User ID</Label>
              <Input
                id="userId"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
                placeholder="Enter your Supabase user ID"
                className="font-mono text-xs"
              />
              <p className="text-xs text-muted-foreground">
                Enter the Supabase user ID from your other device
              </p>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => router.push('/chat')}>
            Cancel
          </Button>
          <Button onClick={handleSetUserId}>
            Set User ID
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
