'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { format, subDays, startOfMonth, endOfMonth } from 'date-fns';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import { fetchWithAuth } from '@/lib/authUtils';
import { useAuth } from '@/contexts/AuthContext';

export default function CreditUsageChart() {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [period, setPeriod] = useState<'7days' | '30days' | 'month'>('7days');
  const { token } = useAuth();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Calculate date range based on selected period
        let fromDate = new Date();
        let toDate = new Date();

        if (period === '7days') {
          fromDate = subDays(new Date(), 7);
        } else if (period === '30days') {
          fromDate = subDays(new Date(), 30);
        } else if (period === 'month') {
          fromDate = startOfMonth(new Date());
          toDate = endOfMonth(new Date());
        }

        const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
        const requestUrl = `${apiUrl}/api/admin/token-usage/daily?from=${format(fromDate, 'yyyy-MM-dd')}&to=${format(toDate, 'yyyy-MM-dd')}`;

        const response = await fetchWithAuth(requestUrl, token);

        if (!response.ok) {
          throw new Error(`Failed to fetch credit usage data: ${response.status}`);
        }

        const responseData = await response.json();
        setData(responseData.data || []);
      } catch (error) {
        console.error('Error fetching credit usage data:', error);
        setError('Failed to load credit usage data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [period, token]);

  const formatYAxis = (value: number): string => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toString();
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Credit Usage</CardTitle>
          <CardDescription>Daily credit consumption</CardDescription>
        </CardHeader>
        <CardContent className="h-80 flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Credit Usage</CardTitle>
          <CardDescription>Daily credit consumption</CardDescription>
        </CardHeader>
        <CardContent className="h-80 flex items-center justify-center">
          <div className="text-red-500">{error}</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Credit Usage</CardTitle>
            <CardDescription>Daily credit consumption</CardDescription>
          </div>
          <Tabs value={period} onValueChange={(value) => setPeriod(value as any)}>
            <TabsList>
              <TabsTrigger value="7days">7 Days</TabsTrigger>
              <TabsTrigger value="30days">30 Days</TabsTrigger>
              <TabsTrigger value="month">This Month</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={data}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis tickFormatter={formatYAxis} />
              <Tooltip
                formatter={(value: number) => [
                  value.toLocaleString(),
                  'Credits',
                ]}
                labelFormatter={(label) => `Date: ${label}`}
              />
              <Legend />
              <Bar name="Prompt Credits" dataKey="prompt_tokens" stackId="a" fill="#8884d8" />
              <Bar name="Completion Credits" dataKey="completion_tokens" stackId="a" fill="#82ca9d" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
