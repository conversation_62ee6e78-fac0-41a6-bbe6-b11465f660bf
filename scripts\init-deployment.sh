#!/bin/bash

# This script initializes the deployment environment on Digital Ocean

# Exit on error
set -e

# Create necessary directories
mkdir -p nginx/conf
mkdir -p nginx/certbot/conf
mkdir -p nginx/certbot/www
mkdir -p nginx/logs

# Copy configuration files
cp nginx/conf/app.conf nginx/conf/app.conf.template

# Replace placeholders with actual domain
read -p "Enter your domain name (without www): " DOMAIN_NAME
sed -i "s/YOUR_DOMAIN.com/$DOMAIN_NAME/g" nginx/conf/app.conf
sed -i "s/YOUR_DOMAIN.com/$DOMAIN_NAME/g" .env.docker
sed -i "s/YOUR_DOMAIN.com/$DOMAIN_NAME/g" backend/.env.production
sed -i "s/YOUR_DOMAIN.com/$DOMAIN_NAME/g" frontend/.env.production

# Copy environment files
cp .env.docker .env
cp backend/.env.production backend/.env
cp frontend/.env.production frontend/.env.local

echo "Deployment initialization complete!"
echo "Next steps:"
echo "1. Start the containers with: docker compose -f docker-compose.prod.yml up -d"
echo "2. Initialize SSL certificates with: ./scripts/init-letsencrypt.sh"
