# Server Configuration
PORT=5001
FRONTEND_URL=https://createlex.com
BACKEND_URL=https://createlex.com/api
NODE_ENV=production
BYPASS_AUTH=false
BYPASS_ADMIN_CHECK=false

# MCP Server Connection
MCP_SERVER_URL=ws://mcp_server:9877
UNREAL_ENGINE_HOST=mcp_server
UNREAL_ENGINE_PORT=9877
UNREAL_API_KEY=your_production_api_key

# Authentication
GOOGLE_CLIENT_ID=your_google_client_id

# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_supabase_service_key

# JWT Configuration
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d

# Development Mode
USE_MOCK_USER=false
ALWAYS_SUBSCRIBED=false

# Subscription/Billing
# Use your production Stripe keys here
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key

STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
BYPASS_WEBHOOK_SIGNATURE=false

# Subscription plans
STRIPE_PRICE_ID_BASIC=your_stripe_price_id_basic
STRIPE_PRICE_ID_PRO=your_stripe_price_id_pro

# Default price ID (used for backward compatibility)
STRIPE_PRICE_ID=your_stripe_price_id

# Token package price IDs
STRIPE_PRICE_ID_TOKENS_SMALL=your_stripe_price_id_tokens_small
STRIPE_PRICE_ID_TOKENS_MEDIUM=your_stripe_price_id_tokens_medium
STRIPE_PRICE_ID_TOKENS_LARGE=your_stripe_price_id_tokens_large

# Email Configuration
EMAIL_HOST=your_email_host
EMAIL_PORT=465
EMAIL_SECURE=true
EMAIL_USER=your_email_user
EMAIL_PASSWORD=your_email_password
EMAIL_FROM=your_email_from
SYSTEM_NOTIFICATION_EMAIL=your_system_notification_email

# AI Model API Keys
PS_OPENAIAPIKEY=your_openai_api_key
PS_DEEPSEEKAPIKEY=your_deepseek_api_key
PS_GOOGLEAPIKEY=your_google_api_key
PS_ANTHROPICAPIKEY=your_anthropic_api_key
