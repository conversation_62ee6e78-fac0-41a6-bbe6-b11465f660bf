# UnrealGenAI Subscription-Protected MCP Server

## Overview

This deployment approach protects your UnrealGenAI MCP Server source code and implements subscription-based access control using Docker containers, similar to the CreateLex model. The system ensures that:

1. **Source code is hidden** through Python bytecode compilation
2. **Subscription verification** controls access to the MCP server
3. **Docker containerization** simplifies deployment and security
4. **Remote hosting** is supported for SaaS deployment

## 🚀 Quick Start

### 1. Prepare the Protected Build

```bash
# Compile source code to protected bytecode
python3 compile_protected.py

# Make deployment script executable
chmod +x deploy-protected.sh

# Deploy the protected MCP server
./deploy-protected.sh deploy
```

### 2. Configuration

Copy and customize the environment file:

```bash
cp env.protected.example .env
# Edit .env with your actual values
```

### 3. Add Your License Key

```bash
# Place your license key file
echo "your-license-key-here" > config/license.key
```

## 🏗️ Architecture

### Container Structure

```
unrealgenai-mcp-server/
├── subscription_manager.py    # Main entry point with subscription control
├── protected/                 # Compiled Python modules (.pyc files)
│   ├── mcp_server.pyc        # Your protected MCP server logic
│   ├── cloud_mcp_server.pyc  # Cloud connectivity
│   └── ...                   # Other compiled modules
├── config/
│   ├── license.key           # License key (mounted securely)
│   └── subscription_cache.json # Cached subscription status
├── logs/                     # Application logs
└── cache/                    # Temporary cache data
```

### Subscription Flow

```mermaid
graph TD
    A[Container Starts] --> B[Load License Key]
    B --> C[Verify Subscription with Remote Server]
    C --> D{Valid?}
    D -->|Yes| E[Cache Subscription Status]
    D -->|No| F[Check Cached Subscription]
    F --> G{Cached Valid?}
    G -->|Yes| H[Start MCP Server]
    G -->|No| I[Exit with Error]
    E --> H
    H --> J[Periodic Subscription Checks]
    J --> K{Still Valid?}
    K -->|Yes| J
    K -->|No| L[Shutdown Server]
```

## 🔒 Security Features

### Source Code Protection

- **Bytecode Compilation**: Python source files are compiled to `.pyc` files
- **No Source Distribution**: Only compiled bytecode is included in Docker images
- **Runtime Loading**: Protected modules are loaded dynamically at runtime

### Subscription Control

- **Remote Verification**: License keys verified with your license server
- **Offline Mode**: Cached subscription allows temporary offline operation
- **Periodic Checks**: Regular subscription validation prevents unauthorized use
- **Graceful Shutdown**: Server stops immediately when subscription becomes invalid

### Container Security

- **Non-root User**: Containers run as unprivileged user
- **Read-only Mounts**: License keys mounted read-only
- **Health Checks**: Automated monitoring of service health
- **Resource Limits**: Configurable CPU and memory constraints

## 📋 Deployment Options

### Option 1: Local Development

```bash
# Start with test license
./deploy-protected.sh deploy

# View logs
./deploy-protected.sh logs

# Stop services
./deploy-protected.sh stop
```

### Option 2: Production Self-Hosted

```bash
# Update .env with production values
SUBSCRIPTION_ENDPOINT=https://your-license-server.com/api/verify
UNREALGENAI_API_KEY=your-production-api-key

# Deploy with SSL and monitoring
docker-compose -f docker-compose.protected.yml up -d
```

### Option 3: Cloud Hosting (Digital Ocean, AWS, etc.)

```bash
# Use the existing cloud deployment scripts
./deploy-to-digitalocean.sh protected

# Or use Docker Swarm/Kubernetes manifests
kubectl apply -f k8s-manifests/
```

## 🌐 Remote Hosting

### Hosting Options

1. **Cloud VPS** (Recommended)
   - Digital Ocean Droplets
   - AWS EC2 instances  
   - Google Cloud Compute Engine
   - Azure Virtual Machines

2. **Container Platforms**
   - Docker Swarm
   - Kubernetes clusters
   - AWS ECS/Fargate
   - Google Cloud Run

3. **Serverless** (Limited)
   - AWS Lambda (with custom runtime)
   - Google Cloud Functions
   - Azure Functions

### Network Configuration

```yaml
# docker-compose.protected.yml
services:
  unrealgenai-mcp:
    ports:
      - "8000:8000"  # Public access
    environment:
      - ALLOWED_ORIGINS=https://yourdomain.com
      - CORS_ENABLED=true
    networks:
      - unrealgenai-network
```

### SSL/TLS Configuration

```bash
# Generate SSL certificates
certbot certonly --standalone -d your-mcp-server.com

# Mount certificates
volumes:
  - /etc/letsencrypt/live/your-mcp-server.com:/app/ssl:ro
```

## 🔧 Configuration Reference

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `SUBSCRIPTION_ENDPOINT` | License verification URL | `https://your-license-server.com/api/verify` |
| `UNREALGENAI_API_KEY` | API key for license server | Required |
| `SUBSCRIPTION_CHECK_INTERVAL` | Check interval in seconds | `3600` |
| `MCP_HOST` | Server bind address | `0.0.0.0` |
| `MCP_PORT` | Server port | `8000` |
| `ALLOWED_ORIGINS` | CORS allowed origins | `*` |
| `LOG_LEVEL` | Logging level | `INFO` |

### License Key Format

```json
{
  "license_key": "abc123def456...",
  "product": "UnrealGenAISupport",
  "version": "1.0.0",
  "user_info": {
    "email": "<EMAIL>",
    "subscription_type": "professional"
  },
  "features": ["mcp_server", "cloud_sync", "advanced_ai"],
  "limits": {
    "max_requests_per_hour": 1000,
    "max_concurrent_connections": 10
  },
  "expires_at": **********
}
```

## 📊 Monitoring & Logs

### Health Monitoring

```bash
# Check health endpoint
curl http://localhost:8000/health

# View detailed logs
docker-compose -f docker-compose.protected.yml logs -f unrealgenai-mcp
```

### Log Levels

- `DEBUG`: Detailed debugging information
- `INFO`: General operational messages
- `WARNING`: Warning conditions
- `ERROR`: Error conditions
- `CRITICAL`: Critical error conditions

### Metrics (Optional)

Enable metrics collection in `.env`:

```bash
ENABLE_METRICS=true
```

Access metrics at: `http://localhost:8000/metrics`

## 🛠️ Troubleshooting

### Common Issues

#### 1. License Key Not Found

```
ERROR: No license key found. Mount license.key to /app/config/
```

**Solution**: Ensure `config/license.key` exists and contains your license key.

#### 2. Subscription Verification Failed

```
Subscription verification failed: HTTP 401
```

**Solution**: Check your `UNREALGENAI_API_KEY` and license key validity.

#### 3. Container Won't Start

```
ERROR: Failed to load protected MCP server module
```

**Solution**: Run `python3 compile_protected.py` to regenerate protected modules.

### Debug Mode

Enable debug logging:

```bash
# Set in .env
LOG_LEVEL=DEBUG

# Restart container
./deploy-protected.sh restart
```

## 📚 Comparison with CreateLex

| Feature | CreateLex | UnrealGenAI Protected |
|---------|-----------|----------------------|
| Source Protection | ✅ Compiled .pyc | ✅ Compiled .pyc |
| Subscription Control | ✅ Google/GitHub OAuth | ✅ License Key System |
| Docker Deployment | ✅ | ✅ |
| Remote Hosting | ✅ | ✅ |
| Offline Mode | ✅ Cached auth | ✅ Cached subscription |
| Periodic Validation | ✅ | ✅ |
| Graceful Shutdown | ✅ | ✅ |

## 🚀 Production Deployment Checklist

- [ ] Replace test license with production license key
- [ ] Update `SUBSCRIPTION_ENDPOINT` to your license server
- [ ] Set strong `UNREALGENAI_API_KEY`
- [ ] Configure SSL/TLS certificates
- [ ] Set up monitoring and alerting
- [ ] Configure backup and disaster recovery
- [ ] Test subscription validation and renewal
- [ ] Set up log aggregation
- [ ] Configure firewall and network security
- [ ] Plan for updates and maintenance

## 📞 Support

For questions or issues with the protected deployment:

1. Check logs: `./deploy-protected.sh logs`
2. Verify subscription status: `curl http://localhost:8000/health`
3. Review configuration in `.env`
4. Consult the troubleshooting section above

---

**Note**: This deployment model provides strong protection for your intellectual property while maintaining the flexibility of containerized deployment. The subscription control ensures only authorized users can access your MCP server functionality. 