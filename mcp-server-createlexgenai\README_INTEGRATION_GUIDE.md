# UnrealGenAI Docker Integration Guide

## 🎯 Overview

This guide shows you how to integrate Docker-based subscription protection with your **existing frontend and backend infrastructure**. Instead of creating a separate license server, this approach leverages your current API endpoints and dashboard authentication system.

## 🏗️ Architecture Integration

### Your Existing System
```
┌─────────────────┐    ┌─────────────────┐
│   Dashboard     │    │    Backend      │
│  (Frontend)     │    │   (Node.js)     │
│                 │────│                 │
│ - User Login    │    │ - JWT Auth      │
│ - Subscription  │    │ - Stripe API    │
│   Status Check  │    │ - /api/sub/*    │
└─────────────────┘    └─────────────────┘
```

### Enhanced with Docker Protection
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Dashboard     │    │    Backend      │    │  Protected MCP  │
│  (Frontend)     │    │   (Node.js)     │    │    Server       │
│                 │────│                 │────│   (Docker)      │
│ - User Login    │    │ - JWT Auth      │    │ - Source Hidden │
│ - Subscription  │    │ - Stripe API    │    │ - Sub Protected │
│   Status Check  │    │ - /api/sub/*    │    │ - Health Check  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                   │
                       ┌─────────────────┐
                       │ Token Extractor │
                       │   (Docker)      │
                       │                 │
                       │ - JWT Monitor   │
                       │ - Auto Extract  │
                       └─────────────────┘
```

## 🔄 Integration Flow

### 1. User Authentication Flow
```mermaid
sequenceDiagram
    participant U as User
    participant D as Dashboard
    participant B as Backend
    participant T as Token Extractor
    participant M as MCP Server

    U->>D: Login (Google/GitHub)
    D->>B: Authenticate
    B-->>D: JWT Token
    D->>B: Check Subscription
    B-->>D: Subscription Status
    
    T->>B: Monitor Sessions
    B-->>T: Active Token Info
    T->>T: Extract Valid JWT
    T->>M: Provide Token
    
    M->>B: Verify Subscription (using JWT)
    B-->>M: Subscription Valid
    M->>M: Start Protected Server
```

### 2. Subscription Verification
Your existing endpoints are used directly:
- `GET /api/subscription/check` - Main subscription validation
- `GET /api/subscription/status` - Subscription status
- `GET /api/subscription/isPremium` - Premium tier check

## 🚀 Quick Start Integration

### Step 1: Use Your Existing Environment Variables

Create `.env` file with your current settings:
```bash
# Your existing backend configuration
GOOGLE_CLIENT_ID=your-google-client-id
JWT_SECRET=your-jwt-secret
STRIPE_SECRET_KEY=your-stripe-secret
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable
STRIPE_PRICE_ID=your-stripe-price-id
STRIPE_PRICE_ID_PRO=your-pro-price-id
STRIPE_PRICE_ID_BASIC=your-basic-price-id

# New Docker integration settings
API_BASE_URL=http://backend:5001
ALWAYS_SUBSCRIBED=false
SUBSCRIPTION_CHECK_INTERVAL=3600

# Development mode (for testing)
DEMO_JWT_TOKEN=your-test-jwt-token
```

### Step 2: Deploy with Existing Services

```bash
# Navigate to the server directory
cd UnrealGenAISupport_with_server/server

# Compile your source code for protection
python3 compile_protected.py

# Deploy with your existing services
docker-compose -f docker-compose.integrated.yml up -d
```

### Step 3: Verify Integration

```bash
# Check if MCP server is running and protected
curl http://localhost:8001/health

# Check if backend integration is working
curl -H "Authorization: Bearer YOUR_JWT" http://localhost:5001/api/subscription/check

# Check if frontend can reach MCP server
curl http://localhost:8000/status
```

## 🔧 Configuration Options

### Environment Variables

| Variable | Description | Your Current Usage |
|----------|-------------|-------------------|
| `API_BASE_URL` | Your backend URL | `http://backend:5001` |
| `ALWAYS_SUBSCRIBED` | Dev override | Uses your `ALWAYS_SUBSCRIBED` |
| `DEMO_JWT_TOKEN` | Test token | Extract from your dashboard |
| `SUBSCRIPTION_CHECK_INTERVAL` | Check frequency | `3600` (1 hour) |

### JWT Token Sources

The system can extract JWT tokens from multiple sources:

1. **Environment Variable** (for development):
   ```bash
   DEMO_JWT_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   ```

2. **Shared File System** (for container communication):
   ```bash
   # Dashboard writes token to shared volume
   echo "jwt-token" > /shared/tokens/current_user.jwt
   ```

3. **Admin API Endpoint** (recommended for production):
   ```javascript
   // Add to your backend: GET /api/admin/latest-active-token
   app.get('/api/admin/latest-active-token', adminAuth, (req, res) => {
     // Return the most recent active user token
     res.json({ token: mostRecentToken, userId: userId });
   });
   ```

## 🔌 Backend Integration Points

### Required: No Changes
Your existing endpoints work as-is:
- ✅ `GET /api/subscription/check`
- ✅ `GET /api/subscription/status` 
- ✅ `GET /api/subscription/isPremium`

### Optional: Enhanced Integration

Add these endpoints for better integration:

#### 1. Active Sessions Endpoint
```javascript
// GET /api/admin/active-sessions
app.get('/api/admin/active-sessions', adminAuth, async (req, res) => {
  try {
    const sessions = await getActiveSessions(); // Your implementation
    res.json({ sessions });
  } catch (error) {
    res.status(500).json({ error: 'Failed to get sessions' });
  }
});
```

#### 2. Latest Active Token Endpoint
```javascript
// GET /api/admin/latest-active-token
app.get('/api/admin/latest-active-token', adminAuth, async (req, res) => {
  try {
    const activeUser = await getMostRecentActiveUser(); // Your implementation
    if (activeUser && activeUser.token) {
      res.json({ 
        token: activeUser.token, 
        userId: activeUser.id,
        lastActive: activeUser.lastActive
      });
    } else {
      res.status(404).json({ error: 'No active user found' });
    }
  } catch (error) {
    res.status(500).json({ error: 'Failed to get active token' });
  }
});
```

## 🎛️ Dashboard Integration

### Option 1: No Changes Required
Your dashboard continues to work exactly as before. The Docker system monitors your existing backend.

### Option 2: Enhanced Integration
Add MCP server status to your dashboard:

```typescript
// Add to your dashboard component
const [mcpServerStatus, setMcpServerStatus] = useState(null);

useEffect(() => {
  const checkMcpServer = async () => {
    try {
      const response = await fetch('http://localhost:8001/health');
      const data = await response.json();
      setMcpServerStatus(data);
    } catch (error) {
      setMcpServerStatus({ status: 'offline' });
    }
  };
  
  checkMcpServer();
  const interval = setInterval(checkMcpServer, 30000);
  return () => clearInterval(interval);
}, []);
```

```jsx
// Add to your dashboard UI
{mcpServerStatus && (
  <div className="mt-4">
    <h3>MCP Server Status</h3>
    <div className={`status ${mcpServerStatus.status === 'healthy' ? 'online' : 'offline'}`}>
      {mcpServerStatus.status === 'healthy' ? '🟢 Online' : '🔴 Offline'}
    </div>
    {mcpServerStatus.subscription_active && (
      <div className="text-sm text-green-600">
        ✅ Subscription Active (Plan: {mcpServerStatus.plan})
      </div>
    )}
  </div>
)}
```

## 🐳 Docker Deployment Scenarios

### Scenario 1: Development (Same Machine)
```bash
# Run everything locally
docker-compose -f docker-compose.integrated.yml up

# Your services:
# - Frontend: http://localhost:3000
# - Backend: http://localhost:5001  
# - MCP Server: http://localhost:8000
# - Health Check: http://localhost:8001/health
```

### Scenario 2: Production (Remote Server)
```bash
# Update environment for remote deployment
export API_BASE_URL=https://your-backend.com
export ALWAYS_SUBSCRIBED=false

# Deploy to remote server
docker-compose -f docker-compose.integrated.yml up -d

# Access via:
# - MCP Server: https://your-mcp-server.com:8000
# - Health: https://your-mcp-server.com:8001/health
```

### Scenario 3: Cloud Deployment
```bash
# Use with DigitalOcean, AWS, etc.
docker stack deploy -c docker-compose.integrated.yml unrealgenai

# Or with Kubernetes
kubectl apply -f k8s-manifests/
```

## 🔒 Security Benefits

### Source Code Protection
- ✅ Python source compiled to `.pyc` bytecode
- ✅ No source code in Docker images
- ✅ Runtime-only module loading

### Subscription Enforcement
- ✅ Real-time verification with your existing backend
- ✅ Automatic shutdown on subscription failure
- ✅ Periodic re-validation
- ✅ Offline mode with cached validation

### Container Security
- ✅ Non-root containers
- ✅ Read-only token mounts
- ✅ Network isolation
- ✅ Resource limits

## 📊 Monitoring & Health Checks

### Built-in Health Endpoint
```bash
curl http://localhost:8001/health
```

Response:
```json
{
  "status": "healthy",
  "subscription_active": true,
  "user_id": "user123",
  "plan": "pro",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Integration with Your Monitoring
Add to your existing monitoring stack:

```yaml
# Prometheus scrape config
- job_name: 'unrealgenai-mcp'
  static_configs:
    - targets: ['localhost:8001']
  metrics_path: '/metrics'
```

## 🚨 Troubleshooting

### Common Issues

#### 1. "No authentication token found"
**Problem**: MCP server can't find JWT token
**Solutions**:
- Check if `auth_token.jwt` file exists in `config/` directory
- Verify token extractor is running: `docker logs unrealgenai-token-extractor`
- Set `DEMO_JWT_TOKEN` environment variable for testing

#### 2. "Subscription verification failed"
**Problem**: Backend API not responding
**Solutions**:
- Verify backend is running: `curl http://localhost:5001/api/subscription/check`
- Check network connectivity between containers
- Verify JWT token is valid: decode token at jwt.io

#### 3. "Protected modules not found"
**Problem**: Source code not compiled
**Solutions**:
```bash
# Recompile protected modules
python3 compile_protected.py

# Rebuild Docker image
docker-compose build unrealgenai-mcp
```

### Debug Mode
Enable debug logging:
```bash
# Set in .env
LOG_LEVEL=DEBUG

# Restart services
docker-compose restart
```

## 🔄 Migration from Current Setup

### Step-by-Step Migration

1. **Backup Current Setup**
   ```bash
   # Backup your current configuration
   cp -r UnrealGenAISupport_with_server/server UnrealGenAISupport_with_server/server.backup
   ```

2. **Test in Parallel**
   ```bash
   # Run Docker version alongside current setup
   docker-compose -f docker-compose.integrated.yml up -d
   
   # Test both versions
   curl http://localhost:8000/health  # Docker version
   curl http://localhost:5001/api/subscription/check  # Current backend
   ```

3. **Gradual Cutover**
   ```bash
   # Update frontend to point to Docker MCP server
   NEXT_PUBLIC_MCP_SERVER_URL=http://localhost:8000
   
   # Monitor both systems during transition
   ```

4. **Full Migration**
   ```bash
   # Stop old MCP server
   # Update all client configurations
   # Remove old server files
   ```

## 📋 Production Checklist

- [ ] Source code compiled to bytecode (`python3 compile_protected.py`)
- [ ] JWT token extraction configured
- [ ] Backend API endpoints accessible
- [ ] Health checks responding
- [ ] Subscription verification working
- [ ] SSL/TLS certificates configured (for remote hosting)
- [ ] Monitoring and alerting set up
- [ ] Backup and disaster recovery planned
- [ ] Documentation updated for team

## 🎉 Benefits Summary

✅ **Keeps Your Current System**: No changes to your existing frontend/backend
✅ **Hides Source Code**: Docker + bytecode compilation protects IP
✅ **Subscription Protected**: Uses your existing Stripe/auth system
✅ **Easy Deployment**: Docker Compose handles everything
✅ **Remote Hosting Ready**: Deploy anywhere Docker runs
✅ **Health Monitoring**: Built-in status and health checks
✅ **Development Friendly**: Override flags for testing

This integration approach gives you the subscription protection and source code hiding benefits of Docker while leveraging all your existing infrastructure investments. 