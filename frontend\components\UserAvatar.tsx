import React, { useState } from 'react';
import Link from 'next/link';

interface UserAvatarProps {
  src?: string | null;
  alt?: string;
  email?: string | null;
  size?: 'sm' | 'md' | 'lg';
  linkToProfile?: boolean;
}

const UserAvatar: React.FC<UserAvatarProps> = ({
  src,
  alt = 'User',
  email,
  size = 'md',
  linkToProfile = true
}) => {
  const [imageError, setImageError] = useState(false);
  
  // Determine size classes
  const sizeClasses = {
    sm: 'h-6 w-6 text-xs',
    md: 'h-8 w-8 text-sm',
    lg: 'h-12 w-12 text-base'
  };
  
  // Get first letter of email for fallback
  const firstLetter = email?.charAt(0).toUpperCase() || alt?.charAt(0).toUpperCase() || 'U';
  
  // Render fallback avatar
  const FallbackAvatar = () => (
    <div 
      className={`${sizeClasses[size]} rounded-full bg-blue-500 flex items-center justify-center text-white`}
    >
      {firstLetter}
    </div>
  );
  
  // Render image avatar
  const ImageAvatar = () => (
    <img
      src={src || ''}
      alt={alt}
      className={`${sizeClasses[size]} rounded-full object-cover`}
      onError={() => setImageError(true)}
    />
  );
  
  // Determine which avatar to show
  const Avatar = () => {
    if (!src || imageError) {
      return <FallbackAvatar />;
    }
    return <ImageAvatar />;
  };
  
  // Wrap in link if needed
  if (linkToProfile) {
    return (
      <Link href="/profile" className="cursor-pointer">
        <Avatar />
      </Link>
    );
  }
  
  return <Avatar />;
};

export default UserAvatar;
