# Development Automation Scripts

This folder contains critical automation scripts for streamlined development workflow with CreatelexGenAI Unreal Engine plugin and MCP Bridge.

## 🎯 Development Workflow Rule

**CRITICAL**: Every time we make any code changes, we test by running the instant refresh script and check Unreal Engine project logs to debug and fix issues.

## 📁 Scripts Overview

### `dev_refresh_bypass.bat` - Main Development Refresh Script
**Purpose**: Complete development environment refresh for CreatelexGenAI plugin testing
**Usage**: Run this script after ANY code changes to the plugin

**What it does**:
1. **Process Cleanup**: Kills all existing MCP/Bridge processes and terminals
2. **Plugin Refresh**: 
   - Stops Unreal Editor and build tools
   - Cleans build artifacts (Binaries, Intermediate, .vs)
   - Removes old plugin and installs fresh copy
   - Pre-builds the project
3. **Service Startup**: 
   - Starts MCP Bridge in bypass mode (no login required)
   - Launches Unreal Editor with proper flags
4. **Verification**: Checks MCP connection and UE Editor status

**Key Benefits**:
- ✅ No manual OAuth authentication required
- ✅ No button clicking needed  
- ✅ Automatic MCP server startup
- ✅ Clean slate for each test iteration
- ✅ Complete automation of development cycle

### `kill_all_mcp.bat` - Process & Terminal Cleanup
**Purpose**: Safely terminates all MCP and Bridge processes while preserving development workflow scripts

**Advanced Features**:
- **Smart Protection**: Automatically detects and protects current development scripts
- **Multiple Cleanup Methods**: 
  - Process termination by PID and command line patterns
  - Window handle-based closing for stuck terminals
  - Port-based cleanup (9877, 7891, 3000)
- **Conservative Targeting**: Only closes specific OLD MCP/Bridge windows
- **Triple Safety**: Current PID + Parent PID + Protected PID list

**Safe Operation**: Will NOT close:
- `dev_refresh_bypass.bat` windows
- `kill_all_mcp.bat` windows  
- Any other development workflow scripts

### `start_mcp_bypass_simple.bat` - Standalone Bridge Startup
**Purpose**: Start MCP Bridge in bypass mode without full development refresh

**Features**:
- Environment variable setup for bypass mode
- Automatic bridge startup with no authentication
- Status verification and error handling

## 🔧 Environment Setup

### Required Paths (update these in scripts if different):
```batch
PROJECT_DIR=C:\Dev\YourLife
SOURCE_PLUGIN=C:\Dev\AiWebplatform\CreatelexGenAI
BRIDGE_DIR=C:\Dev\AiWebplatform\createlex-bridge
UE_EDITOR="C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor.exe"
```

### Bypass Mode Environment Variables:
```batch
NODE_ENV=development
DEV_MODE=true
BYPASS_SUBSCRIPTION=true
BYPASS_LOGIN=true
AUTO_START_MCP=true
SKIP_AUTH=true
CREATELEX_BASE_URL=http://localhost:3000
API_BASE_URL=http://localhost:5001/api
```

## 🚀 Usage Instructions

### Primary Development Workflow:
1. Make code changes to CreatelexGenAI plugin
2. Run `dev_refresh_bypass.bat`
3. Check logs: `C:\Dev\YourLife\Saved\Logs\YourLife.log`
4. Debug and fix any issues found
5. Repeat process for each change

### Standalone Operations:
- **Clean processes only**: Run `kill_all_mcp.bat`
- **Start bridge only**: Run `start_mcp_bypass_simple.bat`

## 📊 Verification Steps

After running `dev_refresh_bypass.bat`, verify:

1. **MCP Bridge**: Check port 9877 is active
   ```cmd
   netstat -an | find ":9877"
   ```

2. **Unreal Editor**: Verify process is running
   ```cmd
   tasklist | find "UnrealEditor.exe"
   ```

3. **Plugin Load**: Check UE Editor logs for CreatelexGenAI plugin loading
4. **MCP Connection**: Test connection between UE and MCP bridge

## 🛡️ Safety Features

### Terminal Protection System:
- **Current Process Detection**: Scripts identify their own PID
- **Parent Process Protection**: Calling scripts are preserved
- **Safe Targeting**: Only specific OLD MCP/Bridge titles are closed
- **Conservative Cleanup**: Multiple verification checks before termination

### Process Cleanup Phases:
1. **Electron processes** (createlex-bridge app)
2. **Node.js bridge processes** (command line detection)
3. **Python MCP servers** (mcp_server patterns)
4. **Network port cleanup** (9877, 7891, 3000)
5. **Terminal window cleanup** (title and handle-based)
6. **Final verification** (port status check)

## 🔍 Troubleshooting

### Common Issues:
- **Script closes itself**: Check terminal protection logic
- **Bridge won't start**: Verify npm dependencies and paths
- **UE Editor fails**: Check engine path and project configuration
- **Port conflicts**: Run `kill_all_mcp.bat` to free ports

### Debug Information:
- Scripts output detailed progress information
- Each phase shows status and PID information
- Error messages indicate specific failure points

## 📝 Maintenance Notes

### When to Update Scripts:
- Unreal Engine version changes (update UE_EDITOR path)
- Project location changes (update PROJECT_DIR, SOURCE_PLUGIN)
- New MCP server patterns (update cleanup detection logic)
- Additional bypass requirements (update environment variables)

### Version History:
- **v1.0**: Basic process cleanup and restart
- **v2.0**: Added bypass mode and window protection
- **v3.0**: Enhanced terminal cleanup with handle-based closing
- **v4.0**: Safe and conservative cleanup with triple protection

## 🎯 Best Practices

1. **Always run scripts from proper working directory**
2. **Check logs after each refresh cycle**
3. **Verify MCP connection before testing plugin features**
4. **Keep backup copies of working script versions**
5. **Update documentation when modifying script behavior**

---

*These scripts are essential for maintaining efficient development iteration cycles with the CreatelexGenAI Unreal Engine plugin and MCP Bridge architecture.*