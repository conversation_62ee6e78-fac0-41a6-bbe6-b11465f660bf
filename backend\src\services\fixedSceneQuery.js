/**
 * Fixed scene query function for the McpBridge
 * This function queries the scene and returns a properly formatted result
 */

const queryScene = async (bridge) => {
  return new Promise((resolve, reject) => {
    const pythonScript = `
import unreal
import json

# Get all actors in the current level
actors = unreal.EditorLevelLibrary.get_all_level_actors()

# Format the result
result = {
    "success": True,
    "actors": []
}

for actor in actors:
    actor_info = {
        "name": actor.get_actor_label(),
        "class": actor.get_class().get_name(),
        "location": [actor.get_actor_location().x, actor.get_actor_location().y, actor.get_actor_location().z],
        "rotation": [actor.get_actor_rotation().roll, actor.get_actor_rotation().pitch, actor.get_actor_rotation().yaw],
        "scale": [actor.get_actor_scale3d().x, actor.get_actor_scale3d().y, actor.get_actor_scale3d().z]
    }
    result["actors"].append(actor_info)

# Print the result as JSO<PERSON> (only once at the end)
print(json.dumps(result))
`;

    bridge.sendCommand({
      command: 'execute_python',
      params: {
        script: pythonScript
      }
    }, (err, res) => {
      if (err) {
        console.error('[FixedSceneQuery] Error executing Python script:', err);
        reject(err);
        return;
      }

      try {
        // Parse the output as JSON
        const outputStr = res.output.trim();
        const lastJsonObject = outputStr.split('\n').pop(); // Get the last line
        const sceneData = JSON.parse(lastJsonObject);
        
        console.log(`[FixedSceneQuery] Found ${sceneData.actors.length} actors in the scene`);
        
        // Count actor types
        const actorTypes = {};
        sceneData.actors.forEach(actor => {
          const type = actor.class;
          actorTypes[type] = (actorTypes[type] || 0) + 1;
        });
        
        console.log('[FixedSceneQuery] Actor types:');
        Object.entries(actorTypes).forEach(([type, count]) => {
          console.log(`  ${count} ${type}${count > 1 ? 's' : ''}`);
        });
        
        resolve(sceneData);
      } catch (e) {
        console.error('[FixedSceneQuery] Error parsing JSON output:', e);
        console.log('[FixedSceneQuery] Raw output:', res.output);
        reject(e);
      }
    });
  });
};

module.exports = queryScene;
