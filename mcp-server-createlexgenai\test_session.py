#!/usr/bin/env python3
import requests
import json

# Test initialize request
payload = {
    'jsonrpc': '2.0',
    'method': 'initialize',
    'id': 0,
    'params': {
        'protocolVersion': '2024-11-05',
        'capabilities': {},
        'clientInfo': {'name': 'test-client', 'version': '1.0.0'}
    }
}

headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json, text/event-stream'
}

print("Testing initialize request...")
response = requests.post('http://localhost:8000/mcp/', json=payload, headers=headers)
print(f'Status: {response.status_code}')
print(f'Headers: {dict(response.headers)}')
print(f'Response: {response.text[:300]}...')

# Check for session ID in headers
session_id = response.headers.get('mcp-session-id')
if session_id:
    print(f'Session ID found: {session_id}')
    
    # Test tools/list with session ID
    print("\nTesting tools/list with session ID...")
    tools_payload = {
        'jsonrpc': '2.0',
        'method': 'tools/list',
        'id': 1,
        'params': {}
    }
    
    headers_with_session = headers.copy()
    headers_with_session['mcp-session-id'] = session_id
    
    tools_response = requests.post('http://localhost:8000/mcp/', json=tools_payload, headers=headers_with_session)
    print(f'Tools Status: {tools_response.status_code}')
    print(f'Tools Response: {tools_response.text[:300]}...')
else:
    print('No session ID found in headers') 