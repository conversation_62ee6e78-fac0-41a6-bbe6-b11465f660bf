require('dotenv').config();
const os = require('os');
const crypto = require('crypto');

// Simulate what CreateLex Bridge does
class BridgeDeviceTest {
  constructor() {
    this.deviceId = this.generateDeviceId();
    this.deviceInfo = this.getDeviceInfo();
  }

  generateDeviceId() {
    const cpus = os.cpus();
    const networkInterfaces = os.networkInterfaces();
    const hostname = os.hostname();
    const platform = os.platform();
    const arch = os.arch();
    
    const hardwareString = JSON.stringify({
      hostname,
      platform,
      arch,
      cpuModel: cpus[0]?.model || 'unknown',
      cpuCount: cpus.length,
      macAddresses: Object.values(networkInterfaces)
        .flat()
        .filter(iface => !iface.internal && iface.mac && iface.mac !== '00:00:00:00:00:00')
        .map(iface => iface.mac)
        .sort()
    });
    
    return crypto.createHash('sha256').update(hardwareString).digest('hex');
  }

  getDeviceInfo() {
    const platform = os.platform();
    const arch = os.arch();
    const hostname = os.hostname();
    
    // Generate user-friendly platform name
    let platformName = `${platform}-${arch}`;
    if (platform === 'win32') {
      platformName = `Windows-${arch}`;
    } else if (platform === 'darwin') {
      platformName = `macOS-${arch}`;
    } else if (platform === 'linux') {
      platformName = `Linux-${arch}`;
    }
    
    return {
      deviceId: this.deviceId,
      deviceName: hostname,
      platform: platformName,
      osVersion: os.release()
    };
  }

  async testDeviceRegistration() {
    console.log('🧪 Testing CreateLex Bridge Device Registration...');
    console.log('📱 Device Info:', this.deviceInfo);
    
    try {
      // Test the device registration endpoint
      const response = await fetch('http://localhost:5001/api/device/check-seat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer mock-token',
          'x-user-id': '5af3ff0f-0ccb-41a2-8dba-3e460aa9bd72' // Test user
        },
        body: JSON.stringify({
          deviceInfo: this.deviceInfo,
          subscriptionPlan: 'basic'
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Device registration successful:', result);
        
        // Now check if it appears in the device list
        console.log('\n📋 Checking device list...');
        const listResponse = await fetch('http://localhost:5001/api/device/seats', {
          headers: {
            'Authorization': 'Bearer mock-token',
            'x-user-id': '5af3ff0f-0ccb-41a2-8dba-3e460aa9bd72'
          }
        });

        if (listResponse.ok) {
          const devices = await listResponse.json();
          console.log('📱 Current devices:', devices);
          
          // Check if our device is in the list
          const ourDevice = devices.devices.find(d => d.device_id === this.deviceId);
          if (ourDevice) {
            console.log('🎉 SUCCESS: Device found in database!');
            console.log('   Device Name:', ourDevice.device_name);
            console.log('   Platform:', ourDevice.platform);
            console.log('   Status:', ourDevice.is_active ? 'ACTIVE' : 'INACTIVE');
          } else {
            console.log('❌ Device not found in list');
          }
        }
      } else {
        const error = await response.text();
        console.error('❌ Registration failed:', response.status, error);
      }
    } catch (error) {
      console.error('❌ Test failed:', error.message);
    }
  }
}

// Add fetch polyfill for Node.js
const fetch = require('node-fetch');

const test = new BridgeDeviceTest();
test.testDeviceRegistration(); 