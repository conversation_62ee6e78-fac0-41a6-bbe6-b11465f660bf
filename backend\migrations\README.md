# Supabase Migration Guide

This directory contains SQL scripts to set up the necessary tables in Supabase for the AI Webplatform.

## Setting Up Supabase

1. Create a Supabase account at [supabase.com](https://supabase.com)
2. Create a new project
3. Get your Supabase URL and service key from the project settings
4. Add these to your `.env` file:
   ```
   SUPABASE_URL=https://your-project-id.supabase.co
   SUPABASE_SERVICE_KEY=your-service-key
   ```
5. Run the SQL scripts in the Supabase SQL editor in the following order:
   - `create_users_table.sql`

## Table Structure

### Users Table

The `users` table stores user information and subscription status:

- `id`: User ID (from Supabase Auth or Google Auth)
- `email`: User's email address
- `name`: User's name
- `picture`: URL to user's profile picture
- `subscription_status`: Current subscription status (active, inactive, canceled)
- `stripe_customer_id`: Stripe customer ID
- `subscription_id`: Stripe subscription ID
- `created_at`: When the user was created
- `subscription_updated_at`: When the subscription was last updated
- `last_checkout_session_id`: Last Stripe checkout session ID

## Row Level Security (RLS)

The SQL scripts set up Row Level Security to ensure that:

1. Users can only view their own data
2. Only the service role can insert or update user data

## Migrating Existing Users

If you have existing users in the file-based storage, you can migrate them to Supabase by:

1. Extracting the user data from the `data/users.json` file
2. Formatting it to match the Supabase table structure
3. Inserting the data into the Supabase `users` table

Example migration script:

```javascript
const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Load users from file
const usersFile = path.join(__dirname, '../../data/users.json');
const userData = JSON.parse(fs.readFileSync(usersFile, 'utf8'));

// Convert users to Supabase format
const supabaseUsers = Object.values(userData).map(user => ({
  id: user.id,
  email: user.email,
  name: user.name,
  picture: user.picture,
  subscription_status: user.subscriptionStatus || 'inactive',
  stripe_customer_id: user.stripeCustomerId,
  subscription_id: user.subscriptionId,
  created_at: user.createdAt || new Date().toISOString(),
  subscription_updated_at: user.subscriptionUpdatedAt,
  last_checkout_session_id: user.lastCheckoutSessionId
}));

// Insert users into Supabase
async function migrateUsers() {
  const { data, error } = await supabase
    .from('users')
    .upsert(supabaseUsers);

  if (error) {
    console.error('Error migrating users:', error);
  } else {
    console.log(`Successfully migrated ${supabaseUsers.length} users`);
  }
}

migrateUsers();
```
