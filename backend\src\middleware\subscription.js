const subscriptionService = require('../services/subscriptionService');

/**
 * Middleware to check if user has an active subscription
 * Should be used after authenticateJWT middleware
 */
const checkSubscription = async (req, res, next) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'User must be authenticated to access this resource'
      });
    }

    const userId = req.user.id;
    console.log(`[Subscription Middleware] Checking subscription for user: ${userId}`);

    // For development purposes, always allow if ALWAYS_SUBSCRIBED is set
    if (process.env.ALWAYS_SUBSCRIBED === 'true') {
      console.log(`[Subscription Middleware] ALWAYS_SUBSCRIBED is true, allowing access for user: ${userId}`);
      return next();
    }

    // Check if the user has an active subscription
    const hasActiveSubscription = await subscriptionService.checkSubscription(userId);
    
    if (!hasActiveSubscription) {
      console.log(`[Subscription Middleware] User ${userId} does not have active subscription`);
      return res.status(403).json({
        error: 'Active subscription required',
        message: 'You need an active subscription to access MCP server updates',
        hasActiveSubscription: false
      });
    }

    console.log(`[Subscription Middleware] User ${userId} has active subscription, allowing access`);
    next();

  } catch (error) {
    console.error('[Subscription Middleware] Error checking subscription:', error);
    return res.status(500).json({
      error: 'Subscription check failed',
      message: 'Unable to verify subscription status'
    });
  }
};

module.exports = {
  checkSubscription
}; 