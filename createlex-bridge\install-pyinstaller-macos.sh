#!/bin/bash

echo "📦 Installing PyInstaller for macOS"
echo "=================================="

# Find the best Python version (same logic as the build script)
python_versions=("python3.12" "python3.11" "python3.10" "python3" "python")
best_python=""

for cmd in "${python_versions[@]}"; do
    if command -v "$cmd" &> /dev/null; then
        version_output=$($cmd --version 2>&1)
        if [[ $version_output =~ Python\ ([0-9]+)\.([0-9]+) ]]; then
            major=${BASH_REMATCH[1]}
            minor=${BASH_REMATCH[2]}
            echo "🐍 Found $cmd: Python $major.$minor"
            
            # Prefer Python 3.10+ for FastMCP compatibility
            if [ "$major" -eq 3 ] && [ "$minor" -ge 10 ]; then
                best_python="$cmd"
                echo "✅ Selected $cmd for PyInstaller installation"
                break
            elif [ "$major" -eq 3 ]; then
                best_python="$cmd"
                echo "⚠️  Selected $cmd (may have compatibility issues)"
                break
            fi
        fi
    fi
done

if [ -z "$best_python" ]; then
    echo "❌ No suitable Python found"
    exit 1
fi

echo ""
echo "📦 Installing PyInstaller with $best_python..."
$best_python -m pip install --user pyinstaller

echo ""
echo "🧪 Testing PyInstaller installation..."
if $best_python -m PyInstaller --version; then
    echo "✅ PyInstaller installed successfully!"
else
    echo "❌ PyInstaller installation failed"
    exit 1
fi

echo ""
echo "🚀 Ready to build MCP executable:"
echo "   npm run build:mcp-exe" 