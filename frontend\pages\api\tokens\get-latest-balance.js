// API route to get the latest token balance directly from the database

// Simple in-memory cache for token balances
let tokenBalanceCache = {};
let lastFetchTimes = {};
const CACHE_TTL = 60000; // 1 minute cache TTL

export default async function handler(req, res) {
  try {
    // Get the user ID from the query parameters
    const { userId, forceRefresh } = req.query;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Check if we should use cached data
    const now = Date.now();
    const lastFetchTime = lastFetchTimes[userId] || 0;
    const timeSinceLastFetch = now - lastFetchTime;
    const shouldUseCache = !forceRefresh && tokenBalanceCache[userId] && timeSinceLastFetch < CACHE_TTL;

    if (shouldUseCache) {
      console.log('[get-latest-balance] Using cached token balance for user:', userId);
      return res.status(200).json({
        ...tokenBalanceCache[userId],
        fromCache: true
      });
    }

    console.log('[get-latest-balance] Fetching latest token balance for user:', userId);

    // Add a timestamp to prevent caching
    const timestamp = Date.now();

    // Make a direct call to the backend to get the latest token balance
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
    const response = await fetch(`${apiUrl}/api/tokens/balance?userId=${userId}&t=${timestamp}&forceRefresh=true&direct=true`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-user-id': userId,
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      },
    });

    // Handle rate limiting specifically
    if (response.status === 429) {
      console.warn('[get-latest-balance] Rate limit exceeded for token balance fetch');

      // If we have cached data, use it even if it's older than the TTL
      if (tokenBalanceCache[userId]) {
        console.log('[get-latest-balance] Using stale cached token balance due to rate limiting');
        return res.status(200).json({
          ...tokenBalanceCache[userId],
          fromCache: true,
          cacheReason: 'rate_limited'
        });
      }

      // If we don't have cached data, return a 429 status with retry information
      return res.status(429).json({
        error: 'Rate limit exceeded. Please try again later.',
        retryAfter: 60 // Suggest retry after 60 seconds
      });
    }

    // Handle other errors
    if (!response.ok) {
      console.error(`[get-latest-balance] Backend API error: ${response.status}`);

      // If we have cached data, use it even if it's older than the TTL
      if (tokenBalanceCache[userId]) {
        console.log('[get-latest-balance] Using stale cached token balance due to backend error');
        return res.status(200).json({
          ...tokenBalanceCache[userId],
          fromCache: true,
          cacheReason: 'backend_error'
        });
      }

      return res.status(response.status).json({ error: `Backend API error: ${response.status}` });
    }

    const data = await response.json();
    console.log('[get-latest-balance] Latest token balance data:', data);

    // Cache the token balance data
    tokenBalanceCache[userId] = data;
    lastFetchTimes[userId] = now;

    // Return the data to the frontend
    res.status(200).json(data);
  } catch (error) {
    console.error('[get-latest-balance] Error fetching latest token balance:', error);

    // If we have cached data, use it in case of error
    const { userId } = req.query;
    if (userId && tokenBalanceCache[userId]) {
      console.log('[get-latest-balance] Using cached token balance due to error');
      return res.status(200).json({
        ...tokenBalanceCache[userId],
        fromCache: true,
        cacheReason: 'error'
      });
    }

    res.status(500).json({ error: 'Failed to fetch latest token balance' });
  }
}
