/**
 * Utility functions for generating Python scripts for Unreal Engine
 */

/**
 * Generate a Python script to create an unlit material with a specific color
 *
 * @param {string} materialName - The name of the material to create
 * @param {Array<number>} color - RGB color values as an array of 3 numbers between 0 and 1
 * @param {string} [actorName] - Optional actor name to apply the material to
 * @returns {string} - The Python script to create the material
 */
function generateUnlitMaterialScript(materialName, color, actorName = null) {
  // Ensure color is valid
  if (!Array.isArray(color) || color.length !== 3) {
    color = [1, 1, 1]; // Default to white if invalid color
  }

  // Format the color values
  const [r, g, b] = color;

  // Create the base script for material creation
  let script = `import unreal

# Create a new material specifically set to unlit mode
material_factory = unreal.MaterialFactoryNew()
asset_tools = unreal.AssetToolsHelpers.get_asset_tools()
material = asset_tools.create_asset("${materialName}", "/Game/Materials", unreal.Material, material_factory)

if material:
    # Set the material to unlit shading model - this is key
    material.set_editor_property("shading_model", unreal.MaterialShadingModel.MSM_UNLIT)

    # Create a color constant with proper positioning
    editor = unreal.MaterialEditingLibrary
    color_constant = editor.create_material_expression(material, unreal.MaterialExpressionConstant3Vector)
    color_constant.constant = unreal.LinearColor(${r}, ${g}, ${b}, 1.0)

    # Position the node in the graph for better visibility
    color_constant.material_expression_editor_x = -300
    color_constant.material_expression_editor_y = 0

    try:
        # Connect to base color with error handling
        result = editor.connect_material_property(color_constant, "RGB", unreal.MaterialProperty.MP_BASE_COLOR)
        if not result:
            # Try alternative connection method
            output_pin = color_constant.get_output_by_name("RGB")
            if output_pin:
                editor.connect_material_property(output_pin, "", unreal.MaterialProperty.MP_BASE_COLOR)
                print("Used alternative connection method")
            else:
                print("Warning: Could not get RGB output pin from color constant")
    except Exception as e:
        print(f"Error connecting material nodes: {str(e)}")

    # Log connection status
    connections = editor.get_material_property_input_connections(material, unreal.MaterialProperty.MP_BASE_COLOR)
    print(f"Base color has {len(connections)} connections")

    # Recompile the material
    editor.recompile_material(material)
`;

  // If an actor name is provided, add code to apply the material to the actor
  if (actorName) {
    script += `
    # Apply to specified actor
    actors = unreal.EditorLevelLibrary.get_all_level_actors()
    for actor in actors:
        if actor.get_name() == "${actorName}":
            # Try to get static mesh component
            if hasattr(actor, 'static_mesh_component'):
                actor.static_mesh_component.set_material(0, material)
                print(f"Applied material to {actor.get_name()}")
            break
`;
  }

  return script;
}

/**
 * Generate a Python script to create a colored object (cube, sphere, etc.)
 *
 * @param {string} objectName - The name of the object to create
 * @param {Array<number>} color - RGB color values as an array of 3 numbers between 0 and 1
 * @param {Array<number>} [location] - Location as an array of 3 numbers [x, y, z]
 * @param {string} [objectType] - Type of object to create (Cube, Sphere, Cylinder, etc.)
 * @returns {string} - The Python script to create the colored object
 */
function generateColoredObjectScript(objectName, color, location = [0, 0, 0], objectType = 'Cube') {
  // Ensure color is valid
  if (!Array.isArray(color) || color.length !== 3) {
    color = [1, 1, 1]; // Default to white if invalid color
  }

  // Format the color values
  const [r, g, b] = color;

  // Format the location values
  const [x, y, z] = location;

  // Create a material name based on the object name
  const materialName = `${objectName}_Material`;

  // Determine the asset path based on the object type
  let assetPath = '/Engine/BasicShapes/Cube';
  if (objectType.toLowerCase() === 'sphere') {
    assetPath = '/Engine/BasicShapes/Sphere';
  } else if (objectType.toLowerCase() === 'cylinder') {
    assetPath = '/Engine/BasicShapes/Cylinder';
  } else if (objectType.toLowerCase() === 'cone') {
    assetPath = '/Engine/BasicShapes/Cone';
  } else if (objectType.toLowerCase() === 'plane') {
    assetPath = '/Engine/BasicShapes/Plane';
  }

  // Create the script
  const script = `import unreal

# Create a ${objectType.toLowerCase()}
object_actor = unreal.EditorLevelLibrary.spawn_actor_from_class(
    unreal.StaticMeshActor.static_class(),
    unreal.Vector(${x}, ${y}, ${z}),
    unreal.Rotator(0, 0, 0)
)

# Set the object name
object_actor.set_actor_label("${objectName}")

# Get the ${objectType.toLowerCase()} mesh
object_mesh = unreal.EditorAssetLibrary.load_asset("${assetPath}")
if object_mesh:
    object_actor.static_mesh_component.set_static_mesh(object_mesh)

# Create a new material specifically set to unlit mode
material_factory = unreal.MaterialFactoryNew()
asset_tools = unreal.AssetToolsHelpers.get_asset_tools()
material = asset_tools.create_asset("${materialName}", "/Game/Materials", unreal.Material, material_factory)

if material:
    # Set the material to unlit shading model - this is key
    material.set_editor_property("shading_model", unreal.MaterialShadingModel.MSM_UNLIT)

    # Create a color constant with proper positioning
    editor = unreal.MaterialEditingLibrary
    color_constant = editor.create_material_expression(material, unreal.MaterialExpressionConstant3Vector)
    color_constant.constant = unreal.LinearColor(${r}, ${g}, ${b}, 1.0)

    # Position the node in the graph for better visibility
    color_constant.material_expression_editor_x = -300
    color_constant.material_expression_editor_y = 0

    try:
        # Connect to base color with error handling
        result = editor.connect_material_property(color_constant, "RGB", unreal.MaterialProperty.MP_BASE_COLOR)
        if not result:
            # Try alternative connection method
            output_pin = color_constant.get_output_by_name("RGB")
            if output_pin:
                editor.connect_material_property(output_pin, "", unreal.MaterialProperty.MP_BASE_COLOR)
                print("Used alternative connection method")
            else:
                print("Warning: Could not get RGB output pin from color constant")
    except Exception as e:
        print(f"Error connecting material nodes: {str(e)}")

    # Log connection status
    connections = editor.get_material_property_input_connections(material, unreal.MaterialProperty.MP_BASE_COLOR)
    print(f"Base color has {len(connections)} connections")

    # Recompile the material
    editor.recompile_material(material)

    # Apply material to object
    object_actor.static_mesh_component.set_material(0, material)
    print(f"Applied material to {objectName}")
`;

  return script;
}

/**
 * Generate a Python script to create a colored cube (legacy function for backward compatibility)
 *
 * @param {string} cubeName - The name of the cube to create
 * @param {Array<number>} color - RGB color values as an array of 3 numbers between 0 and 1
 * @param {Array<number>} [location] - Location as an array of 3 numbers [x, y, z]
 * @returns {string} - The Python script to create the colored cube
 */
function generateColoredCubeScript(cubeName, color, location = [0, 0, 0]) {
  return generateColoredObjectScript(cubeName, color, location, 'Cube');
}

module.exports = {
  generateUnlitMaterialScript,
  generateColoredCubeScript,
  generateColoredObjectScript
};
