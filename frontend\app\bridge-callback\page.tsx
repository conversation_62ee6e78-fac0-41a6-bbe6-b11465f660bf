'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useSearchParams } from 'next/navigation';

export default function BridgeCallbackPage() {
  const { user, token, hasActiveSubscription, isAuthenticated, isLoading } = useAuth();
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState<string>('');

  useEffect(() => {
    const handleBridgeCallback = async () => {
      try {
        // Wait for authentication to complete
        if (isLoading) {
          console.log('Bridge callback: Still loading authentication...');
          return;
        }

        if (!isAuthenticated || !user || !token) {
          throw new Error('User not authenticated. Please try logging in again.');
        }

        console.log('Bridge callback: User authenticated, sending data to bridge...');

        // Prepare authentication data for the bridge
        const authData = {
          token,
          userId: user.id,
          email: user.email || '',
          hasSubscription: hasActiveSubscription,
          timestamp: new Date().toISOString()
        };

        console.log('Bridge callback: Auth data prepared:', { 
          userId: authData.userId, 
          email: authData.email, 
          hasSubscription: authData.hasSubscription 
        });

        // Try multiple methods to communicate with the bridge
        let bridgeNotified = false;

        // Method 1: Try to communicate via postMessage (if opened in popup/iframe)
        if (window.opener) {
          console.log('Bridge callback: Sending data via postMessage to opener...');
          window.opener.postMessage({
            type: 'CREATELEX_BRIDGE_AUTH_SUCCESS',
            data: authData
          }, '*');
          bridgeNotified = true;
        }

        // Method 2: Try to send to bridge's local callback server
        try {
          console.log('Bridge callback: Attempting to notify bridge server...');
          const response = await fetch('http://localhost:7891/auth/success', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(authData)
          });

          if (response.ok) {
            console.log('Bridge callback: Successfully notified bridge server');
            bridgeNotified = true;
          } else {
            console.warn('Bridge callback: Bridge server responded with status:', response.status);
          }
        } catch (fetchError) {
          console.warn('Bridge callback: Could not reach bridge server:', fetchError);
        }

        if (bridgeNotified) {
          setStatus('success');
          
          // Auto-close after 3 seconds if this is a popup
          setTimeout(() => {
            if (window.opener) {
              window.close();
            }
          }, 3000);
        } else {
          throw new Error('Could not communicate with CreateLex Bridge. Please ensure the bridge application is running.');
        }

      } catch (error) {
        console.error('Bridge callback error:', error);
        setErrorMessage(error instanceof Error ? error.message : 'Unknown error occurred');
        setStatus('error');
      }
    };

    handleBridgeCallback();
  }, [user, token, hasActiveSubscription, isAuthenticated, isLoading]);

  const closeWindow = () => {
    if (window.opener) {
      window.close();
    } else {
      // Redirect to dashboard if not a popup
      window.location.href = '/dashboard';
    }
  };

  const retryAuth = () => {
    window.location.href = '/login';
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full mx-4 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            Connecting to CreateLex Bridge...
          </h1>
          <p className="text-gray-600">
            Please wait while we complete your authentication.
          </p>
        </div>
      </div>
    );
  }

  if (status === 'success') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full mx-4 text-center">
          <div className="text-green-500 text-5xl mb-4">✅</div>
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            Authentication Successful!
          </h1>
          <p className="text-gray-600 mb-6">
            You have been successfully authenticated with the CreateLex Bridge.
          </p>
          
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div className="text-sm text-green-800">
              <strong>✓ User authenticated</strong><br/>
              Email: {user?.email}<br/>
              Subscription: {hasActiveSubscription ? 'Active ✅' : 'None ❌'}
            </div>
          </div>

          <p className="text-sm text-gray-500 mb-4">
            The CreateLex Bridge application will open automatically.
            You can close this browser window.
          </p>

          <button
            onClick={closeWindow}
            className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors"
          >
            Close Window
          </button>
        </div>
      </div>
    );
  }

  if (status === 'error') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-500 to-pink-600 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full mx-4 text-center">
          <div className="text-red-500 text-5xl mb-4">❌</div>
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            Authentication Error
          </h1>
          <p className="text-gray-600 mb-4">
            There was an error processing your authentication.
          </p>
          
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="text-sm text-red-800">
              {errorMessage}
            </div>
          </div>

          <div className="flex gap-3 justify-center">
            <button
              onClick={retryAuth}
              className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              Try Again
            </button>
            <button
              onClick={closeWindow}
              className="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              Close Window
            </button>
          </div>
        </div>
      </div>
    );
  }

  return null;
} 