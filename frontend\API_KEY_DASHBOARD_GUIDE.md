# CreateLex API Key Dashboard Guide

## 🎉 Good News!

The API key management dashboard is **already fully implemented** in your CreateLex platform! You just need to access it.

## 🔑 How to Access the API Key Dashboard

### **Option 1: Production (createlex.com)**

1. **Visit**: `https://createlex.com/admin/api-keys`
2. **Sign in** with your admin account
3. **Create API keys** for Unreal Engine integration

### **Option 2: Local Development**

1. **Start the frontend**:
   ```bash
   cd frontend
   npm run dev
   ```

2. **Visit**: `http://localhost:3000/admin/api-keys`
3. **Sign in** with admin credentials

## 👤 Admin Access Requirements

You need admin privileges to access the API key dashboard. Admin access is granted to:

- **Email**: `<EMAIL>`
- **Email**: `<EMAIL>`
- **Users with `is_admin: true`** in the database

### **To Grant Admin Access:**

1. **Database Method** (Recommended):
   ```sql
   UPDATE users SET is_admin = true WHERE email = '<EMAIL>';
   ```

2. **Environment Variable** (Development):
   ```bash
   # Add to .env.local
   NEXT_PUBLIC_BYPASS_AUTH=true
   ```

## 🚀 Using the API Key Dashboard

### **Creating a New API Key:**

1. **Navigate to**: `/admin/api-keys`
2. **Click**: "Create API Key" button
3. **Fill in the form**:
   - **Key Name**: "Unreal Engine Bridge"
   - **Rate Limit**: 100 (requests per minute)
   - **User ID**: (optional, defaults to your account)
4. **Click**: "Create API Key"
5. **Copy the generated key** (shown only once!)

### **Managing Existing Keys:**

- **View all keys**: See name, creation date, usage, status
- **Copy keys**: Click the copy button next to any key
- **Revoke keys**: Use the dropdown menu to revoke active keys
- **Search keys**: Use the search bar to find specific keys

### **Key Features:**

✅ **Create API Keys**: Generate new keys with custom names and rate limits
✅ **View Key Details**: See creation date, last used, rate limits, status
✅ **Copy to Clipboard**: One-click copying of API keys
✅ **Revoke Keys**: Disable keys when no longer needed
✅ **Search & Filter**: Find keys quickly
✅ **Usage Analytics**: Monitor API usage by key
✅ **Rate Limiting**: Set custom rate limits per key

## 🔧 For Unreal Engine Integration

### **Step 1: Create API Key**
1. Go to `/admin/api-keys`
2. Create a new key named "Unreal Engine Bridge"
3. Copy the generated key (starts with `sk_`)

### **Step 2: Set Environment Variable**
```bash
# Windows Command Prompt
set CREATELEX_API_KEY=sk_your_generated_key_here

# Windows PowerShell
$env:CREATELEX_API_KEY="sk_your_generated_key_here"
```

### **Step 3: Test in Unreal Engine**
1. Restart Unreal Engine
2. Start the MCP server
3. Look for successful cloud registration

## 📊 Dashboard Features

### **API Keys Tab:**
- Create, view, and manage API keys
- Copy keys to clipboard
- Revoke unused or compromised keys

### **Usage Tab:**
- Monitor API usage statistics
- View top keys by usage
- Track response times and error rates

### **Settings Tab:**
- Configure default rate limits
- Set token expiration policies
- Manage IP restrictions

## 🛠️ Troubleshooting

### **Can't Access Admin Dashboard:**
- **Check email**: Must be admin email or have `is_admin: true`
- **Check URL**: Make sure you're going to `/admin/api-keys`
- **Check authentication**: Sign in with proper credentials

### **API Key Creation Fails:**
- **Check admin permissions**: Must have admin access
- **Check backend**: Ensure backend server is running
- **Check database**: Verify Supabase connection

### **Mock Data Mode:**
If the backend isn't connected, the dashboard shows mock data for development:
- Sample API keys for testing UI
- Simulated usage statistics
- All functionality works with mock data

## 🎯 Quick Start

1. **Access**: `https://createlex.com/admin/api-keys`
2. **Sign in**: Use admin account
3. **Create**: New API key named "Unreal Engine Bridge"
4. **Copy**: The generated key
5. **Set**: Environment variable `CREATELEX_API_KEY`
6. **Test**: Restart Unreal Engine and check logs

## 📞 Support

If you need help:
- **Check admin access**: Verify you have admin privileges
- **Check logs**: Look for authentication errors
- **Contact support**: With specific error messages

---

**The API key dashboard is ready to use!** 🚀 Just access it through the admin panel and create your Unreal Engine API key.
