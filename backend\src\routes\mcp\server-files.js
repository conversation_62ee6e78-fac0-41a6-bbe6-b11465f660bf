const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');
const { authenticateJWT } = require('../../middleware/auth');

// Serve MCP server files based on tier and platform
router.get('/servers/:tier/:version/:filename', authenticateJWT, async (req, res) => {
  try {
    const { tier, version, filename } = req.params;
    const { user } = req;
    
    // Validate access to this tier
    const hasAccess = await validateTierAccess(user.id, tier);
    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        error: 'Access denied to this server tier'
      });
    }
    
    // Get the appropriate server file
    const serverFile = await getServerFile(tier, version, filename);
    if (!serverFile) {
      return res.status(404).json({
        success: false,
        error: 'Server file not found'
      });
    }
    
    // Set appropriate headers
    res.setHeader('Content-Type', 'text/plain');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour
    
    // Log download for analytics
    await logServerDownload(user.id, tier, version, filename);
    
    res.send(serverFile);
    
  } catch (error) {
    console.error('Error serving MCP server file:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to serve server file',
      details: error.message
    });
  }
});

// Get server file content based on tier and version
async function getServerFile(tier, version, filename) {
  try {
    // Construct the file path based on tier, version, and filename
    const serverPath = path.join(__dirname, '../../storage/mcp-servers', tier, version, filename);
    
    console.log(`[MCP Server Files] Looking for file: ${serverPath}`);
    
    // Check if the specific file exists
    if (fs.existsSync(serverPath)) {
      console.log(`[MCP Server Files] Found file: ${serverPath}`);
      return fs.readFileSync(serverPath, 'utf8');
    }
    
    // Fallback: try different combinations
    const fallbackPaths = [
      path.join(__dirname, '../../storage/mcp-servers', tier, 'latest', filename),
      path.join(__dirname, '../../storage/mcp-servers', 'stable', 'latest', filename),
      path.join(__dirname, '../../../mcp-server-createlexgenai/mcp_server_protected.py')
    ];
    
    for (const fallbackPath of fallbackPaths) {
      if (fs.existsSync(fallbackPath)) {
        console.log(`[MCP Server Files] Using fallback: ${fallbackPath}`);
        return fs.readFileSync(fallbackPath, 'utf8');
      }
    }
    
    console.error(`[MCP Server Files] No file found for ${tier}/${version}/${filename}`);
    return null;
    
  } catch (error) {
    console.error('Error getting server file:', error);
    return null;
  }
}

// Get base server content (your current full-featured server)
async function getBaseServerContent() {
  const serverPath = path.join(__dirname, '../../../../mcp-server-createlexgenai/mcp_server_stdio.py');
  
  if (fs.existsSync(serverPath)) {
    return fs.readFileSync(serverPath, 'utf8');
  }
  
  // Fallback: return the content we know works
  return `# Premium MCP Server with all advanced features
import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from subscription_validator import check_subscription

# Validate subscription on startup
if not check_subscription():
    print("Invalid subscription. Exiting.")
    sys.exit(1)

# Import original server after validation
from mcp_server_stdio import mcp  # use existing FastMCP instance

if __name__ == "__main__":
    print("Premium UnrealGenAI MCP Server")
    print("Subscription validated")
    # Start the FastMCP server in stdio mode
    mcp.run("stdio")
`;
}

// Add premium features (all tools enabled)
async function addPremiumFeatures(baseContent) {
  // Premium users get everything - return as-is
  return baseContent;
}

// Add standard features (remove some advanced tools)
async function addStandardFeatures(baseContent) {
  // Remove premium-only tools for standard tier
  let content = baseContent;
  
  // Remove advanced AI insights
  content = content.replace(/analyze_blueprint_graph_advanced[^}]+}/gs, '');
  
  // Limit smart function generation
  content = content.replace(/def _generate_ai_insights[^}]+}/gs, `def _generate_ai_insights(graph_data, analysis):
    """Limited AI insights for standard tier."""
    return [{"type": "info", "message": "Upgrade to Premium for AI-powered insights"}]`);
  
  return content;
}

// Add free features (basic tools only)
async function addFreeFeatures(baseContent) {
  let content = baseContent;
  
  // Remove all advanced tools for free tier
  const premiumTools = [
    'generate_smart_blueprint_function',
    'get_blueprint_context_detailed', 
    'analyze_blueprint_graph_advanced'
  ];
  
  premiumTools.forEach(tool => {
    const regex = new RegExp(`@mcp\\.tool\\(\\)\\s*def ${tool}[^@]*(?=@mcp\\.tool\\(\\)|def _|if __name__)`, 'gs');
    content = content.replace(regex, '');
  });
  
  // Add upgrade prompts
  content += `
@mcp.tool()
def upgrade_to_premium() -> str:
    """Get information about upgrading to premium for advanced Blueprint tools."""
    return """
🚀 Upgrade to Premium for Advanced Blueprint Tools!

Premium features include:
- 🧠 AI-powered smart function generation
- 📊 Advanced Blueprint graph analysis
- 🔍 Detailed context analysis with recommendations
- ⚡ Performance optimization suggestions
- 🎯 Custom templates and patterns

Visit https://createlex.com/upgrade to unlock all features!
"""
`;
  
  return content;
}

// Add beta features
async function addBetaFeatures(baseContent) {
  // Add experimental features for beta users
  return baseContent + `
@mcp.tool()
def experimental_blueprint_optimizer() -> str:
    """Experimental Blueprint optimization tool (Beta)."""
    return "🧪 Beta: Advanced Blueprint optimization coming soon!"
`;
}

// Platform-specific optimizations
async function addDarwinOptimizations(baseContent) {
  // macOS-specific optimizations
  return baseContent.replace(
    'pythonCmd = \'python\'',
    'pythonCmd = \'/opt/homebrew/bin/python3.11\''
  );
}

async function addLinuxOptimizations(baseContent) {
  // Linux-specific optimizations
  return baseContent.replace(
    'pythonCmd = \'python\'',
    'pythonCmd = \'python3\''
  );
}

// Validate if user has access to specific tier
async function validateTierAccess(userId, requestedTier) {
  // This should check your subscription system
  // For now, allow all access in development
  const isDev = process.env.NODE_ENV !== 'production';
  if (isDev) return true;
  
  // In production, check actual subscription
  const subscription = await getUserSubscription(userId);
  
  const tierHierarchy = {
    'free': 0,
    'standard': 1, 
    'premium': 2,
    'enterprise': 3
  };
  
  const userTierLevel = tierHierarchy[subscription.tier] || 0;
  const requestedTierLevel = tierHierarchy[requestedTier] || 0;
  
  return userTierLevel >= requestedTierLevel;
}

// Get user subscription (integrate with your existing system)
async function getUserSubscription(userId) {
  // This should query your actual subscription data
  return { tier: 'premium', status: 'active' };
}

// Log server downloads for analytics
async function logServerDownload(userId, tier, version, filename) {
  try {
    // Log to your analytics system
    console.log(`MCP Server Download: User ${userId} downloaded ${tier}/${version}/${filename}`);
    
    // You could store this in a database for analytics:
    // - Track which versions are most popular
    // - Monitor download patterns
    // - Identify users who might need support
  } catch (error) {
    console.error('Error logging server download:', error);
  }
}

module.exports = router; 