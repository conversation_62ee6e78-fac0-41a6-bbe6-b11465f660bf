// Script to add test token usage data to the database
require('dotenv').config();
const supabase = require('../src/services/supabaseClient');
const authService = require('../src/services/supabaseAuthService');

async function addTestTokenUsageData() {
  try {
    console.log('Starting to add test token usage data...');
    
    // Get all users from the database
    console.log('Fetching users from Supabase...');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email')
      .limit(5);
    
    if (usersError) {
      console.error('Error fetching users:', usersError);
      return;
    }
    
    console.log(`Found ${users.length} users`);
    
    if (users.length === 0) {
      console.log('No users found. Creating a test user...');
      // Create a test user if none exist
      const testUser = {
        id: '077f1533-9f81-429c-b1b1-52d9c83f146c', // Use a fixed ID for testing
        email: '<EMAIL>',
        name: 'Test User',
        subscriptionStatus: 'active',
        subscriptionPlan: 'pro'
      };
      
      users.push(testUser);
    }
    
    // Models to use for test data
    const models = [
      'claude-3-opus-20240229',
      'claude-3-sonnet-20240229',
      'claude-3-haiku-20240307'
    ];
    
    // Generate random token usage data for each user
    for (const user of users) {
      console.log(`Generating token usage data for user: ${user.email || user.id}`);
      
      // Generate 5 records per user
      for (let i = 0; i < 5; i++) {
        const modelId = models[Math.floor(Math.random() * models.length)];
        const promptTokens = Math.floor(Math.random() * 1000) + 100;
        const completionTokens = Math.floor(Math.random() * 2000) + 200;
        const totalTokens = promptTokens + completionTokens;
        
        // Get user's subscription plan
        let subscriptionPlan = 'basic';
        try {
          const userDetails = await authService.getUserById(user.id);
          subscriptionPlan = userDetails?.subscriptionPlan || 'basic';
        } catch (error) {
          console.log(`Could not get subscription plan for user ${user.id}, using 'basic'`);
        }
        
        // Insert the record
        const { data, error } = await supabase
          .from('token_usage')
          .insert({
            user_id: user.id,
            model_id: modelId,
            prompt_tokens: promptTokens,
            completion_tokens: completionTokens,
            total_tokens: totalTokens,
            request_type: 'chat',
            subscription_plan: subscriptionPlan
          });
        
        if (error) {
          console.error(`Error inserting token usage record for user ${user.id}:`, error);
        } else {
          console.log(`Added token usage record for user ${user.id}: ${promptTokens} prompt tokens, ${completionTokens} completion tokens`);
        }
      }
    }
    
    console.log('Finished adding test token usage data');
  } catch (error) {
    console.error('Error adding test token usage data:', error);
  }
}

// Run the function
addTestTokenUsageData()
  .then(() => {
    console.log('Script completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
