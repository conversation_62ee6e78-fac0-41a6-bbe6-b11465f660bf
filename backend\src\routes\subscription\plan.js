const express = require('express');
const router = express.Router();
const { authenticateJWT } = require('../../middleware/auth');
const subscriptionService = require('../../services/subscriptionService');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

// Get the user's current subscription plan details
router.get('/', authenticateJWT, async (req, res) => {
  try {
    const userId = req.user.id;
    console.log(`[Subscription Plan] Getting subscription plan for user: ${userId}`);

    // Get the user's subscription details
    const user = req.user;

    if (!user || !user.subscriptionId) {
      return res.json({
        success: true,
        plan: {
          name: 'Free',
          price: '$0.00',
          period: 'month',
          status: 'inactive'
        }
      });
    }

    // Check if the user has an active subscription
    if (user.subscriptionStatus !== 'active') {
      return res.json({
        success: true,
        plan: {
          name: 'Free',
          price: '$0.00',
          period: 'month',
          status: user.subscriptionStatus || 'inactive'
        }
      });
    }

    // Get the subscription details from Stripe
    const subscription = await stripe.subscriptions.retrieve(user.subscriptionId, {
      expand: ['items.data.price.product']
    });

    if (!subscription) {
      return res.status(404).json({ 
        success: false,
        error: 'Subscription not found' 
      });
    }

    // Get the price ID from the subscription
    const priceId = subscription.items.data[0]?.price?.id;

    // Determine the plan based on the price ID
    let planName = 'Unknown';
    let planPrice = '$0.00';
    
    if (priceId === process.env.STRIPE_PRICE_ID_BASIC) {
      planName = 'Basic';
      planPrice = '$20.00';
    } else if (priceId === process.env.STRIPE_PRICE_ID_PRO) {
      planName = 'Pro';
      planPrice = '$30.00';
    }

    // Return the plan details
    return res.json({
      success: true,
      plan: {
        name: planName,
        price: planPrice,
        period: 'month',
        status: subscription.status,
        priceId: priceId,
        renewalDate: new Date(subscription.current_period_end * 1000).toISOString()
      }
    });
  } catch (error) {
    console.error('[Subscription Plan] Error getting subscription plan:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to get subscription plan',
      message: error.message
    });
  }
});

module.exports = router;
