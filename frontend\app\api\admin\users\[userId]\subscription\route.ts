import { NextRequest, NextResponse } from 'next/server';
import { adminApiRequest } from '@/lib/admin-api';

export async function PATCH(
  req: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const userId = params.userId;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Get the request body
    const body = await req.json();

    if (!body.status) {
      return NextResponse.json(
        { error: 'Subscription status is required' },
        { status: 400 }
      );
    }

    // Forward the request to the backend using our helper function
    const response = await adminApiRequest(`/api/admin/users/${userId}/subscription`, {
      method: 'PATCH',
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response from backend:', errorText);

      return NextResponse.json(
        { error: `Failed to update subscription: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in subscription update route:', error);

    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}
