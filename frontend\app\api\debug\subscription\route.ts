import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function GET(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader ? authHeader.replace('Bearer ', '') : null;

    // Get all cookies for debugging
    const cookieStore = request.cookies;
    const cookies = cookieStore.getAll().map(c => c.name);

    // Check for Supabase session cookie in various formats
    let sbToken = null;
    const sbAccessTokenCookie = cookieStore.get('sb-access-token');

    if (sbAccessTokenCookie?.value) {
      try {
        // The cookie might be JSON with the session info
        const parsedCookie = JSON.parse(sbAccessTokenCookie.value);
        if (parsedCookie?.access_token) {
          sbToken = parsedCookie.access_token;
        }
      } catch (e) {
        // If it's not JSO<PERSON>, use the value directly
        sbToken = sbAccessTokenCookie.value;
      }
    }

    // If we didn't find a token yet, try other cookie names
    if (!sbToken) {
      sbToken = cookieStore.get('sb:token')?.value;
      const sbAuthToken = cookieStore.get('supabase-auth-token')?.value;
      const sbRefreshToken = cookieStore.get('sb-refresh-token')?.value;
    }

    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

    if (!supabaseUrl || !supabaseAnonKey) {
      return NextResponse.json({
        error: 'Missing Supabase configuration',
        supabaseUrl: !!supabaseUrl,
        supabaseAnonKey: !!supabaseAnonKey
      }, { status: 500 });
    }

    const supabase = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    // Verify the token with Supabase
    const userToken = token || sbToken;
    let userData = null;

    if (userToken) {
      const { data, error } = await supabase.auth.getUser(userToken);
      if (!error && data.user) {
        userData = {
          id: data.user.id,
          email: data.user.email,
          metadata: data.user.user_metadata
        };
      }
    }

    // Check subscription status
    let subscriptionData = null;
    if (userToken && userData) {
      try {
        const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';
        const subscriptionResponse = await fetch(`${apiUrl}/api/subscription/check`, {
          headers: {
            'Authorization': `Bearer ${userToken}`
          }
        });

        if (subscriptionResponse.ok) {
          subscriptionData = await subscriptionResponse.json();
        } else {
          subscriptionData = {
            error: 'Failed to check subscription',
            status: subscriptionResponse.status
          };
        }
      } catch (error: any) {
        subscriptionData = {
          error: 'Error checking subscription',
          message: error.message
        };
      }
    }

    // Return debug information
    return NextResponse.json({
      environment: {
        NEXT_PUBLIC_BYPASS_AUTH: process.env.NEXT_PUBLIC_BYPASS_AUTH,
        NEXT_PUBLIC_BYPASS_SUBSCRIPTION: process.env.NEXT_PUBLIC_BYPASS_SUBSCRIPTION,
        NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
        NODE_ENV: process.env.NODE_ENV
      },
      auth: {
        hasToken: !!userToken,
        hasAuthHeader: !!authHeader,
        hasCookieToken: !!sbToken,
        cookies,
        user: userData
      },
      subscription: subscriptionData
    });
  } catch (error: any) {
    return NextResponse.json({
      error: 'Error in debug route',
      message: error.message
    }, { status: 500 });
  }
}
