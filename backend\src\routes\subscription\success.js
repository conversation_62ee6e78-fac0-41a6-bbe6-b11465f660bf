const express = require('express');
const router = express.Router();
const { authenticateJWT } = require('../../middleware/auth');
const subscriptionService = require('../../services/subscriptionService');
const authService = require('../../services/authService');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

// Log environment variables for debugging (without revealing full keys)
console.log('[Subscription Success] SUPABASE_URL:', supabaseUrl ? 'Found' : 'Missing');
console.log('[Subscription Success] SUPABASE_SERVICE_KEY:', supabaseKey ? `Found (${supabaseKey.substring(0, 10)}...)` : 'Missing');

let supabase = null;

if (!supabaseUrl || !supabaseKey) {
  console.error('[Subscription Success] Supabase URL or key is missing. Subscription success handler may not work properly.');
} else {
  try {
    supabase = createClient(supabaseUrl, supabaseKey);
    console.log('[Subscription Success] Supabase client initialized successfully');
  } catch (error) {
    console.error('[Subscription Success] Error initializing Supabase client:', error);
  }
}

// Handle subscription success
router.post('/', authenticateJWT, async (req, res) => {
  try {
    const userId = req.user.id;
    const { sessionId } = req.body;

    if (!sessionId) {
      return res.status(400).json({ error: 'Session ID is required' });
    }

    console.log(`[Subscription Success] Processing subscription success for user ${userId} with session ${sessionId}`);

    // Get the checkout session from Stripe
    const session = await subscriptionService.getCheckoutSession(sessionId);

    if (!session) {
      return res.status(404).json({ error: 'Checkout session not found' });
    }

    // Get the subscription from the session
    const subscriptionId = session.subscription;

    if (!subscriptionId) {
      return res.status(400).json({ error: 'No subscription found in checkout session' });
    }

    // Get the subscription details from Stripe
    const subscription = await subscriptionService.getSubscriptionDetails(subscriptionId);

    if (!subscription) {
      return res.status(404).json({ error: 'Subscription not found' });
    }

    // Update the user's subscription status in our database
    const status = subscription.status === 'active' ? 'active' : 'inactive';
    const updated = await authService.updateSubscription(userId, status, subscriptionId);

    if (!updated) {
      return res.status(500).json({ error: 'Failed to update subscription status' });
    }

    // Return the subscription details
    return res.json({
      success: true,
      message: 'Subscription activated successfully',
      subscriptionId,
      status,
      subscription
    });
  } catch (error) {
    console.error('[Subscription Success] Error processing subscription success:', error);
    return res.status(500).json({
      error: 'Failed to process subscription success',
      message: error.message
    });
  }
});

// Handle subscription success via query parameters (for redirect from Stripe)
router.get('/', async (req, res) => {
  try {
    const { session_id } = req.query;

    if (!session_id) {
      console.log('[Subscription Success] No session ID provided in query parameters');
      return res.status(400).json({ error: 'Session ID is required' });
    }

    console.log(`[Subscription Success] Processing subscription success from redirect with session ${session_id}`);

    // Get the checkout session from Stripe
    const session = await stripe.checkout.sessions.retrieve(session_id, {
      expand: ['customer', 'subscription']
    });

    if (!session) {
      console.log('[Subscription Success] Checkout session not found');
      return res.status(404).json({ error: 'Checkout session not found' });
    }

    // Get the customer and subscription from the session
    const customerId = session.customer?.id;
    const subscriptionId = session.subscription?.id;

    if (!customerId || !subscriptionId) {
      console.log('[Subscription Success] Missing customer or subscription in session');
      return res.status(400).json({ error: 'Missing customer or subscription in session' });
    }

    console.log(`[Subscription Success] Found customer ${customerId} and subscription ${subscriptionId}`);

    // Find the user with this Stripe customer ID
    if (!supabase) {
      console.error('[Subscription Success] Supabase client not available');
      return res.status(500).json({ error: 'Database connection not available' });
    }

    const { data: users, error } = await supabase
      .from('users')
      .select('*')
      .eq('stripe_customer_id', customerId);

    if (error) {
      console.error('[Subscription Success] Error finding user by Stripe customer ID:', error);
      return res.status(500).json({ error: 'Database error' });
    }

    if (!users || users.length === 0) {
      console.log(`[Subscription Success] No user found for Stripe customer ID: ${customerId}`);

      // Try to find the user by email
      const customerEmail = session.customer_details?.email;

      if (customerEmail) {
        console.log(`[Subscription Success] Searching for user by email: ${customerEmail}`);

        if (!supabase) {
          console.error('[Subscription Success] Supabase client not available');
          return res.status(500).json({ error: 'Database connection not available' });
        }

        const { data: emailUsers, error: emailError } = await supabase
          .from('users')
          .select('*')
          .eq('email', customerEmail);

        if (emailError) {
          console.error('[Subscription Success] Error finding user by email:', emailError);
          return res.status(500).json({ error: 'Database error' });
        }

        if (!emailUsers || emailUsers.length === 0) {
          console.log(`[Subscription Success] No user found for email: ${customerEmail}`);
          return res.status(404).json({ error: 'User not found' });
        }

        // Update the user with the Stripe customer ID
        const user = emailUsers[0];
        console.log(`[Subscription Success] Found user ${user.id} by email, updating with Stripe customer ID`);

        if (!supabase) {
          console.error('[Subscription Success] Supabase client not available');
          return res.status(500).json({ error: 'Database connection not available' });
        }

        const { error: updateError } = await supabase
          .from('users')
          .update({
            stripe_customer_id: customerId,
            subscription_id: subscriptionId,
            subscription_status: 'active',
            subscription_updated_at: new Date().toISOString(),
            last_checkout_session_id: session_id
          })
          .eq('id', user.id);

        if (updateError) {
          console.error('[Subscription Success] Error updating user:', updateError);
          return res.status(500).json({ error: 'Database error' });
        }

        console.log(`[Subscription Success] Updated user ${user.id} with subscription information`);

        // Redirect to the dashboard
        return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:3000'}/dashboard?subscription=success`);
      }

      return res.status(404).json({ error: 'User not found' });
    }

    // Update the user with the subscription information
    const user = users[0];
    console.log(`[Subscription Success] Found user ${user.id}, updating with subscription information`);

    if (!supabase) {
      console.error('[Subscription Success] Supabase client not available');
      return res.status(500).json({ error: 'Database connection not available' });
    }

    const { error: updateError } = await supabase
      .from('users')
      .update({
        subscription_id: subscriptionId,
        subscription_status: 'active',
        subscription_updated_at: new Date().toISOString(),
        last_checkout_session_id: session_id
      })
      .eq('id', user.id);

    if (updateError) {
      console.error('[Subscription Success] Error updating user:', updateError);
      return res.status(500).json({ error: 'Database error' });
    }

    console.log(`[Subscription Success] Updated user ${user.id} with subscription information`);

    // Redirect to the dashboard
    return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:3000'}/dashboard?subscription=success`);
  } catch (error) {
    console.error('[Subscription Success] Error processing subscription success from redirect:', error);
    return res.status(500).json({
      error: 'Failed to process subscription success',
      message: error.message
    });
  }
});

module.exports = router;
