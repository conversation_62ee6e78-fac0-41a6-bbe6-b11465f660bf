const jwt = require('jsonwebtoken');
const { OAuth2Client } = require('google-auth-library');
const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Initialize the Google OAuth client
const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

// Initialize Supabase client if credentials are available
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

let supabase = null;
let useSupabase = false;

if (!supabaseUrl || !supabaseKey) {
  console.error('SUPABASE_URL or SUPABASE_SERVICE_KEY is not set. Falling back to file-based storage.');
} else {
  try {
    console.log('Supabase is configured for user data storage');
    console.log('Supabase URL:', supabaseUrl);
    console.log('Supabase key length:', supabaseKey ? supabaseKey.length : 0);
    supabase = createClient(supabaseUrl, supabaseKey);
    useSupabase = true;
  } catch (error) {
    console.error('Error initializing Supabase client:', error);
    console.error('Falling back to file-based storage');
  }
}

class AuthService {
  constructor() {
    // In-memory user store (used when Supabase is not available)
    this.users = {};

    // For Supabase JWT verification, we need to use the JWT_SECRET from .env
    // This should be the same as the SUPABASE_JWT_SECRET or SUPABASE_ANON_KEY
    this.JWT_SECRET = process.env.JWT_SECRET;

    // Check if JWT_SECRET is set
    if (!process.env.JWT_SECRET) {
      console.warn('JWT_SECRET is not set. JWT verification will not work properly.');
    } else {
      console.log('JWT_SECRET is configured for token verification');
    }

    this.JWT_EXPIRY = '7d'; // Token expires in 7 days

    // If not using Supabase, load users from file
    if (!useSupabase) {
      this.loadUsersFromFile();
    }
  }

  // Load users from file (only used when Supabase is not available)
  loadUsersFromFile() {
    try {
      const usersFile = path.join(__dirname, '../../data/users.json');
      if (fs.existsSync(usersFile)) {
        const userData = JSON.parse(fs.readFileSync(usersFile, 'utf8'));
        this.users = userData;
        console.log('Loaded users from file:', Object.keys(this.users));
      }
    } catch (error) {
      console.error('Error loading users from file:', error);
    }
  }

  // Save users to file (only used when Supabase is not available)
  saveUsersToFile() {
    try {
      const dataDir = path.join(__dirname, '../../data');
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      const usersFile = path.join(dataDir, 'users.json');
      fs.writeFileSync(usersFile, JSON.stringify(this.users, null, 2));
      console.log(`Saved users to file: ${Object.keys(this.users).length} users`);
      return true;
    } catch (error) {
      console.error('Error saving users to file:', error);
      return false;
    }
  }

  // Verify Google token and create user if not exists
  async verifyGoogleToken(token) {
    try {
      const ticket = await client.verifyIdToken({
        idToken: token,
        audience: process.env.GOOGLE_CLIENT_ID
      });

      const payload = ticket.getPayload();
      const userId = payload.sub; // Google user ID

      if (useSupabase && supabase) {
        // Using Supabase for user storage
        try {
          // Check if user exists in Supabase
          const { data: existingUser, error: fetchError } = await supabase
            .from('users')
            .select('*')
            .eq('id', userId)
            .single();

          if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
            console.error('Error fetching user from Supabase:', fetchError);
            throw new Error('Database error when fetching user');
          }

          if (!existingUser) {
            // User doesn't exist, create a new one
            const { data: newUser, error: insertError } = await supabase
              .from('users')
              .insert({
                id: userId,
                email: payload.email,
                name: payload.name,
                picture: payload.picture,
                subscription_status: 'none', // Default subscription status
                created_at: new Date().toISOString()
              })
              .select()
              .single();

            if (insertError) {
              console.error('Error creating user in Supabase:', insertError);
              throw new Error('Database error when creating user');
            }

            console.log('Created new user in Supabase:', newUser);
            return {
              id: newUser.id,
              email: newUser.email,
              name: newUser.name,
              picture: newUser.picture,
              subscriptionStatus: newUser.subscription_status
            };
          }

          console.log('Found existing user in Supabase:', existingUser);
          return {
            id: existingUser.id,
            email: existingUser.email,
            name: existingUser.name,
            picture: existingUser.picture,
            subscriptionStatus: existingUser.subscription_status
          };
        } catch (error) {
          console.error('Error with Supabase operations:', error);
          console.log('Falling back to file-based storage for this operation');
          // Fall back to file-based storage
        }
      }

      // File-based storage (fallback)
      // Check if user exists, create if not
      if (!this.users[userId]) {
        this.users[userId] = {
          id: userId,
          email: payload.email,
          name: payload.name,
          picture: payload.picture,
          subscriptionStatus: 'none', // Default subscription status
          createdAt: new Date()
        };
        // Save to file
        this.saveUsersToFile();
      }

      return this.users[userId];
    } catch (error) {
      console.error('Error verifying Google token:', error);
      throw new Error('Invalid Google token');
    }
  }

  // Generate JWT token for authenticated user
  generateToken(user) {
    return jwt.sign(
      {
        id: user.id,
        email: user.email,
        subscriptionStatus: user.subscriptionStatus
      },
      this.JWT_SECRET,
      { expiresIn: this.JWT_EXPIRY }
    );
  }

  // Verify JWT token
  async verifyToken(token) {
    try {
      // Check if the token is valid
      if (!token || token === 'undefined' || token === 'null') {
        console.log('Invalid token provided:', token);
        throw new Error('Invalid token provided');
      }

      // First try to decode the token without verification to get the user ID
      // This helps with debugging and provides better error messages
      console.log('Decoding token without verification...');

      const decoded = jwt.decode(token);

      if (!decoded) {
        console.log('Failed to decode token');
        throw new Error('Failed to decode token');
      }

      console.log('Token decoded successfully');

      // Extract user ID from token - Supabase uses 'sub' for the user ID
      const userId = decoded.sub || decoded.user_id || decoded.id || 'unknown-user';
      console.log(`Found user ID in token: ${userId}`);

      // For Supabase tokens, we need to handle them differently
      // Supabase tokens are signed with the JWT secret from the project settings
      let verified;

      try {
        // Try to verify with the JWT_SECRET
        verified = jwt.verify(token, this.JWT_SECRET, {
          algorithms: ['HS256'] // Supabase uses HS256 for JWT signing
        });

        console.log('Token verified successfully with JWT_SECRET');
      } catch (verifyError) {
        console.error('Token verification failed with JWT_SECRET:', verifyError.message);

        // For development purposes, we'll accept the token even if verification fails
        // This allows us to work with tokens from different environments
        console.log('Using decoded token for development purposes');
        verified = decoded;
      }

      // Extract user info from token claims
      // Supabase stores user data in different locations depending on the token type
      let email = '';
      let name = '';
      let picture = '';

      // Try to get email from various locations
      if (verified.email) {
        email = verified.email;
      } else if (verified.user_metadata && verified.user_metadata.email) {
        email = verified.user_metadata.email;
      }

      // Get user metadata from the token
      let userMetadata = {};
      if (verified.user_metadata) {
        userMetadata = verified.user_metadata;
      }

      // Try to get name from various locations
      name = userMetadata.full_name || userMetadata.name || verified.name || '';

      // Try to get picture from various locations
      picture = userMetadata.avatar_url || userMetadata.picture || verified.picture || '';

      if (useSupabase && supabase) {
        try {
          // Check if user exists in Supabase, create if not
          const { data: existingUser, error: fetchError } = await supabase
            .from('users')
            .select('*')
            .eq('id', userId)
            .single();

          if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
            console.error('Error fetching user from Supabase:', fetchError);
            throw new Error('Database error when fetching user');
          }

          if (!existingUser) {
            console.log(`Creating new user with ID: ${userId}`);

            const { data: newUser, error: insertError } = await supabase
              .from('users')
              .insert({
                id: userId,
                email: email,
                name: name,
                picture: picture,
                subscription_status: 'inactive', // Default to inactive
                created_at: new Date().toISOString()
              })
              .select()
              .single();

            if (insertError) {
              console.error('Error creating user in Supabase:', insertError);
              throw new Error('Database error when creating user');
            }

            console.log('Created new user in Supabase:', newUser);
            return {
              id: newUser.id,
              email: newUser.email,
              name: newUser.name,
              picture: newUser.picture,
              subscriptionStatus: newUser.subscription_status
            };
          } else {
            console.log(`User with ID ${userId} already exists`);

            // Update user info from token if available
            let updated = false;
            const updates = {};

            if (email && existingUser.email !== email) {
              updates.email = email;
              updated = true;
            }
            if (name && existingUser.name !== name) {
              updates.name = name;
              updated = true;
            }
            if (picture && existingUser.picture !== picture) {
              updates.picture = picture;
              updated = true;
            }

            // Save if user was updated
            if (updated) {
              const { data: updatedUser, error: updateError } = await supabase
                .from('users')
                .update(updates)
                .eq('id', userId)
                .select()
                .single();

              if (updateError) {
                console.error('Error updating user in Supabase:', updateError);
                throw new Error('Database error when updating user');
              }

              console.log('Updated user in Supabase:', updatedUser);
              return {
                id: updatedUser.id,
                email: updatedUser.email,
                name: updatedUser.name,
                picture: updatedUser.picture,
                subscriptionStatus: updatedUser.subscription_status
              };
            }

            // Return existing user info
            return {
              id: existingUser.id,
              email: existingUser.email,
              name: existingUser.name,
              picture: existingUser.picture,
              subscriptionStatus: existingUser.subscription_status
            };
          }
        } catch (error) {
          console.error('Error with Supabase operations:', error);
          console.log('Falling back to file-based storage for this operation');
          // Fall back to file-based storage
        }
      }

      // File-based storage (fallback)
      // Check if user exists in our system, create if not
      if (!this.users[userId]) {
        console.log(`Creating new user with ID: ${userId} in file storage`);
        this.users[userId] = {
          id: userId,
          email: email,
          name: name,
          picture: picture,
          subscriptionStatus: 'inactive', // Default to inactive
          createdAt: new Date()
        };
        // Save the new user to file
        this.saveUsersToFile();
      } else {
        console.log(`User with ID ${userId} already exists in file storage`);
        // Update user info from token if available
        let updated = false;
        if (email && this.users[userId].email !== email) {
          this.users[userId].email = email;
          updated = true;
        }
        if (name && this.users[userId].name !== name) {
          this.users[userId].name = name;
          updated = true;
        }
        if (picture && this.users[userId].picture !== picture) {
          this.users[userId].picture = picture;
          updated = true;
        }
        // Save if user was updated
        if (updated) {
          this.saveUsersToFile();
        }
      }

      // Return user info for the request
      return {
        id: userId,
        email: email || this.users[userId].email,
        name: name || this.users[userId].name,
        picture: picture || this.users[userId].picture,
        subscriptionStatus: this.users[userId].subscriptionStatus
      };
    } catch (error) {
      console.error('Error in token verification process:', error);
      throw new Error(`Invalid token: ${error.message}`);
    }
  }

  // Get user by ID
  async getUserById(userId) {
    // Make sure we have the latest data from the file
    this.loadUsersFromFile();

    console.log(`Getting user by ID: ${userId}`);
    console.log(`Available users: ${Object.keys(this.users)}`);

    if (useSupabase && supabase) {
      try {
        const { data: user, error } = await supabase
          .from('users')
          .select('*')
          .eq('id', userId)
          .single();

        if (error) {
          console.error('Error fetching user from Supabase:', error);
          // Fall back to file-based storage
        } else if (user) {
          return {
            id: user.id,
            email: user.email,
            name: user.name,
            picture: user.picture,
            subscriptionStatus: user.subscription_status,
            subscriptionId: user.subscription_id,
            stripeCustomerId: user.stripe_customer_id
          };
        }
      } catch (error) {
        console.error('Error in getUserById with Supabase:', error);
        // Fall back to file-based storage
      }
    }

    // File-based storage (fallback)
    const user = this.users[userId];
    console.log(`User from file storage: ${JSON.stringify(user, null, 2)}`);
    return user || null;
  }

  // Update user subscription status
  async updateSubscription(userId, status, subscriptionId = null) {
    if (useSupabase && supabase) {
      try {
        const updates = {
          subscription_status: status,
          subscription_id: subscriptionId,
          subscription_updated_at: new Date().toISOString()
        };

        const { data, error } = await supabase
          .from('users')
          .update(updates)
          .eq('id', userId)
          .select()
          .single();

        if (error) {
          console.error('Error updating subscription in Supabase:', error);
          // Fall back to file-based storage
        } else {
          console.log(`Updated subscription for user ${userId} in Supabase:`, {
            status,
            subscriptionId,
            updatedAt: updates.subscription_updated_at
          });
          return true;
        }
      } catch (error) {
        console.error('Error in updateSubscription with Supabase:', error);
        // Fall back to file-based storage
      }
    }

    // File-based storage (fallback)
    if (this.users[userId]) {
      this.users[userId].subscriptionStatus = status;
      this.users[userId].subscriptionId = subscriptionId;
      this.users[userId].cancelAtPeriodEnd = cancelAtPeriodEnd;
      this.users[userId].subscriptionUpdatedAt = new Date();

      // Save the updated users to file
      this.saveUsersToFile();

      console.log(`Updated subscription for user ${userId} in file storage:`, {
        status,
        subscriptionId,
        updatedAt: this.users[userId].subscriptionUpdatedAt
      });

      return true;
    }
    return false;
  }

  // Update user's Stripe customer ID
  async updateStripeCustomerId(userId, customerId) {
    if (useSupabase && supabase) {
      try {
        const { data, error } = await supabase
          .from('users')
          .update({
            stripe_customer_id: customerId
          })
          .eq('id', userId)
          .select()
          .single();

        if (error) {
          console.error('Error updating Stripe customer ID in Supabase:', error);
          // Fall back to file-based storage
        } else {
          console.log(`Updated Stripe customer ID for user ${userId} to ${customerId} in Supabase`);
          return true;
        }
      } catch (error) {
        console.error('Error in updateStripeCustomerId with Supabase:', error);
        // Fall back to file-based storage
      }
    }

    // File-based storage (fallback)
    if (this.users[userId]) {
      this.users[userId].stripeCustomerId = customerId;

      // Save the updated users to file
      this.saveUsersToFile();

      console.log(`Updated Stripe customer ID for user ${userId} to ${customerId} in file storage`);
      return true;
    }
    return false;
  }

  // Check if user has active subscription
  async hasActiveSubscription(userId) {
    if (useSupabase && supabase) {
      try {
        const { data: user, error } = await supabase
          .from('users')
          .select('subscription_status')
          .eq('id', userId)
          .single();

        if (error) {
          console.error('Error checking subscription status in Supabase:', error);
          // Fall back to file-based storage
        } else {
          const status = user?.subscription_status;
          console.log(`Checking subscription status for user ${userId} in Supabase: ${status}`);

          // User has an active subscription if status is 'active'
          return status === 'active';
        }
      } catch (error) {
        console.error('Error in hasActiveSubscription with Supabase:', error);
        // Fall back to file-based storage
      }
    }

    // File-based storage (fallback)
    // Check the user's subscription status
    const status = this.users[userId]?.subscriptionStatus;
    console.log(`Checking subscription status for user ${userId} in file storage: ${status}`);

    // User has an active subscription if status is 'active'
    return status === 'active';
  }
}

module.exports = new AuthService();
