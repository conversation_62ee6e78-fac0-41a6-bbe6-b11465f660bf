'use client';

import { useState, useEffect } from 'react';
import { useSupabaseAuth } from '../../../contexts/SupabaseAuthContext';
import Link from 'next/link';

export default function SubscriptionTestPage() {
  const { user, session } = useSupabaseAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [subscriptionStatus, setSubscriptionStatus] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const checkSubscription = async () => {
    if (!user) {
      setError('You must be logged in to check subscription status');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      // Get the API URL from environment variables
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'https://api.createlex.com';
      
      // Call the subscription check endpoint
      const response = await fetch(`${backendUrl}/api/subscription/check`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session?.access_token || ''}`,
          'x-user-id': user.id,
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setSubscriptionStatus(data);
      } else {
        setError(`API returned status: ${response.status}`);
        try {
          const errorData = await response.json();
          setSubscriptionStatus(errorData);
        } catch (e) {
          // If we can't parse the error as JSON, just show the status
        }
      }
    } catch (error: any) {
      setError(error.message || 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      checkSubscription();
    }
  }, [user]);

  return (
    <div className="container mx-auto p-8">
      <div className="mb-4">
        <Link href="/api-test" className="text-blue-500 hover:underline">
          ← Back to API Test
        </Link>
      </div>
      
      <h1 className="text-3xl font-bold mb-6">Subscription Status Test</h1>
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Authentication Status</h2>
        <div className={`p-4 rounded ${user ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
          {user ? 'You are authenticated! ✅' : 'You are not authenticated! ❌'}
        </div>
      </div>
      
      {!user && (
        <div className="mb-6">
          <p className="text-red-600">
            You need to be logged in to check subscription status.
          </p>
          <Link 
            href="/api-test/auth" 
            className="mt-2 inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Go to Authentication Test
          </Link>
        </div>
      )}
      
      {user && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Actions</h2>
          <button
            onClick={checkSubscription}
            disabled={isLoading}
            className={`px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {isLoading ? 'Checking...' : 'Check Subscription Status'}
          </button>
        </div>
      )}
      
      {error && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Error</h2>
          <div className="p-4 bg-red-100 text-red-800 rounded">
            {error}
          </div>
        </div>
      )}
      
      {subscriptionStatus && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Subscription Status</h2>
          <pre className="p-4 bg-gray-100 rounded overflow-auto">
            {JSON.stringify(subscriptionStatus, null, 2)}
          </pre>
          
          <div className="mt-4 p-4 rounded bg-blue-100">
            <h3 className="font-semibold text-blue-800">Subscription Status Summary</h3>
            <ul className="mt-2 list-disc list-inside text-blue-800">
              <li>
                Status: {subscriptionStatus.active 
                  ? <span className="text-green-600 font-semibold">Active</span> 
                  : <span className="text-red-600 font-semibold">Inactive</span>}
              </li>
              {subscriptionStatus.plan && (
                <li>Plan: <span className="font-semibold">{subscriptionStatus.plan}</span></li>
              )}
              {subscriptionStatus.expiresAt && (
                <li>Expires: <span className="font-semibold">{new Date(subscriptionStatus.expiresAt).toLocaleDateString()}</span></li>
              )}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}
