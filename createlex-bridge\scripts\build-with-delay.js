const { execSync } = require('child_process');
const fs = require('fs-extra');
const path = require('path');

async function buildWithDelay() {
    console.log('🔄 Starting build with delay to avoid file locking...');
    
    try {
        // Step 1: Build Python files
        console.log('📦 Step 1: Building Python files...');
        execSync('npm run build:python', { stdio: 'inherit' });
        
        // Step 2: Build MCP executable
        console.log('📦 Step 2: Building MCP executable...');
        execSync('npm run build:mcp-exe', { stdio: 'inherit' });
        
        // Step 3: Wait for file system to release locks
        console.log('⏳ Waiting 5 seconds for file system to release locks...');
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Step 4: Copy the executable manually to avoid locks
        console.log('📁 Step 4: Manually copying MCP executable...');
        const sourceFile = path.join(__dirname, '..', 'src', 'python-protected', 'mcp_server.exe');
        const tempFile = path.join(__dirname, '..', 'temp', 'mcp_server_temp.exe');
        
        // Ensure temp directory exists
        await fs.ensureDir(path.dirname(tempFile));
        
        // Copy to temp location first
        await fs.copy(sourceFile, tempFile);
        console.log('✅ MCP executable copied to temp location');
        
        // Step 5: Build frontend
        console.log('📦 Step 5: Building frontend...');
        execSync('npm run build:frontend-simple', { stdio: 'inherit' });
        
        // Step 6: Wait again before electron build
        console.log('⏳ Waiting 3 seconds before electron build...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Step 7: Copy back from temp and run electron builder
        await fs.copy(tempFile, sourceFile);
        console.log('📦 Step 7: Running electron builder...');
        execSync('electron-builder --win', { stdio: 'inherit' });
        
        console.log('✅ Build completed successfully!');
        
    } catch (error) {
        console.error('❌ Build failed:', error.message);
        process.exit(1);
    }
}

buildWithDelay(); 