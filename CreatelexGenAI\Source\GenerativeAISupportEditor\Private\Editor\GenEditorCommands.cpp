// Fill out your copyright notice in the Description page of Project Settings.


#include "Editor/GenEditorCommands.h"

#define LOCTEXT_NAMESPACE "GenEditorCommands"

void FGenEditorCommands::RegisterCommands()
{
	UI_COMMAND(
		OpenGenEditorWindow,
		"Gen AI Support",
		"Open the Generative AI Support window",
		EUserInterfaceActionType::Button,
		FInputChord());

	UI_COMMAND(
		SyncWithAI,
		"Sync with AI",
		"Sync current asset context with AI Assistant",
		EUserInterfaceActionType::But<PERSON>,
		FInputChord(EKeys::F12, EModifierKey::Control)); // Ctrl+F12 as shortcut
}

#undef LOCTEXT_NAMESPACE
