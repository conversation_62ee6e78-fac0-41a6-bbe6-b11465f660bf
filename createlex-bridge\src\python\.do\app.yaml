name: unreal-mcp-cloud-server
region: nyc

services:
- name: mcp-server
  source_dir: /UnrealGenAISupport_with_server/server
  github:
    repo: AlexKissiJr/AiWebplatform
    branch: cc_plugin
    deploy_on_push: true
  dockerfile_path: UnrealGenAISupport_with_server/server/Dockerfile.cloud
  
  # Environment variables
  envs:
  - key: PORT
    value: "8000"
  - key: ENVIRONMENT
    value: "production"
  - key: LOG_LEVEL
    value: "info"
    
  # Health check
  health_check:
    http_path: /health
    initial_delay_seconds: 10
    period_seconds: 10
    timeout_seconds: 5
    success_threshold: 1
    failure_threshold: 3
    
  # HTTP configuration
  http_port: 8000
  
  # Instance configuration
  instance_count: 1
  instance_size_slug: basic-xxs  # $5/month
  
  # Auto-scaling (optional)
  autoscaling:
    min_instance_count: 1
    max_instance_count: 3
    metrics:
      cpu:
        percent: 80
  
  # Routes for external access
  routes:
  - path: /
    preserve_path_prefix: true

# Optional: Database for storing instance registrations
# databases:
# - name: mcp-db
#   engine: PG
#   version: "14"
#   size: db-s-dev-database  # $7/month

# Optional: Static site for documentation
# static_sites:
# - name: mcp-docs
#   source_dir: /docs
#   github:
#     repo: AlexKissiJr/AiWebplatform
#     branch: cc_plugin
#   routes:
#   - path: /docs 