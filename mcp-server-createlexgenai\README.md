# CreateLex MCP Server for Unreal Engine

<p align="center">
  <strong>Advanced Model Control Protocol Server for Unreal Engine Integration</strong><br/>
  Professional AI-to-Engine communication powered by CreateLex
</p>

---

## 🚀 **Overview**

The **CreateLex MCP Server** enables direct AI control of Unreal Engine through the Model Control Protocol (MCP). This server acts as a bridge between AI assistants (<PERSON>op, Cursor IDE) and your Unreal Engine projects, allowing natural language commands to control scenes, generate blueprints, and manage assets.

### ✨ **Key Features**

- 🎮 **Direct Engine Control**: AI can spawn objects, modify scenes, and control lighting
- 🔧 **Blueprint Generation**: Create complete Blueprint classes from natural language
- 🎨 **Material Management**: Intelligent material creation and application
- 📁 **Project Organization**: Automated file and folder management
- 🐍 **Python Integration**: Execute custom scripts directly in Unreal Engine
- 🔒 **Secure Authentication**: CreateLex subscription validation and access control
- ☁️ **Cloud Deployment**: Scalable server hosting with enterprise features

---

## 🛠️ **Supported MCP Tools**

### **🎮 Scene Management**
- `spawn_actor` - Spawn objects with intelligent positioning
- `get_actors_in_level` - List and inspect scene objects
- `delete_actor` - Remove objects from scenes
- `set_object_position/rotation/scale` - Transform object properties

### **🎨 Materials & Assets**
- `create_material` - Generate materials with custom properties
- `create_advanced_material` - Complex materials with texture support
- `set_object_material` - Apply materials to scene objects

### **🔧 Blueprint Automation**
- `create_blueprint` - Generate Blueprint classes
- `add_component_to_blueprint` - Add components automatically
- `add_variable_to_blueprint` - Create Blueprint variables
- `add_function_to_blueprint` - Generate custom functions
- `add_node_to_blueprint` - Build node graphs
- `connect_blueprint_nodes` - Wire node connections
- `compile_blueprint` - Compile and validate Blueprints

### **📁 Project Management**
- `create_project_folder` - Organize project structure
- `get_files_in_folder` - Browse project contents
- File and asset management tools

### **🐍 Advanced Features**
- `execute_python_script` - Run Python code in Unreal Engine
- `handshake_test` - Verify engine connectivity
- `check_unreal_connection` - Monitor connection health

---

## 🚀 **Quick Start**

### **1. Prerequisites**
- Python 3.8+
- Unreal Engine 4.26+ with Python Editor Script Plugin enabled
- CreateLex subscription (for production features)

### **2. Installation**

#### **Local Development**
```bash
# Clone or download the MCP server
cd mcp-server-unrealgenAIsupport

# Install dependencies
pip install -r requirements.txt

# Start the server
python mcp_server.py
```

#### **Production Deployment**
```bash
# Use Docker for production
docker-compose up -d

# Or deploy to cloud platforms
./deploy.sh
```

### **3. Configure AI Clients**

#### **Claude Desktop**
Edit `claude_desktop_config.json`:
```json
{
  "mcpServers": {
    "createlex-unreal": {
      "command": "python",
      "args": ["path/to/mcp-server-unrealgenAIsupport/mcp_server.py"],
      "env": {
        "UNREAL_HOST": "localhost",
        "UNREAL_PORT": "9877",
        "CREATELEX_TOKEN": "your-subscription-token"
      }
    }
  }
}
```

#### **Cursor IDE**
Create `.cursor/mcp.json`:
```json
{
  "mcpServers": {
    "createlex-unreal": {
      "command": "python",
      "args": ["path/to/mcp-server-unrealgenAIsupport/mcp_server.py"],
      "env": {
        "UNREAL_HOST": "localhost",
        "UNREAL_PORT": "9877",
        "CREATELEX_TOKEN": "your-subscription-token"
      }
    }
  }
}
```

### **4. Start Unreal Engine**
1. Open your Unreal project
2. Enable the **Python Editor Script Plugin**
3. The MCP server will automatically connect when available

---

## 💡 **Usage Examples**

### **🎮 Scene Creation**
```
"Create a medieval village with 5 houses, a central well, and atmospheric lighting"
```
**Result**: AI automatically spawns buildings, positions them logically, applies materials, and sets up lighting.

### **🔧 Blueprint Generation**
```
"Create a player controller with WASD movement, mouse look, and jump functionality"
```
**Result**: Complete Blueprint class with input bindings, movement logic, and jump mechanics.

### **🎨 Material Creation**
```
"Create a rusty metal material with normal maps and weathering effects"
```
**Result**: Advanced material with multiple texture inputs and realistic properties.

---

## 🏢 **Enterprise Features**

### **🔒 Authentication & Security**
- CreateLex subscription validation
- Secure token-based authentication
- Usage tracking and analytics
- Team collaboration features

### **☁️ Cloud Deployment**
- Docker containerization
- Kubernetes orchestration
- Auto-scaling capabilities
- Global CDN distribution

### **📊 Monitoring & Analytics**
- Real-time performance metrics
- Usage analytics and reporting
- Error tracking and alerting
- Health monitoring dashboards

---

## 🔧 **Configuration**

### **Environment Variables**
```bash
# Required
UNREAL_HOST=localhost
UNREAL_PORT=9877
CREATELEX_TOKEN=your-subscription-token

# Optional
MCP_SERVER_PORT=8000
LOG_LEVEL=INFO
ENABLE_METRICS=true
```

### **Advanced Configuration**
See `config/app_config.py` for detailed configuration options including:
- Custom tool configurations
- Security settings
- Performance tuning
- Integration options

---

## 🚀 **Deployment Options**

### **🐳 Docker Deployment**
```bash
# Build and run with Docker
docker build -t createlex-mcp-server .
docker run -p 8000:8000 createlex-mcp-server
```

### **☁️ Cloud Platforms**
- **DigitalOcean**: Use `deploy-to-digitalocean.sh`
- **AWS/Azure/GCP**: Kubernetes manifests included
- **Railway/Heroku**: One-click deployment configs

### **🔧 Local Development**
```bash
# Development mode with hot reload
python mcp_server.py --dev

# Run tests
python -m pytest tests/
```

---

## 📚 **Documentation**

- **📖 Full Documentation**: [docs.createlex.com/mcp-server](https://docs.createlex.com/mcp-server)
- **🎥 Setup Videos**: [tutorials.createlex.com](https://tutorials.createlex.com)
- **🔧 API Reference**: [api.createlex.com](https://api.createlex.com)
- **💬 Community**: [community.createlex.com](https://community.createlex.com)

---

## 🤝 **Support**

- **🎫 Support Tickets**: [support.createlex.com](https://support.createlex.com)
- **📧 Enterprise Sales**: <EMAIL>
- **💬 Community Forum**: [community.createlex.com](https://community.createlex.com)
- **📖 Knowledge Base**: [help.createlex.com](https://help.createlex.com)

---

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

**Copyright © 2025 CreateLex Inc. All rights reserved.**

---

<p align="center">
  <strong>Transform your Unreal Engine workflow with AI</strong><br/>
  <a href="https://createlex.com/get-started">Get Started</a> | 
  <a href="https://createlex.com/demo">Request Demo</a> | 
  <a href="https://docs.createlex.com">Documentation</a>
</p>

<p align="center">
  Powered by <a href="https://createlex.com">CreateLex</a> 🚀
</p> 