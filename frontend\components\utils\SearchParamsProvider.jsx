'use client';

import { Suspense } from 'react';

/**
 * A wrapper component that provides a Suspense boundary for components using useSearchParams()
 * This prevents the "useSearchParams() should be wrapped in a suspense boundary" warning
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - The child components that use useSearchParams()
 * @param {React.ReactNode} props.fallback - Optional fallback UI to show while loading
 * @returns {React.ReactElement} The wrapped component
 */
export default function SearchParamsProvider({ children, fallback = <div>Loading...</div> }) {
  return (
    <Suspense fallback={fallback}>
      {children}
    </Suspense>
  );
}
