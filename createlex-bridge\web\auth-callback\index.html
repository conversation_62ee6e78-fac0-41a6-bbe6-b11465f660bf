<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CreateLex Bridge - Authentication Success</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #333;
    }

    .callback-container {
      background: white;
      border-radius: 12px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      padding: 40px;
      width: 100%;
      max-width: 500px;
      text-align: center;
    }

    .logo {
      width: 64px;
      height: 64px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 16px;
      margin: 0 auto 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-weight: bold;
      color: white;
    }

    .success-icon {
      color: #16a34a;
      font-size: 48px;
      margin-bottom: 20px;
    }

    .processing-icon {
      color: #667eea;
      font-size: 48px;
      margin-bottom: 20px;
    }

    h1 {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 12px;
      color: #1a202c;
    }

    .message {
      color: #6b7280;
      margin-bottom: 24px;
      font-size: 16px;
      line-height: 1.5;
    }

    .status {
      background: #f0fdf4;
      border: 1px solid #bbf7d0;
      color: #16a34a;
      padding: 12px;
      border-radius: 8px;
      margin: 20px 0;
      font-size: 14px;
    }

    .info {
      background: #eff6ff;
      border: 1px solid #bfdbfe;
      color: #1d4ed8;
    }

    .error {
      background: #fef2f2;
      border: 1px solid #fecaca;
      color: #dc2626;
    }

    .spinner {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 8px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .button {
      background: #667eea;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 16px;
      font-weight: 500;
      transition: all 0.2s ease;
      margin: 8px;
    }

    .button:hover {
      opacity: 0.9;
      transform: translateY(-1px);
    }

    .button-secondary {
      background: #6b7280;
    }
  </style>
</head>
<body>
  <div class="callback-container">
    <div class="logo">CL</div>
    
    <div id="loadingState">
      <div class="processing-icon">
        <span class="spinner"></span>
      </div>
      <h1>Processing Authentication...</h1>
      <p class="message">Please wait while we complete your login to the CreateLex Bridge.</p>
    </div>

    <div id="successState" style="display: none;">
      <div class="success-icon">✅</div>
      <h1>Authentication Successful!</h1>
      <p class="message">You have been successfully authenticated with the CreateLex Bridge.</p>
      
      <div class="status">
        <strong>✓ User authenticated</strong><br>
        <span id="userInfo"></span>
      </div>

      <div class="status info">
        🎯 Redirecting to local bridge dashboard...
      </div>

      <p class="message">
        The CreateLex Bridge application will open automatically.
        You can close this browser window.
      </p>

      <button class="button" onclick="closeWindow()">Close Window</button>
    </div>

    <div id="errorState" style="display: none;">
      <div style="color: #dc2626; font-size: 48px; margin-bottom: 20px;">❌</div>
      <h1>Authentication Error</h1>
      <p class="message">There was an error processing your authentication.</p>
      
      <div class="status error">
        <span id="errorMessage"></span>
      </div>

      <button class="button" onclick="retryAuth()">Try Again</button>
      <button class="button button-secondary" onclick="closeWindow()">Close Window</button>
    </div>
  </div>

  <script>
    console.log('🔗 CreateLex Bridge Auth Callback Page Loaded');
    
    // Parse URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    const userId = urlParams.get('userId');
    const email = urlParams.get('email');
    const hasSubscription = urlParams.get('hasSubscription') === 'true';
    const error = urlParams.get('error');

    console.log('📥 Received auth data:', { token: !!token, userId, email, hasSubscription, error });

    // Check if this is the expected callback
    if (token && userId) {
      // Success case
      processAuthSuccess(token, userId, email, hasSubscription);
    } else if (error) {
      // Error case
      showError(error);
    } else {
      // No expected parameters - might be a direct access
      showError('Invalid authentication callback. Missing required parameters. Please try logging in again.');
    }

    async function processAuthSuccess(token, userId, email, hasSubscription) {
      try {
        console.log('✅ Processing successful authentication');
        
        // Update UI with user info
        document.getElementById('userInfo').innerHTML = `
          User ID: ${userId}<br>
          Email: ${email || 'Not provided'}<br>
          Subscription: ${hasSubscription ? 'Active ✅' : 'None ❌'}
        `;

        // Send auth data to bridge callback server
        const authData = {
          token,
          userId,
          email,
          hasSubscription,
          timestamp: new Date().toISOString()
        };

        console.log('📤 Sending auth data to bridge server...');

        // Try to notify the bridge via fetch to the callback server
        try {
          const response = await fetch('http://localhost:7891/auth/success', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(authData)
          });

          if (response.ok) {
            console.log('✅ Successfully notified bridge server');
            const result = await response.json();
            console.log('📨 Bridge server response:', result);
          } else {
            console.warn('⚠️ Bridge notification failed:', response.status);
            throw new Error(`Bridge server responded with status ${response.status}`);
          }
        } catch (fetchError) {
          console.error('❌ Could not reach bridge callback server:', fetchError);
          
          // Show a different message if the bridge server is not available
          showError('Bridge application not found. Please make sure the CreateLex Bridge application is running and try the authentication process again.');
          return;
        }

        // Show success state
        document.getElementById('loadingState').style.display = 'none';
        document.getElementById('successState').style.display = 'block';

        // Auto-close after 5 seconds
        setTimeout(() => {
          console.log('⏰ Auto-closing window...');
          closeWindow();
        }, 5000);

      } catch (err) {
        console.error('❌ Error processing auth success:', err);
        showError('Failed to process authentication: ' + err.message);
      }
    }

    function showError(message) {
      console.error('❌ Auth error:', message);
      document.getElementById('errorMessage').textContent = message;
      document.getElementById('loadingState').style.display = 'none';
      document.getElementById('errorState').style.display = 'block';
    }

    function closeWindow() {
      console.log('🔄 Attempting to close window...');
      
      // Try multiple methods to close the window
      if (window.opener) {
        window.opener.postMessage({
          type: 'CREATELEX_AUTH_COMPLETE',
          success: true
        }, '*');
      }
      
      // Try to close the window
      window.close();
      
      // If window.close() doesn't work, show instructions
      setTimeout(() => {
        console.log('ℹ️ Window close failed, showing instructions');
        alert('Please close this browser tab and return to the CreateLex Bridge application.');
      }, 1000);
    }

    function retryAuth() {
      console.log('🔄 Retrying authentication...');
      // Redirect back to login
      window.location.href = 'https://createlex.com/login?redirect=' + 
        encodeURIComponent(window.location.href.split('?')[0]) + '&source=bridge';
    }

    // Handle messages from parent window
    window.addEventListener('message', (event) => {
      if (event.data.type === 'CREATELEX_AUTH_REQUEST') {
        console.log('📨 Received auth request from parent window');
        // Send current auth data if available
        if (token && userId) {
          event.source.postMessage({
            type: 'CREATELEX_AUTH_RESPONSE',
            success: true,
            token,
            userId,
            email,
            hasSubscription
          }, event.origin);
        }
      }
    });

    // Add some debugging info
    console.log('🔧 Debug Info:');
    console.log('- Current URL:', window.location.href);
    console.log('- URL Parameters:', Object.fromEntries(urlParams));
    console.log('- Window Origin:', window.location.origin);
  </script>
</body>
</html>