'use client';

import React, { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '../../contexts/AuthContext';
import TokenPurchaseForm from '../../components/TokenPurchaseForm';
import Link from 'next/link';

export default function PurchaseTokensPage() {
  const { isAuthenticated, isLoading, hasActiveSubscription } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const packageId = searchParams?.get('package') || 'small';

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!isLoading && !isAuthenticated) {
      console.log('PurchaseTokensPage: User not authenticated, redirecting to login');

      // Use window.location for a full page reload to ensure clean state
      window.location.href = '/login';
      return;
    }

    console.log('PurchaseTokensPage: Authentication state:', {
      isAuthenticated,
      isLoading,
      hasActiveSubscription
    });
  }, [isAuthenticated, isLoading, hasActiveSubscription, router]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-[#f7f7f7]">
        <div className="text-center">
          <div className="spinner mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-[#f7f7f7]">
      {/* Header */}
      <header className="py-4 px-6 border-b border-gray-200 bg-white">
        <div className="max-w-screen-xl mx-auto flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <Link href="/dashboard">
              <div className="flex items-center space-x-2 cursor-pointer">
                <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full"></div>
                <span className="text-xl font-bold">CreateLex</span>
              </div>
            </Link>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="flex-1 py-8 px-6">
        <div className="max-w-screen-md mx-auto">
          <div className="mb-6">
            <Link href="/dashboard" className="text-blue-600 hover:underline flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to Dashboard
            </Link>
          </div>

          <h1 className="text-2xl font-bold text-gray-800 mb-6">Purchase Additional Tokens</h1>

          {!hasActiveSubscription ? (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
              <h2 className="text-lg font-semibold text-yellow-800 mb-2">Subscription Required</h2>
              <p className="text-yellow-700 mb-4">
                You need an active subscription to purchase additional tokens.
              </p>
              <Link
                href="/subscription"
                className="inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Subscribe Now
              </Link>
            </div>
          ) : (
            <TokenPurchaseForm
              defaultPackage={packageId}
              onCancel={() => router.push('/dashboard')}
            />
          )}
        </div>
      </main>

      {/* Footer */}
      <footer className="py-6 px-8 border-t border-gray-200 bg-white mt-auto">
        <div className="max-w-screen-xl mx-auto flex flex-col md:flex-row justify-between items-center text-sm text-gray-500">
          <div className="mb-4 md:mb-0">
            © {new Date().getFullYear()} CreateLex. All rights reserved.
          </div>
          <div className="flex space-x-6">
            <Link href="/terms" className="hover:text-gray-700">Terms</Link>
            <Link href="/privacy" className="hover:text-gray-700">Privacy</Link>
            <Link href="/contact" className="hover:text-gray-700">Contact</Link>
          </div>
        </div>
      </footer>
    </div>
  );
}
