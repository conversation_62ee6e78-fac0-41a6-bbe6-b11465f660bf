'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../contexts/AuthContext';
import { useSupabaseAuth } from '../../contexts/SupabaseAuthContext';
import { getSupabaseClient } from '@/lib/supabase-singleton';
import Image from 'next/image';
import Link from 'next/link';

export default function ProfilePage() {
  const { isAuthenticated, isLoading, user } = useAuth();
  const { user: supabaseUser, isLoading: supabaseLoading } = useSupabaseAuth();
  const router = useRouter();

  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [avatarUrl, setAvatarUrl] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });
  const [userDetails, setUserDetails] = useState<{
    created_at?: string;
    subscription_status?: string;
    last_login?: string;
  }>({});

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!isLoading && !supabaseLoading) {
      if (!isAuthenticated || !supabaseUser) {
        router.push('/login');
      }
    }
  }, [isAuthenticated, isLoading, supabaseUser, supabaseLoading, router]);

  useEffect(() => {
    if (supabaseUser) {
      setName(supabaseUser.user_metadata?.full_name || '');
      setEmail(supabaseUser.email || '');
      setAvatarUrl(supabaseUser.user_metadata?.avatar_url || '');

      // Fetch additional user details from Supabase
      fetchUserDetails(supabaseUser.id);
    }
  }, [supabaseUser]);

  // Function to fetch user details from Supabase
  const fetchUserDetails = async (userId: string) => {
    try {
      const supabase = getSupabaseClient();
      const { data, error } = await supabase
        .from('users')
        .select('created_at, subscription_status, subscription_updated_at')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching user details:', error);
        return;
      }

      if (data) {
        console.log('User details fetched:', data);
        setUserDetails(data);
      }
    } catch (error) {
      console.error('Error in fetchUserDetails:', error);
    }
  };

  const handleSaveProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    setMessage({ type: '', text: '' });

    try {
      // Update user metadata in Supabase Auth
      const supabase = getSupabaseClient();
      const { error } = await supabase.auth.updateUser({
        data: {
          full_name: name
        }
      });

      if (error) {
        throw error;
      }

      setMessage({ type: 'success', text: 'Profile updated successfully!' });
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating profile:', error);
      setMessage({ type: 'error', text: 'Failed to update profile. Please try again.' });
    } finally {
      setIsSaving(false);
    }
  };

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // In a real app, this would upload the file to storage
      // For this demo, we'll just create a local URL
      const url = URL.createObjectURL(file);
      setAvatarUrl(url);
    }
  };

  if (isLoading || supabaseLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-[#f7f7f7]">
        <div className="text-center">
          <div className="spinner mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-[#f7f7f7]">
      {/* Header */}
      <header className="py-4 px-6 border-b border-gray-200 bg-white">
        <div className="max-w-screen-xl mx-auto flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <Link href="/dashboard">
              <div className="flex items-center space-x-2 cursor-pointer">
                <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full"></div>
                <span className="text-xl font-bold">CreateLex</span>
              </div>
            </Link>
          </div>
          <div className="flex items-center space-x-4">
            {supabaseUser && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">{supabaseUser.email}</span>
                {avatarUrl ? (
                  <img
                    src={avatarUrl}
                    alt={name || 'User'}
                    className="h-8 w-8 rounded-full object-cover"
                  />
                ) : (
                  <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-700">
                    {email.charAt(0).toUpperCase()}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="flex-1 py-8 px-6">
        <div className="max-w-screen-md mx-auto">
          <div className="flex items-center justify-between mb-8">
            <h1 className="text-2xl font-bold text-gray-800">Your Profile</h1>
            <Link
              href="/settings"
              className="text-blue-600 hover:text-blue-800 flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
              </svg>
              Settings
            </Link>
          </div>

          {message.text && (
            <div className={`mb-6 p-4 rounded-md ${message.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
              {message.text}
            </div>
          )}

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="p-6">
              <div className="flex flex-col md:flex-row items-start md:items-center mb-6">
                <div className="relative mb-4 md:mb-0 md:mr-6">
                  {avatarUrl ? (
                    <img
                      src={avatarUrl}
                      alt={name || 'User'}
                      className="h-24 w-24 rounded-full object-cover"
                    />
                  ) : (
                    <div className="h-24 w-24 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 text-2xl">
                      {email.charAt(0).toUpperCase()}
                    </div>
                  )}
                  {isEditing && (
                    <label htmlFor="avatar-upload" className="absolute bottom-0 right-0 bg-blue-600 text-white rounded-full p-2 cursor-pointer hover:bg-blue-700">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                      </svg>
                      <input
                        id="avatar-upload"
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={handleAvatarChange}
                      />
                    </label>
                  )}
                </div>
                <div className="flex-1">
                  <h2 className="text-xl font-semibold">{name || 'User'}</h2>
                  <p className="text-gray-600">{email}</p>
                  {!isEditing && (
                    <button
                      onClick={() => setIsEditing(true)}
                      className="mt-2 text-blue-600 hover:text-blue-800 flex items-center text-sm"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                      </svg>
                      Edit Profile
                    </button>
                  )}
                </div>
              </div>

              {isEditing ? (
                <form onSubmit={handleSaveProfile}>
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                        Full Name
                      </label>
                      <input
                        id="name"
                        type="text"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                        Email Address
                      </label>
                      <input
                        id="email"
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      />
                    </div>
                    <div className="flex justify-end space-x-3 pt-4">
                      <button
                        type="button"
                        onClick={() => {
                          setIsEditing(false);
                          // Reset form
                          if (supabaseUser) {
                            setName(supabaseUser.user_metadata?.full_name || '');
                            setEmail(supabaseUser.email || '');
                            setAvatarUrl(supabaseUser.user_metadata?.avatar_url || '');
                          }
                        }}
                        className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                        disabled={isSaving}
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        className={`px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 ${isSaving ? 'opacity-70 cursor-not-allowed' : ''}`}
                        disabled={isSaving}
                      >
                        {isSaving ? 'Saving...' : 'Save Changes'}
                      </button>
                    </div>
                  </div>
                </form>
              ) : (
                <div className="border-t border-gray-200 pt-6 mt-6">
                  <h3 className="text-lg font-semibold mb-4">Account Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Member Since</p>
                      <p>{userDetails.created_at
                          ? new Date(userDetails.created_at).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })
                          : 'Loading...'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Subscription</p>
                      <p className="flex items-center">
                        <span className={`inline-block h-2 w-2 rounded-full ${
                          userDetails.subscription_status === 'active'
                            ? 'bg-green-500'
                            : 'bg-red-500'
                        } mr-2`}></span>
                        {userDetails.subscription_status === 'active'
                          ? 'Active'
                          : userDetails.subscription_status === 'inactive'
                            ? 'Inactive'
                            : userDetails.subscription_status || 'Unknown'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Last Login</p>
                      <p>{supabaseUser?.last_sign_in_at
                          ? new Date(supabaseUser.last_sign_in_at).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })
                          : 'Today'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Account Type</p>
                      <p>{userDetails.subscription_status === 'active'
                          ? 'Premium'
                          : 'Free'}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>


        </div>
      </main>

      {/* Footer */}
      <footer className="py-6 px-8 border-t border-gray-200 bg-white mt-auto">
        <div className="max-w-screen-xl mx-auto flex flex-col md:flex-row justify-between items-center text-sm text-gray-500">
          <div className="mb-4 md:mb-0">
            © {new Date().getFullYear()} CreateLex. All rights reserved.
          </div>
          <div className="flex space-x-6">
            <Link href="/terms" className="hover:text-gray-700">Terms</Link>
            <Link href="/privacy" className="hover:text-gray-700">Privacy</Link>
            <Link href="/contact" className="hover:text-gray-700">Contact</Link>
          </div>
        </div>
      </footer>
    </div>
  );
}
