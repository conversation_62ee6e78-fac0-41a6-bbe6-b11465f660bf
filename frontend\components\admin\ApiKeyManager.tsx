'use client';

import { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, Key, MoreHorizontal, Plus, Refresh<PERSON>w, Trash } from 'lucide-react';
import { fetchWithAuth } from '@/lib/auth-utils';
import { format } from 'date-fns';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from 'sonner';

interface ApiKey {
  id: string;
  key_name: string;
  api_key: string;
  created_at: string;
  last_used_at: string | null;
  is_active: boolean;
  rate_limit: number;
  user_id: string;
  user_email?: string;
}

const createApiKeySchema = z.object({
  keyName: z.string().min(3, 'Key name must be at least 3 characters'),
  rateLimit: z.number().min(1, 'Rate limit must be at least 1'),
  userId: z.string().optional(),
});

export default function ApiKeyManager() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newKeyData, setNewKeyData] = useState<{ key: string; prefix: string } | null>(null);
  const [selectedKey, setSelectedKey] = useState<ApiKey | null>(null);
  const [confirmDelete, setConfirmDelete] = useState<string | null>(null);

  const form = useForm<z.infer<typeof createApiKeySchema>>({
    resolver: zodResolver(createApiKeySchema),
    defaultValues: {
      keyName: '',
      rateLimit: 100,
    },
  });

  useEffect(() => {
    fetchApiKeys();
  }, []);

  const fetchApiKeys = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔑 Fetching API keys from backend...');

      // Call the backend API directly
      const response = await fetchWithAuth('https://api.createlex.com/api/admin/api-keys');

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API keys fetch failed:', response.status, errorText);
        throw new Error(`Failed to fetch API keys: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('✅ API keys response:', data);

      // Handle different response formats
      if (data.keys) {
        setApiKeys(data.keys);
      } else if (Array.isArray(data)) {
        setApiKeys(data);
      } else {
        setApiKeys([]);
      }

      console.log(`✅ Loaded ${apiKeys.length} API keys`);
    } catch (error) {
      console.error('❌ Error fetching API keys:', error);
      setError(error instanceof Error ? error.message : 'Failed to load API keys');
      setApiKeys([]); // Clear any existing data
    } finally {
      setLoading(false);
    }
  };

  const generateMockData = (): ApiKey[] => {
    return [
      {
        id: 'key1',
        key_name: 'Production API Key',
        api_key: 'sk_prod_••••••••••••••••',
        created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        last_used_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        is_active: true,
        rate_limit: 100,
        user_id: 'admin',
        user_email: '<EMAIL>'
      },
      {
        id: 'key2',
        key_name: 'Development API Key',
        api_key: 'sk_dev_••••••••••••••••',
        created_at: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
        last_used_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        is_active: true,
        rate_limit: 50,
        user_id: 'admin',
        user_email: '<EMAIL>'
      },
      {
        id: 'key3',
        key_name: 'Test API Key',
        api_key: 'sk_test_•••••••••••••••',
        created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        last_used_at: null,
        is_active: false,
        rate_limit: 10,
        user_id: 'user1',
        user_email: '<EMAIL>'
      }
    ];
  };

  const handleCreateApiKey = async (values: z.infer<typeof createApiKeySchema>) => {
    try {
      console.log('🔑 Creating API key:', values);

      const response = await fetchWithAuth('https://api.createlex.com/api/admin/api-keys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          keyName: values.keyName,
          rateLimit: values.rateLimit,
          userId: values.userId,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Create API key failed:', response.status, errorText);
        throw new Error(`Failed to create API key: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('✅ API key created:', data);

      // Store the new key data to display to the user
      setNewKeyData({
        key: data.apiKey || data.api_key,
        prefix: data.prefix || 'sk_',
      });

      // Reset the form
      form.reset();

      // Refresh the API keys list
      fetchApiKeys();

      toast.success('API key created successfully');
    } catch (error) {
      console.error('❌ Error creating API key:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create API key');
    }
  };

  const handleRevokeApiKey = async (keyId: string) => {
    try {
      console.log('🗑️ Revoking API key:', keyId);

      const response = await fetchWithAuth(`https://api.createlex.com/api/admin/api-keys/${keyId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Revoke API key failed:', response.status, errorText);
        throw new Error(`Failed to revoke API key: ${response.status} - ${errorText}`);
      }

      console.log('✅ API key revoked successfully');

      // Refresh the API keys list
      fetchApiKeys();

      toast.success('API key revoked successfully');
    } catch (error) {
      console.error('❌ Error revoking API key:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to revoke API key');
    } finally {
      setConfirmDelete(null);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard');
  };

  const filteredKeys = apiKeys.filter(key =>
    key.key_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    key.user_email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    key.api_key.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between">
          <Skeleton className="h-10 w-[200px]" />
          <Skeleton className="h-10 w-[100px]" />
        </div>
        <div className="space-y-2">
          {[...Array(3)].map((_, i) => (
            <Skeleton key={i} className="h-12 w-full" />
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return <div className="text-red-500 p-4">{error}</div>;
  }

  return (
    <div className="space-y-4">
      {/* Unreal Engine Integration Banner */}
      <div className="rounded-md bg-blue-50 p-4 border border-blue-200">
        <div className="flex">
          <div className="flex-shrink-0">
            <Key className="h-5 w-5 text-blue-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">Unreal Engine Integration</h3>
            <div className="mt-2 text-sm text-blue-700">
              <p>
                Create an API key for Unreal Engine MCP bridge connectivity.
                Use the "🎮 Unreal Engine" preset for optimal settings.
              </p>
              <p className="mt-1 font-mono text-xs">
                Set environment variable: <code>CREATELEX_API_KEY=your_generated_key</code>
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <Input
            placeholder="Search API keys..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
        </div>

        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create API Key
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New API Key</DialogTitle>
              <DialogDescription>
                Create a new API key for accessing the platform programmatically.
              </DialogDescription>
            </DialogHeader>

            {newKeyData ? (
              <div className="space-y-4">
                <div className="rounded-md bg-yellow-50 p-4 border border-yellow-200">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <Key className="h-5 w-5 text-yellow-400" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-yellow-800">Important</h3>
                      <div className="mt-2 text-sm text-yellow-700">
                        <p>
                          This API key will only be displayed once. Please copy it now and store it securely.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="font-medium text-sm">Your API Key</div>
                  <div className="flex">
                    <Input
                      value={newKeyData.key}
                      readOnly
                      className="font-mono text-sm"
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      className="ml-2"
                      onClick={() => copyToClipboard(newKeyData.key)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <DialogFooter>
                  <Button onClick={() => {
                    setNewKeyData(null);
                    setShowCreateDialog(false);
                  }}>
                    Done
                  </Button>
                </DialogFooter>
              </div>
            ) : (
              <Form {...form}>
                <form onSubmit={form.handleSubmit(handleCreateApiKey)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="keyName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Key Name</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., Unreal Engine Bridge" {...field} />
                        </FormControl>
                        <FormDescription>
                          A descriptive name to identify this API key
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Quick preset buttons */}
                  <div className="flex gap-2 flex-wrap">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        form.setValue('keyName', 'Unreal Engine Bridge');
                        form.setValue('rateLimit', 100);
                      }}
                    >
                      🎮 Unreal Engine
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        form.setValue('keyName', 'VSCode Extension');
                        form.setValue('rateLimit', 50);
                      }}
                    >
                      💻 VSCode Extension
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        form.setValue('keyName', 'Production API');
                        form.setValue('rateLimit', 200);
                      }}
                    >
                      🚀 Production
                    </Button>
                  </div>

                  <FormField
                    control={form.control}
                    name="rateLimit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Rate Limit</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min={1}
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value))}
                          />
                        </FormControl>
                        <FormDescription>
                          Maximum number of requests per minute
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <DialogFooter>
                    <Button type="submit">Create API Key</Button>
                  </DialogFooter>
                </form>
              </Form>
            )}
          </DialogContent>
        </Dialog>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Key</TableHead>
              <TableHead>Created</TableHead>
              <TableHead>Last Used</TableHead>
              <TableHead>Rate Limit</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="w-[80px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredKeys.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-4 text-gray-500">
                  No API keys found
                </TableCell>
              </TableRow>
            ) : (
              filteredKeys.map((key) => (
                <TableRow key={key.id}>
                  <TableCell className="font-medium">{key.key_name}</TableCell>
                  <TableCell className="font-mono text-xs">
                    <div className="flex items-center space-x-2">
                      <span>{key.api_key}</span>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6"
                        onClick={() => copyToClipboard(key.api_key)}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell>{format(new Date(key.created_at), 'MMM dd, yyyy')}</TableCell>
                  <TableCell>
                    {key.last_used_at
                      ? format(new Date(key.last_used_at), 'MMM dd, yyyy')
                      : 'Never'}
                  </TableCell>
                  <TableCell>{key.rate_limit} req/min</TableCell>
                  <TableCell>
                    {key.is_active ? (
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                        Active
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                        Revoked
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => copyToClipboard(key.api_key)}>
                          <Copy className="h-4 w-4 mr-2" />
                          Copy Key
                        </DropdownMenuItem>
                        {key.is_active && (
                          <DropdownMenuItem onClick={() => setConfirmDelete(key.id)}>
                            <Trash className="h-4 w-4 mr-2" />
                            Revoke Key
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      <Dialog open={!!confirmDelete} onOpenChange={(open) => !open && setConfirmDelete(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Revoke API Key</DialogTitle>
            <DialogDescription>
              Are you sure you want to revoke this API key? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setConfirmDelete(null)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={() => confirmDelete && handleRevokeApiKey(confirmDelete)}>
              Revoke Key
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
