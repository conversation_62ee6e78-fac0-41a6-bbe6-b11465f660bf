require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials from environment
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

console.log('Supabase URL:', supabaseUrl ? 'Available' : 'Missing');
console.log('Supabase Key:', supabaseKey ? 'Available (first 10 chars): ' + supabaseKey.substring(0, 10) + '...' : 'Missing');

// Create a new Supabase client for testing
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testDatabase() {
  console.log('Testing Supabase connection and token_usage table...');

  try {
    // Test connection
    console.log('Testing connection to Supabase...');
    const { data: connectionTest, error: connectionError } = await supabase.from('token_usage').select('count(*)', { count: 'exact', head: true });

    if (connectionError) {
      console.error('Connection error details:');
      console.error('- Message:', connectionError.message);
      console.error('- Code:', connectionError.code);
      console.error('- Details:', connectionError.details);
      console.error('- Hint:', connectionError.hint);
      console.error('- Full error object:', JSON.stringify(connectionError, null, 2));

      if (connectionError.code === '42P01') {
        console.error('Error: The token_usage table does not exist. Please run the migrations first.');
        console.log('Run: node scripts/run-migrations.js');
      }
      return;
    }

    console.log('Connection successful!');

    // Test inserting a record
    console.log('Testing insert into token_usage table...');
    const testRecord = {
      user_id: '00000000-0000-0000-0000-000000000000', // Test user ID
      model_id: 'test-model',
      prompt_tokens: 10,
      completion_tokens: 20,
      total_tokens: 30,
      request_type: 'test',
      subscription_plan: 'test'
    };

    const { data: insertData, error: insertError } = await supabase
      .from('token_usage')
      .insert(testRecord)
      .select();

    if (insertError) {
      console.error('Insert error:', insertError);
      return;
    }

    console.log('Insert successful!');
    console.log('Inserted record:', insertData);

    // Test querying the table
    console.log('Testing query from token_usage table...');
    const { data: queryData, error: queryError } = await supabase
      .from('token_usage')
      .select('*')
      .eq('user_id', '00000000-0000-0000-0000-000000000000')
      .order('timestamp', { ascending: false })
      .limit(1);

    if (queryError) {
      console.error('Query error:', queryError);
      return;
    }

    console.log('Query successful!');
    console.log('Query result:', queryData);

    // Test deleting the test record
    console.log('Cleaning up test record...');
    const { error: deleteError } = await supabase
      .from('token_usage')
      .delete()
      .eq('id', insertData[0].id);

    if (deleteError) {
      console.error('Delete error:', deleteError);
      return;
    }

    console.log('Cleanup successful!');
    console.log('All tests passed! The token_usage table is working correctly.');

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

testDatabase();
