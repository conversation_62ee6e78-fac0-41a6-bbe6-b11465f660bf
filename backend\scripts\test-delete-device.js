require('dotenv').config();
const fetch = require('node-fetch');

async function testDeleteDevice() {
  console.log('🧪 Testing device deletion functionality...');
  
  const userId = '5af3ff0f-0ccb-41a2-8dba-3e460aa9bd72';
  const deviceId = 'test-device-123';
  const baseUrl = 'http://localhost:5001';
  
  try {
    console.log('\n1️⃣ Getting current devices...');
    const getResponse = await fetch(`${baseUrl}/api/device/seats`, {
      headers: {
        'x-user-id': userId,
        'Authorization': 'Bearer mock-token'
      }
    });
    
    if (getResponse.ok) {
      const data = await getResponse.json();
      console.log('📱 Current devices:', data.devices.length);
      console.log('📋 Device details:', data.devices.map(d => ({
        id: d.device_id,
        name: d.device_name,
        active: d.is_active
      })));
    } else {
      console.error('❌ Failed to get devices:', getResponse.status);
      return;
    }
    
    console.log('\n2️⃣ Testing DELETE endpoint...');
    const deleteResponse = await fetch(`${baseUrl}/api/device/seats/${deviceId}`, {
      method: 'DELETE',
      headers: {
        'x-user-id': userId,
        'Authorization': 'Bearer mock-token'
      }
    });
    
    if (deleteResponse.ok) {
      const result = await deleteResponse.json();
      console.log('✅ Delete successful:', result.message);
    } else {
      const error = await deleteResponse.text();
      console.error('❌ Delete failed:', deleteResponse.status, error);
      return;
    }
    
    console.log('\n3️⃣ Verifying device is deactivated...');
    const verifyResponse = await fetch(`${baseUrl}/api/device/seats`, {
      headers: {
        'x-user-id': userId,
        'Authorization': 'Bearer mock-token'
      }
    });
    
    if (verifyResponse.ok) {
      const data = await verifyResponse.json();
      console.log('📱 Devices after deletion:');
      data.devices.forEach(device => {
        console.log(`   - ${device.device_name}: ${device.is_active ? 'ACTIVE' : 'INACTIVE'}`);
      });
      console.log('📊 Active count:', data.activeCount);
    }
    
    console.log('\n🎉 Device deletion test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testDeleteDevice(); 