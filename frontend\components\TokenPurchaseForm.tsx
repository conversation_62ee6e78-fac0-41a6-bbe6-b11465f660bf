'use client';

import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'react-hot-toast';

interface TokenPackage {
  id: string;
  name: string;
  tokens: number;
  price: number;
  description: string;
}

interface TokenPurchaseFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  defaultPackage?: string;
}

const TOKEN_PACKAGES: TokenPackage[] = [
  {
    id: 'small',
    name: 'Small',
    tokens: 100000,
    price: 5,
    description: '100,000 tokens'
  },
  {
    id: 'medium',
    name: 'Medium',
    tokens: 500000,
    price: 20,
    description: '500,000 tokens'
  },
  {
    id: 'large',
    name: 'Large',
    tokens: 1000000,
    price: 35,
    description: '1,000,000 tokens'
  }
];

const TokenPurchaseForm: React.FC<TokenPurchaseFormProps> = ({
  onSuccess,
  onCancel,
  defaultPackage = 'small'
}) => {
  const { user, token } = useAuth();
  const [selectedPackage, setSelectedPackage] = useState<string>(defaultPackage);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [promoCode, setPromoCode] = useState<string>('');

  const handlePurchase = async () => {
    if (!user || !token) {
      console.error('TokenPurchaseForm: No user or token available', { hasUser: !!user, hasToken: !!token });
      toast.error('You must be logged in to purchase tokens');

      // Redirect to login after a short delay
      setTimeout(() => {
        window.location.href = '/login';
      }, 1500);
      return;
    }

    setIsLoading(true);
    console.log('TokenPurchaseForm: Starting purchase process for package:', selectedPackage);

    try {
      // Prepare the success and cancel URLs
      const baseUrl = window.location.origin;
      const successUrl = `${baseUrl}/dashboard?purchase=success&package=${selectedPackage}&userId=${user.id}`;
      const cancelUrl = `${baseUrl}/dashboard?purchase=cancelled`;

      console.log('TokenPurchaseForm: Prepared URLs', { successUrl, cancelUrl });

      // Create the request body
      const requestBody: any = {
        packageId: selectedPackage,
        successUrl,
        cancelUrl
      };

      // Add promo code if provided
      if (promoCode.trim()) {
        requestBody.promoCode = promoCode.trim();
        console.log('TokenPurchaseForm: Added promo code to request:', promoCode.trim());
      }

      console.log('TokenPurchaseForm: Sending purchase request with body:', requestBody);

      // Make the API call to create a checkout session
      const response = await fetch('/api/tokens/purchase', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'x-user-id': user.id
        },
        body: JSON.stringify(requestBody)
      });

      console.log('TokenPurchaseForm: Received response with status:', response.status);

      const data = await response.json();
      console.log('TokenPurchaseForm: Response data:', data);

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create checkout session');
      }

      // Show success toast before redirecting
      toast.success('Redirecting to secure checkout...');

      // Redirect to the Stripe checkout page
      if (data.url) {
        console.log('TokenPurchaseForm: Redirecting to Stripe checkout URL');

        // Short delay before redirect for better UX
        setTimeout(() => {
          window.location.href = data.url;
        }, 1000);
      } else {
        throw new Error('No checkout URL returned');
      }
    } catch (error: any) {
      console.error('TokenPurchaseForm: Error creating checkout session:', error);
      toast.error(error.message || 'Failed to create checkout session');
      setIsLoading(false);
    }
  };

  // Find the selected package details
  const selectedPackageDetails = TOKEN_PACKAGES.find(pkg => pkg.id === selectedPackage);

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="text-xl font-semibold mb-4">Purchase Additional Tokens</h2>

      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Select Token Package
        </label>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {TOKEN_PACKAGES.map((pkg) => (
            <div
              key={pkg.id}
              className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                selectedPackage === pkg.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-blue-300'
              }`}
              onClick={() => setSelectedPackage(pkg.id)}
            >
              <div className="flex justify-between items-center mb-2">
                <h3 className="font-semibold">{pkg.name}</h3>
                <span className="text-green-600 font-bold">${pkg.price}</span>
              </div>
              <p className="text-gray-600 text-sm">{pkg.description}</p>
            </div>
          ))}
        </div>
      </div>

      <div className="mb-6">
        <label htmlFor="promoCode" className="block text-sm font-medium text-gray-700 mb-2">
          Promo Code (Optional)
        </label>
        <div className="flex">
          <input
            type="text"
            id="promoCode"
            value={promoCode}
            onChange={(e) => setPromoCode(e.target.value)}
            placeholder="Enter promo code"
            className="flex-1 rounded-l-md border border-gray-300 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <p className="mt-1 text-xs text-gray-500">
          Enter a valid promo code to get a discount on your purchase
        </p>
      </div>

      <div className="border-t border-gray-200 pt-4 mt-4">
        <div className="flex justify-between items-center mb-4">
          <span className="text-gray-700">Selected Package:</span>
          <span className="font-semibold">{selectedPackageDetails?.name}</span>
        </div>
        <div className="flex justify-between items-center mb-4">
          <span className="text-gray-700">Tokens:</span>
          <span className="font-semibold">{selectedPackageDetails?.tokens.toLocaleString()}</span>
        </div>
        <div className="flex justify-between items-center mb-4">
          <span className="text-gray-700">Price:</span>
          <span className="font-semibold text-green-600">${selectedPackageDetails?.price}</span>
        </div>
        {promoCode && (
          <div className="flex justify-between items-center mb-4 text-blue-600">
            <span>Promo Code:</span>
            <span className="font-semibold">{promoCode}</span>
          </div>
        )}
      </div>

      <div className="flex justify-end space-x-4 mt-6">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          disabled={isLoading}
        >
          Cancel
        </button>
        <button
          type="button"
          onClick={handlePurchase}
          className={`px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 ${
            isLoading ? 'opacity-50 cursor-not-allowed' : ''
          }`}
          disabled={isLoading}
        >
          {isLoading ? 'Processing...' : 'Purchase Tokens'}
        </button>
      </div>
    </div>
  );
};

export default TokenPurchaseForm;
