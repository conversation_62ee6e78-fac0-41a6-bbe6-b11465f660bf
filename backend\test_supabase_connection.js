#!/usr/bin/env node
/**
 * Test Supabase connection and check database tables
 * This script uses your existing environment variables to test the connection
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

console.log('🔍 Testing Supabase Connection');
console.log('==============================');

// Check environment variables
console.log('\n1. Environment Variables:');
console.log('SUPABASE_URL:', supabaseUrl ? '✅ Found' : '❌ Missing');
console.log('SUPABASE_SERVICE_KEY:', supabaseKey ? `✅ Found (${supabaseKey.length} chars)` : '❌ Missing');

if (!supabaseUrl || !supabaseKey) {
  console.error('\n❌ Missing required environment variables!');
  process.exit(1);
}

// Create Supabase client
let supabase;
try {
  supabase = createClient(supabaseUrl, supabaseKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
  console.log('✅ Supabase client created successfully');
} catch (error) {
  console.error('❌ Error creating Supabase client:', error.message);
  process.exit(1);
}

async function testConnection() {
  console.log('\n2. Testing Database Connection:');
  
  try {
    // Test 1: Check what tables exist
    console.log('\n📋 Checking existing tables...');
    const { data: tables, error: tablesError } = await supabase
      .rpc('get_table_list');
    
    if (tablesError) {
      // Fallback: Try to query a system table
      console.log('Using fallback method to check tables...');
      const { data: systemTables, error: systemError } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public');
      
      if (systemError) {
        console.log('❌ Cannot query system tables:', systemError.message);
      } else {
        console.log('✅ Found tables via system query');
        console.log('Tables:', systemTables.map(t => t.table_name));
      }
    } else {
      console.log('✅ Tables found:', tables);
    }

    // Test 2: Check specific admin tables
    console.log('\n🔍 Testing Admin Tables:');
    
    const adminTables = ['api_keys', 'users', 'token_balance', 'token_usage', 'subscriptions'];
    
    for (const tableName of adminTables) {
      try {
        const { data, error } = await supabase
          .from(tableName)
          .select('count')
          .limit(1);
        
        if (error) {
          if (error.message.includes('relation') && error.message.includes('does not exist')) {
            console.log(`❌ Table "${tableName}" does not exist`);
          } else {
            console.log(`⚠️  Table "${tableName}" exists but error: ${error.message}`);
          }
        } else {
          console.log(`✅ Table "${tableName}" exists and is accessible`);
        }
      } catch (err) {
        console.log(`❌ Error testing table "${tableName}":`, err.message);
      }
    }

    // Test 3: Try to query api_keys table specifically (the one causing 500 error)
    console.log('\n🎯 Testing API Keys Table (source of 500 error):');
    
    try {
      const { data: apiKeys, error: apiKeysError } = await supabase
        .from('api_keys')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(5);
      
      if (apiKeysError) {
        console.log('❌ API Keys query failed:', apiKeysError.message);
        console.log('🔧 This is likely the cause of your 500 error!');
        
        if (apiKeysError.message.includes('relation "api_keys" does not exist')) {
          console.log('\n💡 SOLUTION: You need to create the api_keys table.');
          console.log('Run the SQL script I provided earlier in your Supabase dashboard.');
        }
      } else {
        console.log('✅ API Keys query successful');
        console.log(`Found ${apiKeys.length} API keys`);
        
        if (apiKeys.length > 0) {
          console.log('Sample API key structure:');
          const sample = { ...apiKeys[0] };
          if (sample.api_key) {
            sample.api_key = sample.api_key.substring(0, 8) + '...';
          }
          console.log(JSON.stringify(sample, null, 2));
        }
      }
    } catch (err) {
      console.log('❌ Unexpected error testing API keys:', err.message);
    }

    // Test 4: Test the exact query from dashboard-stats endpoint
    console.log('\n📊 Testing Dashboard Stats Queries:');
    
    try {
      // Test users count
      const { data: usersCount, error: usersError } = await supabase
        .from('users')
        .select('count')
        .limit(1);
      
      if (usersError) {
        console.log('❌ Users table query failed:', usersError.message);
      } else {
        console.log('✅ Users table accessible');
      }

      // Test token_usage count
      const { data: tokenUsage, error: tokenError } = await supabase
        .from('token_usage')
        .select('count')
        .limit(1);
      
      if (tokenError) {
        console.log('❌ Token usage table query failed:', tokenError.message);
      } else {
        console.log('✅ Token usage table accessible');
      }

    } catch (err) {
      console.log('❌ Error testing dashboard stats queries:', err.message);
    }

    console.log('\n🎉 Connection test completed!');
    console.log('\n📋 Summary:');
    console.log('- If you see ❌ for api_keys table, that\'s causing your 500 error');
    console.log('- Run the SQL script I provided to create missing tables');
    console.log('- After creating tables, your admin dashboard should work');

  } catch (error) {
    console.error('❌ Unexpected error during connection test:', error.message);
  }
}

// Run the test
testConnection()
  .then(() => {
    console.log('\n✅ Test completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  });
