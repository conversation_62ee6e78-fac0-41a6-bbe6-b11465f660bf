import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuth } from '../lib/context/auth-context';
import { getSupabaseClient } from '../lib/supabase-singleton';
import { toast } from 'sonner';

// Define the error structure that comes from the backend
interface TokenUsageStatus {
  used: number;
  limit: number;
  percentage: number;
}

interface SocketError {
  message: string;
  usageStatus?: {
    daily?: TokenUsageStatus;
    monthly?: TokenUsageStatus;
    plan?: string;
  };
  upgradeOptions?: {
    suggestUpgrade: boolean;
    currentPlan: string;
    upgradePlan: string;
    benefits: string;
  } | null;
  retryAfter?: number;
}

interface SocketContextType {
  socket: Socket | null;
  isConnected: boolean;
  sendMessage: (message: string, modelId?: string) => void;
  error: SocketError | null;
  clearError: () => void;
  hasTokenLimitError: boolean;
}

const SocketContext = createContext<SocketContextType | undefined>(undefined);

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (context === undefined) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

interface SocketProviderProps {
  children: ReactNode;
  onMessageReceived: (message: string) => void;
}

export const SocketProvider = ({ children, onMessageReceived }: SocketProviderProps) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<SocketError | null>(null);
  const { token, hasActiveSubscription } = useAuth();

  // Check if the error is related to token limits
  const hasTokenLimitError = !!error?.message && (
    error.message.includes("daily limit") ||
    error.message.includes("monthly limit") ||
    error.message.includes("token limit")
  );

  useEffect(() => {
    // Only connect if user has an active subscription
    if (!token || !hasActiveSubscription) {
      if (socket) {
        socket.disconnect();
        setSocket(null);
        setIsConnected(false);
      }
      return;
    }

    // Initialize socket connection
    const newSocket = io(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001');
    setSocket(newSocket);

    // Set up socket event listeners
    newSocket.on('connect', () => {
      console.log('Socket connected, authenticating...');

      // Get the Supabase token if available
      const getSupabaseToken = async () => {
        try {
          const supabase = getSupabaseClient();
          const { data } = await supabase.auth.getSession();
          const supabaseToken = data.session?.access_token;

          // Authenticate the socket connection with the Supabase token
          if (supabaseToken) {
            console.log('Using Supabase token for authentication');
            newSocket.emit('authenticate', { token: supabaseToken });
          } else {
            // Fall back to the token from AuthContext if Supabase token is not available
            console.log('Using fallback token for authentication');
            newSocket.emit('authenticate', { token });
          }
        } catch (error) {
          console.error('Error getting Supabase token:', error);
          // Fall back to the token from AuthContext
          newSocket.emit('authenticate', { token });
        }
      };

      getSupabaseToken();
    });

    newSocket.on('authenticated', async (data) => {
      console.log('Socket authenticated:', data);
      setIsConnected(true);

      // Check token usage limits immediately after authentication
      try {
        console.log('Checking token usage limits after authentication');
        const response = await fetch('/api/token-usage?userId=' + (data.userId || ''));
        if (response.ok) {
          const usageData = await response.json();
          console.log('Token usage after authentication:', usageData);

          // If daily limit is exceeded, set error state
          if (usageData.dailyUsage?.exceeded) {
            setError({
              message: `You've reached your daily limit of ${usageData.dailyUsage.limit.toLocaleString()} tokens. This will reset tomorrow.`,
              usageStatus: {
                daily: {
                  used: usageData.dailyUsage.used,
                  limit: usageData.dailyUsage.limit,
                  percentage: usageData.dailyUsage.percentage
                },
                monthly: {
                  used: usageData.monthlyUsage.used,
                  limit: usageData.monthlyUsage.limit,
                  percentage: usageData.monthlyUsage.percentage
                },
                plan: usageData.plan
              },
              upgradeOptions: usageData.plan === 'basic' ? {
                suggestUpgrade: true,
                currentPlan: 'basic',
                upgradePlan: 'pro',
                benefits: 'Upgrade to Pro for double the daily token limit.'
              } : null
            });

            // Show a toast notification for the error
            toast.error(`You've reached your daily limit of ${usageData.dailyUsage.limit.toLocaleString()} tokens. This will reset tomorrow.`);
          }

          // If monthly limit is exceeded, set error state
          else if (usageData.monthlyUsage?.exceeded) {
            setError({
              message: `You've reached your monthly limit of ${usageData.monthlyUsage.limit.toLocaleString()} tokens. This will reset on the 1st of next month.`,
              usageStatus: {
                daily: {
                  used: usageData.dailyUsage.used,
                  limit: usageData.dailyUsage.limit,
                  percentage: usageData.dailyUsage.percentage
                },
                monthly: {
                  used: usageData.monthlyUsage.used,
                  limit: usageData.monthlyUsage.limit,
                  percentage: usageData.monthlyUsage.percentage
                },
                plan: usageData.plan
              },
              upgradeOptions: usageData.plan === 'basic' ? {
                suggestUpgrade: true,
                currentPlan: 'basic',
                upgradePlan: 'pro',
                benefits: 'Upgrade to Pro for double the monthly token limit.'
              } : null
            });

            // Show a toast notification for the error
            toast.error(`You've reached your monthly limit of ${usageData.monthlyUsage.limit.toLocaleString()} tokens. This will reset on the 1st of next month.`);
          }
        }
      } catch (error) {
        console.error('Error checking token usage limits:', error);
      }
    });

    newSocket.on('disconnect', () => {
      console.log('Socket disconnected');
      setIsConnected(false);
    });

    newSocket.on('error', (data) => {
      console.error('Socket error:', data.message);
      // Store the error in state
      setError(data);

      // Show a toast notification for the error
      toast.error(data.message);
    });

    newSocket.on('messageResponse', (data) => {
      console.log('Received message response:', data);
      if (data.message) {
        onMessageReceived(data.message);
      }
    });

    // Listen for token balance updates
    newSocket.on('tokenBalanceUpdated', (data) => {
      console.log('Received token balance update:', data);

      // Dispatch a custom event to notify other components about the token balance update
      const event = new CustomEvent('tokenBalanceUpdated', {
        detail: {
          balance: data.balance,
          timestamp: data.timestamp
        }
      });
      window.dispatchEvent(event);

      // Show a toast notification for the token balance update
      toast.info(`Your token balance has been updated: ${data.balance.toLocaleString()} tokens available`);
    });

    // Clean up on unmount
    return () => {
      newSocket.disconnect();
    };
  }, [token, hasActiveSubscription, onMessageReceived, socket]);

  // Send message to server
  const sendMessage = (message: string, modelId: string = 'gemini-flash') => {
    // Don't send messages if there's a token limit error
    if (hasTokenLimitError) {
      console.error('Cannot send message: Token limit exceeded');

      // Show a toast notification for the error
      toast.error('Cannot send message: Token limit exceeded');
      return;
    }

    if (socket && isConnected) {
      socket.emit('sendMessage', { message, modelId });
    } else {
      console.error('Socket not connected');
    }
  };

  // Function to clear the current error
  const clearError = () => {
    setError(null);
  };

  const value = {
    socket,
    isConnected,
    sendMessage,
    error,
    clearError,
    hasTokenLimitError
  };

  return <SocketContext.Provider value={value}>{children}</SocketContext.Provider>;
};
