const tokenUsageService = require('../services/tokenUsageService');

/**
 * Middleware to check token usage limits before processing requests
 */
const checkUsageLimits = async (req, res, next) => {
  try {
    // Skip if no user is authenticated
    if (!req.user || !req.user.id) {
      return next();
    }

    // Check if user has exceeded their usage limits
    const usageStatus = await tokenUsageService.checkUsageLimits(req.user.id);

    // If user has exceeded limits, check for purchased tokens
    if (usageStatus.hasExceededLimits) {
      // Check if user has purchased tokens
      const tokenPurchaseService = require('../services/tokenPurchaseService');
      const tokenBalance = await tokenPurchaseService.getUserTokenBalance(req.user.id);

      // If user has purchased tokens, allow the request
      if (tokenBalance && tokenBalance.balance > 0) {
        console.log(`User ${req.user.id} has exceeded subscription limits but has ${tokenBalance.balance} purchased tokens available`);

        // Store token balance in request for later use
        req.tokenBalance = tokenBalance;
        req.useTokenBalance = true;

        // Continue processing
        next();
        return;
      }

      // Determine which limit was exceeded
      if (usageStatus.dailyUsage.exceeded) {
        return res.status(429).json({
          error: 'Daily token limit exceeded',
          message: `You've reached your daily limit of ${usageStatus.dailyUsage.limit.toLocaleString()} tokens. This will reset tomorrow.`,
          usageStatus,
          upgradeOptions: usageStatus.plan === 'basic' ? {
            suggestUpgrade: true,
            currentPlan: 'basic',
            upgradePlan: 'pro',
            benefits: 'Upgrade to Pro for double the daily token limit.'
          } : null,
          purchaseOptions: {
            canPurchaseTokens: true,
            packages: tokenPurchaseService.getTokenPackages()
          }
        });
      }

      if (usageStatus.monthlyUsage.exceeded) {
        return res.status(429).json({
          error: 'Monthly token limit exceeded',
          message: `You've reached your monthly limit of ${usageStatus.monthlyUsage.limit.toLocaleString()} tokens. This will reset on the 1st of next month.`,
          usageStatus,
          upgradeOptions: usageStatus.plan === 'basic' ? {
            suggestUpgrade: true,
            currentPlan: 'basic',
            upgradePlan: 'pro',
            benefits: 'Upgrade to Pro for double the monthly token limit.'
          } : null,
          purchaseOptions: {
            canPurchaseTokens: true,
            packages: tokenPurchaseService.getTokenPackages()
          }
        });
      }
    }

    // Add warning headers if approaching limits
    if (usageStatus.monthlyUsage.percentage > 80) {
      res.set('X-Usage-Warning', 'true');
      res.set('X-Usage-Percentage', Math.round(usageStatus.monthlyUsage.percentage).toString());
      res.set('X-Usage-Limit', usageStatus.monthlyUsage.limit.toString());
      res.set('X-Usage-Used', usageStatus.monthlyUsage.used.toString());
    }

    // Store usage status in request for later use
    req.usageStatus = usageStatus;

    // Continue to next middleware
    next();
  } catch (error) {
    console.error('Error checking usage limits:', error);
    // Continue to next middleware even if check fails
    next();
  }
};

/**
 * Middleware to apply progressive throttling based on usage
 */
const applyProgressiveThrottling = async (req, res, next) => {
  try {
    // Skip if no user is authenticated
    if (!req.user || !req.user.id) {
      return next();
    }

    // Skip if we already have usage status from previous middleware
    if (!req.usageStatus) {
      req.usageStatus = await tokenUsageService.checkUsageLimits(req.user.id);
    }

    const { monthlyUsage } = req.usageStatus;

    // Apply progressive throttling based on monthly usage percentage
    if (monthlyUsage.percentage > 90) {
      // Over 90% of limit - apply strong throttling
      req.throttleDelay = 5000; // 5 second delay
      req.throttleMessage = "You're approaching your monthly limit. Responses may be delayed.";

      // Add upgrade suggestion for basic plan users
      if (req.usageStatus.plan === 'basic') {
        req.upgradeMessage = "Consider upgrading to Pro for double the token limit.";
      }
    } else if (monthlyUsage.percentage > 75) {
      // Over 75% of limit - apply medium throttling
      req.throttleDelay = 2000; // 2 second delay
      req.throttleMessage = "You're using your subscription at a high rate. Slight delays may occur.";

      // Add usage warning
      req.usageWarning = `You've used ${Math.round(monthlyUsage.percentage)}% of your monthly token limit.`;

      // Add upgrade suggestion for basic plan users
      if (req.usageStatus.plan === 'basic') {
        req.upgradeMessage = "Upgrade to Pro for higher limits and faster responses.";
      }
    } else if (monthlyUsage.percentage > 50) {
      // Over 50% of limit - apply light throttling
      req.throttleDelay = 1000; // 1 second delay

      // Add usage info
      req.usageInfo = `You've used ${Math.round(monthlyUsage.percentage)}% of your monthly token limit.`;
    }

    // If throttling is applied, add a delay
    if (req.throttleDelay) {
      // Add headers to inform the client about throttling
      res.set('X-Throttle-Delay', req.throttleDelay);
      if (req.throttleMessage) {
        res.set('X-Throttle-Message', req.throttleMessage);
      }

      // Add usage information headers
      if (req.usageWarning) {
        res.set('X-Usage-Warning', req.usageWarning);
      }

      if (req.usageInfo) {
        res.set('X-Usage-Info', req.usageInfo);
      }

      if (req.upgradeMessage) {
        res.set('X-Upgrade-Message', req.upgradeMessage);
      }

      // Add detailed usage information
      res.set('X-Usage-Percentage', Math.round(monthlyUsage.percentage).toString());
      res.set('X-Usage-Limit', monthlyUsage.limit.toString());
      res.set('X-Usage-Used', monthlyUsage.used.toString());
      res.set('X-Usage-Plan', req.usageStatus.plan);

      // Apply the delay
      await new Promise(resolve => setTimeout(resolve, req.throttleDelay));
    }

    // Continue to next middleware
    next();
  } catch (error) {
    console.error('Error applying throttling:', error);
    // Continue to next middleware even if throttling fails
    next();
  }
};

/**
 * Middleware to optimize prompts before sending to AI service
 */
const optimizePrompts = (req, res, next) => {
  try {
    // Skip if no message in request
    if (!req.body || !req.body.message) {
      return next();
    }

    // Get max request length from usage status or use default
    const maxLength = req.usageStatus?.maxRequestLength || 4000;

    // Optimize the prompt
    req.body.originalMessage = req.body.message; // Save original for reference
    req.body.message = tokenUsageService.optimizePrompt(req.body.message, maxLength);

    // Continue to next middleware
    next();
  } catch (error) {
    console.error('Error optimizing prompt:', error);
    // Continue to next middleware with original prompt
    next();
  }
};

module.exports = {
  checkUsageLimits,
  applyProgressiveThrottling,
  optimizePrompts
};
