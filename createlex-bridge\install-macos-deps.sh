#!/bin/bash

echo "🍎 Installing macOS MCP Dependencies"
echo "===================================="

# Upgrade pip first
echo "📦 Upgrading pip..."
python3 -m pip install --upgrade pip --user

# Install required modules one by one
echo "📦 Installing FastMCP and dependencies..."

modules=(
    "fastmcp"
    "aiohttp"
    "requests"
    "websockets"
    "uvicorn"
    "flask"
    "pydantic"
    "python-dotenv"
)

for module in "${modules[@]}"; do
    echo "Installing $module..."
    python3 -m pip install "$module" --user
done

echo ""
echo "✅ Installation complete!"
echo ""
echo "🧪 Testing installation..."
npm run test-macos-deps 