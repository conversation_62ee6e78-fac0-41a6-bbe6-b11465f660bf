const nodemailer = require('nodemailer');

class EmailService {
  constructor() {
    // Initialize the transporter with environment variables or default settings
    this.transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.EMAIL_PORT || '587', 10),
      secure: process.env.EMAIL_SECURE === 'true',
      auth: {
        user: process.env.EMAIL_USER || '',
        pass: process.env.EMAIL_PASSWORD || '',
      },
    });

    // Log email configuration status
    console.log('Email service initialized with host:', process.env.EMAIL_HOST || 'smtp.gmail.com');

    // Default sender email address
    this.defaultFrom = process.env.EMAIL_FROM || '<EMAIL>';

    // Default recipient for system notifications
    this.systemNotificationEmail = process.env.SYSTEM_NOTIFICATION_EMAIL || '<EMAIL>';
  }

  /**
   * Send an email
   * @param {Object} options - Email options
   * @param {string} options.to - Recipient email
   * @param {string} options.subject - Email subject
   * @param {string} options.text - Plain text content
   * @param {string} options.html - HTML content (optional)
   * @param {string} options.from - Sender email (optional, uses default if not provided)
   * @returns {Promise<Object>} - Sending result
   */
  async sendEmail(options) {
    try {
      const { to, subject, text, html, from = this.defaultFrom } = options;

      if (!to || !subject || !text) {
        throw new Error('Missing required email parameters (to, subject, text)');
      }

      // Log email configuration for debugging
      console.log('[EmailService] Email configuration:', {
        host: process.env.EMAIL_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.EMAIL_PORT || '587', 10),
        secure: process.env.EMAIL_SECURE === 'true',
        auth: {
          user: process.env.EMAIL_USER ? `${process.env.EMAIL_USER.substring(0, 3)}...` : 'not set',
          pass: process.env.EMAIL_PASSWORD ? 'password set' : 'not set'
        }
      });

      const mailOptions = {
        from,
        to,
        subject,
        text,
        html: html || text,
      };

      console.log(`[EmailService] Attempting to send email to ${to} with subject: ${subject}`);
      console.log(`[EmailService] Email content (first 100 chars): ${text.substring(0, 100)}...`);

      const info = await this.transporter.sendMail(mailOptions);
      console.log(`[EmailService] Email sent successfully: ${info.messageId}`);

      return {
        success: true,
        messageId: info.messageId,
      };
    } catch (error) {
      console.error('[EmailService] Error sending email:', error);

      // Log more detailed error information
      if (error.code === 'EAUTH') {
        console.error('[EmailService] Authentication error. Check your email credentials.');
      } else if (error.code === 'ESOCKET') {
        console.error('[EmailService] Socket error. Check your email host and port settings.');
      } else if (error.code === 'ECONNECTION') {
        console.error('[EmailService] Connection error. Check your network and email server settings.');
      }

      return {
        success: false,
        error: error.message,
        code: error.code
      };
    }
  }

  /**
   * Send a subscription cancellation notification
   * @param {Object} data - Cancellation data
   * @param {Object} data.user - User information
   * @param {string} data.subscriptionId - Stripe subscription ID
   * @param {Object} data.stripeResult - Result from Stripe cancellation
   * @returns {Promise<Object>} - Sending result
   */
  async sendSubscriptionCancellationEmail(data) {
    const { user, subscriptionId, stripeResult } = data;

    if (!user || !subscriptionId) {
      console.error('Missing required data for cancellation email');
      return { success: false, error: 'Missing required data' };
    }

    const subject = 'Createlex AI Cancelation';

    // Format cancellation date
    const cancellationDate = new Date().toISOString().split('T')[0];
    const effectiveEndDate = stripeResult?.currentPeriodEnd
      ? new Date(stripeResult.currentPeriodEnd).toISOString().split('T')[0]
      : 'Immediate';

    // Create email content
    const text = `
Subscription Cancellation Notification

User Information:
- User ID: ${user.id}
- Name: ${user.name || 'N/A'}
- Email: ${user.email || 'N/A'}

Subscription Details:
- Subscription ID: ${subscriptionId}
- Cancellation Date: ${cancellationDate}
- Effective End Date: ${effectiveEndDate}
- Cancellation Type: ${stripeResult?.cancelAtPeriodEnd ? 'At Period End' : 'Immediate'}

Stripe Status:
- Current Status: ${stripeResult?.status || 'N/A'}
`;

    // Create HTML version
    const html = `
<h2>Subscription Cancellation Notification</h2>

<h3>User Information:</h3>
<ul>
  <li><strong>User ID:</strong> ${user.id}</li>
  <li><strong>Name:</strong> ${user.name || 'N/A'}</li>
  <li><strong>Email:</strong> ${user.email || 'N/A'}</li>
</ul>

<h3>Subscription Details:</h3>
<ul>
  <li><strong>Subscription ID:</strong> ${subscriptionId}</li>
  <li><strong>Cancellation Date:</strong> ${cancellationDate}</li>
  <li><strong>Effective End Date:</strong> ${effectiveEndDate}</li>
  <li><strong>Cancellation Type:</strong> ${stripeResult?.cancelAtPeriodEnd ? 'At Period End' : 'Immediate'}</li>
</ul>

<h3>Stripe Status:</h3>
<ul>
  <li><strong>Current Status:</strong> ${stripeResult?.status || 'N/A'}</li>
</ul>
`;

    return this.sendEmail({
      to: this.systemNotificationEmail,
      subject,
      text,
      html,
    });
  }
}

module.exports = new EmailService();
