import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, hasSubscription, accessToken } = body;

    console.log('Frontend notifying MCP server of authentication:', { 
      userId, 
      hasSubscription,
      hasToken: !!accessToken 
    });

    // Notify the server-side MCP about successful authentication
    try {
      const mcpResponse = await fetch('http://localhost:8080/auth/success', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: userId,
          subscription_status: hasSubscription,
          access_token: accessToken,
          timestamp: Date.now(),
          source: 'frontend_session'
        }),
      });

      if (mcpResponse.ok) {
        const result = await mcpResponse.json();
        console.log('✅ MCP Server notified successfully:', result);
        
        return NextResponse.json({
          success: true,
          message: 'MCP server notified successfully',
          mcpResponse: result
        });
      } else {
        console.warn('⚠️ MCP Server notification failed:', mcpResponse.status);
        return NextResponse.json({
          success: false,
          message: 'MCP server notification failed',
          status: mcpResponse.status
        }, { status: 200 }); // Don't fail the frontend auth
      }
    } catch (mcpError) {
      console.warn('⚠️ Failed to notify MCP server:', mcpError);
      return NextResponse.json({
        success: false,
        message: 'MCP server unreachable',
        error: mcpError.message
      }, { status: 200 }); // Don't fail the frontend auth
    }

  } catch (error) {
    console.error('Error in MCP auth notification:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error',
      error: error.message
    }, { status: 500 });
  }
} 