global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'unrealgenai-mcp-server'
    static_configs:
      - targets: ['unrealgenai-mcp-server:8001']
    metrics_path: '/health'
    scrape_interval: 30s
    scrape_timeout: 10s

  - job_name: 'unrealgenai-backend'
    static_configs:
      - targets: ['unrealgenai-backend:3001']
    metrics_path: '/health'
    scrape_interval: 30s
    scrape_timeout: 10s

  - job_name: 'unrealgenai-frontend'
    static_configs:
      - targets: ['unrealgenai-frontend:3000']
    scrape_interval: 30s
    scrape_timeout: 10s

  - job_name: 'token-extractor'
    static_configs:
      - targets: ['unrealgenai-token-extractor:8080']
    metrics_path: '/health'
    scrape_interval: 60s
    scrape_timeout: 10s 