#include "AssetRegistryBlueprintAnalyzer.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Engine/Blueprint.h"
#include "Engine/BlueprintGeneratedClass.h"

// Try to include BlueprintGraph modules, fall back gracefully if not available
#if WITH_EDITOR
    #include "EdGraph/EdGraph.h"
    #include "EdGraph/EdGraphNode.h"
    #include "EdGraph/EdGraphPin.h"
    #include "EdGraphSchema_K2.h"
    
    // Advanced Blueprint node types - may not be available in all configurations
    #if UE_BUILD_DEVELOPMENT || UE_BUILD_DEBUG
        #include "K2Node.h"
        #include "K2Node_Event.h"
        #include "K2Node_CallFunction.h"
        #include "K2Node_VariableGet.h"
        #include "K2Node_VariableSet.h"
        #define ADVANCED_BLUEPRINT_ANALYSIS 1
    #else
        #define ADVANCED_BLUEPRINT_ANALYSIS 0
    #endif
#endif

#include "Dom/JsonObject.h"
#include "Engine/World.h"
#include "Engine/LevelScriptActor.h"
#include "Engine/Engine.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "HAL/PlatformFilemanager.h"

FString UAssetRegistryBlueprintAnalyzer::GetCurrentLevelScriptBlueprint()
{
    TSharedPtr<FJsonObject> ResponseJson = MakeShareable(new FJsonObject);
    ResponseJson->SetBoolField("success", false);

    // Get current world
    UWorld* World = nullptr;
    if (GEngine && GEngine->GetWorldContexts().Num() > 0)
    {
        World = GEngine->GetWorldContexts()[0].World();
    }

    if (!World)
    {
        ResponseJson->SetStringField("error", "No active world found");
        return JsonObjectToString(ResponseJson);
    }

    // Get Level Script Actor
    ALevelScriptActor* LevelScriptActor = World->GetLevelScriptActor();
    if (!LevelScriptActor)
    {
        ResponseJson->SetStringField("error", "No Level Script Actor found");
        return JsonObjectToString(ResponseJson);
    }

    // Get the Blueprint class
    UBlueprintGeneratedClass* BlueprintClass = Cast<UBlueprintGeneratedClass>(LevelScriptActor->GetClass());
    if (!BlueprintClass)
    {
        ResponseJson->SetStringField("error", "No Blueprint class found for Level Script");
        return JsonObjectToString(ResponseJson);
    }

    // Find the associated Blueprint
    UBlueprint* LevelBlueprint = Cast<UBlueprint>(BlueprintClass->ClassGeneratedBy);
    if (!LevelBlueprint)
    {
        ResponseJson->SetStringField("error", "Could not find Level Script Blueprint");
        return JsonObjectToString(ResponseJson);
    }

    ResponseJson->SetBoolField("success", true);
    ResponseJson->SetStringField("levelName", World->GetName());
    ResponseJson->SetStringField("blueprintName", LevelBlueprint->GetName());
    ResponseJson->SetStringField("blueprintPath", LevelBlueprint->GetPathName());

    // Analyze the Blueprint in detail
    AnalyzeBlueprintDetails(LevelBlueprint, ResponseJson);

    return JsonObjectToString(ResponseJson);
}

FString UAssetRegistryBlueprintAnalyzer::GetBlueprintAssetInfo(const FString& BlueprintPath)
{
    TSharedPtr<FJsonObject> ResponseJson = MakeShareable(new FJsonObject);
    ResponseJson->SetBoolField("success", false);

    // Use Asset Registry to find the Blueprint
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();

    FAssetData AssetData = AssetRegistry.GetAssetByObjectPath(FSoftObjectPath(BlueprintPath));
    
    if (!AssetData.IsValid())
    {
        ResponseJson->SetStringField("error", "Blueprint not found in Asset Registry");
        return JsonObjectToString(ResponseJson);
    }

    ResponseJson->SetBoolField("success", true);
    ResponseJson->SetStringField("assetPath", AssetData.GetSoftObjectPath().ToString());
    ResponseJson->SetStringField("assetName", AssetData.AssetName.ToString());
    ResponseJson->SetStringField("assetClass", AssetData.AssetClassPath.ToString());

    // Get asset metadata from registry (lightweight)
    TSharedPtr<FJsonObject> MetadataJson = MakeShareable(new FJsonObject);
    for (const auto& TagPair : AssetData.TagsAndValues)
    {
        FString TagValue = TagPair.Value.AsString();
        MetadataJson->SetStringField(TagPair.Key.ToString(), TagValue);
    }
    ResponseJson->SetObjectField("registryMetadata", MetadataJson);

    // Load the actual Blueprint for detailed analysis
    if (UBlueprint* Blueprint = Cast<UBlueprint>(AssetData.GetAsset()))
    {
        AnalyzeBlueprintDetails(Blueprint, ResponseJson);
    }

    return JsonObjectToString(ResponseJson);
}

FString UAssetRegistryBlueprintAnalyzer::GetAllLevelScriptBlueprints()
{
    TSharedPtr<FJsonObject> ResponseJson = MakeShareable(new FJsonObject);
    TArray<TSharedPtr<FJsonValue>> BlueprintsArray;

    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();

    // Query for all Blueprints
    FARFilter Filter;
    Filter.ClassPaths.Add(UBlueprint::StaticClass()->GetClassPathName());
    Filter.bRecursiveClasses = true;

    TArray<FAssetData> AssetDataArray;
    AssetRegistry.GetAssets(Filter, AssetDataArray);

    for (const FAssetData& AssetData : AssetDataArray)
    {
        // Check if this is a Level Script Blueprint
        FString GeneratedClass;
        if (AssetData.GetTagValue("GeneratedClass", GeneratedClass) && 
            GeneratedClass.Contains("LevelScriptBlueprint"))
        {
            TSharedPtr<FJsonObject> BlueprintJson = MakeShareable(new FJsonObject);
            BlueprintJson->SetStringField("name", AssetData.AssetName.ToString());
            BlueprintJson->SetStringField("path", AssetData.GetSoftObjectPath().ToString());
            BlueprintJson->SetStringField("packageName", AssetData.PackageName.ToString());
            
            // Get level-specific metadata
            FString ParentClass;
            AssetData.GetTagValue("ParentClass", ParentClass);
            BlueprintJson->SetStringField("parentClass", ParentClass);
            
            BlueprintsArray.Add(MakeShareable(new FJsonValueObject(BlueprintJson)));
        }
    }

    ResponseJson->SetArrayField("levelScriptBlueprints", BlueprintsArray);
    ResponseJson->SetBoolField("success", true);
    ResponseJson->SetNumberField("count", BlueprintsArray.Num());

    return JsonObjectToString(ResponseJson);
}

FString UAssetRegistryBlueprintAnalyzer::GetBlueprintEventGraph(const FString& BlueprintPath)
{
    TSharedPtr<FJsonObject> ResponseJson = MakeShareable(new FJsonObject);
    ResponseJson->SetBoolField("success", false);

    // Get Blueprint from Asset Registry
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();
    
    FAssetData AssetData = AssetRegistry.GetAssetByObjectPath(FSoftObjectPath(BlueprintPath));
    UBlueprint* Blueprint = Cast<UBlueprint>(AssetData.GetAsset());
    
    if (!Blueprint)
    {
        ResponseJson->SetStringField("error", "Failed to load Blueprint");
        return JsonObjectToString(ResponseJson);
    }

    ResponseJson->SetBoolField("success", true);
    ResponseJson->SetStringField("blueprintName", Blueprint->GetName());
    
    // Find and analyze the Event Graph
    TArray<TSharedPtr<FJsonValue>> NodesArray;
    int32 TotalNodes = 0;
    
    for (UEdGraph* Graph : Blueprint->UbergraphPages)
    {
        if (Graph)
        {
            TotalNodes += Graph->Nodes.Num();
            
            // Analyze main event graph
            if (Graph->GetFName() == "EventGraph" || Blueprint->UbergraphPages.Num() == 1)
            {
                AnalyzeGraphNodes(Graph, NodesArray);
            }
        }
    }

    ResponseJson->SetArrayField("nodes", NodesArray);
    ResponseJson->SetNumberField("eventGraphNodes", NodesArray.Num());
    ResponseJson->SetNumberField("totalNodes", TotalNodes);
    ResponseJson->SetNumberField("totalGraphs", Blueprint->UbergraphPages.Num());

    return JsonObjectToString(ResponseJson);
}

void UAssetRegistryBlueprintAnalyzer::AnalyzeBlueprintDetails(UBlueprint* Blueprint, TSharedPtr<FJsonObject> ResponseJson)
{
    if (!Blueprint) return;

    // Blueprint basic info
    TSharedPtr<FJsonObject> BlueprintInfo = MakeShareable(new FJsonObject);
    BlueprintInfo->SetStringField("name", Blueprint->GetName());
    BlueprintInfo->SetStringField("class", Blueprint->GetClass()->GetName());
    BlueprintInfo->SetStringField("parentClass", Blueprint->ParentClass ? Blueprint->ParentClass->GetName() : "None");
    BlueprintInfo->SetStringField("blueprintType", Blueprint->BlueprintType == BPTYPE_Normal ? "Normal" : 
                                                  Blueprint->BlueprintType == BPTYPE_LevelScript ? "LevelScript" :
                                                  Blueprint->BlueprintType == BPTYPE_MacroLibrary ? "MacroLibrary" : "Other");
    
    ResponseJson->SetObjectField("blueprintInfo", BlueprintInfo);

    // Variables analysis
    TArray<TSharedPtr<FJsonValue>> VariablesArray;
    for (const FBPVariableDescription& Variable : Blueprint->NewVariables)
    {
        TSharedPtr<FJsonObject> VarJson = MakeShareable(new FJsonObject);
        VarJson->SetStringField("name", Variable.VarName.ToString());
        VarJson->SetStringField("type", Variable.VarType.PinCategory.ToString());
        VarJson->SetStringField("subType", Variable.VarType.PinSubCategory.ToString());
        VarJson->SetBoolField("isArray", Variable.VarType.ContainerType == EPinContainerType::Array);
        VarJson->SetBoolField("isMap", Variable.VarType.ContainerType == EPinContainerType::Map);
        VarJson->SetBoolField("isSet", Variable.VarType.ContainerType == EPinContainerType::Set);
        
        VariablesArray.Add(MakeShareable(new FJsonValueObject(VarJson)));
    }
    ResponseJson->SetArrayField("variables", VariablesArray);

    // Functions analysis
    TArray<TSharedPtr<FJsonValue>> FunctionsArray;
    for (UEdGraph* FunctionGraph : Blueprint->FunctionGraphs)
    {
        if (FunctionGraph)
        {
            TSharedPtr<FJsonObject> FuncJson = MakeShareable(new FJsonObject);
            FuncJson->SetStringField("name", FunctionGraph->GetName());
            FuncJson->SetNumberField("nodeCount", FunctionGraph->Nodes.Num());
            
            FunctionsArray.Add(MakeShareable(new FJsonValueObject(FuncJson)));
        }
    }
    ResponseJson->SetArrayField("functions", FunctionsArray);

    // Graphs summary
    TArray<TSharedPtr<FJsonValue>> GraphsArray;
    for (UEdGraph* Graph : Blueprint->UbergraphPages)
    {
        if (Graph)
        {
            TSharedPtr<FJsonObject> GraphJson = MakeShareable(new FJsonObject);
            GraphJson->SetStringField("name", Graph->GetName());
            GraphJson->SetStringField("type", "EventGraph");
            GraphJson->SetNumberField("nodeCount", Graph->Nodes.Num());
            
            GraphsArray.Add(MakeShareable(new FJsonValueObject(GraphJson)));
        }
    }
    ResponseJson->SetArrayField("eventGraphs", GraphsArray);

    // Summary statistics
    TSharedPtr<FJsonObject> StatsJson = MakeShareable(new FJsonObject);
    StatsJson->SetNumberField("totalVariables", Blueprint->NewVariables.Num());
    StatsJson->SetNumberField("totalFunctions", Blueprint->FunctionGraphs.Num());
    StatsJson->SetNumberField("totalEventGraphs", Blueprint->UbergraphPages.Num());
    
    int32 TotalNodes = 0;
    for (UEdGraph* Graph : Blueprint->UbergraphPages)
    {
        if (Graph) TotalNodes += Graph->Nodes.Num();
    }
    StatsJson->SetNumberField("totalNodes", TotalNodes);
    
    ResponseJson->SetObjectField("statistics", StatsJson);
}

void UAssetRegistryBlueprintAnalyzer::AnalyzeGraphNodes(UEdGraph* Graph, TArray<TSharedPtr<FJsonValue>>& NodesArray)
{
#if WITH_EDITOR
    if (!Graph) return;

    for (UEdGraphNode* Node : Graph->Nodes)
    {
        if (!Node) continue;

        TSharedPtr<FJsonObject> NodeJson = MakeShareable(new FJsonObject);
        NodeJson->SetStringField("name", Node->GetName());
        NodeJson->SetStringField("class", Node->GetClass()->GetName());
        NodeJson->SetStringField("title", Node->GetNodeTitle(ENodeTitleType::FullTitle).ToString());

        // Node position
        NodeJson->SetNumberField("posX", Node->NodePosX);
        NodeJson->SetNumberField("posY", Node->NodePosY);

        // Node guid for unique identification
        NodeJson->SetStringField("guid", Node->NodeGuid.ToString());

        // Determine node type and extract specific information
        FString NodeType = "unknown";
        
#if ADVANCED_BLUEPRINT_ANALYSIS
        if (UK2Node_Event* EventNode = Cast<UK2Node_Event>(Node))
        {
            NodeType = "event";
            NodeJson->SetStringField("eventName", EventNode->EventReference.GetMemberName().ToString());
            NodeJson->SetStringField("eventClass", EventNode->EventReference.GetMemberParentClass() ? 
                                     EventNode->EventReference.GetMemberParentClass()->GetName() : "Unknown");
        }
        else if (UK2Node_CallFunction* FuncNode = Cast<UK2Node_CallFunction>(Node))
        {
            NodeType = "function_call";
            NodeJson->SetStringField("functionName", FuncNode->FunctionReference.GetMemberName().ToString());
            if (FuncNode->GetTargetFunction())
            {
                NodeJson->SetStringField("functionClass", FuncNode->GetTargetFunction()->GetOuterUClass()->GetName());
            }
        }
        else if (Cast<UK2Node_VariableGet>(Node))
        {
            NodeType = "variable_get";
            if (UK2Node_VariableGet* VarNode = Cast<UK2Node_VariableGet>(Node))
            {
                NodeJson->SetStringField("variableName", VarNode->VariableReference.GetMemberName().ToString());
            }
        }
        else if (Cast<UK2Node_VariableSet>(Node))
        {
            NodeType = "variable_set";
            if (UK2Node_VariableSet* VarNode = Cast<UK2Node_VariableSet>(Node))
            {
                NodeJson->SetStringField("variableName", VarNode->VariableReference.GetMemberName().ToString());
            }
        }
        else
#endif
        {
            // Fallback: Basic node type detection based on class name
            FString NodeClassName = Node->GetClass()->GetName();
            if (NodeClassName.Contains("Event"))
            {
                NodeType = "event";
            }
            else if (NodeClassName.Contains("CallFunction") || NodeClassName.Contains("Function"))
            {
                NodeType = "function_call";
            }
            else if (NodeClassName.Contains("Variable"))
            {
                if (NodeClassName.Contains("Get"))
                {
                    NodeType = "variable_get";
                }
                else if (NodeClassName.Contains("Set"))
                {
                    NodeType = "variable_set";
                }
                else
                {
                    NodeType = "variable";
                }
            }
            else if (NodeClassName.Contains("Comment"))
            {
                NodeType = "comment";
            }
        }

        NodeJson->SetStringField("type", NodeType);

        // Pins information
        TArray<TSharedPtr<FJsonValue>> PinsArray;
        for (UEdGraphPin* Pin : Node->Pins)
        {
            if (Pin)
            {
                TSharedPtr<FJsonObject> PinJson = MakeShareable(new FJsonObject);
                PinJson->SetStringField("name", Pin->PinName.ToString());
                PinJson->SetStringField("type", Pin->PinType.PinCategory.ToString());
                PinJson->SetStringField("subType", Pin->PinType.PinSubCategory.ToString());
                PinJson->SetStringField("direction", Pin->Direction == EGPD_Input ? "input" : "output");
                PinJson->SetNumberField("connections", Pin->LinkedTo.Num());
                PinJson->SetBoolField("isExecution", Pin->PinType.PinCategory == UEdGraphSchema_K2::PC_Exec);
                
                if (!Pin->DefaultValue.IsEmpty())
                {
                    PinJson->SetStringField("defaultValue", Pin->DefaultValue);
                }
                
                PinsArray.Add(MakeShareable(new FJsonValueObject(PinJson)));
            }
        }
        NodeJson->SetArrayField("pins", PinsArray);
        NodeJson->SetNumberField("pinCount", PinsArray.Num());

        NodesArray.Add(MakeShareable(new FJsonValueObject(NodeJson)));
    }
#else
    // Fallback for non-editor builds
    TSharedPtr<FJsonObject> PlaceholderJson = MakeShareable(new FJsonObject);
    PlaceholderJson->SetStringField("note", "Graph analysis requires editor build");
    PlaceholderJson->SetStringField("status", "not_available");
    NodesArray.Add(MakeShareable(new FJsonValueObject(PlaceholderJson)));
#endif
}

FString UAssetRegistryBlueprintAnalyzer::JsonObjectToString(TSharedPtr<FJsonObject> JsonObject)
{
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);
    return OutputString;
} 