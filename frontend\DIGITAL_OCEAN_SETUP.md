# Digital Ocean Deployment Guide for CreateLex AI Frontend

This guide will walk you through deploying the CreateLex AI frontend application to a Digital Ocean Droplet without using Docker.

## Prerequisites

- A Digital Ocean account
- A domain name pointing to your Digital Ocean Droplet's IP address
- Basic knowledge of Linux commands

## Step 1: Create a Digital Ocean Droplet

1. Log in to your Digital Ocean account
2. Click on "Create" and select "Droplets"
3. Choose an image: Ubuntu 22.04 LTS
4. Choose a plan: Basic (Recommended: at least 2GB RAM / 1 CPU)
5. Choose a datacenter region close to your target audience
6. Add your SSH key or create a password
7. Click "Create Droplet"

## Step 2: Connect to Your Droplet

```bash
ssh root@your-droplet-ip
```

## Step 3: Update the System

```bash
apt update && apt upgrade -y
```

## Step 4: Install Required Software

```bash
# Install Node.js and npm
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
apt install -y nodejs

# Install Nginx
apt install -y nginx

# Install Certbot for SSL
apt install -y certbot python3-certbot-nginx

# Install PM2 globally
npm install -g pm2
```

## Step 5: Set Up Nginx

1. Copy the provided nginx.conf file to your server:

```bash
# Replace 'createlex.com' with your domain name in the file
nano /etc/nginx/sites-available/createlex
```

2. Create a symbolic link to enable the site:

```bash
ln -s /etc/nginx/sites-available/createlex /etc/nginx/sites-enabled/
```

3. Test the Nginx configuration:

```bash
nginx -t
```

4. Restart Nginx:

```bash
systemctl restart nginx
```

## Step 6: Set Up SSL with Let's Encrypt

```bash
certbot --nginx -d createlex.com -d www.createlex.com
```

Follow the prompts to complete the SSL setup.

## Step 7: Create a User for the Application

```bash
# Create a new user
adduser createlex

# Add user to sudo group
usermod -aG sudo createlex

# Switch to the new user
su - createlex
```

## Step 8: Clone the Repository

```bash
# Create application directory
mkdir -p ~/app

# Navigate to the directory
cd ~/app

# Clone the repository
git clone https://github.com/AlexKissiJr/AiWebplatform.git .

# Checkout the digital branch
git checkout digital
```

## Step 9: Set Up Environment Variables

Create a `.env.production` file in the frontend directory:

```bash
cd frontend
nano .env.production
```

Add the following environment variables (replace with your actual values):

```
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-supabase-url.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# API Configuration
NEXT_PUBLIC_API_URL=https://api.createlex.com

# Authentication Bypass (set to false in production)
NEXT_PUBLIC_BYPASS_AUTH=false
NEXT_PUBLIC_BYPASS_SUBSCRIPTION=false

# Other Configuration
NEXT_PUBLIC_SITE_URL=https://createlex.com
```

## Step 10: Deploy the Application

Make the deploy script executable and run it:

```bash
chmod +x deploy.sh
./deploy.sh
```

## Step 11: Set Up PM2 to Start on Boot

```bash
# Generate the startup script
pm2 startup

# Save the current PM2 process list
pm2 save
```

## Step 12: Configure Firewall

```bash
# Allow SSH, HTTP, and HTTPS
ufw allow ssh
ufw allow http
ufw allow https

# Enable the firewall
ufw enable
```

## Step 13: Test the Application

Visit your domain in a web browser to ensure the application is working correctly.

## Troubleshooting

### Check Application Logs

```bash
# View PM2 logs
pm2 logs createlex-frontend

# View Nginx logs
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

### Restart Services

```bash
# Restart the application
pm2 restart createlex-frontend

# Restart Nginx
sudo systemctl restart nginx
```

### Check Application Status

```bash
# Check PM2 status
pm2 status

# Check Nginx status
systemctl status nginx
```

## Maintenance

### Updating the Application

```bash
# Pull the latest changes
git pull

# Run the deploy script again
./deploy.sh
```

### Monitoring

```bash
# Monitor the application
pm2 monit
```

## Additional Resources

- [PM2 Documentation](https://pm2.keymetrics.io/docs/usage/quick-start/)
- [Nginx Documentation](https://nginx.org/en/docs/)
- [Let's Encrypt Documentation](https://letsencrypt.org/docs/)
- [Digital Ocean Documentation](https://docs.digitalocean.com/)
