'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';

export default function ResetUserIdPage() {
  const [userId, setUserId] = useState<string | null>(null);
  const [userIdSource, setUserIdSource] = useState<string | null>(null);
  const [resetComplete, setResetComplete] = useState(false);

  useEffect(() => {
    // Get the current user ID from localStorage
    const currentUserId = localStorage.getItem('ai-chat-user-id');
    const currentUserIdSource = localStorage.getItem('ai-chat-user-id-source');

    setUserId(currentUserId);
    setUserIdSource(currentUserIdSource);
  }, []);

  const resetUserId = () => {
    // Clear the user ID from localStorage
    localStorage.removeItem('ai-chat-user-id');
    localStorage.removeItem('ai-chat-user-id-source');
    
    // Also clear any Supabase session
    localStorage.removeItem('supabase.auth.token');
    
    setUserId(null);
    setUserIdSource(null);
    setResetComplete(true);
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100 p-4">
      <div className="bg-white rounded-lg shadow-md p-8 max-w-md w-full">
        <h1 className="text-2xl font-bold mb-6 text-center">Reset User ID</h1>
        
        {resetComplete ? (
          <div className="text-center">
            <div className="mb-4 text-green-600 font-semibold">User ID has been reset successfully!</div>
            <p className="mb-6 text-gray-600">Your user ID has been cleared from localStorage. The next time you use the chat, a new ID will be generated or retrieved from your authentication.</p>
            <Link href="/chat" className="inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
              Return to Chat
            </Link>
          </div>
        ) : (
          <div>
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-2">Current User ID Information</h2>
              <div className="bg-gray-50 p-4 rounded-md">
                <p><span className="font-medium">User ID:</span> {userId || 'None'}</p>
                <p><span className="font-medium">Source:</span> {userIdSource || 'None'}</p>
              </div>
            </div>
            
            <div className="mb-6">
              <p className="text-red-600 font-semibold mb-2">Warning:</p>
              <p className="text-gray-600 mb-2">Resetting your user ID will disconnect you from your chat history. This action cannot be undone.</p>
              <p className="text-gray-600">If you're experiencing issues with authentication or user ID syncing, this may help resolve them.</p>
            </div>
            
            <div className="flex justify-center">
              <button
                onClick={resetUserId}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              >
                Reset User ID
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
