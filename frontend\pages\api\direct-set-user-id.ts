import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '@/lib/supabase';

/**
 * API endpoint to directly set the user ID for all devices
 * This endpoint allows setting a specific user ID as the primary ID for all devices
 * without requiring authentication
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { targetUserId } = req.body;

    if (!targetUserId) {
      return res.status(400).json({ error: 'Target user ID is required' });
    }

    console.log(`API: Setting primary user ID to ${targetUserId} for all devices`);
    
    // Get all user IDs from chats
    const { data: chats, error: chatsError } = await supabase
      .from('chats')
      .select('user_id')
      .order('created_at', { ascending: false });
      
    if (chatsError) {
      console.error('Error getting chats:', chatsError);
      return res.status(500).json({ error: 'Error getting chats' });
    }
    
    // Extract unique user IDs
    const userIds = new Set<string>();
    
    chats?.forEach(chat => {
      if (chat.user_id) {
        userIds.add(chat.user_id);
      }
    });
    
    // Migrate all chats to the target user ID
    for (const userId of userIds) {
      if (userId !== targetUserId) {
        console.log(`API: Migrating chats from ${userId} to ${targetUserId}`);
        
        // Update all chats to use the target user ID
        const { error: updateError } = await supabase
          .from('chats')
          .update({ user_id: targetUserId })
          .eq('user_id', userId);
          
        if (updateError) {
          console.error(`API: Error migrating chats from ${userId} to ${targetUserId}:`, updateError);
        } else {
          console.log(`API: Successfully migrated chats from ${userId} to ${targetUserId}`);
        }
      }
    }
    
    // Update all user ID mappings to use the target user ID
    const { error: mappingError } = await supabase
      .from('user_id_mappings')
      .update({ primary_user_id: targetUserId });
      
    if (mappingError) {
      console.error('API: Error updating user ID mappings:', mappingError);
    } else {
      console.log('API: Successfully updated all user ID mappings');
    }
    
    return res.status(200).json({
      success: true,
      primaryUserId: targetUserId,
      migratedUserIds: Array.from(userIds).filter(id => id !== targetUserId),
    });
  } catch (error) {
    console.error('Error in direct-set-user-id API:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
