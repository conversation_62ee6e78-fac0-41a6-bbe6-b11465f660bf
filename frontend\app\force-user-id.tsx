'use client';

import { useEffect } from 'react';
import { getSupabaseClient } from '@/lib/supabase-singleton';
import { fetchUserIdFromMainApp } from '@/lib/fetch-user-id';
import { fetchAuthTokenFromMainApp } from '@/lib/fetch-auth-token';

/**
 * This component ensures the user ID is consistent with Supabase auth
 * It checks multiple sources to get the correct user ID
 */
export default function ForceUserId() {
  useEffect(() => {
    // Only run on the client side
    if (typeof window === 'undefined') return;

    const syncUserId = async () => {
      try {
        // Get the current user ID from localStorage
        const currentUserId = localStorage.getItem('ai-chat-user-id');
        const currentUserIdSource = localStorage.getItem('ai-chat-user-id-source');

        console.log('SYNC USER ID: Current user ID:', currentUserId);
        console.log('SYNC USER ID: Current user ID source:', currentUserIdSource);

        // Check for an existing Supabase session
        console.log('SYNC USER ID: Checking for existing Supabase session');
        const supabase = getSupabaseClient();
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

        if (!sessionError && sessionData.session) {
          const supabaseUserId = sessionData.session.user.id;
          console.log('SYNC USER ID: Found existing Supabase session for user:', supabaseUserId);

          // If the current user ID is not the Supabase user ID, set it
          if (currentUserId !== supabaseUserId) {
            console.log(`SYNC USER ID: Updating user ID from ${currentUserId} to ${supabaseUserId}`);

            // Set the user ID in localStorage
            localStorage.setItem('ai-chat-user-id', supabaseUserId);
            localStorage.setItem('ai-chat-user-id-source', 'supabase');

            // Reload the page to ensure all components use the new user ID
            window.location.reload();
          } else {
            console.log('SYNC USER ID: User ID is already synced with Supabase');
          }

          // Exit early since we found an existing session
          return;
        }

        // If no existing session, try to get the user ID from localStorage
        console.log('SYNC USER ID: Trying to fetch user ID from localStorage');
        const mainAppUserId = await fetchUserIdFromMainApp();

        if (mainAppUserId) {
          console.log('SYNC USER ID: Found user ID from main app:', mainAppUserId);

          // If the current user ID is not the main app user ID, set it
          if (currentUserId !== mainAppUserId) {
            console.log(`SYNC USER ID: Updating user ID from ${currentUserId} to ${mainAppUserId}`);

            // Set the user ID in localStorage
            localStorage.setItem('ai-chat-user-id', mainAppUserId);
            localStorage.setItem('ai-chat-user-id-source', 'supabase');

            // Reload the page to ensure all components use the new user ID
            window.location.reload();
          } else {
            console.log('SYNC USER ID: User ID is already synced with main app');
          }

          // Exit early since we found the user ID from the main app
          return;
        }

        // If we couldn't get the user ID from the main app, try Supabase directly
        console.log('SYNC USER ID: Trying to get user ID from Supabase directly');

        // Use the existing supabase client
        const { data, error } = await supabase.auth.getUser();

        if (error) {
          console.error('Error getting user from Supabase:', error);

          // Check if the current user ID looks like a UUID (Supabase format)
          if (currentUserId && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(currentUserId)) {
            console.log('SYNC USER ID: Current user ID looks like a Supabase UUID, keeping it:', currentUserId);

            // Update the source to indicate it's a Supabase ID
            if (currentUserIdSource !== 'supabase') {
              localStorage.setItem('ai-chat-user-id-source', 'supabase');
              console.log('SYNC USER ID: Updated source to supabase');

              // Reload the page to ensure all components use the updated source
              window.location.reload();
            }
          }

          return;
        }

        // If we have a user from Supabase
        if (data?.user?.id) {
          const supabaseUserId = data.user.id;
          console.log('SYNC USER ID: Found Supabase user ID:', supabaseUserId);

          // If the current user ID is not the Supabase user ID, set it
          if (currentUserId !== supabaseUserId) {
            console.log(`SYNC USER ID: Updating user ID from ${currentUserId} to ${supabaseUserId}`);

            // Set the user ID in localStorage
            localStorage.setItem('ai-chat-user-id', supabaseUserId);
            localStorage.setItem('ai-chat-user-id-source', 'supabase');

            // Reload the page to ensure all components use the new user ID
            window.location.reload();
          } else {
            console.log('SYNC USER ID: User ID is already synced with Supabase');
          }
        } else {
          console.log('SYNC USER ID: No authenticated user found in Supabase');

          // Check if the current user ID looks like a UUID (Supabase format)
          if (currentUserId && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(currentUserId)) {
            console.log('SYNC USER ID: Current user ID looks like a Supabase UUID, keeping it:', currentUserId);

            // Update the source to indicate it's a Supabase ID
            if (currentUserIdSource !== 'supabase') {
              localStorage.setItem('ai-chat-user-id-source', 'supabase');
              console.log('SYNC USER ID: Updated source to supabase');

              // Reload the page to ensure all components use the updated source
              window.location.reload();
            }
          }
        }
      } catch (error) {
        console.error('Error syncing user ID:', error);
      }
    };

    syncUserId();
  }, []);

  // This component doesn't render anything
  return null;
}
