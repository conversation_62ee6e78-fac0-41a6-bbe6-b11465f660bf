<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>CreateLex Bridge – Dashboard</title>
  <style>
    body { font-family: Arial, sans-serif; background:#fafafa; color:#202124; margin:0; }
    header { background:#1a73e8; color:#fff; padding:15px; }
    main { padding:20px; }
    .status { margin-top:10px; font-weight:bold; }
    button { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; background: #1a73e8; color: white; display: inline-flex; align-items: center; gap: 8px; }
    button:disabled { background: #ccc; cursor: not-allowed; }
    .spinner { animation: spin 1s linear infinite; width: 16px; height: 16px; }
    @keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }
  </style>
</head>
<body>
  <header>
    <h2>CreateLex Bridge Dashboard</h2>
  </header>
  <main>
    <p>Welcome! Your local MCP server is running.</p>
    <button id="startBtn">
      <span id="startBtnText">Start MCP Server</span>
      <span id="startBtnSpinner" style="display: none;">
        <svg class="spinner" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" stroke-opacity="0.25"></circle>
          <path fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Starting...
      </span>
    </button>
    <div class="status" id="status">MCP server status: <span id="mcpStatus">checking…</span></div>
  </main>
  <script>
    // Query server status via IPC if needed in future
    const statusSpan = document.getElementById('mcpStatus');
    const startBtn = document.getElementById('startBtn');

    async function refreshStatus() {
      const status = await window.bridgeApi.getMCPStatus();
      statusSpan.textContent = status.isRunning ? `running (port ${status.port})` : 'stopped';
    }

    startBtn.addEventListener('click', async () => {
      const startBtnText = document.getElementById('startBtnText');
      const startBtnSpinner = document.getElementById('startBtnSpinner');
      
      // Show loading spinner
      startBtn.disabled = true;
      startBtnText.style.display = 'none';
      startBtnSpinner.style.display = 'inline-flex';
      
      try {
        await window.bridgeApi.startMCP();
        await refreshStatus();
      } finally {
        // Hide loading spinner
        startBtnText.style.display = 'inline';
        startBtnSpinner.style.display = 'none';
        startBtn.disabled = false;
      }
    });

    refreshStatus();
  </script>
</body>
</html> 