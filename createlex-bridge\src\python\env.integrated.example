# ===========================================
# UnrealGenAI Integrated Docker Environment
# ===========================================
# This file contains all environment variables from your existing
# frontend and backend setup, configured for the integrated Docker deployment

# ===========================================
# API Integration (Required)
# ===========================================
# Your existing backend API URL (for Docker Compose)
API_BASE_URL=http://backend:5001

# For local development/testing
NEXT_PUBLIC_API_URL=http://localhost:5001
FRONTEND_URL=http://localhost:3000

# ===========================================
# Authentication & JWT (From your backend)
# ===========================================
# JWT secret for token signing/verification
JWT_SECRET=your-jwt-secret-key-here
JWT_EXPIRES_IN=7d

# Google OAuth configuration
GOOGLE_CLIENT_ID=your-google-client-id.googleusercontent.com
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id.googleusercontent.com

# ===========================================
# Stripe Configuration (From your backend)
# ===========================================
# Stripe API keys
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key

# Stripe webhook secret
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# Stripe price IDs for subscriptions
STRIPE_PRICE_ID=price_your-default-price-id
STRIPE_PRICE_ID_BASIC=price_your-basic-plan-id
STRIPE_PRICE_ID_PRO=price_your-pro-plan-id

# Token purchase price IDs
STRIPE_PRICE_ID_TOKENS_SMALL=price_your-small-tokens-id
STRIPE_PRICE_ID_TOKENS_MEDIUM=price_your-medium-tokens-id
STRIPE_PRICE_ID_TOKENS_LARGE=price_your-large-tokens-id

# ===========================================
# Supabase Configuration (From your backend)
# ===========================================
# Supabase connection details
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your-supabase-service-key
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# ===========================================
# AI API Keys (From your backend)
# ===========================================
# OpenAI API
PS_OPENAIAPIKEY=sk-your-openai-api-key
OPENAI_API_KEY=sk-your-openai-api-key

# DeepSeek API
PS_DEEPSEEKAPIKEY=your-deepseek-api-key

# Anthropic API
PS_ANTHROPICAPIKEY=sk-ant-your-anthropic-api-key
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key

# Google API
PS_GOOGLEAPIKEY=your-google-api-key
GOOGLE_API_KEY=your-google-api-key

# ===========================================
# Email Configuration (From your backend)
# ===========================================
# SMTP settings for notifications
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>
SYSTEM_NOTIFICATION_EMAIL=<EMAIL>

# ===========================================
# MCP Server Configuration
# ===========================================
# Original MCP server settings
MCP_SERVER_URL=ws://mcp_server:8765
NEXT_PUBLIC_MCP_SERVER_URL=http://localhost:8000

# Protected MCP server settings
MCP_HOST=0.0.0.0
MCP_PORT=8000
MCP_SERVER_PORT=8000

# Unreal Engine connection
UNREAL_HOST=localhost
UNREAL_PORT=9877

# ===========================================
# Docker Subscription Protection Settings
# ===========================================
# Development mode override (set to true for testing)
ALWAYS_SUBSCRIBED=false

# Subscription check interval (in seconds)
SUBSCRIPTION_CHECK_INTERVAL=3600

# Health check configuration
HEALTH_PORT=8001
HEALTH_CHECK_INTERVAL=30

# ===========================================
# Development/Testing Configuration
# ===========================================
# Node environment
NODE_ENV=production

# For testing JWT token extraction
DEMO_JWT_TOKEN=

# Admin token for backend access (optional)
ADMIN_TOKEN=

# Local test mode flags
LOCAL_TEST_MODE=false
LOCAL_AUTH_TOKEN=test-token-123
DIRECT_UNREAL_MODE=false

# ===========================================
# Security & CORS Configuration
# ===========================================
# Allowed origins for CORS
ALLOWED_ORIGINS=https://yourdomain.com,http://localhost:3000,http://localhost:5001

# CORS settings
CORS_ENABLED=true

# Rate limiting
RATE_LIMIT_ENABLED=true
MAX_REQUESTS_PER_MINUTE=60

# ===========================================
# Logging & Monitoring
# ===========================================
# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Metrics and monitoring
ENABLE_METRICS=true
ENABLE_TELEMETRY=false

# Grafana password (for monitoring stack)
GRAFANA_PASSWORD=admin

# ===========================================
# Remote Hosting Configuration (Optional)
# ===========================================
# Remote server settings
REMOTE_HOST=your-server.com
REMOTE_PORT=8000

# SSL/TLS configuration
SSL_CERT_PATH=/app/ssl/cert.pem
SSL_KEY_PATH=/app/ssl/key.pem

# Cloud provider settings
CLOUD_PROVIDER=digitalocean
CLOUD_REGION=nyc3
INSTANCE_SIZE=s-2vcpu-4gb

# ===========================================
# Feature Flags
# ===========================================
# Enable/disable features
ENABLE_OFFLINE_MODE=true
ENABLE_AUTO_UPDATES=true

# Database configuration (if using separate license server)
POSTGRES_USER=unrealgenai_admin
POSTGRES_PASSWORD=secure-db-password-here

# ===========================================
# Token Extractor Configuration
# ===========================================
# Token extraction settings
BACKEND_URL=http://backend:5001
TOKEN_OUTPUT_PATH=/app/config/auth_token.jwt
EXTRACTION_INTERVAL=300

# ===========================================
# Port Configuration
# ===========================================
# Service ports
PORT=5001
FRONTEND_PORT=3000
BACKEND_PORT=5001
MCP_DOCKER_PORT=8000
HEALTH_CHECK_PORT=8001 